<template>
  <div class="container">
    <el-tabs v-model="tabName" type="card">
      <el-tab-pane label="领取任务" name="first">
        <receive :businessType="businessType" @changeTab="changeTab"></receive>
      </el-tab-pane>
      <el-tab-pane label="我的任务" name="second">
        <list :businessType="businessType" ref="prepareList"></list>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import receive from "./components/auditReceive";
import list from "./components/auditList";

export default {
  name: "taskToBeAudit",
  components: {
    receive,
    list,
  },
  data() {
    return {
      tabName: "first",
    };
  },
  created() {
    if(this.$route.query.tab) {
      this.tabName = this.$route.query.tab
    }
  },
  computed: {
    // 是否为库外图片：2 库外；1库内
    businessType: function () {
      return this.$route.query.businessType;
    },
  },
  methods: {
    changeTab(name) {
      this.tabName = name;
      if(name == 'second'){
        this.$refs.prepareList.searchForm();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding: 15px 15px 0;
}
</style>
