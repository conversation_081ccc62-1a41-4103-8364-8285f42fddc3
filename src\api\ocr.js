/**
 * OCR相关API接口
 */
import request from '@/utils/request'

/**
 * 通过后端代理调用OCR识别接口
 * @param {string} imgUrl 图片URL
 * @param {string} type OCR类型，默认为'GENERAL_STRUCTURE'
 * @returns {Promise} OCR识别结果
 */
export function ocrIdentifyProxy(imgUrl, type = 'GENERAL_STRUCTURE') {
  return request({
    url: '/api/ocr/proxy/identify',
    method: 'POST',
    data: {
      imgUrl: imgUrl,
      type: type
    }
  })
}

/**
 * 直接调用OCR识别接口（用于开发环境或已配置CORS的环境）
 * @param {string} imgUrl 图片URL
 * @param {string} type OCR类型，默认为'GENERAL_STRUCTURE'
 * @returns {Promise} OCR识别结果
 */
export function ocrIdentifyDirect(imgUrl, type = 'GENERAL_STRUCTURE') {
  return request({
    url: 'https://xyy-ocr.test.ybm100.com/ocr/identify',
    method: 'POST',
    data: {
      imgUrl: imgUrl,
      type: type
    }
  })
}

export default {
  ocrIdentifyProxy,
  ocrIdentifyDirect
}
