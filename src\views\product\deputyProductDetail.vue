<template>
  <div class="component-container">
    <el-tabs v-model="activeName" type="border-card">
      <el-tab-pane label="基础属性" name="first">
        <approval-process :approvalData="approvalData"></approval-process>
        <spu ref="spu" :spuData="spuData" :skuData="skuData" :formDisable="true"></spu>
        <sku ref="sku" :skuData="skuData" :sauData="sauData" :formDisable="true"></sku>
        <label-attr ref="labelAttr" :skuData="skuData" :spuData="spuData" :loadForm="false" :formDisable="true"
        :showEmpty="spuCategory.type == 'GENERAL_MEDICINE' || spuCategory.type == 'TRADITIONAL_MEDICINE' ? false : true"></label-attr>
        <extended-attr ref="extend" v-if="spuCategory.type == 'GENERAL_MEDICINE'" :formDisable="true"></extended-attr>
        <extended-attr2 ref="extend" v-if="spuCategory.type == 'TRADITIONAL_MEDICINE'" :formDisable="true"></extended-attr2>
        <div class="basic-info-title">
          {{ urlParam.pageType ? "管理属性" : "添加管理属性"
          }}<span v-if="!urlParam.pageType" class="tip">注意：1、每添加一行，就添加了一个副商品；2、删除的副商品无法提交到工作流；</span>
        </div>
        <el-tabs type="border-card" v-model="tabIndex">
          <el-tab-pane v-for="(item, index) in [skuData]" :key="index" :label="'sku' + (index + 1)" :name="index">
            <vxe-table
              :height="250"
              style="padding-bottom:72px"
              border
              resizable
              auto-resize
              size="small"
              align="center"
              :row-class-name="rowClassName"
              :tooltip-config="{ enterable: false }"
              :data="sauTableData[index]"
              :editConfig="{ trigger: 'click', mode: 'cell', activeCellMethod: true }"
            >
              <vxe-table-column type="seq" title="序号" min-width="50" show-header-overflow show-overflow> </vxe-table-column>
              <vxe-table-column field="sauCode" title="商品编码" min-width="120" show-header-overflow show-overflow></vxe-table-column>
              <vxe-table-column field="skuCode" title="sku" min-width="120" show-header-overflow show-overflow></vxe-table-column>
              <vxe-table-column title="是否线下业务" min-width="120" show-header-overflow show-overflow>
                <template v-slot="{ row }">
                  <el-radio :disabled="(!row.isNew && urlParam.pageType !== 'edit') || banEdit" v-model="row.offlineBusinessType" :label="1"
                    >是</el-radio
                  >
                  <el-radio :disabled="(!row.isNew && urlParam.pageType !== 'edit') || banEdit" v-model="row.offlineBusinessType" :label="0"
                    >否</el-radio
                  >
                </template>
              </vxe-table-column>
              <vxe-table-column
                field="attrValue"
                title="销售渠道"
                min-width="120"
                show-header-overflow
                show-overflow
                :editRender="{ name: 'input', events: { input: changeComment } }"
              >
                <template #edit="{ row }">
                  <el-input
                    :disabled="(!row.isNew && urlParam.pageType !== 'edit') || banEdit"
                    v-model="row.attrValue"
                    maxlength="10"
                  ></el-input>
                </template>
              </vxe-table-column>
              <vxe-table-column
                v-if="urlParam.pageType"
                field="mergeProduct"
                title="合并商品"
                min-width="120"
                show-header-overflow
                show-overflow
              ></vxe-table-column>
              <vxe-table-column title="操作" min-width="120" show-header-overflow show-overflow>
                <template v-slot="{ row, $rowIndex }">
                  <span v-if="row.isNew && !urlParam.pageType">
                    <el-link :underline="false" type="primary" @click.stop="deleteItem($rowIndex)">删除</el-link></span
                  >
                </template>
              </vxe-table-column>
            </vxe-table>
          </el-tab-pane>
        </el-tabs>
        <modify-record v-if="urlParam.pageType === 'detail'" :recordData="recordData" style="padding-bottom:10px"></modify-record>
      </el-tab-pane>
      <el-tab-pane label="资质属性" name="third">
        <qualification-attr></qualification-attr>
      </el-tab-pane>
      <el-tab-pane label="精修图版本" name="fourth">
        <sales-attr :imgTitle="imgTitle" :showChange="true" :isDeputy="true"></sales-attr>
      </el-tab-pane>
    </el-tabs>
    <el-row class="bottom-btns">
      <div :span="24" class="bottom-btn-wrap">
        <el-button type="primary" @click="editProduct">修改</el-button>
      </div>
    </el-row>
  </div>
</template>

<script>
import modifyRecord from "./modifyRecord"
import qualificationAttr from "./qualificationAttr"
import salesAttr from "./salesAttr"
// 商品基础共用信息混入对象
import { productMixinBase } from "@/mixin/productMixinBase.js"
import {
  getApplyInfo, //审批流信息
  getProductData, //副商品详情
  modifyedSau //副商品详情2
} from "@/api/product"

export default {
  name: "",
  mixins: [productMixinBase],
  filters: {
    filterUnit(e) {
      switch (e) {
        case 1:
          return "日"
        case 2:
          return "月"
        case 3:
          return "年"
      }
    }
  },
  components: {
    modifyRecord,
    qualificationAttr,
    salesAttr
  },
  watch: {},
  data() {
    return {
      activeName: "first",
      banBtn: false,
      banEdit: false,
      tabIndex: 0,
      spuData: {},
      skuData: {},
      sauTableData: [],
      recordData: []
    }
  },
  computed: {
    // 路由参数
    urlParam() {
      return this.$route.query
    },
    spuCategory() {
      return this.$store.state.product.spuCategory;
    },
  },
  async created() {
    this.$store.commit("product/SET_OPERATION_TYPE", "detail")
    // 获取审批流信息
    this.getApplyInfo()
    if (this.urlParam.pageType) {
      this.banBtn = true
      this.banEdit = this.urlParam.pageType === "edit" ? false : true
      // 获取详情
      await this.getSauDetail()
    }
  },
  methods: {
    editProduct(){
      let productCode, productType
      productCode = this.urlParam.productCode
      productType = this.urlParam.productType
      try {
        parent.CreateTab(
          "../static/dist/index.html#/product/"+(productType == 2 ? 'editProduct' : 'addDeputyProduct')+"?productCode=" +
            productCode +
            "&sauCode=" +
            this.sauTableData[0].sauCode +
            "&preOperateStatus=" +
            this.sauTableData[0].preOperateStatus +
            "&productType=" +
            productType +
            "&spuCode=" +
            this.urlParam.spuCode +
            "&pageType=edit",
          "商品修改",
        );
      } catch {
        this.$router.push({
          path: `/product/${productType == 2 ? 'editProduct' : 'addDeputyProduct'}`,
          query: {
            productCode: productCode,
            productId: this.sauTableData[0].productId,
            productType: productType,
            spuCode: this.urlParam.spuCode,
            sauCode: this.sauTableData[0].sauCode,
            preOperateStatus: this.sauTableData[0].preOperateStatus,
            pageType:"edit",
          },
        });
      }
    },
    // 修改销售渠道
    changeComment({ rowIndex, column }) {
      this.sauTableData[this.tabIndex][rowIndex].attrValue = column.model.value
    },
    // 删除副商品btn
    deleteItem(e) {
      this.sauTableData[this.tabIndex].splice(e, 1)
    },
    async getApplyInfo() {
      let param = {
        selectType: 1, // 查询类型, (0:草稿, 1:新增, 2:属性修改,3:审核或驳回修改,4:查询详情)
        applyCode: "", //单据编号
        productCode: "", //商品编码
        productType: 1, //商品类型(1-SPU;2-SKU;3-SAU)
        spuCode: "" //SPU编码
      }
      let res = await getApplyInfo(param)
      if (res.success) {
        this.approvalData = res.data
        if (this.urlParam.pageType) {
          this.approvalData.approvalProcess.showFlow = false
        }
      } else {
        this.$message.error(res.retMsg)
      }
    },
    // 详情
    async getSauDetail() {
      try {
        const res = this.urlParam.pageType === "edit" ? await modifyedSau(this.urlParam) : await getProductData(this.urlParam)
        console.log(res)
        if (res.retCode === 0) {
          this.spuData = res.data.spu
          this.skuData = res.data.sku
          if (this.urlParam.pageType === "edit") {
            this.sauTableData = [res.data.sau]
          } else {
            // 过滤
            this.sauTableData = [[]]
            res.data.sau.map(item => {
              if (item.skuCode === this.urlParam.skuCode) {
                this.sauTableData[0].push(item)
              }
            })
          }
          this.recordData = res.data.record
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 行样式 精确查询变蓝
    rowClassName({ row }) {
      if (row.sauCode === this.urlParam.productCode && this.urlParam.pageType === "detail") {
        return "row-blue"
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.el-tabs--border-card {
  border: none;
  box-shadow: none;
  /deep/ .el-tabs__content {
    padding-bottom: 80px;
  }
}
.component-container {
  padding-top: 0px;
  .loading {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
  }
  /deep/ .el-tabs__header {
    border-bottom: none;
    background-color: #f2f2f2;
  }
  /deep/.el-tabs__content {
    padding: 0 !important;
  }
  .el-tabs--border-card {
    min-height: 150px;
    border: none;
    box-shadow: none;
    /deep/ .el-tabs__content {
      padding-bottom: 80px;
      padding-left: 0;
      padding-right: 0;
    }
  }
  .bottom-btns {
    background: #f0f2f5;
    width: 100%;
    position: fixed;
    bottom: 0;
    padding: 15px;
    z-index: 10;
  }
}
.bottom-btn-wrap {
  display: flex;
  justify-content: center;
}
// csw
.empty {
  background: rgb(240, 242, 245);
  height: 20px;
}
.basic-info-title {
  font-weight: 600;
  margin-bottom: 22px;
  border-bottom: 1px solid #e4e4eb;
  line-height: 50px;
  padding-left: 20px;
}
.tip {
  margin-left: 20px;
  font-size: 12px;
  color: rgb(197, 197, 197);
}
/deep/ .row-blue {
  color: #fff;
  background-color: #3b95a8;
}
</style>
