<template>
  <div class="component-container" style="width:100%">
    <!-- 资质属性 -->
    <vxe-table
      border
      highlight-hover-row
      auto-resize
      resizable
      align="center"
      :tooltip-config="{enterable: false}"
      :data="tableData"
      ref="table"
    >
      <vxe-table-column type="index" title="序号" width="60" show-header-overflow show-overflow></vxe-table-column>
      <vxe-table-column
        field="mechanism"
        title="所属机构"
        min-width="120"
        show-header-overflow
        show-overflow
      ></vxe-table-column>
      <vxe-table-column
        field="businessCode"
        title="商品编码"
        min-width="120"
        show-header-overflow
        show-overflow
      ></vxe-table-column>
      <vxe-table-column
        field="campDate"
        title="首营时间"
        min-width="120"
        show-header-overflow
        show-overflow
      >
        <template v-slot="{ row }">{{ row.campDate | dateTime }}</template>
      </vxe-table-column>
      <vxe-table-column
        field="approvalName"
        title="批件名称"
        min-width="120"
        show-header-overflow
        show-overflow
      ></vxe-table-column>
      <vxe-table-column
        field="approvalCode"
        title="批件编号"
        min-width="120"
        show-header-overflow
        show-overflow
      ></vxe-table-column>
      <vxe-table-column
        field="approvedContent"
        title="核准内容"
        min-width="120"
        show-header-overflow
        show-overflow
      ></vxe-table-column>
      <vxe-table-column
        field="issueDate"
        title="签发日期"
        min-width="120"
        show-header-overflow
        show-overflow
      >
        <template v-slot="{ row }">{{ row.issueDate | dateTime }}</template>
      </vxe-table-column>
      <vxe-table-column
        field="validityDate"
        title="有效期至"
        min-width="120"
        show-header-overflow
        show-overflow
      >
        <template v-slot="{ row }">{{ row.validityDate | dateTime }}</template>
      </vxe-table-column>
      <vxe-table-column
        field="annexAttr"
        title="附件"
        min-width="120"
        show-header-overflow
        show-overflow
      >
        <template v-slot="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="previewImg(row.annexAttr)"
            :disabled="!row.annexAttr || !row.annexAttr.length"
          >
            <i class="el-icon-view"></i>
            预览（{{ row.annexNum }}）
          </el-link>
        </template>
      </vxe-table-column>
    </vxe-table>

    <image-preview
      v-if="previewDlgVisible"
      :on-close="closeApprovalViewer"
      :url-list="previewImgList"
    ></image-preview>
  </div>
</template>

<script>
import { parseTimestamp } from "@/utils/index.js";
import imagePreview from "@/components/common/preview/imagePreview";

export default {
  name: "QualificationAttrNew",
  components: { imagePreview },
  filters: {
    dateTime: function(value) {
      if (!value) return "";
      return parseTimestamp(value);
    }
  },
  props: {},
  data() {
    return {
      tableData: [],
      previewDlgVisible: false,
      previewImgList: []
    };
  },
  computed: {
    // 路由参数
    urlParam() {
      return this.$route.query;
    }
  },
  watch: {},
  created() {
    this.initMockData();
  },
  mounted() {},
  methods: {
    // 初始化模拟数据
    initMockData() {
      // 模拟资质属性数据
      this.tableData = [
        {
          id: 1,
          mechanism: "华润医药集团",
          businessCode: "P001",
          campDate: Date.now() - 365 * 24 * 60 * 60 * 1000, // 一年前
          approvalName: "阿莫西林胶囊药品注册证",
          approvalCode: "国药准字*********",
          approvedContent: "用于敏感菌所致的各种感染",
          issueDate: Date.now() - 730 * 24 * 60 * 60 * 1000, // 两年前
          validityDate: Date.now() + 1095 * 24 * 60 * 60 * 1000, // 三年后
          annexNum: 3,
          annexAttr: [
            {
              fileName: "药品注册证.pdf",
              fileUrl: "https://example.com/files/registration.pdf"
            },
            {
              fileName: "生产许可证.pdf", 
              fileUrl: "https://example.com/files/production.pdf"
            },
            {
              fileName: "质量标准.pdf",
              fileUrl: "https://example.com/files/quality.pdf"
            }
          ]
        },
        {
          id: 2,
          mechanism: "华润医药集团",
          businessCode: "P001",
          campDate: Date.now() - 180 * 24 * 60 * 60 * 1000, // 半年前
          approvalName: "GMP认证证书",
          approvalCode: "GMP20230001",
          approvedContent: "药品生产质量管理规范认证",
          issueDate: Date.now() - 365 * 24 * 60 * 60 * 1000, // 一年前
          validityDate: Date.now() + 730 * 24 * 60 * 60 * 1000, // 两年后
          annexNum: 2,
          annexAttr: [
            {
              fileName: "GMP证书.pdf",
              fileUrl: "https://example.com/files/gmp.pdf"
            },
            {
              fileName: "检查报告.pdf",
              fileUrl: "https://example.com/files/inspection.pdf"
            }
          ]
        },
        {
          id: 3,
          mechanism: "华润医药集团",
          businessCode: "P001", 
          campDate: Date.now() - 90 * 24 * 60 * 60 * 1000, // 三个月前
          approvalName: "药品经营许可证",
          approvalCode: "药经许字20230002",
          approvedContent: "药品批发经营许可",
          issueDate: Date.now() - 180 * 24 * 60 * 60 * 1000, // 半年前
          validityDate: Date.now() + 1460 * 24 * 60 * 60 * 1000, // 四年后
          annexNum: 1,
          annexAttr: [
            {
              fileName: "经营许可证.pdf",
              fileUrl: "https://example.com/files/business.pdf"
            }
          ]
        }
      ];
    },

    // 预览图片
    previewImg(list) {
      if (list.length) {
        this.previewImgList = []; // 清空之前的数据
        list.forEach(item => {
          let { fileName, fileUrl } = item;
          this.previewImgList.push({
            mediaName: fileName,
            mediaUrl: fileUrl
          });
        });
        this.previewDlgVisible = true;
      }
    },

    // 关闭批件图片预览
    closeApprovalViewer() {
      this.previewImgList = [];
      this.previewDlgVisible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.component-container {
  padding: 20px;
}
</style>
