import axios from 'axios'
import { _Message } from '@/components/common/resectMessage';
import Popup from '@/components/ajaxDialog/share.js'
console.log('process.env.VUE_APP_BASE_API',process.env.VUE_APP_BASE_API);
const service = axios.create({
  // baseURL: "http://meproduct.dev.ybm100.com:8081", // url = base url + request url
  baseURL: process.env.VUE_APP_BASE_API,
  withCredentials: true, // send cookies when cross-domain requests
  timeout: 500000, // request timeout
  onUploadProgress: p => { return 100 * ( p.loaded / p.total ) }
})
service.interceptors.request.use(
  config => {
    return config
  },
  error => {
    console.log(error) // for debug
    _Message.error("请求异常")
  }
)

service.interceptors.response.use(
  response => {
    console.log('process.env.VUE_APP_BASE_API',process.env.VUE_APP_BASE_API);
    let resp = "";
    // 判断是否被用户中心302重定向到登录
    if (typeof response.data == "string" || response.code == '401') {
      Popup.install()
    }
    if (response.config.needResponseHeader) {
      resp = Object.assign({
        data: response.data,
        responseHeaders: response.headers
      });
    } else {
      resp = response.data;
    }
    return resp
  },
  error => {
    Popup.install()
  }
)

export default service
