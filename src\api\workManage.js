import request from "@/utils/request";
import qs from "qs"; // 请求数据 转成form data 形式

/**
 * author:caoshuwen
 * date: 2021-07-21
 * description:获取工作流列表
 * **/
export function userApprovalProcessList(data) {
    return request({
        url: "/api/approve/find/userApprovalProcessList",
        method: "get"
    });
}

/**
 * author:caoshuwen
 * date: 2021-07-21
 * description:获取角色类型列表
 * **/
export function userRoleList(data) {
    return request({
        url: "/api/approve/find/userRoleList",
        method: "get"
    });
}

/**
 * author:caoshuwen
 * date: 2021-07-21
 * description:获取任务来源列表
 * **/
export function taskSourceList(data) {
    return request({
        url: "/api/approve/find/taskSourceList",
        method: "get"
    });
}

/**
 * author:caoshuwen
 * date: 2021-07-26
 * description:获取当前审核节点列表
 * **/
export function auditNodeList(data) {
    return request({
        url: "/api/approve/list/find/currentAuditNodeList",
        method: "get"
    });
}

/**
 * author:caoshuwen
 * date: 2021-07-22
 * description:获取我的已审列表
 * **/
export function getMyFinishProcessList(data) {
    return request({
        url: "/api/approve/list/myFinishProcessList",
        method: "post",
        data
    });
}

/**
 * author:caoshuwen
 * date: 2021-07-22
 * description:获取我的已申请列表
 * **/
export function getMyApplyProcessList(data) {
    return request({
        url: "/api/approve/list/myApplyProcessList",
        method: "post",
        data
    });
}

/**
 * author:caoshuwen
 * date: 2021-07-26
 * description:导出我的已申请列表
 * **/
export function exportMyApplyExportList(data) {
    return request({
        url: "/api/approve/list/find/myApplyExportList",
        method: "post",
        data
    });
}

/**
 * author:caoshuwen
 * date: 2021-07-26
 * description:导出我的已审列表
 * **/
export function exportMyFinishProcessExportList(data) {
    return request({
        url: "/api/approve/list/find/myFinishProcessExportList",
        method: "post",
        data
    });
}

/**
 * author:caoshuwen
 * date: 2021-07-26
 * description:查询待领取任务数量
 * **/
export function preClaimTaskNum(data) {
    return request({
        url: "/api/approve/find/preClaimTaskNum",
        method: "post",
        data
    });
}

/**
 * author:caoshuwen
 * date: 2021-07-26
 * description:提交批量领取待办事项
 * **/
export function batchClaimTask(data) {
    return request({
        url: "/api/approve/submit/batchClaimTask",
        method: "post",
        data
    });
}

/**
 * author:caoshuwen
 * date: 2021-07-26
 * description:已处理审批流任务列表
 * **/
export function myFinishProcessList(data) {
    return request({
        url: "/api/approve/list/myDaiBanProcessList",
        method: "post",
        data
    });
}

/**
 * author:caoshuwen
 * date: 2021-07-26
 * description:导出已处理工作流
 * **/
export function exportMyFinishProcessList(data) {
    return request({
        url: "/api/approve/list/find/daiBanExportList",
        method: "post",
        data
    });
}

/**
 * author:caoshuwen
 * date: 2021-07-27
 * description:新的审批流商品详情页（商品新增）
 * **/
export function getAddProductData(data) {
    return request({
        url: '/api/approve/list/get/spuDetail',
        // data: qs.stringify(data),
        params: data,
        method: 'get'
    })
}

/**
 * author:caoshuwen
 * date: 2021-07-28
 * description:新的查询流程图（商品新增）
 * **/
export function getAddProcessData(data) {
    return request({
        url: '/api/approve/list/find/queryTaskAuditDetail',
        // data: qs.stringify(data),
        params: data,
        method: 'get'
    })
}

/**
 * author:caoshuwen
 * date: 2022-02-14
 * description:新的查询流程图（新商品新增）
 * **/
export function newGetAddProcessData(data) {
    return request({
        url: '/api/approve/getProcessingFlow',
        params: data,
        method: 'get'
    })
}

/**
 * author:caoshuwen
 * date: 2021-07-30
 * description:获取审核按钮权限
 * **/
export function getAuth(data) {
    return request({
        url: '/api/approve/list/getButtonName',
        // data: qs.stringify(data),
        params: data,
        method: 'get'
    })
}

/**
 * author:caoshuwen
 * date: 2021-08-02
 * description:审批结束任务
 * **/
export function auditRejectEnd(data) {
    return request({
        url: "/api/approve/audit/auditRejectEnd",
        method: "post",
        data
    });
}

/**
 * author:caoshuwen
 * date: 2021-08-02
 * description:审批回退到提交人
 * **/
export function auditRejectStart(data) {
    return request({
        url: "/api/approve/audit/auditRejectStart",
        method: "post",
        data
    });
}

/**
 * author:caoshuwen
 * date: 2021-08-02
 * description:审批回退到上一级审批人
 * **/
export function auditRejectPrev(data) {
    return request({
        url: "/api/approve/audit/auditRejectPrev",
        method: "post",
        data
    });
}

/**
 * author:caoshuwen
 * date: 2021-08-02
 * description:商品新增审批通过
 * **/
export function auditPass(data) {
    return request({
        url: "/api/approve/audit/auditPass",
        method: "post",
        data
    });
}

/**
 * author:caoshuwen
 * date: 2021-08-02
 * description:新品上报审批通过 提交商品信息
 * **/
export function auditPassPresentProduct(data) {
    return request({
        url: "/api/approve/audit/auditPassPresentProduct",
        method: "post",
        data
    });
}

/**
 * author:caoshuwen
 * date: 2021-08-03
 * description:新品上报审批驳回
 * **/
export function newPresentReject(data) {
    return request({
        url: "/api/approve/audit/presentReject",
        method: "post",
        data
    });
}

/**
 * author:caoshuwen
 * date: 2021-08-03
 * description:字典查询 新审批流
 * **/
export function dictListSearch(data) {
    return request({
        url: '/api/dict/find/dictAllList',
        method: 'post',
        data
    })
}

/**
 * author:caoshuwen
 * date: 2021-08-04
 * description:驳回修改商品重新提交
 * **/
export function rejectModify(data) {
    return request({
        url: '/api/spu/do/rejectModify',
        method: 'post',
        data
    })
}

/**
 * author:caoshuwen
 * date: 2021-08-16
 * description:生成替图任务
 * **/
export function addTask(data) {
    return request({
        url: '/api/productPictureReplace/addTask',
        method: 'post',
        data
    })
}

/**
 * author:caoshuwen
 * date: 2021-09-01
 * description:商品批量预览查询
 * **/
export function batchPreview(data) {
    return request({
        url: '/api/batchUpload/find/preview',
        method: 'post',
        data
    })
}

/**
 * author:caoshuwen
 * date: 2021-08-16
 * description:查询替图任务详情
 * **/
export function finishingDetail(data) {
    return request({
        url: `/api/productPictureReplace/get/detail/${data.applyCode}`,
        // data: qs.stringify(data),
        // params:data,
        method: 'get'
    })
}

/**
 * author:caoshuwen
 * date: 2021-09-02
 * description:商品批量驳回
 * **/
export function batchAuditReject(data) {
    return request({
        url: '/api/approve/audit/batchAuditReject',
        method: 'post',
        data
    })
}

/**
 * author:caoshuwen
 * date: 2021-08-18
 * description:替图任务审核驳回
 * **/
export function replaceReject(data) {
    return request({
        url: '/api/approve/audit/pictureReplace/auditRejectEnd',
        method: 'post',
        data
    })
}

/**
 * author:caoshuwen
 * date: 2021-08-18
 * description:替图任务审核通过 一审和二审
 * **/
export function replacePass(data) {
    return request({
        url: '/api/approve/audit/pictureReplace/auditPass',
        method: 'post',
        data
    })
}

/**
 * author:caoshuwen
 * date: 2021-08-19
 * description:替图任务 - 获取友商库图片接口
 * **/
export function getFriendLibraryPicture(data) {
    return request({
        url: '/api/productPictureReplace/getFriendLibraryPicture',
        method: 'post',
        // data
        params: data
    })
}

/**
 * author:caoshuwen
 * date: 2021-09-02
 * description:商品批量通过
 * **/
export function batchAuditPass(data) {
    return request({
        url: '/api/approve/audit/batchAuditPass',
        method: 'post',
        data
    })
}

/**
 * author:caoshuwen
 * date: 2021-11-05
 * description:精修图版本停启用详情
 * **/
export function imgVersionDetail(data) {
    return request({
        url: '/api/productPictureVersion/task/detail',
        method: 'post',
        data
    })
}

/**
 * author:caoshuwen
 * date: 2022-02-21
 * description:获取所有机构
 * **/
export function getOrganizeList() {
    return request({
        url: '/api/organization/tree',
        method: 'get'
    })
}

/**
 * author:caoshuwen
 * date: 2022-02-21
 * description:获取机构下的部门人员
 * **/
export function getEmployee(data) {
    return request({
        url: `/api/organization/employee/${data}`,
        method: 'get'
    })
}

/**
 * author:caoshuwen
 * date: 2022-04-06
 * description:批量修改详情
 * **/
export function getBatchDetail(data) {
    return request({
        url: `/api/modifyRecord/batch/detail/${data}`,
        method: 'get'
    })
}

/**
 * author:caoshuwen
 * date: 2022-04-06
 * description:点击批量修改的下载后修改下载状态
 * **/
export function batchDownloadDocheck(data) {
    return request({
        url: "api/modifyRecord/batch/doCheck",
        method: 'post',
        data
    })
}
/**
 * author:xiaohan
 * date: 2024-06-09
 * description:查询商品业务编码
 * **/
export function queryOriginBarcodes(data) {
    return request({
        url: `/api/inner/query/queryOriginBarcodes?barcodes=${data.barcodes}`,
        method: 'post'
    })
}

/**
 * author:yanling
 * date: 2025-06-12
 * description:标品查重
 * **/
export function checkRepeat(data) {
    return request({
        url: '/api/sau/matchProducts',
        method: 'post',
        data
    })
}
