<!--
 * @Descripttion: 
 * @Version: 1.0
 * @Author: zyh
 * @Date: 2025-02-07 14:55:47
 * @LastEditors: zyh
 * @LastEditTime: 2025-02-18 14:08:21
-->

<template>
    <div class="container">
        <el-dialog title="拓展字典配置"
                   :visible.sync="dialogVisible"
                   width="650px">
            <div class="search-form-box">
                <el-form :inline="true"
                         :model="formData">
                    <el-form-item label="拓展类型">
                        <el-select v-model="formData.categoryCode"
                                   disabled
                                   placeholder="拓展类型">
                            <el-option v-for="item in categoryCodeList"
                                       :key="item.id"
                                       :label="item.dictName"
                                       :value="item.id"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="拓展字段">
                        <el-input v-model.trim="formData.dictName"
                                  clearable
                                  placeholder="拓展字段"></el-input>
                    </el-form-item>
                    <el-form-item style="margin-left:15px">
                        <el-button size="medium"
                                   type="primary"
                                   @click="onSearch">查询</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <div class="table-wrap">
                <vxe-table border
                           highlight-hover-row
                           resizable
                           auto-resize
                           size="small"
                           align="center"
                           :tooltip-config="{ enterable: false }"
                           :loading="tableLoading"
                           :data="tableData"
                           :seq-config="{ startIndex: (pageNum - 1) * pageSize }"
                           ref="refVxeTable">
                    <vxe-table-column type="seq"
                                      title="序号"
                                      width="60"
                                      show-header-overflow
                                      show-overflow></vxe-table-column>
                    <vxe-table-column field="categoryName"
                                      title="拓展类型"
                                      width="160"
                                      show-header-overflow
                                      show-overflow>
                    </vxe-table-column>
                    <vxe-table-column field="dictName"
                                      title="拓展字段"
                                      show-header-overflow
                                      show-overflow>
                        <template v-slot="{ row }">
                            <div v-if="isEditing && (tableData.length == row.index + 1 )">
                                <el-input v-model.trim="row.dictName"
                                          placeholder="拓展字段"></el-input>
                            </div>
                            <span v-else>{{row.dictName}}</span>
                        </template>
                    </vxe-table-column>
                    <vxe-table-column field="updateUser"
                                      title="是否启用"
                                      width="120"
                                      show-header-overflow
                                      show-overflow>
                        <template v-slot="{ row }">
                            <!-- 状态 0停用 1启用 -->
                            <div v-if="isEditing && (tableData.length == row.index + 1 )">
                                <el-switch disabled
                                           v-model="row.status == 1" />
                            </div>
                            <el-switch v-else
                                       v-model="row.status == 1"
                                       @change="switchChange(row)" />
                        </template>
                    </vxe-table-column>
                </vxe-table>
            </div>
            <span slot="footer"
                  class="dialog-footer">
                <el-button type="primary"
                           size="medium"
                           :disabled="isEditing"
                           @click="addNewItem">新增</el-button>
                <el-button @click="save">保存</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import {apiGetQueryExtList, apiGetCategoryDict, apiExtUpdateStatus, apiExtSaveItem } from "@/api/dict.js";

export default {
    name: "",
    data () {
        return {
            isEditing: false,
            dialogVisible: false,
            tableData: [],
            tableLoading: false,
            categoryCodeList: [],
            formData: {
                categoryCode: 2,
                dictName: "",
            },
        };
    },
    
     watch: {
        dialogVisible (newValue) {
             if(!newValue)//关闭弹窗
                this.$emit('close');
        }
    },
    methods: {
        async getCategoryDict () {
            const res = await apiGetCategoryDict();
            if (res.retCode == 0) {
                this.categoryCodeList = res.data;
            } else {
                this.$message({
                    showClose: true,
                    type: "error",
                    message: res.retMsg,
                });
            }
        },
        async getQueryExtList () {
            this.tableLoading = true;
            let res = await apiGetQueryExtList({ ...this.formData });
            this.tableLoading = false;
            if (res.retCode == 0) {
                this.tableLoading = false;
                this.tableData = res.data.list || [];
            } else {
                this.$message({
                    showClose: true,
                    type: "error",
                    message: res.retMsg,
                });
            }
        },
        open () {
            this.dialogVisible = true;
            this.isEditing = false;
            this.getCategoryDict();
            this.getQueryExtList();
        },
        switchChange (row) {
            if (row.status == 1) {
                this.$confirm("停用后, 该拓展值不再被商业商品维护使用", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                }).then(() => {
                    this.updateStatus(row);
                })
            } else {
                this.updateStatus(row);
            }
        },
        async updateStatus (row) {
            const status = row.status == 1 ? 0 : 1;
            const formdata = new FormData();
            formdata.append("status", status);
            formdata.append("id", row.id);
            const res = await apiExtUpdateStatus(formdata);
            if (res.retCode == 0) {
                this.getQueryExtList();
            } else {
                this.$message({
                    showClose: true,
                    type: "error",
                    message: res.retMsg,
                });
            }
        },
        onSearch () {
            // if (!this.formData.dictName) return this.$message.info("请先输入通用名")
            this.getQueryExtList();
        },
        addNewItem () {
            this.isEditing = true;
            this.tableData.push({
                categoryCode: 2,
                categoryName: '中药',
                dictName: '',
                status: 1,
                index: this.tableData.length
            });
            setTimeout(() => {
                const dialogBody = document.querySelector('.el-dialog__body');
                if (dialogBody) {
                    dialogBody.scroll({
                        top: dialogBody.scrollHeight,
                        behavior: 'smooth'
                    });
                }
            }, 100);  // 等待100ms
        },
        async save () {
            if (!this.isEditing) return this.$message.info('请先新增编辑行');
            const lastItem = this.tableData[this.tableData.length - 1];
            const { categoryCode, dictName, status } = lastItem;
            if (!lastItem.dictName) return this.$message.info('请输入拓展字段');
            const res = await apiExtSaveItem({ categoryCode, dictName, status });
            if (res.retCode == 0) {
                this.isEditing = false;
                this.getQueryExtList();
            } else {
                this.$message({
                    showClose: true,
                    type: "error",
                    message: res.retMsg,
                });
            }
        },
    },
};
</script>
<style lang="scss" scoped>
.search-form-box {
    margin-bottom: 12px;
    .el-form-item {
        margin-right: 20px;
        margin-bottom: 15px;
        .el-input {
            width: 150px;
        }
        .el-select {
            width: 140px;
        }
    }
}
</style>

<style lang="scss">
.vxe-table--tooltip-wrapper  {
  z-index: 3000!important;
}

</style>