/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string}
 */
export function parseTime(time, cFormat) {
    if (!time) {
        return null;
    }
    if (arguments.length === 0) {
        return null
    }
    const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
    let date
    if (typeof time === 'object') {
        date = time
    } else {
        if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
            time = parseInt(time)
        }
        if ((typeof time === 'number') && (time.toString().length === 10)) {
            time = time * 1000
        }
        date = new Date(time)
    }
    const formatObj = {
        y: date.getFullYear(),
        m: date.getMonth() + 1,
        d: date.getDate(),
        h: date.getHours(),
        i: date.getMinutes(),
        s: date.getSeconds(),
        a: date.getDay()
    }
    const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
        let value = formatObj[key]
        // Note: getDay() returns 0 on Sunday
        if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value] }
        if (result.length > 0 && value < 10) {
            value = '0' + value
        }
        return value || 0
    })
    return time_str
}

export function filterProductType(type) {
    switch (type * 1) {
        case 2:
            return '主商品';
        case 3:
            return '副商品';
    }
}
export function filterPreOperateStatus(type) {
    switch (type * 1) {
        case 1:
            return '非自营';
        case 2:
            return '自营';
        default:
            return '预首营'
    }
}

export function parseTimestamp(timestamp) {
    let ret = timestamp;
    let date = null;
    if (timestamp) {
        try {
            date = new Date(timestamp);
            ret = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, 0)}-${date.getDate().toString().padStart(2, 0)} ${date.getHours().toString().padStart(2, 0)}:${date.getMinutes().toString().padStart(2, 0)}:${date.getSeconds().toString().padStart(2, 0)}`;
            date = null;
        } catch (err) {
            console.log('Error:utils/index.js parseTimestamp ' + err);
        }
    }
    return ret;
}
export function filterJCPreOperateStatus(type) {
    switch (type * 1) {
        case 1:
            return '待预首营';
        case 2:
            return '无需预首营';
        case 3:
            return '已预首营';
        default:
            return '待预首营'
    }
}

export function filterOfflineBusinessType(type) {
    switch (type * 1) {
        case 0:
            return '否';
        case 1:
            return '是';
        default:
            return '否'
    }
}

export function filterApprovalProcess(type) {
    switch (type * 1) {
        case -1:
            return '条形码修改';
        case 0:
            return '商品新增';
        case 1:
            return '商品修改';
        case 2:
            return '商品合并';
        case 3:
            return '批量停用';
        case 4:
            return '批量扩展';
        case 5:
            return '批量修改';
        case 6:
            return '预首营审核';
        case 7:
            return '批量新增';
        case 8:
            return '批量用药指导';
        case 9:
            return '数据梳理更新';
        case 10:
            return '取消合并';
        case 12:
            return '商品上架修改';
        case 13:
            return '商品纠错修改';
        case 14:
            return '批量修改税率';
        case 15:
            return '商品去重';
        case 16:
            return '移动商品';
        case 17:
            return 'spu停启用';
        default:
            return '商品新增'
    }
}

export function filterSpuCategory(type) {
    switch (type * 1) {
        case 1:
            return '普通药品';
        case 2:
            return '中药';
        case 3:
            return '医疗器械';
        case 4:
            return '非药';
        case 5:
            return '赠品';
        default:
            return '赠品'
    }
}


export function filterReviewStatus(type) {
    switch (type * 1) {
        case -1:
            return '录入中';
        case 0:
            return '运营中心审核中';
        case 1:
            return '质管总部审核中';
        case 2:
            return '审核驳回';
        case 3:
            return '审核通过';
        case 4:
            return '审核不通过';
        case 5:
            return '财务部审核中';
        default:
            return '运营中心审核中'
    }
}

export function filterSource(type) {
    switch (type * 1) {
        case 0:
            return '无';
        case 1:
            return '百草';
        case 2:
            return '系统上传';
        case 3:
            return '荷叶健康';
        case 4:
            return '智慧脸商城';
        case 5:
            return 'POP';
        case 6:
            return 'EC';
        case 7:
            return '客服';
        case 8:
            return '供多多';
        case 9:
            return '智鹿';
        case 10:
            return '神农';
        case 11:
            return '运营创建-改精修图';
        case 12:
            return '系统创建-改精修图';
        case 20:
            return '爬虫';
        case 21:
            return '新品上报';
        case 22:
            return '友商库-自动创建';
        case 23:
            return '友商库-运营创建';
        case 25:
            return '荷叶积分';
        default:
            return '无'
    }
}

export function filterValidity(validity) {
    if (validity == 0) {
        return '*'
    } else if (validity == -1) {
        return '-'
    } else if (!isNaN(validity)) {
        return validity + '月'
    } else {
        return '-'
    }
}

export function filterStatus(validity) {
    if (validity == 0) {
        return '未下载'
    } else if (validity == 1) {
        return '已下载'
    } else if (validity ==2) {
        return '已上传'
    } else {
        return '-'
    }
}
