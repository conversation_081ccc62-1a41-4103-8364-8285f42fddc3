import Layout from '@/layout'
import Iframe from '@/iframe'
const basicDatabaseRouters = {
  path: '/basicDatabase',
  name: 'basicDatabase',
  // component: Layout,
  component: Iframe,
  redirect: 'noRedirect',
  meta: {title: '基础资料库',icon: 'nested'},
  children: [{
      path: 'spuList',
      name: 'spuList',
      component: () => import('@/views/basicDatabase/spuList'),
      meta: {title: 'spu列表'}
    },
    {
      path: 'skuList',
      name: 'skuList',
      component: () => import('@/views/basicDatabase/skuList'),
      meta: {title: 'sku列表'}
    },
    {
      path: 'addProduct',
      name: 'addProduct',
      component: () => import('@/views/basicDatabase/addProduct'),
      meta: {title: '新增商品'},
      hidden: true
    },
    {
      path: 'testForm',
      name: 'testForm',
      component: () => import('@/views/form/testForm'),
      meta: {title: '测试表单'}
    },
    {
      path: 'testTable',
      name: 'testTable',
      component: () => import('@/views/form/testTable'),
      meta: {title: '测试表格'}
    }
  ]
}

export default basicDatabaseRouters
