
<template>
  <div class="task-to-be-film-container">
    <!-- 查询表单 -->
    <div class="search-form-wrap">
      <el-form :model="model" ref="refSearchForm" label-width="96x">
        <el-row type="flex" :gutter="20">
          <!-- 标题 -->
          <el-col :span="12">
            <el-form-item label="标题">
              <el-input
                v-model="formData.noticeTitle"
                placeholder=""
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- 按钮 -->
          <el-col :span="12" class="search-btn-wrap">
            <el-button type="primary" size="medium" @click="btnSearchClick"
              >查询</el-button
            >
            <el-button size="medium" @click="btnResetClick">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="btn-wrap">
      <el-button
        v-if="hasPermission('公告增加')"
        type="primary"
        size="medium"
        @click="addDict"
        >新增</el-button
      >
      <el-button
        v-if="hasPermission('公告删除')"
        type="danger"
        size="medium"
        @click="handleGroupDelete"
        >删除</el-button
      >
    </div>
    <!-- table -->
    <div class="table-wrap">
      <vxe-table
        border
        highlight-hover-row
        resizable
        height="100%"
        auto-resize
        size="small"
        align="center"
        :tooltip-config="{ enterable: false }"
        :loading="tableLoading"
        :data="tableData"
        :seq-config="{ startIndex: (pageNum - 1) * pageSize }"
        ref="refVxeTable"
        :checkbox-config="{ checkMethod: checCheckboxkMethod }"
        @sort-change="sortQueryList" @checkbox-all="selectAllEvent"
        @checkbox-change="selectChangeEvent" >
        <vxe-table-column
          type="checkbox"
          width="60"
          fixed="left"
        ></vxe-table-column>
        <vxe-table-column
          type="seq"
          title="序号"
          width="60"
          show-header-overflow
          show-overflow
          fixed="left"
        ></vxe-table-column>
        <vxe-table-column
          field="title"
          title="标题"
          min-width="200"
          show-header-overflow
          show-overflow
          ><template v-slot="{ row }">
            <div class="active" @click="handleDetail(row)">
              <span class="" v-if="row.toppingStatus == 1">[置顶]</span>
              <span class="">{{ row.noticeTitle }}</span>
            </div>
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="viewsCount"
          title="浏览量"
          width="80"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="createUser"
          title="创建人"
          width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="createTime"
          title="创建时间"
          width="150"
          show-header-overflow
          show-overflow
          sortable
        >
          <template v-slot="{ row }">
            {{ row.createTime | parseTimestamp }}
          </template>
        </vxe-table-column>
        <vxe-table-column title="操作" width="200" fixed="right">
          <template v-slot="{ row }">
            <span class="btn" v-if="hasPermission('公告编辑')">
              <el-link :underline="false" type="primary" @click.stop="edit(row)"
                >编辑</el-link
              >
            </span>
            <span class="btn" v-if="hasPermission('公告置顶')">
              <el-link
                :underline="false"
                type="primary"
                @click.stop="handleTop(row)"
                >{{ row.toppingStatus ? "取消置顶" : "置顶" }}</el-link
              >
            </span>
            <span class="btn red" v-if="hasPermission('公告删除')">
              <el-link
                :underline="false"
                type="primary"
                @click.stop="handelDelete(row.id)"
                v-if="row.toppingStatus != 1"
                >删除</el-link
              >
            </span>
          </template>
        </vxe-table-column>
      </vxe-table>
    </div>
    <!-- 分页 -->
    <el-row class="custom-pagination-wrap">
      <el-col>
        <el-pagination
          background
          :current-page.sync="pageNum"
          :page-sizes="[20, 50, 100, 200]"
          :page-size.sync="pageSize"
          :total="total"
          layout="prev, pager, next, jumper,total, sizes"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </el-col>
    </el-row>
    <add ref="addDialog" @refresh="refresh"></add>
  </div>
</template>

<script>
import { hasPermission } from "@/utils/index.js";
import {
  getNoticeList,
  noticeTop,
  deleteNotice,
  noticeViews,
} from "@/api/workbench.js";
import add from "./components/add.vue";
export default {
  name: "dictList",
  filters: {},
  props: {},
  components: { add },
  data() {
    return {
      pageNum: 1,
      pageSize: 20,
      total: 0, //总条数
      sortFiled: "toppingStatus desc,createTime", // 排序查询
      sortRule: "desc", //(升序-ASC, 降序-DESC)
      formData: {},
      tableLoading: false,
      tableData: [],
      checkedList: [],
    };
  },
  computed: {},
  watch: {},
  created() {
    this.searchForm();
  },
  mounted() {},
  methods: {
    /**
     * 查询table数据
     */
    async searchForm() {
      try {
        this.tableLoading = true;
        let param = Object.assign({}, this.formData);
        param.page = this.pageNum;
        param.limit = this.pageSize;
        param.sortList = this.sortFiled
          ? [
              {
                order: "0",
                columnName: this.sortFiled,
                lift: this.sortRule, //(升序-ASC, 降序-DESC)
              },
            ]
          : [];
        let res = await getNoticeList(param);
        this.tableLoading = false;
        if (!res.retCode) {
          this.tableData = res.data.list;
          this.total = res.data.total;
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      } catch (error) {
        console.error(error);
      }
    },
    refresh(isAdd) {
      if (isAdd) {
        this.btnResetClick();
      } else {
        this.searchForm();
      }
    },
    /**
     * 查询按钮点击
     */
    btnSearchClick() {
      this.pageNum = 1;
      this.searchForm();
    },

    /**
     * 重置
     */
    btnResetClick() {
      this.formData = {
        title: "",
      };
      this.$refs.refVxeTable.clearSort();
      this.sortFiled = "toppingStatus desc,createTime";
      this.sortRule = "desc";
      this.pageNum = 1;
      this.searchForm();
    },
    // 排序查询
    sortQueryList({ column, property, order }) {
      if (order) {
        this.sortFiled = property;
        this.sortRule = order;
      } else {
        this.sortFiled = "toppingStatus desc,createTime";
        this.sortRule = "desc";
      }
      this.searchForm();
      this.pageNum = 1;
    },
    /**
     * pageSize 改变事件
     */
    handleSizeChange(pageSize) {
      this.pageNum = 1;
      this.pageSize = pageSize;
      this.searchForm();
    },

    /**
     * pageNum 改变事件
     */
    handleCurrentChange(currentPage) {
      this.pageNum = currentPage;
      this.searchForm();
    },
    hasPermission(str) {
      return hasPermission(str);
    },
    selectAllEvent({ checked, records }) {
      this.checkedList = records;
    },
    selectChangeEvent({ checked, records }) {
      this.checkedList = records;
    },
    checCheckboxkMethod({ row }) {
      return row.toppingStatus == 0;
    },
    // 新增
    addDict() {
      this.$refs.addDialog.open({
        title: "新增公告",
      });
    },
    // 修改
    edit(row) {
      const obj = JSON.parse(JSON.stringify(row));
      this.$refs.addDialog.open({
        title: "编辑公告",
        obj,
      });
    },
    // 批量删除
    handleGroupDelete() {
      if (this.checkedList.length == 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      let arr = [];
      this.checkedList.forEach((item) => {
        arr.push(item.id);
      });
      this.handelDelete(arr.join(","));
    },
    // 删除
    handelDelete(id) {
      this.$confirm("此操作将永久删除数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteNotice({
            id,
          }).then((res) => {
            if (!res.retCode) {
              this.$message.success("操作成功");
              this.searchForm();
            } else {
              this.$message({
                showClose: true,
                type: "error",
                message: res.retMsg,
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 查看
    async handleDetail(row) {
      let res = await noticeViews({ id: row.id });
      try {
        parent.CreateTab(
          "../static/dist/index.html#/workbench/announcementDetail?announcementID=" +
            row.id,
          "公告详情"
        );
      } catch {
        this.$router.push({
          path: "/workbench/announcementDetail",
          query: {
            announcementID: row.id,
          },
        });
      }
    },
    // 置顶
    handleTop(row) {
      noticeTop({
        id: row.id,
        toppingStatus: row.toppingStatus ? 0 : 1,
      }).then((res) => {
        if (!res.retCode) {
          this.$message.success("操作成功");
          this.searchForm();
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.task-to-be-film-container {
  /**
   * 查询表单
   */
  .search-form-wrap {
    width: 100%;
    padding: 15px;
    border-bottom: 1px dashed #e4e4eb;

    .el-row {
      flex-wrap: wrap;

      .el-col {
        display: flex;
        justify-content: flex-end;
        .el-form-item {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          width: 100%;
          margin-right: 0;
          margin-bottom: 15px;

          /deep/ {
            .el-form-item__label {
              padding: 0 12px;
              color: #292933;
              font-weight: normal;
            }

            .el-form-item__content {
              flex: 1;
              margin-left: 0 !important;
              .el-range-editor.el-input__inner {
                width: 100%;

                .el-range-separator {
                  width: 14px;
                  padding: 0;
                }
              }
            }
          }
          .el-input {
            width: 100%;
          }

          .el-select {
            width: 100%;
          }
          .el-cascader {
            width: 100%;
          }
        }
      }
    }
  }
  /**
     * 按钮组
     */
  .btn-group-wrap {
    font-size: 0;
    // padding: 15px;
    .el-col {
      .el-button,
      .vxe-column-filter-container {
        margin-right: 10px;
        height: 40px;
      }
    }
  }
  .search-btn-wrap {
    padding-bottom: 15px;
  }
  /**
     * table
     */
  .table-wrap {
    width: 100%;
    min-height: 440px;
    height: calc(100vh - 346px);
    padding: 0 15px;
    .active {
      color: #3b95a8;
      &:hover {
        cursor: pointer;
      }
    }
  }
}
.btn {
  padding: 0 10px;
}
.btn-wrap /deep/ {
  padding: 15px 15px 0;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  .el-button {
    margin-bottom: 15px;
    margin-left: 0;
    margin-right: 15px;
  }
  .vxe-toolbar {
    flex: 1;
    .vxe-button--wrapper {
      display: none;
    }
    .vxe-tools--operate {
      margin-top: 2px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
.red /deep/ .el-link--inner {
  color: #f56c6c !important;
}
</style>