<template>
  <div class="skuTable-wrap">
    <el-table :data="skuTableData" border max-height="300px">
      <el-table-column
        prop="skuCode"
        label="sku编码"
        min-width="120"
        show-header-overflow
        show-overflow
        sortable
      ></el-table-column>

      <el-table-column
        prop="productId"
        label="商品id"
        min-width="100"
        show-header-overflow
        show-overflow
        sortable
      ></el-table-column>

      <el-table-column
        prop="skuName"
        label="商品名"
        min-width="100"
        show-overflow-tooltip
      >
      </el-table-column>

      <el-table-column
        prop="tasteName"
        label="口味"
        min-width="130"
        show-header-overflow
        show-overflow
      >
      </el-table-column>

      <el-table-column
        prop="sizeName"
        label="尺码"
        min-width="130"
        show-header-overflow
        show-overflow
      >
      </el-table-column>

      <el-table-column
        prop="colorName"
        label="颜色"
        min-width="130"
        show-header-overflow
        show-overflow
      >
      </el-table-column>

      <el-table-column
        prop="spec"
        label="规格型号"
        min-width="130"
        show-header-overflow
        show-overflow
        sortable
      >
      </el-table-column>

      <el-table-column
        prop="packageUnitName"
        label="包装单位"
        min-width="100"
        show-header-overflow
        show-overflow
      ></el-table-column>

      <el-table-column
        prop="prescriptionCategoryName"
        label="处方分类"
        min-width="100"
        show-header-overflow
        show-overflow
      ></el-table-column>

      <el-table-column
        prop="smallPackageCodeList"
        label="小包装条码"
        min-width="140"
        show-header-overflow
        show-overflow
        sortable
      ></el-table-column>

      <el-table-column
        prop="mediumPackageCodeList"
        label="中包装条码"
        min-width="100"
        show-header-overflow
        show-overflow
      ></el-table-column>

      <el-table-column
        prop="piecePackageCodeList"
        label="件包装条码"
        min-width="100"
        show-header-overflow
        show-overflow
      ></el-table-column>

      <el-table-column
        prop="brand"
        label="品牌/商标"
        min-width="130"
        show-overflow-tooltip
        sortable
      ></el-table-column>

      <el-table-column
        prop="validity"
        label="有效期"
        min-width="100"
        show-header-overflow
        show-overflow
      >
        <template v-slot="{ row }">{{
          transformValidity(row.validityTransition)
        }}</template>
      </el-table-column>

      <el-table-column
        prop="delegationProduct"
        label="是否委托生产"
        min-width="140"
        show-header-overflow
        show-overflow
        sortable
      >
        <template v-slot="{ row }">{{
          row.delegationProduct == 1 ? "是" : "否"
        }}</template>
      </el-table-column>

      <el-table-column
        prop="entrustedManufacturerName"
        label="受托生产厂家"
        min-width="140"
        show-header-overflow
        show-overflow
        sortable
      ></el-table-column>

      <el-table-column
        prop="preOperateStatusName"
        label="自营状态"
        min-width="130"
        show-header-overflow
        show-overflow
        sortable
      ></el-table-column>

      <el-table-column
        prop="qualityStandard"
        label="质量标准"
        min-width="100"
        show-header-overflow
        show-overflow
      ></el-table-column>

      <el-table-column
        prop="brandCategoryName"
        label="品牌分类"
        min-width="100"
        show-header-overflow
        show-overflow
      ></el-table-column>

      <el-table-column
        prop="conEvaluateVariety"
        label="一致性评价品种"
        min-width="150"
        show-header-overflow
        show-overflow
      >
        <template v-slot="{ row }">
          {{ radioText(row.conEvaluateVariety) }}
        </template>
      </el-table-column>

      <el-table-column
        prop="hospitalVariety"
        label="医院品种"
        min-width="100"
        show-header-overflow
        show-overflow
      >
        <template v-slot="{ row }">
          {{ radioText(row.hospitalVariety) }}
        </template>
      </el-table-column>

      <el-table-column
        prop="storage"
        label="贮藏"
        min-width="100"
        show-header-overflow
        show-overflow
      ></el-table-column>

      <el-table-column
        prop="standardCodes"
        label="本位码"
        min-width="100"
        show-header-overflow
        show-overflow
      ></el-table-column>

      <el-table-column
        prop="storageCondName"
        label="存储条件"
        min-width="100"
        show-header-overflow
        show-overflow
      ></el-table-column>
      <el-table-column
        prop="alias"
        label="别名"
        min-width="100"
        show-header-overflow
        show-overflow
      ></el-table-column>
      <el-table-column
        label="操作"
        v-if="operationType == 'reuse'"
        width="120"
        prop="op"
        fixed="right"
      >
        <template v-slot="{ row }">
          <span @click="copySku(row)"
            ><el-link :underline="false" type="primary">复制行</el-link></span
          >
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
export default {
  props: {
    skuTableData: {
      type: Array,
      required: false,
      default: () => [],
    },
  },
  computed: {
    // 商品操作类型
    operationType: function () {
      // 驳回修改SKU 以 修改处理
      if (this.$store.getters.operationType == "RejectEdit") {
        return "edit";
      } else {
        return this.$store.getters.operationType;
      }
    },
  },
  methods: {
    copySku(row) {
      this.$emit("copySku", row);
    },
    /**
     * @description: 转换表格有效期显示内容
     * @param {string} validity 有效期
     * @param {any} unit :true 不需要单位, false 需要单位
     */
    transformValidity(validity, unit) {
      // -1 对应 - ;0 对应 *;
      switch (validity) {
        case -1:
          return "-";
          break;
        case 0:
          return "*";
          break;
        default:
          if (unit) {
            return validity;
          } else {
            return validity + "月";
          }
          break;
      }
    },
    /**
     * @description: 根据参数展示table 中的字段展示值
     * @param {string} val 需要处理的值
     * @return: {string}
     */
    radioText(val) {
      if (val == 1) {
        return "是";
      } else if (val === 0) {
        return "否";
      } else {
        return "";
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.skuTable-wrap{
  height: 100%;
  /deep/ .el-table .el-table__header th{
    background:#F5F7FA;
  }
}
</style>