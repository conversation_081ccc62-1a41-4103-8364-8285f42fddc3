<template>
  <div class="container">
    <el-tabs v-model="tabName" type="card">
      <el-tab-pane label="工作台任务" name="first">
        <tableTask @changeTab="changeTab"></tableTask>
      </el-tab-pane>
      <el-tab-pane label="图片任务" name="second">
        <pictureTask @changeTab="changeTab" ref="prepareList"></pictureTask>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import pictureTask from "./components/pictureTask";
import tableTask from "./components/list";

export default {
  name: "prepare",
  components: {
    tableTask,
    pictureTask,
  },
  data() {
    return {
      tabName: "first",
    };
  },
  methods: {
    changeTab(name) {
      this.tabName = name;
      this.$refs.prepareList.searchForm();
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding: 15px 15px 0;
}
</style>
