<template>
  <div class="preview-original">
    <!-- 预览原图对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="show"
      close-on-click-modal="false"
      width="800px"
      height="80vh"
      @open="openDlg"
      @close="closeDlg"
      class="dlg1"
    >
      <div class="pic-box">
        <img v-show="rejectStatus" src="../../assets/images/reject.png" class="reject-png" />
        <img v-show="deleteStatus" src="../../assets/images/delete.png" class="delete-png" />
        <img v-show="passStatus && !deleteStatus" src="../../assets/images/pass.png" class="delete-png" />
        <h3 class="h3 txt-center">{{ subTitle }}</h3>
        <el-carousel
          trigger="click"
          height="400px"
          :autoplay="false"
          @change="changePicture"
          :prev="prev"
          :next="next"
          :setActiveItem="prev"
          indicator-position="none"
        >
          <el-carousel-item v-for="item in imgList" :key="item">
            <!-- 当前图片 -->
            <div class="img-l">
              <img ref="img" :src="item.pictureUrl"
              :style="imgStyle"
              @mousedown="handleMouseDown($event,transform)">
            </div>
          </el-carousel-item>
        </el-carousel>
        <div class="height41" v-if="currSkuData.businessType==1">
          <p class="p red txt-center" v-show="rejectSkuNum || rejectPicNum">
            运营审核原图：已驳回sku{{ rejectSkuNum }}个，已删除pic{{ deletePicNum }}张
          </p>
        </div>
        
        <div class="height41" v-if="currSkuData.businessType==2">
          <p class="p red txt-center" v-show="deletePicNum || changeRefinedSkuPic || usablePic">
            正在审核非自营存量图片：删除图片 {{ deletePicNum}}，转精修图片 {{ changeRefinedSkuPic }}，可用图片 {{usablePic}}
          </p>
        </div>
      </div>
      <div slot="footer" class="dlg-footer">
        <div class="dlg-sub-footer">
          <el-button-group class="btn-lf">
            <el-button size="medium" icon="el-icon-zoom-in" title="放大" @click="toBig"></el-button>
            <el-button size="medium" icon="el-icon-zoom-out" title="缩小" @click="toSmall"></el-button>
            <el-button size="medium" @click="changeSku('上')">上一个SKU</el-button>
            <el-button size="medium" @click="changeSku('下')">下一个SKU</el-button>
          </el-button-group>
          <el-button size="medium" v-show="deleteStatus" @click="cancelDelete">取消删除</el-button>

          <template v-if="applyType != 1">
            <el-button
              size="medium"
              v-show="!rejectStatus && !deleteStatus && currImgData.readonly!=1"
              type="primary"
              @click="deleteDetail"
            >删除精修图</el-button>
          </template>
        </div>
        <div class="sku-text">
          <span>商品名称：{{ currSkuData.productName }}</span>
          <span>小包装条码：{{ currSkuData.smallPackageCode }}</span>
          <span>规格型号：{{ currSkuData.spec }}</span>
          <span>批准文号：{{ currSkuData.approvalNo }}</span>
          <span>生产厂家：{{ currSkuData.manufacturer }}</span>
          <span>品牌商标：{{ currSkuData.brand }}</span>
        </div>
        <!-- <div class="inline-block btn-checked rt20" v-show="this.imgList[this.currImgIndex].readonly!=1" @click="rejectReasonDlg = true, reject = false">
          <i class="el-icon-error error"></i>
          <p>审核不通过</p>
        </div>
        <div class="inline-block btn-checked" v-show="currImgData.readonly!=1" @click="auditPass">
          <i class="el-icon-success success"></i>
          <p>审核通过</p>
        </div> -->
        <el-button-group style="text-align:right" v-show="currImgData.readonly!=1">
          <el-button type="primary" v-if="applyType!=3 && currSkuData.businessType==2" @click="()=> dialogVisibleForSku=true">另选商品</el-button>
          <el-button type="primary" v-if="applyType==3"  @click="dialogVisibleForRefined = true, reject = false">转精修</el-button>
          <el-button type="primary" @click="auditPass">审核通过</el-button>
          <el-button type="primary" @click="rejectReasonDlg = true, reject = false">审核不通过</el-button>
        </el-button-group>
      </div>
    </el-dialog>

    <!-- 选择审核不通过原因-->
    <el-dialog title="请选择审核不通过原因" :visible.sync="rejectReasonDlg" width="400px">
      <el-form :model="ruleForm" ref="ruleForm" style="height:100px">
        <el-form-item
          label="审核不通过原因"
          prop="reason"
          :rules="{ required: true, message: '必选', trigger: 'change' }"
        >
        <el-input v-model="ruleForm.reason" maxlength="30" placeholder="请输入驳回原因"></el-input>
          <!-- <el-select v-model="ruleForm.reason" placeholder="请选择">
            <el-option
              v-for="item in reasonDict"
              :key="item"
              :value="item.label"
              :label="item.label"
            ></el-option>
          </el-select> -->
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button size="medium" @click="rejectReasonDlg = false">取 消</el-button>
        <el-button size="medium" type="primary" @click="changeReason('ruleForm')">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="选择SKU"
      fullscreen="true"
      :visible.sync="dialogVisibleForSku">
        <sku-list v-on="$listeners" :currentSkuData="currSkuData"></sku-list>
    </el-dialog>
    <el-dialog
      width="400px"
      title="请选择转精修原因"
      :visible.sync="dialogVisibleForRefined">
        <el-form :model="ruleForm" ref="ruleForm" style="height:100px" v-if="dialogVisibleForRefined">
        <el-form-item
          label="转精修原因"
          prop="reason"
          :rules="{ required: true, message: '请选择转精修原因', trigger: 'change' }">
            <el-select v-model="ruleForm.reason" placeholder="请选择">
              <el-option label="图内容过大、过高、过长" value="图内容过大、过高、过长"></el-option>
              <el-option label="图背景有异物" value="图背景有异物"></el-option>
              <el-option label="图颜色失真" value="图颜色失真"></el-option>
              <el-option label="颜色灰度过高" value="颜色灰度过高"></el-option>
              <el-option label="药品带背景（无遮挡商品）" value="药品带背景（无遮挡商品）"></el-option>
              <el-option label="包装袋有少许扭拧" value="包装袋有少许扭拧"></el-option>
              <el-option label="药品自带光效投影" value="药品自带光效投影"></el-option>
              <el-option label="药品位移不在中心" value="药品位移不在中心"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button size="medium" @click="dialogVisibleForRefined = false">取 消</el-button>
        <el-button size="medium" type="primary" @click="changeRefined()">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  postReviewPictures, // 1.5.1 审核精修图
} from "@/api/productPicture.js";
import { on, off } from 'element-ui/src/utils/dom';
import { rafThrottle, isFirefox } from 'element-ui/src/utils/util';
import skuList from "@/components/common/skuList";
export default {
  name: "",
  components: {
    skuList
  },
  filters: {},
  props: {
    // 对话框状态
    show: {
      type: Boolean,
      default: false,
    },

    // 列表sku总数
    skuNum: {
      type: Number,
      default: 0,
    },

    // 当前sku数据
    skuData: {
      type: Object,
      default: () => {},
    },

    // 当前sku 索引
    skuIndex: {
      type: Number,
      default: 0,
    },
    // 整个table中的数据
    all: {
      type: Array,
      default: () => [],
    }
  },
  watch: {
    // "ruleForm.reason":function(value){
    //   value.replace(/[`~!@#$^&*()=|{}':;'\\\[\]\.<>\/?~！@#￥……&*（）——|{}【】'；：""'？\s]/g,"")
    // }
  },
  data() {
    return {
      dialogVisibleForRefined:false,
      dialogVisibleForSku:false,
      scale: 1,
      currImgIndex: 0, // 当前图片索引位置
      // reject: true, // 驳回 or 审核不通过
      rejectReasonDlg: false, // 驳回 or 审核不通过对话框状态
      rejectArr: [], // 所有sku下图片驳回数组
      currSkuArr: [], //
      ruleForm: {
        reason: "", // 驳回 or 审核不通过原图
      },
      rejectProductList:[],
      transform: {
        scale: 1,
        deg: 0,
        offsetX: 0,
        offsetY: 0,
        enableTransition: false
      },
      // reasonDict: [
      //   { value: "1", label: "信息不符" },
      //   { value: "2", label: "图片不清晰" },
      //   { value: "3", label: "商品拍摄不完整" },
      // ],
    };
  },
  computed: {
    imgStyle() {
      const { scale, deg, offsetX, offsetY, enableTransition } = this.transform;
      const style = {
        'margin-left': `${offsetX}px`,
        'margin-top': `${offsetY}px`
      };
      return style;
    },
    allData() {
      return this.all;
    },
    //转精修状态的SKU
    changeRefinedSkuList(){
      return this.allData.filter(item => {
        return item.auditStatus==9;
      })
    },
    // 转精修状态下的所有图片数量
    changeRefinedSkuPic(){
      let num=0;
      this.changeRefinedSkuList.forEach(item=>{
        num+=item.historyVersionDetailPicture.length;
      })
      return num;
    },
    // applyType单据类型
    // PENDING((byte) 0, "待定"),
    // DETAIL((byte) 1, "纯精修图的单据"),
    // ORIGINAL((byte) 2, "有原图的单据"),
    // NONE_REFINE((byte) 3, "无需精修的单据");
    applyType() {
      return this.allData.length && this.skuIndex !== "" ? this.allData[this.skuIndex].applyType : "";
    },

    // 当前sku数据
    currSkuData() {
      return this.skuData;
    },

    // 单据编号
    applyCode() {
      return this.currSkuData.applyCode;
    },

    // 商品编码
    productCode() {
      return this.currSkuData.productCode;
    },

    // 当前sku的img数据
    imgList() {
      return this.currSkuData.hasOwnProperty("imgProductList")
        ? this.currSkuData.imgProductList
        : [];
    },
    // 当前img单条数据
    currImgData() {
      return this.imgList.length ? this.imgList[this.currImgIndex] : {};
    },

    // 当前图片位置
    pictureOrdinal() {
      return this.currImgData.pictureOrdinal;
    },

    pictureId() {
      return this.currImgData.id;
    },

    // 标题
    title() {
      return [
        this.currSkuData.smallPackageCode,
        this.currSkuData.productName,
        this.currSkuData.pictureDetailVersion,
      ].join("_");
    },

    // 子标题
    subTitle() {
      return this.imgList.length ? `${this.currSkuData.smallPackageCode || ""}
          _${this.currSkuData.productName || ""  }
          _${this.currImgData.pictureVersion || ""}
          -${this.currImgData.pictureOrdinal || ""}` : "";
    },

    // 驳回sku总数
    rejectSkuNum() {
      // debugger
      // console.log(this.rejectArr,"----------")
      // let arr = this.rejectArr
      //   .filter((item) => {
      //     return item.auditStatus == 12; // 驳回
      //   })
      //   .map((item) => {
      //     return item.productCode;
      //   })
      //   .reduce(function (prev, cur, index, arr) {
      //     // console.log(prev, cur);
      //     return !prev.includes(cur) ? prev.concat(cur) : prev;
      //   }, []);
      return this.rejectProductList.length;
    },

    // 驳回图片总数
    rejectPicNum() {
      return this.rejectArr.length;
    },

    // 删除pic总数
    deletePicNum() {
      let arr = this.rejectArr.filter((item) => {
        return item.auditStatus == 3; // 删除
      });
      return arr.length;
    },
    // 可用图片数量
    usablePic(){
      let allLength=0;
      for(let item of this.all){
        allLength+=item.historyVersionDetailPicture.length;
      }
      let usableNum = allLength - this.rejectPicNum - this.changeRefinedSkuPic;
      return usableNum > 0 ? usableNum : 0;
    },
    // 当前sku驳回图片数量
    skuRejectPicNum() {
      return this.rejectArr.filter((item) => {
        return item.productCode == this.productCode && item.auditStatus == 2;
      }).length;
    },

    // 当前sku删除图片数量
    skuDeletePicNum() {
      return this.rejectArr.filter((item) => {
        return item.productCode == this.productCode && item.auditStatus == 3;
      }).length;
    },

    // 当前商品驳回状态
    rejectStatus() {
      let arr = this.rejectArr.filter((item) => {
        return item.pictureId == this.pictureId;
      });

      return arr.length && arr[0].auditStatus == 2 ? true : false;
    },

    // 当前商品删除状态
    deleteStatus() {
      let arr = this.rejectArr.filter((item) => {
        return item.pictureId == this.pictureId;
      });
      return arr.length && arr[0].auditStatus == 3 ? true : false;
    },
    passStatus(){
      if(this.applyType != 1){
        //  审核状态(0:待审核, 1:通过, 2:驳回, 3：删除，4:待预审核, 5:预审核通过, 6:预审核驳回)
        return this.currImgData.auditStatus== 5 || this.currImgData.auditStatus== 1 ? true:false
      }else{
        return this.currSkuData.auditStatus == 1 || this.currSkuData.auditStatus == 11 ? true : false
      }
    }
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    /**
     * @description: 转精修：sku维度，将当前sku打回待精修商品列表，商品列表中的审核状态为转精修。
     * 转精修的sku如果在转精修前有删除图片操作，已删除的图片还是进待精修列表，即转精修优先级高于删除图片，只要转精修，将覆盖删除图片操作
     * @param {type} 
     * @return {type} 
     */
    changeToRefined(){
      // let params = {
      //   applyCode: this.applyCode,
      //   productCode: this.productCode,
      //   auditStatus: state, // 审核状态(1:通过,4:不通过)
      //   detailRejectReason: reasonStr,
      //   rejectDetailPictureList: subList, // 图片信息列表
      // };
      // let data = await postReviewPictures(params);
    },
    /**
     * @description: 鼠标事件处理
     * @param {type}  e ：事件对象
     * @return {type} transform：动画对象
     */
    handleMouseDown(e,transform) {
      if (this.loading || e.button !== 0) return;
      const { offsetX, offsetY } = transform;
      const startX = e.pageX;
      const startY = e.pageY;
      this._dragHandler = rafThrottle(ev => {
        transform.offsetX = offsetX + ev.pageX - startX;
        transform.offsetY = offsetY + ev.pageY - startY;
      });
      on(document, 'mousemove', this._dragHandler);
      on(document, 'mouseup', ev => {
        off(document, 'mousemove', this._dragHandler);
      });
      e.preventDefault();
    },
    // 关闭的回调
    closeDlg() {
      this.$emit("update:show", false);
      this.scale = 1;
      this.currImgIndex = 0; // 当前图片索引
      this.ruleForm.reason = ""; // 驳回原因
    },

    // 打开的回调
    openDlg() {
      console.log(this.applyType);
      let arr = [];
      this.allData.forEach((item) => {
        arr.push(
          ...item.historyVersionDetailPicture.map((it) => {
            return {
              pictureId: it.id,
              pictureOrdinal: it.pictureOrdinal,
              auditStatus: it.auditStatus, 
              productCode: it.productCode,
              rejectReason: it.rejectReason,
            };
          })
        );
      });

      this.rejectArr = arr.filter((item) => {
        return item.auditStatus == 2 || item.auditStatus == 3; //
      });

      this.currImgIndex = 0;
    },

    // 上一张 下一张 图片
    changePicture(index) {
      this.currImgIndex = index;
      this.scale != 1 && this.styleRecovery();
    },

    // 放大
    toBig() {
      let el = this.$refs.img[this.currImgIndex].style;
      if (this.scale >= 5) {
        this.$message.error("已经达到最大尺寸");
      } else {
        this.scale += 0.2;
        el.setProperty("transform", `scale(${this.scale.toFixed(2)})`);
      }
    },

    // 缩小
    toSmall() {
      let el = this.$refs.img[this.currImgIndex].style;
      if (this.scale <= 0.22) {
        this.$message.error("已经达到最小尺寸");
      } else {
        this.scale -= 0.2;
        el.setProperty("transform", `scale(${this.scale.toFixed(2)})`);
      }
    },

    // 图片样式复原
    styleRecovery() {
      this.scale = 1;
      let el = this.$refs.img[this.currImgIndex].style;
      el.setProperty("transform", `scale(1)`);
    },

    // 上下切换sku
    changeSku(str) {
      if (str == "上" && this.skuIndex == 0) {
        this.$message.error("已经是第一个了");
        return;
      }
      if (str == "下" && this.skuIndex == this.skuNum - 1) {
        this.$message.error("已经是最后一个了");
        return;
      }

      this.$emit(
        "update:sku-index",
        str == "上" ? this.skuIndex - 1 : this.skuIndex + 1
      );
      this.currImgIndex = 0;
    },

    // 修改原因
    changeReason(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // 确定审核不通过-原因
          this.auditNoPass();
          this.$refs[formName].resetFields();
          this.rejectReasonDlg = false;
        } else {
          return false;
        }
      });
    },
    /**
     * @description: 转精修原因
     * @param {type} 
     * @return {type} 
     */
    changeRefined(){
        this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.submitReview(9,this.ruleForm.reason)
          this.$refs.ruleForm.resetFields();
          this.dialogVisibleForRefined = false;
        } else {
          return false;
        }
      });
    },
    // 删除精修图
    deleteDetail() {
      // 单个删除精修图
      let curObj = {
        pictureId: this.pictureId,
        pictureOrdinal: this.pictureOrdinal,
        auditStatus: 3, 
        productCode: this.productCode,
        rejectReason: this.ruleForm.reason,
      };

      // 从驳回数组中找出当前被删除图片的位置
      let index = this.rejectArr.findIndex((item) => {
        return item.pictureId == this.pictureId;
      });
      // 修改对应位置的数据
      index == -1 ? this.rejectArr.push(curObj) : this.$set(this.rejectArr, index, curObj);
    },

    // 取消删除 当前图片
    cancelDelete() {
      let index = this.rejectArr.findIndex((item) => {
        return item.pictureId == this.pictureId;
      });
      index !== -1 && this.rejectArr.splice(index, 1);
    },

    // 审核通过
    auditPass() {
      //  当前sku删除图片数量 等于 当前sku下的图片list长度 即 都被删除了
      if (this.imgList.length && this.skuDeletePicNum == this.imgList.length) {
        this.$message.error("当前sku所有图片均被删除，不可操作审核通过");
        return;
      }
      this.submitReview(11, ""); // 1 通过
    },

    // 审核不通过
    auditNoPass() {
      // sku审核不通过原因覆盖sku下单个图片驳回原因

      // 删除数组中旧数据
      let arr1 = this.rejectArr.filter((item) => {
        return item.productCode != this.productCode;
      });

      // 新数据替换
      let arr2 = this.imgList
          // 过滤掉 readonly =1 的数据
        .filter((imgItem) => {
          return imgItem.readonly != 1
        })
        .map((item) => {
          return {
            pictureId: item.id,
            pictureOrdinal: item.pictureOrdinal,
            auditStatus: 6, // 审核状态(0:待审核, 1:通过, 2:驳回, 3：删除，4:待预审核, 5:预审核通过, 6:预审核驳回)
            productCode: this.productCode,
            rejectReason: this.ruleForm.reason,
          };
        });

      // 合并
      this.rejectArr = [...arr1, ...arr2];

      this.submitReview(12, this.ruleForm.reason);
    },

    // 审核
    /**
     * @description: 处理当前商品的审核逻辑
     * @param {state} 审核状态（0：待审核，1通过，2原图全驳，3原图半驳，4精修图全驳，5精修图半驳，9 转精修 11预审核通过，12预审核不通过） 
     * @return {reasonStr} 驳回原因
     */
    async submitReview(state, reasonStr) {
      let subList = [];
      if (state == 11) {
        // 当前sku删除的数据
        let currSkuRejectArr = this.rejectArr.filter((item) => {
          return item.productCode == this.productCode && item.auditStatus == 3;
        });

        // 找出审核通过的数据并处理审核通过的数据
        let diff = this.imgList
          .filter((imgItem) => {
            return currSkuRejectArr.findIndex((el) => {
                return el.pictureId == imgItem.id;
              }) == -1 && imgItem.readonly != 1
          })
          .map((item) => {
            return {
              pictureId: item.id,
              pictureOrdinal: item.pictureOrdinal,
              auditStatus:5, // 审核状态(0:待审核, 1:通过, 2:驳回, 3：删除，4:待预审核, 5:预审核通过, 6:预审核驳回)
              productCode: this.productCode,
              rejectReason: "",
            };
          });

        // 合并
        subList = [...diff, ...currSkuRejectArr];
        let deleteSkuIndex=this.rejectProductList.indexOf(this.productCode);
        if(this.rejectProductList.length>0 && deleteSkuIndex !== -1){
          this.rejectProductList.splice(deleteSkuIndex,1);
        }
      }else if(state == 9){
        // 转精修 - 包含删除的图片数据
        subList = this.rejectArr.filter((item) => {
          return item.productCode == this.productCode && item.auditStatus == 3;
        });
      }else {
        // 4 不通过
        subList = this.rejectArr.filter((item) => {
          return item.productCode == this.productCode;
        });
        if(!this.rejectProductList.includes(this.productCode)){
          this.rejectProductList.push(this.productCode)
        }
      }

      let params = {
        applyCode: this.applyCode,
        productCode: this.productCode,
        auditStatus: state, 
        detailRejectReason: reasonStr,
        rejectDetailPictureList: subList, // 图片信息列表
      };
      console.log(params, "提交");
      let data = await postReviewPictures(params);
      if (data.retCode == 0) {
        this.$message.success("操作成功！");
        this.$emit("refresh");
        this.skuNum == 1 && this.closeDlg();
      } else {
        this.$message.error("操作失败！");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.preview-original {
  .el-dialog__wrapper.dlg1 /deep/ .el-dialog .el-dialog__body {
    padding: 10px 0 0;
  }
  .el-dialog__wrapper.dlg1 /deep/ .el-dialog .el-dialog__footer {
    padding: 10px 20px;
  }

  .el-dialog__wrapper /deep/ .el-dialog {
    top: 50% !important;
  }

  h3 {
    margin: 0 0 10px;
  }

  .height41 {
    height: 41px;
  }

  .p {
    margin: 0;
    padding-top: 10px;
  }

  .red {
    color: red;
  }

  .txt-center {
    text-align: center;
  }

  .pic-box {
    position: relative;
    .reject-png {
      position: absolute;
      width: 150px;
      top: 0;
      right: 0;
      z-index: 100;
    }
    .delete-png {
      position: absolute;
      width: 100px;
      top: 0;
      right: 20px;
      z-index: 100;
    }
  }

  .dlg-sub-footer {
    display: inline-block;
    padding: 10px 0;
    text-align: right;
    width: 100%;
    clear: both;
    .btn-lf {
      float: left;
    }
  }

  .dlg-footer {
    border-top: 1px dashed #ddd;
    color: #606266;
    text-align: right;
  }

  .rt20 {
    margin-right: 20px;
  }

  .sku-text {
    font-size: 13px;
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 5px;
    text-align: left;
    > span {
      display: inline-block;
      margin-right: 10px;
      padding: 5px 10px;
      margin-bottom: 5px;
      border-radius: 10px;
      background: #eee;
    }
  }

  .btn-sku-group {
    width: 112px;
    height: 107px;
    text-align: center;
    display: flex;
    flex-flow: column nowrap;
    justify-content: space-around;
  }

  .btn-checked {
    width: 100px;
    height: 107px;
    text-align: center;
    padding-top: 10px;
    color: #606266;
    text-align: center;
    border: 1px solid #ddd;
    border-radius: 4px;
    i {
      font-size: 50px;
    }
    p {
      margin: 0;
      padding: 10px 0;
    }
    .success {
      color: green;
    }
    .error {
      color: red;
    }
  }

  .btn-checked:hover,
  .btn-checked:focus {
    color: #3b95a8;
    border-color: #c4dfe5;
    background-color: #ebf4f6;
    cursor: pointer;
  }

  .img-l {
    position: relative;
    width: 800px;
    height: 400px;
    cursor:move;
    // overflow: auto;
    display: flex;
    justify-content: center;
    align-items: center;
    img{
      max-height: 100%;
    }
  }
}
</style>
