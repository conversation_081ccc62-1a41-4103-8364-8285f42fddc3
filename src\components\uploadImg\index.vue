<template>
  <div class="wrap">
    <el-upload
      :action="uploadUrl"
      list-type="picture-card"
      multiple
      :disabled="disabled"
      :limit="limit"
      :on-exceed="handleExceed"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :file-list="list"
    >
      <i slot="default" class="el-icon-plus"></i>
      <div slot="file" slot-scope="{ file }" style="width: 100%; height: 100%">
        <img
          class="el-upload-list__item-thumbnail"
          v-if="file.status == 'success'"
          :src="file.url || file.mediaUrl"
          alt
          width="100%"
          height="100%"
        />
        <el-progress
          v-if="file.status === 'uploading' || file.status === 'ready'"
          :type="'circle'"
          :stroke-width="6"
          :percentage="parsePercentage(file.percentage)"
        >
        </el-progress>
        <span
          class="el-upload-list__item-actions"
          v-if="file.status !== 'uploading'"
        >
          <span @click="handleLeft(file)" v-if="!disabled">
            <i class="el-icon-back"></i>
          </span>

          <span @click="handlePreview(file)" v-if="preview">
            <i class="el-icon-zoom-in"></i>
          </span>

          <span @click="handleRemove(file)" v-if="!disabled">
            <i class="el-icon-delete"></i>
          </span>

          <span @click="handleRight(file)" v-if="!disabled">
            <i class="el-icon-right"></i>
          </span>
        </span>
      </div>
    </el-upload>
    <imagePreview
      :on-close="closeImageViewer"
      v-if="imgPreview"
      :url-list="list"
      :initialIndex="initialIndex"
    ></imagePreview>
  </div>
</template>

<script>
//http://meproduct.stage.ybm100.com/api/file/upload/file
Array.prototype.remove = function (val) {
  var index = this.indexOf(val);
  if (index > -1) {
    this.splice(index, 1);
  }
};
import imageConversion from "image-conversion";
import imagePreview from "@/components/common/preview/imagePreview";
export default {
  props: {
    fileList: {
      type: Array,
      default: () => [],
    },
    uploadUrl: {
      type: String,
      default: "",
    },
    limit: {
      type: Number,
      default: 10,
    },
    minWidth: {
      type: Number,
      default: 0,
    },
    maxSize: {
      type: Number,
      default: 10,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    preview: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    imagePreview,
  },
  data() {
    return {
      initialIndex:0,
      list: [],
      fileNameList: [],
      imgPreview: false,
      imgPreviewList: [],
    };
  },
  computed: {
    // list:function(){
    //   return this.fileList;
    // }
  },
  created() {},
  watch: {
    list: {
      handler: function (newValue, oldValue) {
        // 检测list的变动。避免取消后 fileNameList 和 list 不一致导致提示重复问题
        this.fileNameList = [];
        for (let item of newValue) {
          if (item.name) {
            this.fileNameList.push(item.name);
          }
        }
      },
      immediate: true,
    },
    fileList: {
      handler: function (newValue, oldValue) {
        this.list = newValue;
      },
      immediate: true,
    },
  },
  methods: {
    parsePercentage(val) {
      if (val == 100) {
        // 模拟一个数，避免完成时动画展示不自然
        let min = Math.ceil(0),
          max = Math.floor(1);
        return 97 + Math.floor(Math.random() * (max - min + 1)) + min;
      } else {
        return parseInt(val, 10);
      }
    },
    getImgIndex(url) {
      // debugger
      let index = null;
      this.list.forEach((item, i) => {
        if (item.mediaUrl === url) {
          index = i;
          return;
        }
      });
      return index;
    },
    getImgIndex2(url) {
      // debugger
      let index = null;
      this.list.forEach((item, i) => {
        if (item.uid === url) {
          index = i;
          return;
        }
      });
      return index;
    },
    handlePreview(file) {
      this.initialIndex = +this.getImgIndex2(file.uid)
      // console.log(file);
      file.mediaUrl = file.url ? file.url : file.mediaUrl;
      this.imgPreviewList = [];
      this.imgPreviewList.push(file);
      this.imgPreview = true;
    },
    closeImageViewer() {
      this.imgPreview = false;
    },
    handleRemove(file) {
      if (this.disabled) {
        this.$message.error("当前状态不可操作");
        return;
      }
      let index = this.getImgIndex(file.mediaUrl);
      if (index !== null) {
        this.list.splice(index, 1);
        this.$emit("change", this.list);
        this.fileNameList.remove(file.name);
      }
    },
    handleLeft(file) {
      if (this.disabled) {
        this.$message.error("当前状态不可操作");
        return;
      }
      // console.log(this.list);
      let index = this.getImgIndex(file.mediaUrl);
      if (index != 0) {
        this.list.splice(
          index,
          1,
          ...this.list.splice(index - 1, 1, this.list[index])
        );
        // debugger
        this.$emit("change", this.list);
      }
    },
    handleRight(file) {
      // debugger
      if (this.disabled) {
        this.$message.error("当前状态不可操作");
        return;
      }
      let index = this.getImgIndex(file.mediaUrl);
      if (index != this.list.length - 1) {
        this.list.splice(
          index,
          1,
          ...this.list.splice(index + 1, 1, this.list[index])
        );
        this.$emit("change", this.list);
      }
    },
    handleExceed(files, fileList) {
      this.$message.warning(`最多上传${this.limit}张`);
    },
    async beforeUpload(file) {
      if (this.fileNameList.includes(file.name)) {
        this.$message.error("该文件已经被选择了！");
        return Promise.reject();
      }
      const isImage =
        file.type === "image/jpeg" ||
        file.type === "image/jpg" ||
        file.type === "image/png";
      const isMaxSize = file.size / 1024 / 1024 < this.maxSize;
      if (!isImage) {
        this.$message.error("上传图片只能是 JPG,PNG,JPEG 格式！");
        return Promise.reject();
      }
      if (!isMaxSize) {
        this.$message.error(`上传头像图片大小不能超过${this.maxSize}MB!`);
        return Promise.reject();
      }
      if (this.minWidth) {
        const isCheckWidth = await this.checkWidth(file);
        if (!isCheckWidth) {
          this.$message.error(`请上传宽度大于${this.minWidth}px(含)的图片`);
          return Promise.reject();
        }
      }
      // 对大于1M的图片进行压缩
      if (file.size / 1024 / 1024 > 1 && isImage && isMaxSize) {
        return imageConversion.compress(file, 0.6);
      } else {
        return isImage && isMaxSize;
      }
    },
    checkWidth(file) {
      const isSize = new Promise((resolve, reject) => {
        let _URL = window.URL || window.webkitURL;
        let img = new Image();
        img.onload = () => {
          let valid = true;
          if (img.width < this.minWidth) valid = false;
          valid ? resolve() : reject();
        };
        img.src = _URL.createObjectURL(file);
      }).then(
        () => {
          return true;
        },
        () => {
          return false;
        }
      );
      return isSize;
    },
    handleSuccess(response, file, fileList) {
      this.fileNameList.push(file.name);
      // 将fileList赋值给list避免 只选择，不保存时切换位置报错
      this.list = fileList;
      this.$emit("change", fileList);
    },
    handleError(err, file, fileList) {
      console.log(err, file, fileList);
      this.$message.error("上传失败！");
    },
  },
};
</script>

<style lang="scss" scoped>
.wrap {
  /deep/ .el-upload--picture-card {
    width: 100px;
    height: 100px;
  }
  /deep/ .el-upload {
    width: 100px;
    height: 100px;
    line-height: 100px;
  }
  /deep/ .el-upload-list--picture-card .el-upload-list__item {
    width: 100px;
    height: 100px;
    line-height: 100px;
  }
  /deep/ .el-upload-list--picture-card .el-upload-list__item-thumbnail {
    width: 100px;
    height: 100px;
    line-height: 100px;
  }
  /deep/ .avatar {
    width: 100px;
    height: 100px;
  }
}
</style>
