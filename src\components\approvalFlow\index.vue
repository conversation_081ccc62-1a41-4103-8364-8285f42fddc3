<!--
 * @Author: your name
 * @Date: 2020-03-05 18:18:50
 * @LastEditTime: 2020-03-05 18:34:43
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \xyy-me-product-vue\src\components\approvalFlow\index.vue
 -->
<template>
  <div class="component-container">
    <el-steps :active="2" align-center>
      <el-step title="步骤1" description="这是一段很长很长很长的描述性文字"></el-step>
      <el-step title="步骤2" description="这是一段很长很长很长的描述性文字"></el-step>
      <el-step title="步骤3" description="这是一段很长很长很长的描述性文字"></el-step>
      <el-step title="步骤4" description="这是一段很长很长很长的描述性文字"></el-step>
    </el-steps>
    <div class="border-bottom-dashed">
      <h4 class="detail-title">申请属性</h4>
      <el-form ref="form" :model="form" label-width="100px">
        <el-row>
          <el-col :span="6">
            <el-form-item label="申请日期">
              <el-input v-model="form.name"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="申请人">
              <el-input v-model="form.name"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="所属机构">
              <el-input v-model="form.name"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="商品来源">
              <el-input v-model="form.name"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  name: "",
  components: {},
  filters: {},
  props: {},
  data() {
    return {
      form: {
        name
      }
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {}
};
</script>

<style lang="scss" scoped>
.component-container {
  .border-bottom-dashed {
    border-bottom: 1px dashed #e4e4eb;
  }

  .detail-title {
    margin: 20px;
    font-size: 16px;
    color: #292933;
  }
}
</style>