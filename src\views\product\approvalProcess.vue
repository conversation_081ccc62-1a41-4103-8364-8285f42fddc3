<template>
  <div class="approval-process">
    <div class="approval" v-if="approvalProcess.showFlow && !isAdd">
      <!-- <img :src="approvalProcessImg" alt /> -->
      <div class="approval-box">
        <el-steps
          :active="approvalProcess.nextProcessNode"
          finish-status="success"
          align-center
        >
          <!--  -->
          <el-step
            :icon="!item.exceptionFlag ? 'el-icon-warning-outline' : ''"
            :class="{ isReject: !item.isReject }"
            v-for="(item, index) in approvalProcess.processList"
            :key="index"
            :title="
              item.optName ? item.optJob + ':' + item.optName : item.optJob
            "
            :description="
              item.optTime ? item.optTime + ':' + item.optAction : ''
            "
          ></el-step>
        </el-steps>
      </div>
    </div>
    <div
      class="partition-content"
      style="height: 20px; background: #f0f2f5"
      v-if="approvalProcess.showFlow && !isAdd"
    ></div>
    <div class="apply" style="padding-top: 10px" v-if="processInfo.createTime">
      <div class="title">申请信息</div>
      <el-form
        :model="processInfo"
        :rules="rules"
        ref="spuForm"
        :validate-on-rule-change="false"
      >
        <el-row type="flex" class="info">
          <el-col :lg="6" :md="8" :xs="24" :sm="12" :xl="6"
            ><el-form-item label="申请时间">
              <el-input
                v-model="processInfo.createTime"
                disabled
              ></el-input> </el-form-item
          ></el-col>
          <el-col :lg="6" :md="8" :xs="24" :sm="12" :xl="6"
            ><el-form-item label="单据编号">
              <el-input
                v-model="processInfo.applyCode"
                disabled
              ></el-input> </el-form-item
          ></el-col>
          <el-col :lg="6" :md="8" :xs="24" :sm="12" :xl="6"
            ><el-form-item label="申请人">
              <el-input v-model="processInfo.createUser" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="8" :xs="24" :sm="12" :xl="6"
            ><el-form-item label="所属机构">
              <el-input v-model="processInfo.affiliation" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :lg="24" :md="24" :xs="24" :sm="24" :xl="24"
            ><el-form-item label="申请原因" prop="applyReason">
              <el-input
                type="textarea"
                v-model="processInfo.applyReason"
                :disabled="approvalProcess.showFlow"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="apply" v-if="applyAttribute.applyTime">
      <div class="title">申请信息</div>
      <el-row type="flex" class="info">
        <el-col :lg="6" :md="6" :xs="24" :sm="12" :xl="6"
          >申请日期 ：{{ applyAttribute.applyTime }}</el-col
        >
        <el-col :lg="6" :md="6" :xs="24" :sm="12" :xl="6"
          >申请人 ：{{ applyAttribute.applyUserName }}</el-col
        >
        <el-col :lg="6" :md="6" :xs="24" :sm="12" :xl="6"
          >所属机构 ：{{
            applyAttribute.affiliation ? applyAttribute.affiliation : "--"
          }}</el-col
        >
        <el-col :lg="6" :md="6" :xs="24" :sm="12" :xl="6"
          >商品来源 ：{{ applyAttribute.productSource }}</el-col
        >
        <el-col span="12" v-if="coorection">
          <span style="color: red">上报留言：</span>
          {{ coorection.msg }}
        </el-col>
        <el-col span="12" v-if="coorection">
          <div class="img-correction-box">
            <div class="image-title" style="color: red">上报图片：</div>
            <div class="image-box">
              <el-image
                class="img-correction"
                v-for="item in coorection.imageList"
                :key="item.id"
                :src="item.mediaUrl"
                :preview-src-list="[item.mediaUrl]"
              ></el-image>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <div
      class="partition-content"
      style="height: 20px; background: #f0f2f5"
    ></div>
  </div>
</template>

<script>
import { parseTimestamp } from "@/utils/index.js";
import { getProcessCofig } from "@/utils/processConfig.js";
export default {
  name: "",
  data() {
    return {
      processConfig: {
        processList: [],
        active: 0,
      },
      rules: {
        applyReason: [
          { required: true, message: "请输入申请原因", trigger: "blur" },
        ],
      },
    };
  },
  props: {
    /**
     * sku渲染数据
     */
    approvalData: {
      type: Object,
      required: false,
      default: () => {},
    },
    isCorrection: {
      type: Boolean,
      required: false,
      default: true,
    },
    processInfo: {
      type: Object,
      required: false,
      default: () => {
        return {};
      },
    },
    isAdd: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    applyAttribute: function () {
      if (this.approvalData.applyAttribute) {
        let applyAttribute = this.approvalData.applyAttribute;
        applyAttribute.applyTime = parseTimestamp(applyAttribute.applyTime);
        return applyAttribute;
      } else {
        return {};
      }
    },
    approvalProcess: function () {
      if (
        !!this.approvalData.approvalProcess &&
        !!this.approvalData.approvalProcess.processList
      ) {
        this.approvalData.approvalProcess.processList.forEach((item) => {
          item.optTime = parseTimestamp(item.optTime);
        });
        return this.approvalData.approvalProcess;
      } else {
        return {};
      }
    },
    coorection: function () {
      if (this.approvalData.coorection) {
        return this.approvalData.coorection;
      } else {
        return false;
      }
    },
  },
  created() {
    // this.getApplyInfo()
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
.approval-process {
  .approval {
    width: 1070px;
    position: relative;
    margin: 10px auto 20px;
    padding-top: 30px;
    // background: #ccc;
    height: 110px;
    img {
      display: block;
      margin: 0 auto;
    }
    .approval-box /deep/ {
      display: flex;
      flex-wrap: nowrap;
      justify-content: center;
      .el-step {
        width: 300px !important;
        .el-icon-warning-outline {
          color: #f56c6c;
        }
        .is-process {
          border-color: #3b95a8;
          .is-text {
            background: #3b95a8;
            color: #fff;
          }
        }
        .el-step__title {
          font-size: 14px;
          font-weight: bold;
          color: #333;
        }
        .el-step__description {
          color: #aeaebf !important;
        }
      }
    }
  }
  .apply {
    padding-bottom: 15px;
    /deep/ .el-form-item {
      display: flex;
      width: 100%;
      .el-form-item__content {
        flex: 1;
        padding-right: 20px;
      }
    }
    .title {
      line-height: 50px;
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 22px;
      padding: 0 20px;
      border-bottom: 1px solid #e4e4eb;
    }
    .img-correction-box {
      display: flex;
      .image-title {
        flex: none;
      }
      .image-box {
        flex: auto;
        display: flex;
        flex-wrap: wrap;
        margin-left: 5px;
        .img-correction {
          width: 80px;
          height: 80px;
          margin-right: 10px;
          margin-bottom: 10px;
          border-radius: 2px;
        }
      }
    }
    .info {
      padding: 0 40px;
      color: #292933;
      font-size: 14px;
    }
    .el-row {
      flex-wrap: wrap;
      .el-col {
        display: flex;
        justify-content: flex-start;
        margin-bottom: 10px;
      }
    }
  }
}
</style>