<template>
    <!-- 预览弹框 -->
    <el-dialog
      style="padding:0"
      class="search-form-wrap"
      fullscreen
      title="请选择"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisible"
      :close-on-press-escape="false"
    >
        <el-form>
            <el-form-item label="换绑机构">
                <el-checkbox-group v-model="formData.bindMechanismList">
                    <el-checkbox border v-for="(item, index) in bindMechanismList" :key='index' :label="item.mechanism">{{item.mechanismName}}</el-checkbox>
                </el-checkbox-group>
            </el-form-item>
            <el-form-item label="更换的版本号">
                <el-radio-group v-model="formData.pictureVersion">
                    <el-radio border v-for="(item, index) in otherPictureVersionList" :key='index' :label="item.pictureVersion">
                        <div class="version-box">
                            <img :src="item.mainPictureUrl" alt="">
                            <span>{{item.pictureVersion}}</span>
                        </div>
                    </el-radio>
                </el-radio-group>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button :disabled='banBtn || !formData.bindMechanismList.length || !formData.pictureVersion' type="primary" @click="submit">提交任务</el-button>
        </div>
    </el-dialog>
</template>

<script>
import { submitUpdataBind } from "@/api/product"
export default {
  name: "",
  props: {},
  data() {
    return {
        productInfo:{},//带进来的信息
        dialogFormVisible:false,
        banBtn:false,
        bindMechanismList:[],//换绑机构列表
        otherPictureVersionList:[],//版本号列表
        formData: {
            bindMechanismList:[],
            pictureVersion:''
        },//表单数据
    };
  },
  watch: {},
  created() {

  },
  methods: {
    //   打开弹框
      openDlg(e) {
          this.productInfo = e
          this.formData = {
              bindMechanismList:[],
              pictureVersion:''
          }
          this.bindMechanismList = e.bindMechanismList
          this.otherPictureVersionList = e.otherPictureVersionList
          this.bindMechanismList.map(item => {
              this.formData.bindMechanismList.push(item.mechanism)
          })
          this.dialogFormVisible = true
      },
    //   提交任务
      async submit() {
          if (!this.formData.bindMechanismList.length) {
              this.$message.error('请选择换绑机构')
              return
          }
          if (!this.formData.pictureVersion) {
              this.$message.error('请选择更换的版本号')
              return
          }
          this.banBtn = true
          try {
              let param = {
                  productCode:this.productInfo.productCode,
                  sourcePictureVersion:this.productInfo.pictureVersion,
                  pictureVersion:this.formData.pictureVersion,
                  bindMechanismList:this.formData.bindMechanismList.join()
              }
              const res = await submitUpdataBind(param)
              if (res.retCode === 0) {
                  this.$message.success(res.retMsg)
                  this.dialogFormVisible = false
              } else {
                  this.$message.error(res.retMsg)
              }
              this.banBtn = false
          } catch (error) {
              console.log(error);
              this.banBtn = false
          }
      },
  }
};
</script>

<style lang="scss" scoped>
/deep/ .el-radio {
    width: 200px;
    height: 200px;
}
/deep/ .is-bordered {
    margin:10px 0 0 10px;
}
.version-box{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
img{
    width:150px;
    height: 150px;
}
.dialog-footer{
    display: flex;
    justify-content: center;
}
</style>