
<template>
  <div class="task-to-be-film-container" v-loading="loading">
    <!-- 查询表单 -->
    <div class="search-form-wrap">
      <el-form :model="model" ref="refSearchForm">
        <el-row type="flex">
          <!-- 上报时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="申请时间" prop="createTime">
              <el-date-picker
                v-model="formData.time"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                clearable
              ></el-date-picker>
            </el-form-item>
          </el-col>

          <!-- 来源 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="来源">
              <el-select
                v-model="formData.source"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" value=""></el-option>
                <el-option label="SASS" :value="9"></el-option>
                <el-option label="荷叶健康" :value="3"></el-option>
                <el-option label="智慧脸商城" :value="4"></el-option>
                <el-option label="POP" :value="5"></el-option>
                <el-option label="EC" :value="6"></el-option>
                <el-option label="客服" :value="7"></el-option>
                <el-option label="供多多" :value="8"></el-option>
                <el-option label="批量导入" :value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 申请人 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="申请人">
              <el-input
                v-model="formData.applyUser"
                placeholder="模糊查询"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 状态 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="状态">
              <el-select
                v-model="formData.status"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" value=""></el-option>
                <el-option label="待处理" :value="1"></el-option>
                <el-option label="未找到网图" :value="2"></el-option>
                <el-option label="已处理" :value="3"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 申请原因 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="申请原因">
              <el-select
                v-model="formData.applyReason"
                placeholder="请选择"
                clearable
              >
                <el-option label="新品上架" value=""></el-option>
                <el-option label="新品上架" :value="1"></el-option>
                <el-option label="图片纠错" :value="2"></el-option>
                <el-option label="图片上传" :value="3"></el-option>
                <el-option label="审核转精修" :value="4"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 商品编码 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="商品编码">
              <el-input
                v-model="formData.productCode"
                placeholder="模糊查询"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 商品ID -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="商品ID">
              <el-input
                v-model="formData.productId"
                placeholder="模糊查询"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 商品名称-->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="商品名称">
              <el-input
                v-model="formData.productName"
                placeholder="模糊查询"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 生产厂家-->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="生产厂家">
              <el-input
                v-model="formData.manufacturerName"
                placeholder="模糊查询"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 按钮 -->
          <el-col :span="6" :offset="18">
            <el-button type="primary" size="medium" @click="btnSearchClick"
              >查询</el-button
            >
            <el-button size="medium" @click="btnResetClick">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- table上方按钮组 -->
    <el-row class="btn-group-wrap">
      <el-col>
        <el-button
          type="primary"
          size="medium"
          @click="addTask"
          :disabled="addTaskDis"
          >创建任务单</el-button
        >
      </el-col>
    </el-row>

    <!-- table -->
    <div class="table-wrap">
      <!-- createStartTime:"", // 开始时间
          createEndTime:"",//结束时间
          sourceDetail:"",//上报来源
          correctionType:"",//纠错类型
          createUser:"",//上报人
          productCode:"",//商品编码
          productId:"",//商品ID
          productName:"",//商品名称
          manufacturerName:"",//生产厂家名称
          reviewStatus:""//处理状态 -->
      <vxe-table
        border
        highlight-hover-row
        resizable
        height="100%"
        auto-resize
        size="small"
        align="center"
        :tooltip-config="{ enterable: false }"
        :loading="tableLoading"
        :data="tableData"
        :seq-config="{ startIndex: (pageNum - 1) * pageSize }"
        ref="refVxeTable"
      >
        <vxe-table-column
          type="checkbox"
          width="60"
          fixed="left"
        ></vxe-table-column>
        <vxe-table-column
          type="seq"
          title="序号"
          width="60"
          show-header-overflow
          show-overflow
          fixed="left"
        ></vxe-table-column>
        <vxe-table-column
          field="applyDate"
          title="申请时间"
          width="120"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">
            {{ parseTime(row.applyDate) }}
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="source"
          title="来源"
          min-width="120"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">
            {{ ["", "荷叶健康", "荷叶健康", "荷叶健康", "荷叶健康"][row.source] }}
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="type"
          title="申请原因"
          min-width="120"
          show-header-overflow
          show-overflow
        >
          <!-- <el-option label="新品上架" value=""></el-option>
                <el-option label="新品上架" :value="1"></el-option>
                <el-option label="图片纠错" :value="2"></el-option>
                <el-option label="图片上传" :value="3"></el-option>
                <el-option label="审核转精修" :value="4"></el-option> -->
          <template v-slot="{ row }">
            {{
              [
                "",
                "新品上架",
                "新品上架",
                "图片纠错",
                "图片上传",
                "审核转精修",
              ][row.applyReason]
            }}
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="applyUser"
          title="申请人"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="applyMechanism"
          title="申请人所属机构"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="productCode"
          title="商品编码"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="productId"
          title="商品ID"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="productName"
          title="商品名称"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="spec"
          title="规格/型号"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="manufacturerName"
          title="生产厂家"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="approvalNo"
          title="批准文号"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="smallPackageCode"
          title="小包装条码"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="packageUnitName"
          title="包装单位"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="imageCount"
          title="图片数量"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="remark"
          title="申请留言"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <!-- <vxe-table-column title="操作" width="120" show-header-overflow show-overflow fixed="right">
          <template v-slot="{ row }">
            <span>
              <el-link :underline="false" type="primary" @click.stop="receive(row)">处理</el-link>
            </span>
          </template>
        </vxe-table-column> -->
      </vxe-table>
    </div>
    <el-dialog
      width="40%"
      title="处理弹层"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :modal-append-to-body="true"
      :append-to-body="true"
    >
      <el-form
        :model="disposeForm"
        ref="disposeForm"
        :rules="rules"
        label-position="top"
      >
        <el-row>
          <el-col>
            <el-form-item label="处理方式" prop="handle">
              <el-radio-group v-model="disposeForm.handle">
                <el-radio label="1">未找到网图</el-radio>
                <el-radio label="2">已找到网图</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col v-show="visible.quality">
            <el-form-item label="图片质量" prop="quality">
              <el-radio-group v-model="disposeForm.quality">
                <el-radio label="1">需设计精细</el-radio>
                <el-radio label="2">可直接使用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col v-show="visible.fileName">
            <el-form-item label="文件名称" prop="fileName">
              <el-input
                v-model="disposeForm.fileName"
                disabled=""
                placeholder="模糊查询"
              >
                <el-upload
                  slot="append"
                  class="upload-wrapper"
                  ref="refUpload"
                  :action="uploadUrl"
                  :data="disposeForm"
                  :multiple="false"
                  accept=".zip,.ZIP"
                  :show-file-list="false"
                  :auto-upload="false"
                  :on-change="uploadOnChange"
                  :on-success="uploadOnSuccess"
                  :on-error="uploadOnError"
                >
                  选择文件
                  <el-popover
                    placement="top-start"
                    title="提示"
                    width="300"
                    trigger="hover"
                  >
                    <span class="popover-content">
                      1、图片放在文件夹中，将文件夹压缩成ZIP格式后上传。<br />
                      2、文件夹内图片支持jpg、png、jpeg、bmp格式，不支持pdf或其他非图片的文件格式。<br />
                      3、图片名称格式：商品编码+“-”+数字。
                    </span>
                    <i
                      class="el-icon-question el-icon--right icon-question"
                      slot="reference"
                    ></i>
                  </el-popover>
                </el-upload>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col v-show="visible.message">
            <el-form-item label="备注留言" prop="message">
              <el-input
                v-model="formData.message"
                placeholder="模糊查询"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item>
              <el-button type="primary" @click="submit">提交</el-button>
              <el-button>取消</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>

    <!-- 分页 -->
    <el-row class="custom-pagination-wrap">
      <el-col>
        <el-pagination
          background
          :current-page.sync="pageNum"
          :page-sizes="[20, 50, 100, 200]"
          :page-size.sync="pageSize"
          :total="total"
          layout="prev, pager, next, jumper,total, sizes"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { parseTimestamp } from "@/utils/index.js";
import { productShootRecordList } from "@/api/list.js";
import { createTaskByRecord } from "@/api/productPicture.js";
export default {
  name: "",
  components: {},
  filters: {},
  props: {},
  data() {
    return {
      uploadUrl:
        process.env.VUE_APP_BASE_API + "/api/picture/imageUploadShootRecord",
      dialogVisible: false,
      loading: false,
      pageNum: 1,
      pageSize: 20,
      total: 0, //总条数
      formData: {
        time: "",
        source: "", //上报来源
        correctionType: "", //纠错类型
        createUser: "", //上报人
        productCode: "", //商品编码
        productId: "", //商品ID
        productName: "", //商品名称
        manufacturerName: "", //生产厂家名称
        reviewStatus: "", //处理状态
      },
      disposeForm: {
        fileName: "",
        handle: "",
        quality: "",
        message: "",
      },
      rules: {
        fileName: [
          {
            required: false,
            message: "请先选择需要上传的文件",
            trigger: "change",
          },
        ],
        handle: [
          { required: true, message: "请选择处理方式", trigger: "change" },
        ],
        quality: [
          { required: false, message: "请选择图片质量", trigger: "change" },
        ],
        message: [
          { required: false, message: "请填写备注留言", trigger: "blur" },
        ],
      },
      visible: {
        fileName: false,
        quality: false,
        message: false,
      },
      tableLoading: false,
      tableData: [],
      addTaskDis: false,
    };
  },
  computed: {},
  watch: {
    "disposeForm.handle": function (newValue, oldValue) {
      if (newValue == 2) {
        this.visible.quality = true;
        this.visible.fileName = true;
        this.visible.message = false;
        this.rules.quality[0].required = true;
        this.rules.fileName[0].required = true;
        this.rules.message[0].required = false;
      } else {
        this.visible.quality = false;
        this.visible.fileName = false;
        this.visible.message = true;
        this.rules.quality[0].required = false;
        this.rules.fileName[0].required = false;
        this.rules.message[0].required = true;
      }
    },
  },
  created() {
    this.searchForm();
  },
  mounted() {},
  methods: {
    /**
     * 查询table数据
     */
    async searchForm() {
      try {
        this.tableLoading = true;
        let param = Object.assign({}, this.formData);
        param.page = this.pageNum;
        param.limit = this.pageSize;
        param.applyStartDate = param.time[0];
        param.applyEndDate = param.time[1];
        param.type = 2;
        delete param.time;
        let res = await productShootRecordList(param);
        this.tableLoading = false;
        console.log(res);
        if (res.success) {
          this.tableData = res.data.list;
          this.total = res.data.total;
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      } catch (error) {
        console.error(error);
      }
    },

    /**
     * 查询按钮点击
     */
    btnSearchClick() {
      this.pageNum = 1;
      this.searchForm();
    },

    /**
     * 重置
     */
    btnResetClick() {
      // this.$refs["refSearchForm"].resetFields();
      // this.formData.createTime = "";
      this.formData = {
        time: "",
        source: "", //上报来源
        correctionType: "", //纠错类型
        createUser: "", //上报人
        productCode: "", //商品编码
        productId: "", //商品ID
        productName: "", //商品名称
        manufacturerName: "", //生产厂家名称
        reviewStatus: "", //处理状态
      };
      this.pageNum = 1;
      this.searchForm();
    },

    /**
     * pageSize 改变事件
     */
    handleSizeChange(pageSize) {
      this.pageNum = 1;
      this.pageSize = pageSize;
      this.searchForm();
    },

    /**
     * pageNum 改变事件
     */
    handleCurrentChange(currentPage) {
      this.pageNum = currentPage;
      this.searchForm();
    },
    receive(row) {
      this.dialogVisible = true;
    },
    /**
     * @description: 格式化时间参数
     * @param {date} time 时间戳
     * @return: string 格式化后的时间戳
     */
    parseTime(time) {
      return parseTimestamp(time);
    },
    submit() {
      this.$refs.disposeForm.validate((valid) => {
        if (valid) {
          this.uploadSubmit();
        } else {
          console.log("error submit!!");
        }
      });
    },
    /**
     * 选择上传文件
     */
    uploadOnChange(file, fileList) {
      //只在添加文件时触发
      if (file.status === "ready") {
        this.disposeForm.fileName = file.name;
        if (fileList.length > 1) {
          fileList.splice(0, fileList.length - 1);
        }
      }
    },

    /**
     * 上传到服务器
     */
    uploadSubmit() {
      if (this.$refs.refUpload.uploadFiles.length) {
        let fileName = this.$refs.refUpload.uploadFiles[0].name;
        if (
          !["zip", "ZIP"].includes(
            fileName.substr(fileName.lastIndexOf(".") + 1)
          )
        ) {
          this.$message({
            showClose: true,
            type: "error",
            message: "请上传xlsx格式的文件",
          });
          return false;
        }
        this.$refs.refUpload.submit();
      }
    },

    /**
     * 上传成功回调
     */
    uploadOnSuccess(response, file, fileList) {
      if (response.retCode === 0) {
        /**
            this.$message({
                message: '上传成功',
                type: 'success'
            });
            */
        this.$emit("success", { response, file, fileList });
        this.dialogVisible = false;
      } else {
        this.$message({
          message: response.retMsg,
          type: "error",
        });
      }
      this.selectedFileName = "";
      this.uploadDisabled = false;
      this.uploadBtnDisabled = true;
      this.uploadBtnLoading = false;
    },

    /**
     * 上传失败回调
     */
    uploadOnError(err, file, fileList) {
      this.$message({
        message: "上传失败,请重试",
        type: "error",
      });
    },
    /**
     * @description: 创建任务单功能逻辑
     * @param {type}
     */
    async addTask() {
      try {
        let selectRecords = this.$refs.refVxeTable.getCheckboxRecords();
        if (selectRecords.length < 1 || selectRecords.length > 30) {
          this.$message.error("请先选择1-30个申请任务");
          return;
        }
        this.addTaskDis = true;
        let productCodeList = selectRecords.map((item) => item.productCode);
        let ids = selectRecords.map((item) => item.id);
        let res = await createTaskByRecord({ productCodeList, ids });
        this.addTaskDis = false;
        if (res.success) {
          this.$message.success("任务单创建成功");
          this.searchForm();
        } else {
          this.$message.error(res.retMsg);
        }
      } catch (error) {
        console.error(error);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.task-to-be-film-container {
  /**
   * 查询表单
   */
  .search-form-wrap {
    width: 100%;
    padding: 15px;
    border-bottom: 1px dashed #e4e4eb;

    .el-row {
      flex-wrap: wrap;

      .el-col {
        display: flex;
        justify-content: flex-end;

        .el-form-item {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          width: 100%;
          margin-right: 0;
          margin-bottom: 15px;

          /deep/ {
            .el-form-item__label {
              width: 96px;
              line-height: normal;
              padding: 0 12px;
              color: #292933;
              font-weight: normal;
            }

            .el-form-item__content {
              flex: 1;

              .el-range-editor.el-input__inner {
                width: 100%;

                .el-range-separator {
                  width: 14px;
                  padding: 0;
                }
              }
            }
          }

          .el-input {
            width: 100%;
          }

          .el-select {
            width: 100%;
          }
        }
      }
    }
  }
  /**
     * 按钮组
     */
  .btn-group-wrap {
    font-size: 0;
    padding: 15px;
    .el-col {
      .el-button,
      .vxe-column-filter-container {
        margin-right: 10px;
      }
    }
  }
  /**
     * table
     */
  .table-wrap {
    width: 100%;
    min-height: 440px;
    height: calc(100vh - 346px);
    padding: 0 15px;
  }
}
</style>
