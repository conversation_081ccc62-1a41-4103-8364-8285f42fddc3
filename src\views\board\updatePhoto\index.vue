<template>
  <div class="container">
    <el-upload
      slot="append"
      class="upload-wrapper"
      :action="no"
      :multiple="false"
      accept=".zip,.ZIP"
      :show-file-list="false"
      :auto-upload="false"
      :on-change="uploadOnChange"
    >
      <el-button type="primary" size="medium">选择文件</el-button>
    </el-upload>
  </div>
</template>
<script>
import { updatePhotoApi } from "@/api/follow.js";
export default {
  components: {},
  data() {
    return {};
  },
  methods: {
    async uploadOnChange(file) {
        if (
            ".zip,.ZIP".indexOf(
            file.name.substr(file.name.lastIndexOf(".") + 1)
            ) == -1
        ) {
            this.$message.warning("请上传zip格式的文件");
            return;
        }
        console.log(file)
        let formData = new FormData();
        formData.append("file", file.raw);
        const res = await updatePhotoApi(formData)
        if(res.retCode === 0) {
            this.$message.success("上传成功");
        } else {
            this.$message.error(res.retMsg);
        }
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding: 15px 15px 0;
}
</style>
