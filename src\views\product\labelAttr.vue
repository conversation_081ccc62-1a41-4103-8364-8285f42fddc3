<template>
  <div class="component-container" :style="{'padding-bottom': showEmpty ? '70px' : '0px'}">
    <div class="basic-info-title" @click="showForm = !showForm">
      标签属性<i style="color:#3B95A8" :class="showForm ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
    </div>
    <el-form :model="form" ref="form" label-width="120px" v-show="showForm">
      <el-row type="flex">
        <!-- 一致性评价品种 -->
        <el-col :md="8" :sm="12" :xs="24">
          <el-form-item
            :disabled="formDisable"
            label="一致性评价品种"
            prop="conEvaluateVariety"
            :class="{
              'is-change': changeList.includes('conEvaluateVariety') || coorectionType['conEvaluateVariety']
            }"
          >
            <template v-slot:label>
              <el-tooltip
                class="item"
                effect="dark"
                :content="coorectionType['conEvaluateVariety']"
                :disabled="!coorectionType['conEvaluateVariety']"
                placement="top-start"
              >
                <span>一致性评价品种</span>
              </el-tooltip>
            </template>
            <el-radio-group
              v-model="skuForm.conEvaluateVariety"
              size="small"
              :disabled="checkOperatePermission('conEvaluateVariety') || formDisable"
            >
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row type="flex">
        <!-- 医院品种 -->
        <el-col :md="8" :sm="12" :xs="24">
          <el-form-item
            label="医院品种"
            prop="hospitalVariety"
            :class="{
              'is-change': changeList.includes('hospitalVariety') || coorectionType['hospitalVariety']
            }"
          >
            <template v-slot:label>
              <el-tooltip
                class="item"
                effect="dark"
                :content="coorectionType['hospitalVariety']"
                :disabled="!coorectionType['hospitalVariety']"
                placement="top-start"
              >
                <span>医院品种</span>
              </el-tooltip>
            </template>
            <el-radio-group v-model="skuForm.hospitalVariety" size="small" :disabled="checkOperatePermission('hospitalVariety') || formDisable">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row type="flex">
        <!-- 慢病品种 -->
        <!-- TYPEENUM: {
            EMPTY: "", //未选择
            GIFT: 6, //赠品
            NOT_MEDICINE: 5, //非药
            MEDICAL_INSTRUMENT: 4, //医疗器械
            TRADITIONAL_MEDICINE: 3, //中药
            GENERAL_MEDICINE: 1 //普通药品
          }-->
        <el-col :md="8" :sm="12" :xs="24" v-show="
          colToggle(
            [
              TYPEENUM.EMPTY,
              TYPEENUM.NOT_MEDICINE,
              TYPEENUM.MEDICAL_INSTRUMENT,
              TYPEENUM.TRADITIONAL_MEDICINE,
              TYPEENUM.GENERAL_MEDICINE,
            ],
            'chronicDiseasesVariety'
          )
        ">
          <el-form-item
            label="慢病品种"
            prop="chronicDiseasesVariety"
            ref="chronicDiseasesVariety"
            :class="{
              'is-change': changeList.includes('chronicDiseasesVariety') || coorectionType['chronicDiseasesVariety']
            }"
          >
            <template v-slot:label>
              <el-tooltip
                class="item"
                effect="dark"
                :content="coorectionType['chronicDiseasesVariety']"
                :disabled="!coorectionType['chronicDiseasesVariety']"
                placement="top-start"
              >
                <span>慢病品种</span>
              </el-tooltip>
            </template>
            <el-radio-group
              v-model="model.chronicDiseasesVariety"
              :disabled="modelAttr.chronicDiseasesVarietyDisabled || formDisable"
              size="small"
            >
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="partition-content" style="height: 20px; background: #f0f2f5; padding-left: -20px"></div>
  </div>
</template>

<script>
import { skuForm, dataModel, dataModelAttr } from "@/utils/basicInfoData"
const _defaultDataModel = _.cloneDeep(dataModel);
export default {
  name: "",
  components: {},
  filters: {},
  props: {
    modify: {
      type: String,
      default: ""
    },
    // 新版本的审批流
    newAudit: {
      type: Boolean,
      default: false
    },
    // 商品修改改变字段
    changeList: {
      type: Array,
      required: false,
      default: () => []
    },
    /**
     * spu渲染数据
     */
    spuData: {
      type: Object,
      required: false,
      default: () => {}
    },
    /**
     * sku渲染数据 (赠品使用)
     */
    skuData: {
      type: Array,
      required: false,
      default: () => []
    },
    coorection: {
      type: Object,
      required: false,
      default: () => {}
    },
    formDisable:{
      type: Boolean,
      default: false
    },
    showEmpty:{
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      showForm: true,
      //sku表单对象
      skuForm,
      model: dataModel,
      TYPEENUM: {
        EMPTY: "", //未选择
        GIFT: 5, //赠品
        NOT_MEDICINE: 4, //非药
        MEDICAL_INSTRUMENT: 3, //医疗器械
        TRADITIONAL_MEDICINE: 2, //中药
        GENERAL_MEDICINE: 1 //普通药品 1
      },
      modelAttr: dataModelAttr,
      isInit: false // 初始化
    }
  },
  computed: {
    // 操作类型
    operationType() {
      return this.$store.getters.operationType
    },
    // 路由参数
    urlParam() {
      return this.$route.query
    },
    coorectionType: function() {
      return this.coorection ? this.coorection : {}
    }
  },
  watch: {},
  created() {
    // 进入页面spu 加载完成触发
    this.$bus.$on("spuLoading", loading => {
      // console.log(loading, "spuLoading加载完成");
      if (loading) {
        this.isInit = true
        if (this.skuData.length) {
          this.skuForm = this.skuData[0]
        }
      }
    })
  },
  methods: {
    colToggle(spuCategoryAry, modelField) {
      // 根据商品类型检测是否显示当前列
      return this.checkStateByspuCategory(spuCategoryAry, modelField);
    },
    /**
     * @description: 根据商品分类判断是否显示对应的SPU字段
     * @param {array} spuCategoryAry 商品分类数组，数组包含的商品分类显示下方参数 modelField 对应的SPU字段
     * @param {array} modelField  SPU字段名称
     * @return:{function} 根据 spuCategoryAry 与 modelField 计算是否展示的将箭头函数
     */
    checkStateByspuCategory(spuCategoryAry, modelField) {
      return (spuCategoryAry, modelField) => {
        let res = spuCategoryAry.includes(this.model.spuCategory);
        modelField.split(",").forEach((item) => {
          this.modelAttr[item + "Display"] = res;
        });
        if (!res) {
          this.resetModeValue(modelField);
        }
        return res;
      };
    },
    /**
     * @description: 重置参数中的表单选项值为初始值
     * @param {array} spukey SPU字段中需要重置的KEY
     * @return:
     */
    resetModeValue(spukey) {
      this.model[spukey] = _defaultDataModel[spukey];
    },
    /**
     * @description: 判断预首营修改时的 FORM 字段编辑权限
     * @param {string} skuFormRulesKey skuFormRules 对象的 key
     * @return:
     */
    checkOperatePermission(skuFormRulesKey) {
      if (this.operationType == "detail") {
        return true
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.component-container {
  padding-bottom: 70px;
  .basic-info-title {
    font-weight: 600;
    margin-bottom: 22px;
    border-bottom: 1px solid #e4e4eb;
    line-height: 50px;
    padding-left: 20px;
  }
  .el-form-item {
    &.nMr {
      /deep/ .el-form-item__content {
        margin-left: 0 !important;
        .el-select {
          width: 100% !important;
        }
      }
    }
    &.width100 {
      /deep/ .el-select {
        width: 100% !important;
      }
      /deep/ .el-input {
        width: 100% !important;
      }
    }
    /deep/ {
      .el-form-item__label {
        color: #292933;
        font-weight: normal;
      }
    }
  }
  .is-change {
    /deep/ {
      // 调整修改项lable样式
      .el-form-item__label {
        color: #f56c6c;
        font-weight: bold;
      }
    }
  }

  .detail-title {
    margin: 20px;
    font-size: 16px;
    color: #292933;
  }

  .martop0 {
    margin-top: 0;
  }
  .marbot20 {
    margin-bottom: 20px;
  }
  .martop20 {
    margin-top: 20px;
  }

  .flex-text {
    display: flex;
    color: #292933;
    font-weight: normal;
    margin: 20px 0 10px 0;
    > span:first-child {
      width: 130px;
      text-align: right;
    }
    > span:last-child {
      flex: 1;
    }
  }
  .el-dialog__wrapper /deep/ .el-dialog {
    top: 50% !important;
  }
  .el-dialog__wrapper.drugs-guide /deep/ .el-dialog .el-dialog__body {
    padding: 0;
  }
  .el-dialog__wrapper.drugs-guide /deep/ .el-dialog .el-dialog__footer {
    padding: 10px;
  }
  .drugs-guide-content {
    height: 600px;
    overflow-y: auto;
    padding: 20px 20px 0 20px;
  }
}
</style>
