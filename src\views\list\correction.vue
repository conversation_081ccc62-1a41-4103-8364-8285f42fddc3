
<template>
  <div class="task-to-be-film-container" v-loading="loading">
    <!-- 查询表单 -->
    <div class="search-form-wrap">
      <el-form :model="model" ref="refSearchForm">
        <el-row type="flex">
          <!-- 上报时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="上报时间" prop="createTime">
              <el-date-picker
                v-model="formData.createTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                clearable
              ></el-date-picker>
            </el-form-item>
          </el-col>

          <!-- 上报来源 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="上报来源">
              <el-select v-model="formData.source" placeholder="请选择" clearable>
                <el-option label="其它" :value="0"></el-option>
                <el-option label="荷叶健康" :value="1"></el-option>
                <el-option label="SaaS" :value="2"></el-option>
                <el-option label="POP" :value="3"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 纠错类型 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="纠错类型">
              <el-select v-model="formData.correctionType" placeholder="请选择" clearable>
                <el-option label="基础资料错误" :value="1"></el-option>
                <el-option label="图片错误" :value="2"></el-option>
                <el-option label="说明书错误" :value="3"></el-option>
                <el-option label="缺少精修图" :value="4"></el-option>
                <el-option label="缺少说明书" :value="5"></el-option>
                <el-option label="商品重复" :value="6"></el-option>
                <!-- <el-option label="图片补全" :value="7"></el-option> -->
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 上报人 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="上报人">
              <el-input v-model="formData.createUser" placeholder="模糊查询" clearable></el-input>
            </el-form-item>
          </el-col>

          <!-- 商品编码 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="商品编码">
              <el-input v-model="formData.productCode" placeholder="模糊查询" clearable></el-input>
            </el-form-item>
          </el-col>

          <!-- 商品ID -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="商品ID">
              <el-input v-model="formData.productId" placeholder="模糊查询" clearable></el-input>
            </el-form-item>
          </el-col>

          <!-- 商品名称-->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="商品名称">
              <el-input v-model="formData.productName" placeholder="模糊查询" clearable></el-input>
            </el-form-item>
          </el-col>

          <!-- 生产厂家-->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="生产厂家">
              <el-input v-model="formData.manufacturerName" placeholder="模糊查询" clearable></el-input>
            </el-form-item>
          </el-col>

          <!-- 处理状态-->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="处理状态" prop="mechanism">
              <el-select v-model="formData.reviewStatus" placeholder="请选择" clearable>
                <el-option label="已驳回" :value="2"></el-option>
                <el-option label="审核通过" :value="1"></el-option>
                <el-option label="待处理" :value="0"></el-option>
                <el-option label="审核中" :value="3"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 按钮 -->
          <el-col :span="6" :offset="18">
            <el-button type="primary" size="medium" @click="btnSearchClick">查询</el-button>
            <el-button size="medium" @click="btnResetClick">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- <div class="btn-wrap">
      <excel-file-upload
        :scope="uploadConfiguration"
        :includeFileType="['xlsx']"
        :accept="'.xlsx'"
        @success="excelUpload"
        >批量导入商品图片补全申请</excel-file-upload
      >
    </div> -->
    <!-- table表单 -->
    <!-- table -->
    <div class="table-wrap">
      <!-- createStartTime:"", // 开始时间
          createEndTime:"",//结束时间
          source:"",//上报来源
          correctionType:"",//纠错类型
          createUser:"",//上报人
          productCode:"",//商品编码
          productId:"",//商品ID
          productName:"",//商品名称
          manufacturerName:"",//生产厂家名称
          reviewStatus:""//处理状态 -->
      <vxe-table
        border
        highlight-hover-row
        resizable
        height="100%"
        auto-resize
        size="small"
        align="center"
        :tooltip-config="{ enterable: false }"
        :loading="tableLoading"
        :data="tableData"
        @cell-dblclick="cellDBLClickEvent"
        :seq-config="{ startIndex: (pageNum - 1) * pageSize }"
        ref="refVxeTable"
      >
        <vxe-table-column type="seq" title="序号" width="60" show-header-overflow show-overflow fixed="left"></vxe-table-column>
        <vxe-table-column field="createTime" title="上报时间" width="120" show-header-overflow show-overflow>
          <template v-slot="{ row }">
            {{ parseTime(row.createTime) }}
          </template>
        </vxe-table-column>
        <vxe-table-column field="source" title="上报来源" min-width="120" show-header-overflow show-overflow>
          <template v-slot="{ row }">
            {{ ["其它", "荷叶健康", "SaaS", "POP"][row.source] }}
          </template>
        </vxe-table-column>
        <vxe-table-column field="createUser" title="上报人" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column
          field="createInstitutionName"
          title="上报人所属机构"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column field="correctionType" title="纠错类型" min-width="120" show-header-overflow show-overflow>
          <template v-slot="{ row }">
            {{ ["", "基础资料错误", "图片错误", "说明书错误", "缺少精修图", "缺少说明书", "商品重复", ""][row.correctionType] }}
          </template>
        </vxe-table-column>
        <vxe-table-column field="reviewStatus" title="处理状态" min-width="120" show-header-overflow show-overflow>
          <template v-slot="{ row }">
            {{ ["待处理", "审核通过", "已驳回", "审核中"][row.reviewStatus] }}
          </template>
        </vxe-table-column>
        <vxe-table-column field="productCode" title="商品编码" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="productId" title="商品ID" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="productName" title="商品名称" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="preOperateStatus" title="自营状态" min-width="120" show-header-overflow show-overflow>
          <template v-slot="{ row }">
            {{ row.preOperateStatus | filterJCPreOperateStatus }}
          </template>
        </vxe-table-column>
        <vxe-table-column field="spec" title="规格/型号" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="manufacturerName" title="生产厂家" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="correctionMsg" title="上报留言" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column
          field="filingsAuthor"
          title="化妆品备案人/注册人"
          min-width="180"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column title="操作" width="120" show-header-overflow show-overflow fixed="right">
          <template v-slot="{ row }">
            <div v-if="row.correctionType != 7">
              <span v-if="row.reviewStatus === 0">
                <el-link :underline="false" v-if="row.receive == 0" type="primary" @click.stop="receive(row)">领取</el-link>
                <el-link :underline="false" v-if="row.receive == 1" type="primary" @click.stop="goDetail(row)">审核</el-link>
              </span>
              <span v-else>
                <el-link disabled>-</el-link>
              </span>
            </div>
          </template>
        </vxe-table-column>
      </vxe-table>
    </div>

    <!-- 分页 -->
    <el-row class="custom-pagination-wrap">
      <el-col>
        <el-pagination
          background
          :current-page.sync="pageNum"
          :page-sizes="[20, 50, 100, 200]"
          :page-size.sync="pageSize"
          :total="total"
          layout="prev, pager, next, jumper,total, sizes"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { parseTimestamp, hasPermission } from "@/utils/index.js"
import { correctionList, correctionReveive } from "@/api/list.js"
import {
  correctionDetail, // 商品纠错信息
} from "@/api/product"
import excelFileUpload from "@/components/common/excelFileUpload"

export default {
  name: "",
  components: { excelFileUpload },
  filters: {},
  props: {},
  data() {
    return {
      loading: false,
      pageNum: 1,
      pageSize: 20,
      total: 0, //总条数
      formData: {
        createStartTime: "", // 开始时间
        createEndTime: "", //结束时间
        createTime: "",
        source: "", //上报来源
        correctionType: "", //纠错类型
        createUser: "", //上报人
        productCode: "", //商品编码
        productId: "", //商品ID
        productName: "", //商品名称
        manufacturerName: "", //生产厂家名称
        reviewStatus: "", //处理状态
      },
      tableLoading: false,
      tableData: [],
      uploadConfiguration: {
        applyCode: "",
        disabled: false,
        type: "4",
        buttonType: "button",
        url: "/api/correction/batch/picComplement",
        downloadName: "图片补全商品模板.xlsx",
        downloadUrl: "../../../static/assets/excel/图片补全商品模板.xlsx",
      },
    }
  },
  computed: {},
  watch: {},
  created() {
    this.searchForm()
  },
  mounted() {},
  methods: {
    /**
     * 查询table数据
     */
    async searchForm() {
      try {
        this.tableLoading = true
        let param = Object.assign({}, this.formData)
        param.page = this.pageNum
        param.limit = this.pageSize
        // this.saveLocalStore(param);//保存本地
        param.createStartTime = param.createTime[0]
        param.createEndTime = param.createTime[1]
        delete param.createTime
        let res = await correctionList(param)
        this.tableLoading = false
        console.log(res)
        if (res.success) {
          this.tableData = res.data.list
          this.total = res.data.total
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          })
        }
      } catch (error) {
        console.error(error)
      }
    },

    /**
     * 查询按钮点击
     */
    btnSearchClick() {
      this.pageNum = 1
      this.searchForm()
    },

    /**
     * 重置
     */
    btnResetClick() {
      // this.$refs["refSearchForm"].resetFields();
      this.formData = {
        createStartTime: "", // 开始时间
        createEndTime: "", //结束时间
        createTime: "",
        source: "", //上报来源
        correctionType: "", //纠错类型
        createUser: "", //上报人
        productCode: "", //商品编码
        productId: "", //商品ID
        productName: "", //商品名称
        manufacturerName: "", //生产厂家名称
        reviewStatus: "", //处理状态
      }
      this.pageNum = 1
      this.searchForm()
    },

    /**
     * pageSize 改变事件
     */
    handleSizeChange(pageSize) {
      this.pageNum = 1
      this.pageSize = pageSize
      this.searchForm()
    },

    /**
     * pageNum 改变事件
     */
    handleCurrentChange(currentPage) {
      this.pageNum = currentPage
      this.searchForm()
    },
    clearLocalStore() {
      localStorage.removeItem('correction')
    },
    //保存本地
    saveLocalStore(form) {
      localStorage.setItem("correction", JSON.stringify(form))
    },
    //取出本地储存的数据
    getLocationStore() {
      let localStoragefrorm = localStorage.getItem("correction")
      if (localStoragefrorm) {
        let formall = JSON.parse(localStoragefrorm)
        let formkeys = Object.keys(this.formData)
        for (let index = 0; index < formkeys.length; index++) {
          const key = formkeys[index]
          if (formall[key] && formall[key] !== this.formData[key]) {
            this.formData[key] = formall[key]
          }
        }
        this.pageNum = formall.pageNum
        this.pageSize = formall.pageSize
        this.total = formall.total
      }
    },

    async receive(row) {
      try {
        let res = await correctionReveive({ uniqueCode: row.uniqueCode })
        if (res.success) {
          this.btnSearchClick()
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.data.retMsg,
          })
        }
      } catch (error) {
        console.error(error)
      }
    },
    async goDetail(row) {
      try {
        this.loading = true
        let res = await correctionDetail({
          uniqueCode: row.uniqueCode,
        })
        if (res.success) {
          localStorage.setItem("coorectionData", JSON.stringify(res.data))
          try {
            parent.CreateTab(
              "../static/dist/index.html#/product/editProduct?type=coorection&sendCode=" +
                res.data.applyCode +
                "&productCode=" +
                res.data.productCode +
                "&productType=" +
                res.data.productType +
                "&spuCode=" +
                res.data.spuCode +
                "&uniqueCode=" +
                row.uniqueCode,
              "修改商品",
              true
            )
          } catch (error) {
            console.log(error)
            this.$router.push({
              name: "editProduct",
              query: {
                type: "coorection",
                sendCode: res.data.applyCode,
                productCode: res.data.productCode,
                productType: res.data.productType,
                spuCode: res.data.spuCode,
                uniqueCode: row.uniqueCode,
              },
            })
          }
        } else {
          this.$message.error(res.retMsg)
        }
        this.loading = false
      } catch (error) {
        this.loading = false
        console.error(error)
      }
    },
    /**
     * @description: 格式化时间参数
     * @param {date} time 时间戳
     * @return: string 格式化后的时间戳
     */
    parseTime(time) {
      return parseTimestamp(time)
    },
    hasPermission(str) {
      return hasPermission(str)
    },
  },
}
</script>
<style lang="scss" scoped>
.task-to-be-film-container {
  /**
   * 查询表单
   */
  .search-form-wrap {
    width: 100%;
    padding: 15px;
    border-bottom: 1px dashed #e4e4eb;

    .el-row {
      flex-wrap: wrap;

      .el-col {
        display: flex;
        justify-content: flex-end;

        .el-form-item {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          width: 100%;
          margin-right: 0;
          margin-bottom: 15px;

          /deep/ {
            .el-form-item__label {
              width: 96px;
              line-height: normal;
              padding: 0 12px;
              color: #292933;
              font-weight: normal;
            }

            .el-form-item__content {
              flex: 1;

              .el-range-editor.el-input__inner {
                width: 100%;

                .el-range-separator {
                  width: 14px;
                  padding: 0;
                }
              }
            }
          }

          .el-input {
            width: 100%;
          }

          .el-select {
            width: 100%;
          }
        }
      }
    }
  }

  /**
     * table
     */
  .table-wrap {
    width: 100%;
    min-height: 440px;
    height: calc(100vh - 295px);
    padding: 0 15px;
    margin-top: 15px;
  }
}
.btn-wrap {
  padding: 15px 0 0 10px;
}
.radio-wrap {
  .title {
    padding-right: 20px;
  }
}
</style>
