<template>
  <div class="v-container">
    <el-form :model="form" ref="form" label-width="110px">
      <el-row class="search-form">
        <el-col :xs="24" :sm="12" :md="8" :xl="6">
          <el-form-item label="申请时间" prop="applyDate">
            <el-date-picker
              v-model="form.applyDate"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :xs="12" :sm="12" :md="6" :xl="6">
          <el-form-item label="申请人" prop="applyer">
            <el-input v-model="form.applyer"></el-input>
          </el-form-item>
        </el-col>

        <el-col :xs="12" :sm="12" :md="6" :xl="6">
          <el-form-item label="导出列表" prop="modifyType">
            <el-select v-model="form.modifyType" placeholder="请选择">
              <el-option value label="全部"></el-option>
              <el-option value="1" label="SPU列表"></el-option>
              <el-option value="2" label="SKU列表"></el-option>
              <el-option value="3" label="商品列表"></el-option>

              <el-option value="4" label="商品修改记录"></el-option>
              <el-option value="5" label="批量新增记录"></el-option>
              <el-option value="6" label="批量修改记录-单据列表"></el-option>
              <el-option value="7" label="批量属性修改-商品列表"></el-option>
              <el-option value="8" label="批量停启用记录-单据列表"></el-option>
              <el-option value="12" label="批量停启用记录-商品列表"></el-option>
              <el-option value="9" label="预首营记录"></el-option>
              <el-option value="10" label="商品品类列表"></el-option>
              <el-option value="11" label="商品医保列表"></el-option>
              <el-option value="13" label="客户商品审核"></el-option>

              <el-option value="16" label="待拍摄商品"></el-option>
              <el-option value="17" label="新品上报列表"></el-option>
              <el-option value="18" label="拍摄任务单列表"></el-option>
              <el-option value="19" label="友商库图片补全申请记录"></el-option>
              <el-option value="20" label="我的申请审批流导出列表"></el-option>
              <el-option value="21" label="已处理审批流导出列表"></el-option>
              <el-option value="22" label="领取与处理审批流导出列表"></el-option>
              <el-option value="23" label="商品图片补全列表"></el-option>

              <el-option value="24" label="友商库图片列表"></el-option>
              <el-option value="25" label="非通过商品列表"></el-option>
              <el-option value="26" label="条码列表"></el-option>
              <el-option value="27" label="字典列表"></el-option>
              <el-option value="28" label="分类数据列表"></el-option>
              <el-option value="29" label="任务看板数据列表"></el-option>
              <el-option value="30" label="同步待提交商品列表"></el-option>
              <el-option value="31" label="图片补全转换数据列表"></el-option>
              <el-option value="32" label="图片任务列表"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="12" :md="4" :xl="6">
          <div style="text-align: right">
            <el-button size="medium" type="primary" @click="queryList"
              >查询</el-button
            >
            <el-button size="medium" @click="resetList">重置</el-button>
          </div>
        </el-col>
      </el-row>
    </el-form>

    <div class="table-wrap">
      <vxe-table
        border
        highlight-hover-row
        resizable
        height="100%"
        auto-resize
        size="small"
        align="center"
        :tooltip-config="{ enterable: false }"
        :loading="tableLoading"
        :data="tableData"
        @sort-change="sortQueryList"
        ref="refVxeTable"
      >
        <vxe-table-column
          type="index"
          title="序号"
          width="60"
          show-header-overflow
          show-overflow
          fixed="left"
        ></vxe-table-column>

        <vxe-table-column
          field="applyDate"
          title="申请时间"
          min-width="150"
          show-header-overflow
          show-overflow
          sortable
        >
          <template v-slot="{ row }">{{ row.applyDate | dateTime }}</template>
        </vxe-table-column>

        <vxe-table-column
          field="applyer"
          title="申请人"
          min-width="100"
          show-header-overflow
          show-overflow
          sortable
        ></vxe-table-column>

        <vxe-table-column
          field="modifyType"
          title="导出列表"
          min-width="100"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">{{
            {
              1: "SPU列表",
              2: "SKU列表",
              3: "商品列表",
              4: "商品修改记录",
              5: "批量新增记录",
              6: "批量修改记录-单据列表",
              7: "批量属性修改-商品列表",
              8: "批量停启用记录-单据列表",
              12: "批量停启用记录-商品列表",
              9: "预首营记录",
              10: "商品品类列表",
              11: "商品医保列表",
              13: "客户商品审核",
              16: "待拍摄商品",
              17: "新品上报列表",
              18: "拍摄任务单列表",
              19: "友商库图片补全申请记录",
              20: "我的申请审批流导出列表",
              21: "已处理审批流导出列表",
              22: "领取与处理审批流导出列表",
              23: "商品图片补全列表",
              24: "友商库图片列表",
              25: "非通过商品列表",
              26: "条码列表",
              27: "字典列表",
              28: "分类数据列表",
              29: "任务看板数据列表",
              30: "同步待提交商品列表",
              31: "图片补全转换数据列表",
              32: "图片任务列表"
            }[row.modifyType]
          }}</template>
        </vxe-table-column>

        <vxe-table-column
          field="applyCondition"
          title="查询条件"
          min-width="300"
          show-header-overflow
          show-overflow
        ></vxe-table-column>

        <vxe-table-column
          field="applyTotalNum"
          title="导出数量"
          min-width="100"
          show-header-overflow
          show-overflow
        ></vxe-table-column>

        <vxe-table-column
          field="downloadStatus"
          title="导出状态"
          min-width="100"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">{{
            ["", "导出中", "导出成功", "导出失败"][row.downloadStatus]
          }}</template>
        </vxe-table-column>

        <vxe-table-column
          title="操作"
          min-width="100"
          fixed="right"
          show-overflow
        >
          <template v-slot="{ row }">
            <el-link
              type="primary"
              :underline="false"
              :disabled="row.downloadStatus != 2 || !row.downloadFileUrl"
              @click="download(row)"
              >下载</el-link
            >
          </template>
        </vxe-table-column>
      </vxe-table>

      <!-- 分页 -->
      <el-row class="custom-pagination-wrap">
        <el-col>
          <el-pagination
            background
            :current-page.sync="pageNum"
            :page-size.sync="pageSize"
            :page-sizes="[20, 50, 100, 200]"
            :total="total"
            layout="prev, pager, next, jumper,total, sizes"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { getExportListData } from "../../api/product.js";
import { getExportListDataSelf } from "../../api/product.js";
import { fileDownLoad } from "../../api/productPicture.js";
import { parseTime, parseDate } from "@/utils/index.js";

export default {
  name: "",
  components: {},
  filters: {
    dateTime(value) {
      return parseTime(value);
    },
  },
  props: {},
  data() {
    return {
      form: {
        applyDate: [], // 申请时间
        applyer: "", // 申请人
        modifyType: "", // 列表导出 下载类型(“”全部, 1:SPU, 2:SKU, 3:商品)
      },

      pageNum: 1,
      pageSize: 20,
      total: 0, //总条数

      sortFiled: "", // 排序查询
      sortRule: "", //(升序-ASC, 降序-DESC)

      tableLoading: false,
      tableData: [],
    };
  },
  computed: {},
  watch: {},
  created() {
    // 查询列表
    this.queryList();
  },
  mounted() {},
  methods: {
    getQueryParams() {
      let {
        applyDate,
        modifyType, // 拍摄任务单号
        applyer, // 发起人
      } = this.form;
      return {
        applyStartDate: applyDate[0] || "",
        applyEndDate: applyDate[1] || "",
        modifyType,
        applyer,
        downloadStatus: 2, //下载状态(1:导出中, 2:导出成功, 3:导出失败)
        page: this.pageNum,
        limit: this.pageSize,
        sortList: this.sortFiled
          ? [
              {
                order: "1",
                columnName: this.sortFiled,
                lift: this.sortRule, //(升序-ASC, 降序-DESC)
              },
            ]
          : [],
      };
    },

    // 排序查询
    sortQueryList({ column, property, order }) {
      if (order) {
        this.pageNum = 1;
        this.sortFiled = property;
        this.sortRule = order;
        this.queryList();
      }
    },

    // 查询
    async queryList() {
      this.tableLoading = true;
      let res = null;
      if (this.$route.query.type == 1) {
        res = await getExportListData(this.getQueryParams());
      } else {
        res = await getExportListDataSelf(this.getQueryParams());
      }
      this.tableLoading = false;
      this.tableData = res.data.list;
      this.total = res.data.total;
    },

    //  重置
    resetList() {
      this.$refs["form"].resetFields();
      this.queryList();
    },
    getXlsxName(type) {
      let typeName = "";
      switch (type) {
        case 1:
          typeName = "SPU列表";
          break;
        case 2:
          typeName = "SKU列表";
          break;
        case 3:
          typeName = "商品列表";
          break;
        case 4:
          typeName = "商品修改记录";
          break;
        case 5:
          typeName = "批量新增记录";
          break;
        case 6:
          typeName = "批量修改记录-单据列表";
          break;
        case 7:
          typeName = "批量属性修改-商品列表";
          break;
        case 8:
          typeName = "批量停启用记录-单据列表";
          break;
        case 12:
          typeName = "批量停启用记录-商品列表";
          break;
        case 9:
          typeName = "预首营记录";
           break;
        case 10:
          typeName = "商品品类列表";
          break;
        case 11:
          typeName = "商品医保列表";
          break;
        case 13:
          typeName = "客户商品审核";
          break;
        case 16:
          typeName = "待拍摄商品";
           break;
        case 17:
          typeName = "新品上报列表";
          break;
        case 18:
          typeName = "拍摄任务单列表";
          break;
        case 19:
          typeName = "友商库图片补全申请记录";
          break;
        case 20:
          typeName = "我的申请审批流导出列表";
          break;
        case 21:
          typeName = "已处理审批流导出列表";
          break;
        case 22:
          typeName = "领取与处理审批流导出列表";
          break;
        case 23:
          typeName = "商品图片补全列表";
          break;
        case 24:
          typeName = "友商库图片列表";
          break;
        case 25:
          typeName = "非通过商品列表";
          break;
        case 26:
          typeName = "条码列表";
          break;
        case 27:
          typeName = "字典列表";
          break;
        case 28:
          typeName = "分类数据列表";
          break;
        case 29:
          typeName = "任务看板数据列表";
          break;
        case 30:
          typeName = "同步待提交商品列表";
          break;
        case 31:
          typeName = "图片补全转换数据列表";
          break;
        case 32:
          typeName = "图片任务列表";
          break;
        default:
          return;
      }
      return `导出${typeName}数据_${parseDate(new Date())}.xlsx`;
    },
    download(rowData) {
      let xlsxName = this.getXlsxName(rowData.modifyType);
      this.$message("开始下载，请耐心等待");
      fileDownLoad(rowData.downloadFileUrl, xlsxName)
        .then((resp) => {
          if (resp.data && resp.data.retCode) {
            //失败
            this.$message({
              showClose: true,
              type: "error",
              message: resp.data.retMsg,
            });
          } else {
            let blob = new Blob([resp]);
            let blobUrl = window.URL.createObjectURL(blob);
            let a = document.createElement("a");
            a.style.display = "none";
            a.download = xlsxName;
            a.href = blobUrl;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
          }
        })
        .catch((err) => {
          this.$message({
            showClose: true,
            type: "error",
            message: "下载失败，请重试",
          });
        });
    },

    /**
     * pageSize 切换每页条数
     */
    handleSizeChange(pageSize) {
      this.pageNum = 1;
      this.pageSize = pageSize;
      this.queryList();
    },

    /**
     * pageNum 切换页码 上一页 下一页 前往第几页
     */
    handleCurrentChange(currentPage) {
      this.pageNum = currentPage;
      this.queryList();
    },
  },
};
</script>

<style lang="scss" scoped>
.v-container {
  .search-form {
    width: 100%;
    padding: 15px;
    // border-bottom: 1px dashed #e4e4eb;
    .el-date-editor,
    .el-select {
      width: 100%;
    }
  }
  .tool-btn {
    width: 100%;
    padding: 15px;
  }
  .table-wrap {
    padding: 0 15px;
    min-height: 300px;
    width: 100%;
    height: calc(100vh - 177px);
  }
}
</style>
