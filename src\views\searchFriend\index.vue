
<template>
    <div class="page-container">
      <!-- 查询表单 -->
      <div class="search-form-wrap">
        <el-form ref="refSearchForm">
          <el-row type="flex">
            <!-- 商品ID -->
            <el-col :lg="8" :xs="24" :sm="12" :xl="6">
              <el-form-item label="商品ID">
                <el-input v-model="productId" clearable>
                    <el-button :disabled="!productId" :class="productId ? 'load-btn' : ''" slot="append" @click="loadForm">载入</el-button>
                </el-input>
              </el-form-item>
            </el-col>
            <!-- 通用名 -->
            <el-col :lg="8" :xs="24" :sm="12" :xl="6">
              <el-form-item label="通用名">
                <el-input v-model="searchFormData.generalName.value" clearable :disabled="searchFormData.generalName.disabled">
                    <el-button slot="append" :icon="searchFormData.generalName.disabled ? 'el-icon-unlock' : 'el-icon-lock'" @click="changeLock('generalName')"></el-button>
                </el-input>
              </el-form-item>
            </el-col>
            <!-- 生产厂家 -->
            <el-col :lg="8" :xs="24" :sm="12" :xl="6">
              <el-form-item label="生产厂家">
                <el-input v-model="searchFormData.manufacturer.value" clearable :disabled="searchFormData.manufacturer.disabled">
                    <el-button slot="append" :icon="searchFormData.manufacturer.disabled ? 'el-icon-unlock' : 'el-icon-lock'" @click="changeLock('manufacturer')"></el-button>
                </el-input>
              </el-form-item>
            </el-col>
            <!-- 品牌 -->
            <el-col :lg="8" :xs="24" :sm="12" :xl="6">
              <el-form-item label="品牌">
                <el-input v-model="searchFormData.brand.value" clearable :disabled="searchFormData.brand.disabled">
                    <el-button slot="append" :icon="searchFormData.brand.disabled ? 'el-icon-unlock' : 'el-icon-lock'" @click="changeLock('brand')"></el-button>
                </el-input>
              </el-form-item>
            </el-col>
            <!-- 批准文号 -->
            <el-col :lg="8" :xs="24" :sm="12" :xl="6">
              <el-form-item label="批准文号">
                <el-input v-model="searchFormData.approvalNo.value" clearable :disabled="searchFormData.approvalNo.disabled">
                    <el-button slot="append" :icon="searchFormData.approvalNo.disabled ? 'el-icon-unlock' : 'el-icon-lock'" @click="changeLock('approvalNo')"></el-button>
                </el-input>
              </el-form-item>
            </el-col>
            <!-- 小包装条码 -->
            <el-col :lg="8" :xs="24" :sm="12" :xl="6">
              <el-form-item label="小包装条码">
                <el-input v-model="searchFormData.smallPackageCode.value" clearable :disabled="searchFormData.smallPackageCode.disabled">
                    <el-button slot="append" :icon="searchFormData.smallPackageCode.disabled ? 'el-icon-unlock' : 'el-icon-lock'" @click="changeLock('smallPackageCode')"></el-button>
                </el-input>
              </el-form-item>
            </el-col>
            <!-- 规格型号 -->
            <el-col :lg="8" :xs="24" :sm="12" :xl="6">
              <el-form-item label="规格型号">
                <el-input v-model="searchFormData.spec.value" clearable :disabled="searchFormData.spec.disabled">
                    <el-button slot="append" :icon="searchFormData.spec.disabled ? 'el-icon-unlock' : 'el-icon-lock'" @click="changeLock('spec')"></el-button>
                </el-input>
              </el-form-item>
            </el-col>
            <!-- 来源 -->
            <el-col :lg="8" :xs="24" :sm="12" :xl="6">
                <el-form-item label="来源">
                    <el-select
                        filterable
                        multiple
                        collapse-tags
                        v-model="otherFormData.source"
                        placeholder="请选择来源"
                        @remove-tag="removeTag"
                        @change="changeSelect"
                    >
                    <el-option
                        label="全选"
                        value="全选"
                        @click.native="selectAll"
                        ></el-option>
                        
                        <el-option
                        v-for="item in sourceList"
                        :key="item.externalSource"
                        :label="item.webName"
                        :value="item.externalSource"
                        ></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <!-- 图片数量下限 -->
            <el-col :lg="8" :xs="24" :sm="12" :xl="6">
              <el-form-item label="图片数量下限">
                <el-input v-model="otherFormData.imageNumMin" clearable></el-input>
              </el-form-item>
            </el-col>
            <!-- 图片数量上限 -->
            <el-col :lg="8" :xs="24" :sm="12" :xl="6">
              <el-form-item label="图片数量上限">
                <el-input v-model="otherFormData.imageNumMax" clearable></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <!-- 按钮 -->
            <el-col :span="6" :offset="18">
              <el-button type="primary" size="medium" @click="btnSearchClick(0)"
                >查询</el-button
              >
              <el-button size="medium" @click="btnResetClick">重置</el-button>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <div class="btn-wrap">
        <el-button @click="btnSearchClick(0,'approvalNo')" type="primary" size="medium"
          >只按“批文”搜索</el-button
        >
        <el-button @click="btnSearchClick(0,'smallPackageCode')" type="primary" size="medium"
          >只按“条码”搜索</el-button
        >
        <el-button @click="showPending" type="primary" size="medium"
          >待定图片({{ pendingImgList.length }})</el-button
        >
        <el-button @click="showRecord" type="primary" size="medium"
          >下载记录</el-button
        >
      </div>
      <!-- table -->
      <div class="table-wrap">
        <vxe-table
          border
          highlight-hover-row
          resizable
          height="100%"
          auto-resize
          size="small"
          align="center"
          :tooltip-config="{ enterable: false }"
          :data="tableData"
          :seq-config="{
            startIndex: (searchFormData.pageNum - 1) * searchFormData.pageSize,
          }"
          ref="refVxeTable"
          :default-sort = "{prop: 'imgList', order: 'descending'}"
          :sort-config="{remote:true}"
          @sort-change="sortSpuTable"
          :cell-class-name="cellClassName"
          :row-class-name="rowClassName"
        >
          <vxe-table-column
            title="序号"
            width="80"
            show-header-overflow
            show-overflow
            fixed="left"
          ><template v-slot="{ seq }">
            <span>{{seq == 1 ? '输入内容' : seq - 1}}</span>
            </template></vxe-table-column>
          <vxe-table-column
            title="通用名/厂家/品牌/showname"
            min-width="120"
            show-header-overflow
            show-overflow
          ><template v-slot="{ row }">
            <span :class="row.generalNameCol ? 'col-red' : ''">{{row.generalName}}</span>
            <br>
            <span :class="row.manufacturerCol ? 'col-red' : ''">{{row.manufacturer}}</span>
            <br>
            <span :class="row.brandCol ? 'col-red' : ''">{{row.brand}}</span>
            <br>
            <span>{{row.showName}}</span>
            </template></vxe-table-column>
          <vxe-table-column
            field="approvalNo"
            title="批准文号"
            min-width="60"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="smallPackageCode"
            title="小包装条码"
            min-width="60"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="spec"
            title="规格型号"
            min-width="60"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            prop='imgList'
            title="图片缩略图"
            min-width="450"
            show-header-overflow
            show-overflow
            sortable
            align='left'
          >
          <template v-slot="{ row, seq }">
            <img @click="showDetail(row, index)" @mouseover="showBigCard(item.pictureUrl)" @mouseout="showBigCard(0)" v-show='index <= 5' v-for='(item, index) in row.imageList' :src="item.pictureUrl" :key='index' alt="" style="width:80px;max-height:80px;margin:2px;">
            <span>{{seq != 1 && row.imageList ? row.imageList.length + '张' : ''}}</span>
          </template>
        </vxe-table-column>
        </vxe-table>
        <img v-show='showCard' class="big-img" :src="imgSrc" alt="">
      </div>
      <record ref="record"></record>
      <preview-img ref="previewImg" :productInfo="productInfo" :pendingImgList="pendingImgList" @changePendingList="changePendingList" :isSearchPending="true"></preview-img>
      <pending-dialog ref="pendingDialog" @updatePendingImgList="updatePendingImgList"></pending-dialog>
    </div>
</template>

<script>
import { isNumber } from "@/utils"
import { getFriendList, getFriendLibraryPictureByCondition, getProductInfoById } from "@/api/searchFriend"
import previewImg from "@/components/uploadImg/previewImg";
import pendingDialog from "./components/pendingDialog.vue"
import record from "./components/record.vue"
export default {
  name: "",
  components: { previewImg, pendingDialog, record },
  filters: {},
  props: {},
  data() {
    return {
      productId:'',//商品ID
      searchFormData: {
          generalName:{//通用名
              value:'',
              disabled:false
          },
          manufacturer:{//生产厂家
              value:'',
              disabled:false
          },
          brand:{//品牌
              value:'',
              disabled:false
          },
          approvalNo:{//批准文号
              value:'',
              disabled:false
          },
          smallPackageCode:{//小包装条码
              value:'',
              disabled:false
          },
          spec:{//规格型号
              value:'',
              disabled:false
          }
      },
      otherFormData: {
          source: [],
          imageNumMin:null,
          imageNumMax:null,
          orderBy:''
      },
      sourceList:[],//来源列表
      tableData: [],
      showCard:false, //是否显示放大的图片
      imgSrc:'', //放大图片的地址
      productInfo: {}, //图片详情弹框的文案
      pendingImgList:[], //待定图片
    };
  },
  computed: {},
  watch: {},
  created() {
      this.getSourceList()
    //   this.btnSearchClick()
  },
  methods: {
    // 载入表单信息
    async loadForm() {
        if (this.productId) {
            try {
                const res = await getProductInfoById(this.productId)
                if(res.retCode === 0) {
                    console.log(res);
                    if (res.data) {
                        for(let item in res.data) {
                            if(this.searchFormData[item]) {
                                this.searchFormData[item].value = res.data[item]
                            }
                        }
                    }
                }else{
                    this.$message.error(res.retMsg)
                }          
            } catch (error) {
                console.log(error);
            }
        } else {
            this.$message.error('商品ID不能为空')
        }
    },
    // 锁定、解锁表单
    changeLock(e) {
        this.searchFormData[e].disabled = !this.searchFormData[e].disabled
    },
    // 重置表单
    resetDialog() {
        this.tableData = []
        this.searchFormData = {
          generalName:{
              value:'',
              disabled:false
          },
          manufacturer:{
              value:'',
              disabled:false
          },
          brand:{
              value:'',
              disabled:false
          },
          approvalNo:{
              value:'',
              disabled:false
          },
          smallPackageCode:{
              value:'',
              disabled:false
          },
          spec:{
              value:'',
              disabled:false
          }
      }
      this.otherFormData = {
          source: [],
          imageNumMin:null,
          imageNumMax:null,
          orderBy:''
      }
      this.selectAll()
    },
    //   列表查询 e: 0模糊+精确查询 1精确查询 2模糊查询 t:只按照某个字段搜索
    async btnSearchClick(e, t) {
        if(!this.searchFormData.approvalNo.value && !this.searchFormData.smallPackageCode.value) {
            this.$message.error('批文和条码请至少输入一个！')
            return
        }
        let param = {}
        // 只按xx搜索
        if(t) {
          // 处理单条表单数据
          param[t] = this.searchFormData[t].value
          // 禁用其他表单
          for(let item in this.searchFormData) {
            this.searchFormData[item].disabled =  item === t ? false : true
          }
          if(!this.searchFormData[t].value) {
            if(t === 'approvalNo') {
              this.$message.error('输入有效的批准文号再进行搜索！')
              return
            } else if(t === 'smallPackageCode') {
              this.$message.error('请输入有效的条码再进行搜索！')
              return
            }
          }
        } else {
          // 处理表单数据
          for(let item in this.searchFormData) {
            if(!this.searchFormData[item].disabled) {
              param[item] = this.searchFormData[item].value
            }
          }
        }
        let min = isNumber(this.otherFormData.imageNumMin)
        let max = isNumber(this.otherFormData.imageNumMax)
        if((!min && this.otherFormData.imageNumMin) || (!max && this.otherFormData.imageNumMax)) {
          this.$message.error('图片数量范围需填写正数')
          return
        } else if((+this.otherFormData.imageNumMin <= 0 && this.otherFormData.imageNumMin) || (+this.otherFormData.imageNumMax <= 0 && this.otherFormData.imageNumMax)) {
          this.$message.error('图片数量范围需填写正数')
          return
        } else if(+this.otherFormData.imageNumMin > +this.otherFormData.imageNumMax) {
          this.$message.error('图片数量下限不能大于上限')
          return
        }
        param.accurateMatching = 2
        param.imageNumMin = +this.otherFormData.imageNumMin || null
        param.imageNumMax = +this.otherFormData.imageNumMax || null
        let source = _.cloneDeep(this.otherFormData.source)
        if(source[0] === '全选') {
          source.shift()
        }
        param.source = source.join()
        // param.productId = this.initSearchForm.productId
        param.orderBy = this.otherFormData.orderBy
        param.sortColumn = "imageNum"
        param.selectedImageUrlList = []
        this.productInfo = param
        this.productInfo.manufacturerName = this.searchFormData.manufacturer.value
        console.log(param);
        try {
          let res = await getFriendLibraryPictureByCondition(param)
          console.log(res);
          if(res.retCode === 0) {
            this.tableData = res.data
            this.tableData.unshift({
              generalName:this.searchFormData.generalName.value,
              manufacturer:this.searchFormData.manufacturer.value,
              brand:this.searchFormData.brand.value,
              approvalNo:this.searchFormData.approvalNo.value,
              smallPackageCode:this.searchFormData.smallPackageCode.value,
              spec:this.searchFormData.spec.value,
              imageList:[],
            })
          } else {
            this.$message.error(res.retMsg)
          }
        }
        catch(error) {
          this.$message.error('请求失败')
          console.log(error);
        }
    },
    // 重置表格
    btnResetClick() {
        this.resetDialog()
    },
    changeSelect(val) {
      if (!val.includes("全选") && val.length === this.sourceList.length) {
        this.otherFormData.source.unshift("全选");
      } else if (
        val.includes("全选") &&
        val.length - 1 < this.sourceList.length
      ) {
        this.otherFormData.source = this.otherFormData.source.filter((item) => {
          return item !== "全选";
        });
      }
    },
    // 来源移除全选
    removeTag(val) {
      if (val === "全选") {
        this.otherFormData.source = [];
      }
    },
    // 来源全选
    selectAll() {
      if (this.otherFormData.source.length < this.sourceList.length) {
        this.otherFormData.source = [];
        this.sourceList.map((item) => {
          this.otherFormData.source.push(item.externalSource);
        });
        this.otherFormData.source.unshift("全选");
      } else {
        this.otherFormData.source = [];
      }
    },
    // 获取来源列表
    async getSourceList() {
      try{
        const res = await getFriendList()
        if(res.retCode === 0) {
            this.sourceList = res.data
            this.selectAll()
        } else {
            this.$message.error(res.retMsg)
        }
      }
      catch(error) {
        console.log(error);
      }
    },
    // 排序
    sortSpuTable({ column, property, order }) {
        this.otherFormData.orderBy = order
        this.btnSearchClick()
    },
    // 鼠标悬浮显隐大图
    showBigCard(e) {
      this.showCard = e ? true : false
      this.imgSrc = e || ''
    },
    // 显示图片详情
    showDetail(e,t) {
      this.$refs.previewImg.openDlg(
        e.imageList,
        t,
        e.sourceName
      );
    },
    // 显示待定图片
    showPending() {
      if (this.pendingImgList.length) {
        this.$refs.pendingDialog.openDlg(this.pendingImgList);
      }else{
        this.$message.error('请先标记待定图片')
      }
    },
    // 显示下载记录
    showRecord() {
      this.$refs.record.openDlg()
    },
    // 更新待定图片
    changePendingList(e,t) {
      console.log(e,t);
      if(t) {
        this.pendingImgList.push(e)
      } else {
        this.pendingImgList.map((item, i) => {
          if(item.pictureUrl === e.pictureUrl) {
            this.pendingImgList.splice(i, 1)
          }
        })
      }
    },
    // 更新待定图片列表
    updatePendingImgList(e) {
      console.log(e);
      this.pendingImgList = e
    },
    // 单元格样式 非全等就变红
    cellClassName ({ row, rowIndex, column }){
      if(!row.accurateMatching && rowIndex) {
        if(column.title === '通用名/厂家/品牌/showname') {
          if(row.generalName !== this.searchFormData.generalName.value){
            this.tableData[rowIndex].generalNameCol = 1
          }
          if(row.manufacturer !== this.searchFormData.manufacturer.value){
            this.tableData[rowIndex].manufacturerCol = 1
          }
          if(row.brand !== this.searchFormData.brand.value){
            this.tableData[rowIndex].brandCol = 1
          }
        } else if(column.title === '批准文号') {
          if(row.approvalNo !== this.searchFormData.approvalNo.value) {
            return 'col-red'
          }
        } else if(column.title === '小包装条码') {
          if(row.smallPackageCode !== this.searchFormData.smallPackageCode.value) {
            return 'col-red'
          }
        } else if(column.title === '规格型号') {
          if(row.spec !== this.searchFormData.spec.value) {
            return 'col-red'
          }
        }
      }
    },
    // 行样式 精确查询变蓝
    rowClassName({ row, rowIndex, column, columnIndex }) {
      if(row.accurateMatching) {
        return 'row-blue'
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.page-container {
  /**
   * 查询表单
   */
  .search-form-wrap {
    width: 100%;
    padding: 15px;
    border-bottom: 1px dashed #e4e4eb;

    .el-row {
      flex-wrap: wrap;

      .el-col {
        display: flex;
        justify-content: flex-end;

        .el-form-item {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          width: 100%;
          margin-right: 0;
          margin-bottom: 15px;

          .load-btn{
              background: #3B95A8;
              color: #ffffff;
          }

          /deep/ {
            .el-form-item__label {
              width: 116px;
              line-height: normal;
              padding: 0 12px;
              color: #292933;
              font-weight: normal;
            }

            .el-form-item__content {
              flex: 1;

              .el-range-editor.el-input__inner {
                width: 100%;

                .el-range-separator {
                  width: 14px;
                  padding: 0;
                }
              }
            }
          }

          .el-input {
            width: 100%;
          }

          .el-select {
            width: 100%;
          }
        }
      }
    }
  }

  /**
     * table
     */
  .table-wrap {
    width: 100%;
    // min-height: 440px;
    // height: calc(100vh - 296px);
    height: 700px;
    padding: 0 15px;
    margin-top: 15px;
  }
}
.btn-wrap /deep/ {
  padding: 15px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  .el-button {
    margin-bottom: 15px;
    margin-left: 0;
    margin-right: 15px;
  }
  .vxe-toolbar {
    flex: 1;
    .vxe-button--wrapper {
      display: none;
    }
    .vxe-tools--operate {
      margin-top: 2px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
.big-img {
  pointer-events: none;
  position: absolute;
  right: 20%;
  top:20%;
  z-index: 1002;
  max-width:60%;
  max-height: 60%;
}
/deep/ .vxe-body--row {
  height: 100px;
}
/deep/ .vxe-cell  {
  max-height: 100px!important;
}
</style>
<style lang='scss'>
.vxe-table--tooltip-wrapper {
  z-index: 3000!important;
}
.col-red{
  color:red;
}
.row-blue {
  color: #3B95A8;
  background-color: #EDF5F7;
}
</style>
