<template>
  <div class="component-container" style="width:100%">
    <!-- 资质属性 -->
    <vxe-table
      border
      highlight-hover-row
      auto-resize
      resizable
      align="center"
      :tooltip-config="{enterable: false}"
      :data="tableData"
      ref="table"
    >
      <vxe-table-column type="index" title="序号" width="60" show-header-overflow show-overflow></vxe-table-column>
      <vxe-table-column
        field="mechanism"
        title="所属机构"
        min-width="120"
        show-header-overflow
        show-overflow
      ></vxe-table-column>
      <vxe-table-column
        field="businessCode"
        title="商品编码"
        min-width="120"
        show-header-overflow
        show-overflow
      ></vxe-table-column>
      <vxe-table-column
        field="campDate"
        title="首营时间"
        min-width="120"
        show-header-overflow
        show-overflow
      >
        <template v-slot="{ row }">{{ row.campDate | dateTime }}</template>
      </vxe-table-column>
      <vxe-table-column
        field="approvalName"
        title="批件名称"
        min-width="120"
        show-header-overflow
        show-overflow
      ></vxe-table-column>
      <vxe-table-column
        field="approvalCode"
        title="批件编号"
        min-width="120"
        show-header-overflow
        show-overflow
      ></vxe-table-column>
      <vxe-table-column
        field="approvedContent"
        title="核准内容"
        min-width="120"
        show-header-overflow
        show-overflow
      ></vxe-table-column>
      <vxe-table-column
        field="issueDate"
        title="签发日期"
        min-width="120"
        show-header-overflow
        show-overflow
      >
        <template v-slot="{ row }">{{ row.issueDate | dateTime }}</template>
      </vxe-table-column>
      <vxe-table-column
        field="validityDate"
        title="有效期至"
        min-width="120"
        show-header-overflow
        show-overflow
      >
        <template v-slot="{ row }">{{ row.validityDate | dateTime }}</template>
      </vxe-table-column>
      <vxe-table-column
        field="annexAttr"
        title="附件"
        min-width="120"
        show-header-overflow
        show-overflow
      >
        <template v-slot="{ row }">
          <el-link
            :underline="false"
            type="primary"
            @click="previewImg(row.annexAttr)"
          >预览（{{ row.annexNum }}）</el-link>
        </template>
      </vxe-table-column>
    </vxe-table>

    <image-preview
      v-if="previewDlgVisible"
      :on-close="closeApprovalViewer"
      :url-list="previewImgList"
    ></image-preview>
  </div>
</template>

<script>
import { parseTimestamp } from "@/utils/index.js";
import { getProdQualif } from "@/api/product.js";
import imagePreview from "@/components/common/preview/imagePreview";

export default {
  name: "",
  components: { imagePreview },
  filters: {
    dateTime: function(value) {
      if (!value) return "";
      return parseTimestamp(value);
    }
  },
  props: {},
  data() {
    return {
      tableData: [],
      previewDlgVisible: false,
      previewImgList: []
    };
  },
  computed: {
    // 路由参数
    urlParam() {
      return this.$route.query;
    }
  },
  watch: {},
  created() {
    let { productCode, productType } = this.urlParam;
    getProdQualif({ productCode, productType }).then(res => {
      this.tableData = res;
    });
  },
  mounted() {},
  methods: {
    // 预览图片
    previewImg(list) {
      if (list.length) {
        list.forEach(item => {
          let { fileName, fileUrl } = item;
          this.previewImgList.push({
            mediaName: fileName,
            mediaUrl: fileUrl
          });
        });
        this.previewDlgVisible = true;
      }
    },

    // 关闭批件图片预览
    closeApprovalViewer() {
      this.previewImgList = [];
      this.previewDlgVisible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
</style>
