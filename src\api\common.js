import request from '@/utils/request'
import qs from "qs" // 请求数据 转成form data 形式

// 获取导入记录列表
// modifyType
// 17: 客户商品导入记录
export function getBatchModifyRecordList(data) {
  return request({
    url: '/api/modifyRecord/find/batchModifyRecordList',
    data,
    method: 'POST',
  })
}

// 下载新增副商品模板
export function exportBatchUploadList(data) {
  return request({
      url: '/api/batchUpload/downloadFuProduct/excelDemo',
      data,
      responseType: 'blob',
      method: 'post',
  })
}

/**
 * author:caoshuwen
 * date: 2021-08-28
 * description:获取图片宽高
 * **/
export function getPictureSizeInfo(data) {
  return request({
      url: '/api/productPictureReplace/getPictureSizeInfo',
      method: 'post',
      // data
      params:data
  })
}

/**
 * author:caoshuwen
 * date: 2021-12-15
 * description:图片加白底
 * **/
export function addBackground(data) {
  return request({
      url: '/api/productPictureReplace/addWhiteBackgroudColor',
      method: 'post',
      data
  })
}

/**
 * author:caoshuwen
 * date: 2021-12-15
 * description:保存加白底图片
 * **/
export function saveAddBackground(data) {
  return request({
      url: `/api/productPictureReplace/updatePictureUrl?pictureId=${data.pictureId}&pictureUrl=${data.pictureUrl}`,
      method: 'post'
  })
}