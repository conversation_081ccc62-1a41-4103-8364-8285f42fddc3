import Vue from 'vue'
import VueClipboard from 'vue-clipboard2'
Vue.use(VueClipboard)

// import 'element-ui/lib/theme-chalk/index.css'
// import ElementUI from 'element-ui'

import { _Message } from '@/components/common/resectMessage';

//vxe-table
import 'xe-utils'
import VXETable from 'vxe-table'
import 'normalize.css/normalize.css' // A modern alternative to CSS resets

// 自定义主题色
import '@/styles/custom-element-variables.scss'
import '@/styles/index.scss' // global css

// icon
import '@/icons'


// zTree实现tree结构
import $ from "jquery";
import "ztree";
import "ztree/css/metroStyle/metroStyle.css";

import App from './App'
import store from './store'
import router from './router'
import _ from 'lodash';
import * as filters from './filters'
// permission control
import '@/permission'
/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */
// import { mockXHR } from '../mock'
// if (process.env.NODE_ENV === 'production') {
//   mockXHR()
// }

// set ElementUI lang to EN
// Vue.use(ElementUI)
const echarts = require("echarts")
Vue.prototype.$echarts = echarts
Vue.config.devtools = true;
Vue.use(VXETable)
Object.keys(filters).forEach(key => {
    Vue.filter(key, filters[key])
})
Vue.prototype.$bus = new Vue();
Vue.prototype.$message = _Message;
//自定义拖动
Vue.directive('drag',
    function (el, binding) {
        let oDiv = el;   //当前元素
        oDiv.onmousedown = function (e) {
            e.preventDefault();
            let bw = document.body.clientWidth;
            let bh = document.body.clientHeight;

            //鼠标按下，计算当前元素距离可视区的距离
            let disX = e.clientX;
            let disY = e.clientY;
            let divX = oDiv.style.left?parseInt(oDiv.style.left):0;
            let divY = oDiv.style.top?parseInt(oDiv.style.top):0;
            // 计算两边坐标
            document.onmousemove = function (e) {
                let l = 0, t = 0;
                l = e.clientX - disX + divX;
                t = e.clientY - disY + divY;
                //移动当前元素
                oDiv.style.left = l + 'px';
                oDiv.style.top = t + 'px';
            };
            // 鼠标停止移动时，事件移除
            document.onmouseup = function (e) {
                document.onmousemove = null;
                document.onmouseup = null;
            };
        };
    }
);

new Vue({
    el: '#app',
    router,
    store,
    render: h => h(App)
})
