import Iframe from '@/iframe'
const board = {
  path: '/board',
  name: 'board',
  component: Iframe,
  redirect: 'noRedirect',
  meta: { title: '数据看板', icon: 'nested' },
  children: [
    {
      path: 'updatePhoto',
      name: 'updatePhoto',
      component: () => import('@/views/board/updatePhoto/index'),
      meta: { title: '批量修改图片' }
    },
    {
      path: 'taskBoardList',
      name: 'taskBoardList',
      component: () => import('@/views/board/taskBoard/index'),
      meta: { title: '任务看板' }
    },
    {
      path: 'batchSearchExport',
      name: 'batchSearchExport',
      component: () => import('@/views/board/batchSearchExport/index'),
      meta: { title: '批量查询与导出' }
    },
    {
      path: 'pictureTaskReport',
      name: 'pictureTaskReport',
      component: () => import('@/views/board/pictureTaskBoard/index'),
      meta: { title: '图片任务报表' }
    },
  ]
}

export default board