<template>
  <div class="component-container">
    <div
      class="loading"
      v-loading="productLoading"
      v-show="productLoading"
    ></div>
    <approval-process-new :approvalData="approvalData" :boardData="urlParam"></approval-process-new>
    <div class="approval-process">
    <div class="title">商品信息</div>
     <vxe-table
        border
        highlight-hover-row
        auto-resize
        resizable
        max-height="500"
        align="center"
        :tooltip-config="{ enterable: false }"
        :data="tableData"
        ref="recordTable"
      >
        <vxe-table-column
          field="productId"
          title="标准库ID"
          min-width="80"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="productCode"
          title="商品编码"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="originalProductCode"
          title="原商品编码"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="generalName"
          title="通用名"
          min-width="100"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
         <vxe-table-column
          field="spuCategoryName"
          title="商品大类"
          min-width="100"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
         <vxe-table-column
          field="manufacturerName"
          title="生产厂家"
          min-width="100"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
         <vxe-table-column
          field="spec"
          title="规格型号"
          min-width="100"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
         <vxe-table-column
          field="approvalNo"
          title="批准文号"
          min-width="100"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
         <vxe-table-column
          field="smallPackageCode"
          title="小包装条码"
          min-width="100"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
      </vxe-table>
    <div
      style="height: 20px; background: #f0f2f5;margin-top:10px"
    ></div>
  </div>
  <div class="approval-process">
    <div class="title">处理信息</div>
  </div>
  <div style="width:1000px;margin:0 auto">
    <el-row>
      <el-col>
       <drag-img
    ref="dragImg"
    :productInfo="tableData[0]"
    :imgList="imgList"
    :finishingAudit="true"
  ></drag-img>
    </el-col>
  </el-row>
  <el-form>
    <el-form-item label="是否精修" required="true">
      <el-radio-group
        v-model="form.pictureQualityStatus"
      >
        <el-radio :label="1">需设计精修</el-radio>
        <el-radio :label="2">直接上线</el-radio>>
      </el-radio-group>
    </el-form-item>
  </el-form>
 </div>
  
    <el-row class="btns" :style="{ top: scrollTop < 40 ? '40px' : '0px' }">
      <!-- 一审和二审 -->
      <el-col
        :span="24"
        class="text-rt"
      >
        <el-button type="info" @click="cancel"
          >取消</el-button
        >
        <el-button type="warning" @click="dialogFormVisible2 = true"
          >回退到上一级节点</el-button
        >
        <el-button type="danger" @click="dialogFormVisible = true"
          >驳回</el-button
        >
        <el-button type="success" @click="pass"
          >提交</el-button
        >
      </el-col>
    </el-row>
    <!-- 审核弹框 -->
    <el-dialog
      title="审核"
      width="800px"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisible"
      :close-on-press-escape="false"
      :show-close="false"
      @close="dialogClose"
    >
      <el-form :model="reviewForm" :rules="rules" ref="reviewForm">
        <el-form-item
          label="请选择驳回原因"
          prop="type"
          class="checkbox-wrap"
        >
          <el-checkbox-group
            v-model="reviewForm.type"
            @change="changeReasonType"
          >
            <el-checkbox :label="1">商品图片与资料不符</el-checkbox>
            <el-checkbox :label="2">主图是背面图</el-checkbox>
            <el-checkbox :label="3">图片规格与资料不符</el-checkbox>
            <el-checkbox :label="4">图片品牌与资料不符</el-checkbox>
            <el-checkbox :label="5">图片资料不全</el-checkbox>
            <el-checkbox :label="6">图片不清晰</el-checkbox>
            <el-checkbox :label="0">自定义内容</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item
          label="自定义内容"
          prop="value"
        >
          <el-input
            :disabled="!rules.value[0].required"
            type="textarea"
            placeholder="审核意见"
            v-model="reviewForm.value"
            autocomplete="off"
            maxlength="100"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="goSubmit()">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 回退弹框 -->
    <el-dialog
      title="回退到上一级节点"
      width="500px"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisible2"
      :close-on-press-escape="false"
      :show-close="false"
      @close="dialogClose"
    >
      <el-form :model="reviewForm2" :rules="rules2" ref="reviewForm2">
        <el-form-item
          label="审核意见"
          prop="value"
        >
          <el-input
            type="textarea"
            placeholder="审核意见"
            v-model="reviewForm2.value"
            autocomplete="off"
            maxlength="100"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible2 = false">取 消</el-button>
        <el-button type="primary" @click="backToLast">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// 商品基础共用信息混入对象
import { productMixinBase } from "@/mixin/productMixinBase.js";
import dragImg from "@/views/productPicture/selfSupport/dragImg";
import { getAddProcessData, replacePass, replaceReject, finishingDetail, auditRejectPrev } from "@/api/workManage"

export default {
  name: "",
  mixins: [productMixinBase],
  components: { dragImg },
  computed: {
    // 路由参数
    urlParam() {
      return this.$route.query;
    },
    spuCategory() {
      return this.$store.state.product.spuCategory;
    },
  },
  data() {
    return {
      form:{
        pictureQualityStatus: 1
      },
      imgList:[],
      tableData:[],
      productLoading:false,
      scrollTop: 0,
      dialogFormVisible: false,
      dialogFormVisible2: false,
      reviewForm: {
        value: "",
        type: []
      },
      reviewForm2: {
        value: "",
      },
      rules: {
        value: [
          {
            required: false,
            message: "审核意见不能为空",
            trigger: "blur",
          },
        ],
      },
      rules2: {
        value: [
          {
            required: true,
            message: "审核意见不能为空",
            trigger: "blur",
          },
        ],
      }
    };
  },
  created() {
    console.log(this.urlParam);
    this.init();
    window.onscroll = () => {
      this.scrollTop =
        document.documentElement.scrollTop || document.body.scrollTop;
    };
  },
  methods: {
    changeReasonType(val) {
      if (val.indexOf(0) != -1) {
        this.rules.value[0].required = true;
      } else {
        this.reviewForm.value = ''
        this.rules.value[0].required = false;
      }
    },
    close() {
      let tab = 'second'
      parent.CreateTab(
        `../static/dist/index.html#/workManage/receiveDeal?tab=${tab}`,
        "领取与处理",
        true
      );
      this.cancel()
    },
    // 回退到上一级节点
    backToLast() {
      this.$refs['reviewForm2'].validate(async(valid) => {
        if (valid) {
          let param = {
            applyCode:this.urlParam.applyCode,
            comment:this.reviewForm2.value,
            procInstId:this.urlParam.procInstId,
            procKey:this.urlParam.procKey,
            taskId:this.urlParam.taskId
          }
          console.log(param)
          const res = await auditRejectPrev(param)
          console.log(res);
          if(res.retCode === 0) {
            this.close()
          } else {
            this.$message.error(res.retMsg)
          }
        }
      });
    },
    goSubmit() {
      this.$refs['reviewForm'].validate(async(valid) => {
        if (valid) {
          // console.log(this.urlParam);
          let list = ['', '商品图片与资料不符', '主图是背面图', '图片规格与资料不符', '图片品牌与资料不符', '图片资料不全', '图片不清晰']
          let comment = []
          this.reviewForm.type.map(item => {
            if(!item) {
              comment.push(this.reviewForm.value)
            } else {
              comment.push(list[item])
            }
          })
          let param = {
            taskId:this.urlParam.taskId,
            comment:comment.join(),
            procInstId:this.urlParam.procInstId,
            procKey:this.urlParam.procKey,
            applyCode:this.urlParam.applyCode,
            productCode:this.urlParam.productCode,
            auditOpinion:this.reviewForm.type.join(),
            auditOpinionCustom:this.rules.value[0].required?this.reviewForm.value:'',
            taskNode:this.urlParam.taskNode
          }
          console.log(param);
          let res = await replaceReject(param)
          console.log(res);
          if(res.retCode === 0) {
            this.close()
          } else {
            this.$message.error(res.retMsg)
          }
        }
      });
    },
    // 审核通过
    async pass() {
      let isToFinishing = false
      let obj = this.$refs.dragImg.getSubmitData()
      obj.imgList.map(item => {
        if(item.isToFinishing && item.deleteStatus != 1) {
          isToFinishing = true
        }
      })
      let param = {
        applyCode:this.urlParam.applyCode,
        procInstId:this.urlParam.procInstId,
        procKey:this.urlParam.procKey,
        taskId:this.urlParam.taskId,
        taskNode:this.urlParam.taskNode,
        productCode:this.urlParam.productCode,
        pictureQualityStatus: this.form.pictureQualityStatus,
        productPictureList:obj.imgList
      }
      console.log(param)
      if (!obj.isFlag) {
        return
      }
      if (this.form.pictureQualityStatus === 1 && !isToFinishing) {
        // 选中需设计精修 图片列表必须有转精修图片
        this.$message.error('请选择需要转精修的图片')
        return
      } else if (this.form.pictureQualityStatus === 2 && isToFinishing) {
        // 选中直接上线 图片列表不能有转精修图片
        this.$message.error('有标记的待精修图片，请保持图片标记与是否修图选择一致')
        return
      }
      const res = await replacePass(param)
      console.log(res);
      if(res.retCode === 0) {
        this.close()
      } else {
        this.$message.error(res.retMsg)
      }
    },
    cancel() {
      parent.CloseTab(
        "../static/dist/index.html#/workManage/finishingAudit"
      );
    },
    async init() {
      // 获取审批流信息
      await this.getApplyInfo();
      // 获取审核信息数据
      await this.getProductInfo();
    },
    // 获取商品数据
    async getProductInfo() {
      try {
        let { data } = await finishingDetail({applyCode: this.urlParam.applyCode})
        this.tableData = [data]
        this.imgList = data.productPictureList
      }
      catch(error) {
          console.log(error);
      }
    },
    dialogClose() {
      this.$refs.reviewForm.resetFields();
    },
    async getApplyInfo() {
      let param = {
        procDefId: this.urlParam.procDefId,
        procInstId: this.urlParam.procInstId
      };
      let res = await getAddProcessData(param);
      console.log(res);
      if (res.success) {
        this.approvalData = res.data;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.component-container {
  padding-top: 0px;
  // padding-bottom: 70px;
  .loading {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
  }
  /deep/.el-tabs__header {
    border-bottom: none;
    background-color: #f2f2f2;
  }
  /deep/ .el-tabs__content {
    padding-top: 72px;
  }
  .el-tabs--border-card {
    border: none;
    box-shadow: none;
  }
  .btns {
    background: #fff;
    width: 100%;
    position: fixed;
    top: 40px;
    padding: 15px;
    z-index: 1500;
  }
}
.approval-process {
  .title {
    font-weight: 600;
    margin-bottom: 22px;
    border-bottom: 1px solid #e4e4eb;
    line-height: 50px;
    padding-left: 20px;
  }
}
</style>
