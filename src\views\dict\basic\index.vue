
<template>
  <div class="task-to-be-film-container">
    <!-- 查询表单 -->
    <div class="search-form-wrap">
      <el-form :model="model" ref="refSearchForm" label-width="96x">
        <el-row type="flex" :gutter="20">
          <!-- 字典类型 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="字典类型">
              <el-select v-model="formData.type" placeholder="请选择" clearable>
                <el-option label="全部" value="all"></el-option>
                <el-option
                  v-for="item in dictTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 名称 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="名称">
              <el-input
                v-model="formData.dictName"
                placeholder="id/名称/助记码/关键词"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- 是否停用 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="是否停用">
              <el-select v-model="formData.isValid" placeholder="请选择" clearable>
                <el-option label="全部" value></el-option>
                <el-option label="启用" value="1"></el-option>
                <el-option label="停用" value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 按钮 -->
          <el-col :span="6" :offset="18">
            <el-button type="primary" size="medium" @click="btnSearchClick"
              >查询</el-button
            >
            <el-button size="medium" @click="btnResetClick">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="btn-wrap">
      <el-button
        v-if="hasPermission('字典管理-新增')"
        type="primary"
        size="medium"
        @click="addDict"
        >新增</el-button
      >
      <el-button
        size="medium"
        v-if="hasPermission('字典管理-导出')"
        @click="handleExport"
        >导出EXCEL</el-button
      >
    </div>
    <!-- table -->
    <div class="table-wrap">
      <vxe-table
        border
        highlight-hover-row
        resizable
        height="100%"
        auto-resize
        size="small"
        align="center"
        :tooltip-config="{ enterable: false }"
        :loading="tableLoading"
        :data="tableData"
        :seq-config="{ startIndex: (pageNum - 1) * pageSize }"
        ref="refVxeTable"
      >
        <vxe-table-column
          type="seq"
          title="序号"
          width="60"
          show-header-overflow
          show-overflow
          fixed="left"
        ></vxe-table-column>
        <vxe-table-column
          field="typeName"
          title="字典类型"
          width="120"
          show-header-overflow
          show-overflow
          sortable
        ></vxe-table-column>
        <vxe-table-column
          field="id"
          title="id"
          width="120"
          show-header-overflow
          show-overflow
          sortable
        >
        </vxe-table-column>

        <vxe-table-column
          field="dictName"
          title="名称"
          width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="mnemonicCode"
          title="助记码"
          width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="isValid"
          title="是否停用"
          width="120"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">
            <span>{{ row.isValid == 1 ? "启用" : "停用" }}</span>
          </template></vxe-table-column
        >
        <vxe-table-column
          field="productCount"
          title="商品数量"
          width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="keywords"
          title="关键词"
          min-width="120"
          show-header-overflow
          show-overflow
          sortable
        >
        </vxe-table-column>
        <vxe-table-column
          field="createUser"
          title="创建人"
          min-width="120"
          show-header-overflow
          show-overflow
          sortable
        >
        </vxe-table-column>
        <vxe-table-column
          field="createTime"
          title="创建时间"
          min-width="120"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">
            {{ parseTime(row.createTime) }}
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="updateUser"
          title="最后修改人"
          min-width="120"
          show-header-overflow
          show-overflow
          sortable
        >
        </vxe-table-column>
        <vxe-table-column
          field="updateTime"
          title="最后修改时间"
          min-width="120"
          show-header-overflow
          show-overflow
          ><template v-slot="{ row }">
            {{ parseTime(row.updateTime) }}
          </template></vxe-table-column
        >
        <vxe-table-column
          title="操作"
          width="150"
          show-header-overflow
          show-overflow
          fixed="right"
        >
          <template v-slot="{ row }">
            <span class="btn" v-if="hasPermission('字典管理-编辑')">
              <el-link :underline="false" type="primary" @click.stop="edit(row)"
                >修改</el-link
              >
            </span>
            <span class="btn">
              <el-link
                :underline="false"
                type="primary"
                @click.stop="handleRecord(row.id)"
                >修改记录</el-link
              >
            </span>
          </template>
        </vxe-table-column>
      </vxe-table>
    </div>
    <!-- 分页 -->
    <el-row class="custom-pagination-wrap">
      <el-col>
        <el-pagination
          background
          :current-page.sync="pageNum"
          :page-sizes="[20, 50, 100, 200]"
          :page-size.sync="pageSize"
          :total="total"
          layout="prev, pager, next, jumper,total, sizes"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </el-col>
    </el-row>
    <edit-record ref="editRecord"></edit-record>
    <add-dict ref="addDict" @refresh="refresh"></add-dict>
  </div>
</template>

<script>
import { parseTimestamp, hasPermission } from "@/utils/index.js";
import { dictTypeList } from "@/utils/config.js";
import { findDictList, dictExport } from "@/api/dict.js";
import editRecord from "./components/editRecord.vue";
import addDict from "./components/addDict.vue";
export default {
  name: "dictList",
  filters: {},
  props: {},
  components: { editRecord, addDict },
  data() {
    return {
      pageNum: 1,
      pageSize: 20,
      total: 0, //总条数
      formData: {
        type: "all",
        dictName: "",
        isValid: "",
      },
      tableLoading: false,
      tableData: [],
      dictTypeList: dictTypeList,
    };
  },
  computed: {},
  watch: {},
  created() {
    this.searchForm();
  },
  mounted() {},
  methods: {
    /**
     * 查询table数据
     */
    async searchForm() {
      try {
        this.tableLoading = true;
        let param = Object.assign({}, this.formData);
        param.page = this.pageNum;
        param.limit = this.pageSize;
        let res = await findDictList(param);
        this.tableLoading = false;
        if (!res.retCode) {
          this.tableData = res.data.list;
          this.total = res.data.total;
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      } catch (error) {
        console.error(error);
      }
    },
    refresh(num) {
      if (num) {
        this.pageNum = num;
        this.formData = {
          type: "all",
          dictName: "",
          isValid: "",
        };
      }
      this.searchForm();
    },
    /**
     * 查询按钮点击
     */
    btnSearchClick() {
      this.pageNum = 1;
      this.searchForm();
    },

    /**
     * 重置
     */
    btnResetClick() {
      this.formData = {
        type: "all",
        dictName: "",
        isValid: "",
      };
      this.pageNum = 1;
      this.searchForm();
    },
    // 查看修改记录
    handleRecord(id) {
      this.$refs.editRecord.open(id);
    },
    // 导出
    handleExport() {
      dictExport(this.formData).then((res) => {
        if (res.data && res.data.retCode) {
          //失败
          this.$message({
            showClose: true,
            type: "error",
            message: res.data.retMsg,
          });
        } else {
          this.$message.success(res.retMsg);
        }
      });
    },
    /**
     * pageSize 改变事件
     */
    handleSizeChange(pageSize) {
      this.pageNum = 1;
      this.pageSize = pageSize;
      this.searchForm();
    },

    /**
     * pageNum 改变事件
     */
    handleCurrentChange(currentPage) {
      this.pageNum = currentPage;
      this.searchForm();
    },
    /**
     * @description: 格式化时间参数
     * @param {date} time 时间戳
     * @return: string 格式化后的时间戳
     */
    parseTime(time) {
      return parseTimestamp(time);
    },
    hasPermission(str) {
      return hasPermission(str);
    },
    // 新增
    addDict() {
      this.$refs.addDict.open({
        title: "新增字典",
      });
    },
    // 修改
    edit(row) {
      this.$refs.addDict.open({
        title: "编辑字典",
        row,
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.task-to-be-film-container {
  /**
   * 查询表单
   */
  .search-form-wrap {
    width: 100%;
    padding: 15px;
    border-bottom: 1px dashed #e4e4eb;

    .el-row {
      flex-wrap: wrap;

      .el-col {
        display: flex;
        justify-content: flex-end;
        .el-form-item {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          width: 100%;
          margin-right: 0;
          margin-bottom: 15px;

          /deep/ {
            .el-form-item__label {
              padding: 0 12px;
              color: #292933;
              font-weight: normal;
            }

            .el-form-item__content {
              flex: 1;
              margin-left: 0 !important;
              .el-range-editor.el-input__inner {
                width: 100%;

                .el-range-separator {
                  width: 14px;
                  padding: 0;
                }
              }
            }
          }
          .el-input {
            width: 100%;
          }

          .el-select {
            width: 100%;
          }
          .el-cascader {
            width: 100%;
          }
        }
      }
    }
  }
  /**
     * 按钮组
     */
  .btn-group-wrap {
    font-size: 0;
    padding: 15px;
    .el-col {
      .el-button,
      .vxe-column-filter-container {
        margin-right: 10px;
      }
    }
  }
  /**
     * table
     */
  .table-wrap {
    width: 100%;
    min-height: 440px;
    height: calc(100vh - 346px);
    padding: 0 15px;
  }
}
.btn {
  padding: 0 10px;
}
.btn-wrap /deep/ {
  padding: 15px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  .el-button {
    margin-bottom: 15px;
    margin-left: 0;
    margin-right: 15px;
  }
  .vxe-toolbar {
    flex: 1;
    .vxe-button--wrapper {
      display: none;
    }
    .vxe-tools--operate {
      margin-top: 2px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
.tree-wrap /deep/ {
  padding-bottom: 20px;
  ul {
    li {
      line-height: 32px;
      span {
        font-size: 14px !important;
      }
      a {
        height: 32px;
        span {
          display: inline-block;
          vertical-align: middle !important;
          font-size: 14px !important;
          &.node_name {
            line-height: 32px;
          }
        }
      }
    }
  }
}
.tree-btn {
  border-bottom: 1px solid #dbdbdb;
  padding-bottom: 10px;
  span {
    color: #f56c6c;
  }
}
</style>