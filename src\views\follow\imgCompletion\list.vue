
<template>
  <div class="task-to-be-film-container" v-loading="loading">
    <!-- 查询表单 -->
    <div class="search-form-wrap">
      <el-form :model="model" ref="refSearchForm" v-if="tabName == 'third'">
        <el-row type="flex">
          <!-- 导入时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="导入时间" prop="createTime">
              <el-date-picker
                v-model="formData1.createTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                clearable
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <!-- 导入人 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="导入人" prop="createUser">
              <el-input
                v-model="formData1.createUser"
                placeholder="请输入导入人"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- 申请人 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="申请人" prop="applyUser">
              <el-input
                v-model="formData1.applyUser"
                placeholder="请输入申请人"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 按钮 -->
          <el-col :span="6" :offset="18">
            <el-button
              type="primary"
              size="medium"
              :disabled="tabName == 'second'"
              @click="
                () => {
                  isUnfold = !isUnfold;
                }
              "
              >{{ isUnfold ? "收起" : "展开" }}</el-button
            >
            <el-button type="primary" size="medium" @click="btnSearchClick"
              >查询</el-button
            >
            <el-button
              size="medium"
              :disabled="tabName == 'second'"
              @click="btnResetClick"
              >重置</el-button
            >
          </el-col>
        </el-row>
      </el-form>
      <el-form :model="model" ref="refSearchForm" v-else-if="tabName == 'four'">
        <el-row type="flex">
          <!-- 创建时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="创建时间" prop="createTime">
              <el-date-picker
                v-model="formData4.createTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                clearable
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <!-- 商品ID -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="商品ID" prop="productId">
              <el-input
                v-model="formData4.productId"
                placeholder="请输入商品ID"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 按钮 -->
          <el-col :span="6" :offset="18">
            <el-button type="primary" size="medium" @click="btnSearchClick"
              >查询</el-button
            >
            <el-button v-if="tabName == 'four'" type="primary"  @click="exportExcel"
              >导出Excel</el-button>
            <el-button
              size="medium"
              @click="btnResetClick"
              >重置</el-button
            >
          </el-col>
        </el-row>
      </el-form>
      <el-form :model="model" ref="refSearchForm" v-else>
        <el-row type="flex">
          <!-- 导入时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="导入时间" prop="createTime">
              <el-date-picker
                v-model="formData.createTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                clearable
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <!-- 商品所属机构 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="商品所属机构" prop="mechanism">
              <el-select
              multiple
                collapse-tags
                v-model="formData.mechanism"
                placeholder="请选择"
                @remove-tag="removeTag1"
                @change="changeSelect1"
                clearable
              >

              <el-option
                  label="全选"
                  value="全选"
                  @click.native="selectAll1"
                ></el-option>

                <el-option
                  v-for="item in mechanismOptions"
                  :key="'key_mechanismOptions_' + item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 申请人 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="申请人" prop="applyer">
              <el-input
                v-model="formData.applyer"
                placeholder="请输入申请人"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 商品大类 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="商品大类">
              <el-select
                v-model="formData.spuCategory"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" value></el-option>
                <el-option
                  v-for="item in spuCategoryList"
                  :key="item.id"
                  :label="item.dictName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 来源 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="来源">
              <el-select
                v-model="formData.source"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" value=""></el-option>
                <el-option label="无" value="0"></el-option>
                <!-- <el-option label="百草" :value="1"></el-option> -->
                <el-option label="系统上传" :value="2"></el-option>
                <el-option label="荷叶健康" :value="3"></el-option>
                <!-- <el-option label="智慧脸商城" :value="4"></el-option> -->
                <!-- <el-option label="POP" :value="5"></el-option>
                <el-option label="EC" :value="6"></el-option>
                <el-option label="客服" :value="7"></el-option>
                <el-option label="供多多" :value="8"></el-option>
                <el-option label="智鹿" :value="9"></el-option>
                <el-option label="神农" :value="10"></el-option> -->
                <el-option label="爬虫" :value="20"></el-option>
                <el-option label="新品上报" :value="21"></el-option>
                <el-option label="友商库-自动创建" :value="22"></el-option>
                <el-option label="友商库-运营创建" :value="23"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 商品编码 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="商品编码">
              <el-input
                v-model="formData.mixtureProduct"
                placeholder="精确查询"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 通用名-->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="通用名">
              <el-input
                v-model="formData.generalName"
                placeholder="模糊查询"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 审核状态-->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="审核状态">
              <el-select
                v-model="formData.auditStatus"
                placeholder="请选择"
                clearable
              >
                <el-option label="未审核" :value="0"></el-option>
                <el-option label="通过" :value="1"></el-option>
                <el-option label="驳回" :value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 初审审核人-->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="初审审核人">
              <el-input
                v-model="formData.reviewUser"
                placeholder="请输入初审审核人"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 审核时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="审核时间" prop="createTime">
              <el-date-picker
                v-model="formData.auditTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                clearable
              ></el-date-picker>
            </el-form-item>
          </el-col>

          <!-- 操作状态-->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="操作状态" prop="">
              <el-select
                v-model="formData.operationStatus"
                placeholder="请选择"
                clearable
              >
                <el-option label="待拍摄" :value="1"></el-option>
                <el-option label="已拍摄" :value="2"></el-option>
                <el-option label="无" :value="3"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 是否完成-->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="isUnfold">
            <el-form-item label="是否完成" prop="isDone">
              <el-select
                v-model="formData.isDone"
                placeholder="请选择"
                clearable
              >
                <el-option label="是" :value="1"></el-option>
                <el-option label="否" :value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 是否有图-->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="isUnfold">
            <el-form-item label="是否有图" prop="isPicture">
              <el-select
                v-model="formData.isPicture"
                placeholder="请选择"
                clearable
              >
                <el-option label="是" :value="1"></el-option>
                <el-option label="否" :value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 驳回原因-->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="isUnfold && tabName == 'first'">
            <el-form-item label="驳回原因" prop="auditOpinions">
              <el-select
                multiple
                collapse-tags
                v-model="formData.auditOpinions"
                placeholder="请选择"
                clearable
                @change="changeAuditOpinion"
              >
                <el-option label="商品图片与资料不符" :value="1"></el-option>
                <el-option label="主图是背面图" :value="2"></el-option>
                <el-option label="图片规格与资料不符" :value="3"></el-option>
                <el-option label="图片品牌与资料不符" :value="4"></el-option>
                <el-option label="图片资料不全" :value="5"></el-option>
                <el-option label="图片不清晰" :value="6"></el-option>
                <el-option label="自定义内容" :value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 自定义原因 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="isUnfold && tabName == 'first'">
            <el-form-item label="自定义原因" prop="auditOpinionCustom">
              <el-input
                :disabled = 'banAuditOpinionCustom'
                v-model="formData.auditOpinionCustom"
                placeholder="请输入自定义原因"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 按钮 -->
          <el-col :span="6" :offset="18">
            <el-button
              type="primary"
              size="medium"
              :disabled="tabName == 'second'"
              @click="
                () => {
                  isUnfold = !isUnfold;
                }
              "
              >{{ isUnfold ? "收起" : "展开" }}</el-button
            >
            <el-button type="primary" size="medium" @click="clickExport" v-if="tabName === 'first'"
              >导出excel</el-button
            >
            <el-button type="primary" size="medium" @click="btnSearchClick"
              >查询</el-button
            >
            <el-button
              size="medium"
              :disabled="tabName == 'second'"
              @click="btnResetClick"
              >重置</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="btn-wrap">
      <el-button type="primary" size="medium" @click="handleApply"
        >批量导入商品图片补全申请</el-button
      >
      <el-button type="primary" size="medium" @click="handleCaptureApply"
        >批量商品抓取图片申请</el-button
      >
    </div>
    <!-- table表单 -->
    <!-- table -->
    <div class="tab-wrap">
      <el-tabs v-model="tabName" type="card">
        <el-tab-pane label="商品图片补全列表" name="first">
          <div class="table-wrap">
            <vxe-table
              border
              highlight-hover-row
              resizable
              height="100%"
              auto-resize
              size="small"
              align="center"
              :tooltip-config="{ enterable: false }"
              :loading="tableLoading"
              :data="tableData"
              :seq-config="{ startIndex: (pageNum - 1) * pageSize }"
              ref="refVxeTable"
            >
              <vxe-table-column
                type="seq"
                title="序号"
                width="60"
                show-header-overflow
                show-overflow
                fixed="left"
              ></vxe-table-column>
              <vxe-table-column
                field="applyDate"
                title="导入时间"
                width="120"
                show-header-overflow
                show-overflow
              >
                <template v-slot="{ row }">
                  {{ row.applyDate | parseTime }}
                </template>
              </vxe-table-column>
              <vxe-table-column
                field="mechanismName"
                title="商品所属机构"
                width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                field="applyUser"
                title="申请人"
                min-width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                field="spuCategory"
                title="商品大类"
                width="120"
                show-header-overflow
                show-overflow
              >
                <template v-slot="{ row }">
                  {{ row.spuCategory | filterSpuCategory }}
                </template>
              </vxe-table-column>
              <vxe-table-column
                field="source"
                title="来源"
                min-width="120"
                show-header-overflow
                show-overflow
              >
                <template v-slot="{ row }">
                  {{ row.source | filterSource }}
                </template>
              </vxe-table-column>
              <vxe-table-column
                field="skuPrimaryType"
                title="商品类型"
                width="120"
                show-header-overflow
                show-overflow
                ><template v-slot="{ row }">
                  {{ ["副商品", "主商品"][row.skuPrimaryType] }}
                </template>
              </vxe-table-column>
              <vxe-table-column
                field="productCode"
                title="商品编码"
                min-width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                field="skuCode"
                title="sku编码"
                width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                field="originalProductCode"
                title="原商品编码"
                width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                field="productId"
                title="商品ID"
                min-width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                field="generalName"
                title="通用名"
                width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                field="productName"
                title="商品名"
                min-width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                field="auditStatus"
                title="审核状态"
                min-width="120"
                show-header-overflow
                show-overflow
              >
                <template v-slot="{ row }">
                  {{ ["未审核", "通过", "驳回"][row.auditStatus] }}
                </template>
              </vxe-table-column>
              <vxe-table-column
                field="reviewUser"
                title="初审审核人"
                min-width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                field="reviewDate"
                title="审核时间"
                width="120"
                show-header-overflow
                show-overflow
              >
                <template v-slot="{ row }">
                  {{ row.reviewDate | parseTime }}
                </template>
              </vxe-table-column>
              <vxe-table-column
                field="auditOpinionStr"
                title="驳回理由"
                min-width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                field="operationStatus"
                title="操作状态"
                width="120"
                show-header-overflow
                show-overflow
              >
                <template v-slot="{ row }">
                  {{ ["-", "待拍摄", "已拍摄", "无"][row.operationStatus] }}
                </template>
              </vxe-table-column>
              <vxe-table-column
                field="isDone"
                title="是否完成"
                min-width="120"
                show-header-overflow
                show-overflow
              >
                <template v-slot="{ row }">
                  {{ ["否", "是"][row.isDone] }}
                </template>
              </vxe-table-column>
              <vxe-table-column
                field="containPictureStatus"
                title="是否有图"
                min-width="120"
                show-header-overflow
                show-overflow
              >
                <template v-slot="{ row }">
                  {{ ["否", "是"][row.containPictureStatus] }}
                </template>
              </vxe-table-column>
            </vxe-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="批量导入商品图片补全记录" name="second">
          <div class="table-wrap">
            <vxe-table
              border
              highlight-hover-row
              resizable
              height="100%"
              auto-resize
              size="small"
              align="center"
              :tooltip-config="{ enterable: false }"
              :loading="tableLoading"
              :data="tableData"
              :seq-config="{ startIndex: (pageNum - 1) * pageSize }"
              ref="refVxeTable"
            >
              <vxe-table-column
                type="seq"
                title="序号"
                width="60"
                show-header-overflow
                show-overflow
                fixed="left"
              ></vxe-table-column>
              <vxe-table-column
                field="applyDate"
                title="申请时间"
                min-width="150"
                show-header-overflow
                show-overflow
              >
                <template v-slot="{ row }">
                  {{ row.applyDate | parseTime }}
                </template>
              </vxe-table-column>
              <vxe-table-column
                field="applyer"
                title="申请人"
                min-width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                field="applyCode"
                title="单据编号"
                min-width="150"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                field="applyTotalNum"
                title="提交商品数量"
                min-width="120"
                show-header-overflow
                show-overflow
              >
                <template v-slot="{ row }">
                  <a
                    :class="row.applyTotalNum != 0 ? 'active' : ''"
                    :href="row.originalFileUrl"
                    :download="row.originalFileName"
                    >{{ row.applyTotalNum }}</a
                  >
                </template>
              </vxe-table-column>
              <vxe-table-column
                field="applyAbnormalNum"
                title="异常商品数量"
                min-width="120"
                show-header-overflow
                show-overflow
              >
                <template v-slot="{ row }">
                  <a
                    :class="row.applyAbnormalNum != 0 ? 'active' : ''"
                    :href="row.abnormalFileUrl"
                    :download="row.abnormalFileName"
                    >{{ row.applyAbnormalNum }}</a
                  >
                </template>
              </vxe-table-column>
              <vxe-table-column
                field="analysisStatus"
                title="解析状态"
                min-width="120"
                show-header-overflow
                show-overflow
              >
                <template v-slot="{ row }">
                  {{ ["失败", "成功", "解析中"][row.analysisStatus] }}
                </template>
              </vxe-table-column>
              <vxe-table-column
                field="remark"
                title="备注"
                min-width="200"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                field="analysisEndDate"
                title="解析完成时间"
                min-width="150"
                show-header-overflow
                show-overflow
              >
                <template v-slot="{ row }">
                  {{ row.analysisEndDate | parseTime }}
                </template>
              </vxe-table-column>
            </vxe-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="批量抓取图片申请记录" name="third">
          <div class="table-wrap">
            <vxe-table
              border
              highlight-hover-row
              resizable
              height="100%"
              auto-resize
              size="small"
              align="center"
              :tooltip-config="{ enterable: false }"
              :loading="tableLoading"
              :data="tableData"
              :seq-config="{ startIndex: (pageNum - 1) * pageSize }"
              ref="refVxeTable"
            >
              <vxe-table-column
                type="seq"
                title="序号"
                width="60"
                show-header-overflow
                show-overflow
                fixed="left"
              ></vxe-table-column>
              <vxe-table-column
                field="applyTime"
                title="导入时间"
                min-width="150"
                show-header-overflow
                show-overflow
              >
                <template v-slot="{ row }">
                  {{ row.applyTime | parseTime }}
                </template>
              </vxe-table-column>
              <vxe-table-column
                field="createUser"
                title="导入人"
                min-width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                field="mechanismName"
                title="机构"
                min-width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                field="applyUser"
                title="申请人"
                min-width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                field="taskNum"
                title="上传成功任务数量"
                min-width="150"
                show-header-overflow
                show-overflow
              >
                <template v-slot="{ row }">
                  <a
                    :class="row.taskNum != 0 ? 'active' : ''"
                    :href="row.taskUrl"
                    :download="row.taskName"
                    >{{ row.taskNum }}</a
                  >
                </template>
              </vxe-table-column>
              <vxe-table-column
                field="errorNum"
                title="上传失败任务数量"
                min-width="150"
                show-header-overflow
                show-overflow
              >
                <template v-slot="{ row }">
                  <a
                    :class="row.errorNum != 0 ? 'active' : ''"
                    :href="row.errorUrl"
                    :download="row.errorName"
                    >{{ row.errorNum }}</a
                  >
                </template>
              </vxe-table-column>
              <vxe-table-column
                field="detailVersionNum"
                title="中台有精修版本的任务数量"
                min-width="150"
                show-header-overflow
                show-overflow
              >
                <template v-slot="{ row }">
                  <a
                    :class="row.detailVersionNum != 0 ? 'active' : ''"
                    :href="row.detailVersionUrl"
                    :download="row.detailVersionName"
                    >{{ row.detailVersionNum }}</a
                  >
                </template>
              </vxe-table-column>
              <vxe-table-column
                field="progressTaskNum"
                title="有进行中的任务数量"
                min-width="150"
                show-header-overflow
                show-overflow
              >
                <template v-slot="{ row }">
                  <a
                    :class="row.progressTaskNum != 0 ? 'active' : ''"
                    :href="row.progressTaskUrl"
                    :download="row.progressTaskName"
                    >{{ row.progressTaskNum }}</a
                  >
                </template>
              </vxe-table-column>
              <vxe-table-column
                field="applyNum"
                title="申请的商品数量"
                min-width="150"
                show-header-overflow
                show-overflow
              >
                <template v-slot="{ row }">
                  <a
                    :class="row.applyNum != 0 ? 'active' : ''"
                    :href="row.applyUrl"
                    :download="row.applyName"
                    >{{ row.applyNum }}</a
                  >
                </template>
              </vxe-table-column>
              <vxe-table-column
                field="doneNum"
                title="爬虫返回的商品数量"
                min-width="150"
                show-header-overflow
                show-overflow
              >
                <template v-slot="{ row }">
                  <a
                    :class="row.doneNum != 0 ? 'active' : ''"
                    :href="row.doneUrl"
                    :download="row.doneName"
                    >{{ row.doneNum }}</a
                  >
                </template>
              </vxe-table-column>
              <vxe-table-column
                field="friendLibraryDoneNum"
                title="友商库返回的商品数量"
                min-width="150"
                show-header-overflow
                show-overflow
              >
                <template v-slot="{ row }">
                  <a
                    :class="row.friendLibraryDoneNum != 0 ? 'active' : ''"
                    :href="row.friendLibraryDoneUrl"
                    :download="row.friendLibraryDoneName"
                    >{{ row.friendLibraryDoneNum }}</a
                  >
                </template>
              </vxe-table-column>
              <vxe-table-column
                field="analysisStatus"
                title="解析状态"
                min-width="120"
                show-header-overflow
                show-overflow
              >
                <template v-slot="{ row }">
                  {{ ["解析失败", "解析成功", "解析中", "已提交到爬虫", "爬虫接收数据异常"][row.analysisStatus] }}
                </template>
              </vxe-table-column>
              <vxe-table-column
                field="remark"
                title="备注"
                min-width="200"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column title="操作" width="150" fixed="right">
                <template v-slot="{ row }">
                  <span class="btn" v-if="row.isSubmit == 0">
                    <el-link
                      :underline="false"
                      type="primary"
                      :disabled="row.isRemoveTask"
                      @click.stop="submit(row.applyCode, 1)"
                      >标记增图后提交</el-link
                    >
                    <el-link
                      :underline="false"
                      :disabled="row.isRemoveTask"
                      type="primary"
                      @click.stop="submit(row.applyCode, 2)"
                      >直接提交</el-link
                    >
                  </span>
                  <span class="btn" v-else>
                    <el-link
                      :underline="false"
                      disabled
                      type="primary"
                      >——</el-link
                    >
                  </span>
                </template>
              </vxe-table-column>
            </vxe-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="友商库商品图片补全申请记录" name="four">
          <div class="table-wrap">
            <vxe-table
              border
              highlight-hover-row
              resizable
              height="100%"
              auto-resize
              size="small"
              align="center"
              :tooltip-config="{ enterable: false }"
              :loading="tableLoading"
              :data="tableData"
              :seq-config="{ startIndex: (pageNum - 1) * pageSize }"
              ref="refVxeTable"
            >
              <vxe-table-column
                type="seq"
                title="序号"
                width="60"
                show-header-overflow
                show-overflow
                fixed="left"
              ></vxe-table-column>

              <vxe-table-column
                field="applyCode"
                title="单据编号"
                min-width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
               
              <vxe-table-column
                field="createUser"
                title="申请人"
                min-width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              
              <vxe-table-column
                field="productId"
                title="商品ID"
                min-width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>

              <vxe-table-column
                field="state"
                title="状态"
                min-width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
               
              <vxe-table-column
                field="msg"
                title="备注"
                min-width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>

              <vxe-table-column
                field="createTime"
                title="创建时间"
                width="160"
                show-header-overflow
                show-overflow
              >
                <template v-slot="{ row }">
                  {{ row.createTime | parseTime }}
                </template>
              </vxe-table-column>
               
            </vxe-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 分页 -->
    <el-row class="custom-pagination-wrap">
      <el-col>
        <el-pagination
          background
          :current-page.sync="pageNum"
          :page-sizes="[20, 50, 100, 200]"
          :page-size.sync="pageSize"
          :total="total"
          layout="prev, pager, next, jumper,total, sizes"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </el-col>
    </el-row>
    <apply-dialog ref="applyDialog"></apply-dialog>
    <capture-apply-dialog ref="captureApplyDialog"></capture-apply-dialog>
  </div>
</template>

<script>
import { getMechanismList, friendLibraryProductShootRecordList } from "@/api/productPicture.js";
import { findDictList } from "@/api/dict.js";
import { hasPermission } from "@/utils/index.js";
import {
  getProductShootRecordList,
  getComplementUploadRecordList,
  getPicComplementCrawlList,
  sumbitPicComplementCrawlList,
  getFriendLibraryProductShootRecordList,
  exportPictureComplementList,
} from "@/api/follow.js";

import applyDialog from "./components/applyDialog";
import captureApplyDialog from "./components/captureApplyDialog";

export default {
  name: "",
  components: { applyDialog, captureApplyDialog },
  filters: {},
  props: {},
  data() {
    return {
      loading: false,
      pageNum: 1,
      pageSize: 20,
      total: 0, //总条数
      formData1: {
        createTime: "",
        createUser: "",
        applyUser: "",
      },
      formData4: {
        createTime: "",
        productId: "",
      },
      formData: {
        mechanism:[],
        createTime: "",
        applyMechanism: "",
        spuCategory: "",
        source: "",
        mixtureProduct: "",
        generalName: "",
        auditStatus: "",
        status: "",
        isDone: "",
        isPicture: "",
        auditOpinions:[],
        auditOpinionCustom:"",
        applyer: "",
        reviewUser:"",
        auditTime:""
      },
      tableLoading: false,
      tableData: [],
      isUnfold: false,
      tabName: "first",
      mechanismOptions: [],
      spuCategoryList: [],
      banAuditOpinionCustom: true,
    };
  },
  computed: {},
  watch: {
    tabName(val) {
      this.pageNum = 1;
      this.searchForm();
      this.getCategoryList();
    },
  },
  created() {
    this.searchForm();
    this.getCategoryList();
    this.getMechanismList();
  },
  mounted() {},
  methods: {
    // 导出商品图片补全列表
    async clickExport() {
      let param = Object.assign(
        {
          page: this.pageNum,
          limit: this.pageSize,
          createStartTime: this.formData.createTime
            ? this.formData.createTime[0]
            : "",
          createEndTime: this.formData.createTime
            ? this.formData.createTime[1]
            : "",
          auditOpinion: this.formData.auditOpinions.length ? this.formData.auditOpinions.join() : '',
          reviewStartTime: this.formData.auditTime
            ? this.formData.auditTime[0]
            : "",
          reviewEndTime: this.formData.auditTime
            ? this.formData.auditTime[1]
            : "",
        },
        this.formData
      );
      delete param.createTime;
      delete param.auditOpinions
      delete param.auditTime
      if (this.formData.mechanism[0] === '全选') {
        param.mechanism.shift()
        param.mechanism = param.mechanism.join()
      } else {
        param.mechanism = param.mechanism.join()
      }
      if(param.auditOpinion.indexOf(0) !== -1 && !param.auditOpinionCustom) {
        this.$message.error('请输入自定义原因')
        return
      }
      let res = await exportPictureComplementList(param)
      if (res.retCode === 0) {
        this.$message.success(res.retMsg)
      } else {
        this.$message({
          showClose: true,
          type: "error",
          message: res.retMsg,
        });
      }
    },
    // 修改驳回原因下拉框
    changeAuditOpinion(e) {
      if (e.indexOf(0) === -1) {
        this.formData.auditOpinionCustom = ''
        this.banAuditOpinionCustom = true
      } else {
        this.banAuditOpinionCustom = false
      }
    },
    changeSelect1(val) {
      if (!val.includes("全选") && val.length === this.mechanismOptions.length) {
        this.formData.mechanism.unshift("全选");
      } else if (
        val.includes("全选") &&
        val.length - 1 < this.mechanismOptions.length
      ) {
        console.log(this.formData.mechanism)
        this.formData.mechanism = this.formData.mechanism.filter((item) => {
          return item !== "全选";
        });
      }
    },
    removeTag1(val) {
      if (val === "全选") {
        this.formData.mechanism = [];
      }
    },
    selectAll1() {
      if (this.formData.mechanism.length < this.mechanismOptions.length) {
        this.formData.mechanism = [];
        this.mechanismOptions.map((item) => {
          this.formData.mechanism.push(item.value);
        });
        this.formData.mechanism.unshift("全选");
      } else {
        this.formData.mechanism = [];
      }
    },

    /**
     * 导出excel
     */
    exportExcel() {
      let data = {
        startTime: this.formData4.createTime
            ? this.formData4.createTime[0]
            : "",
        endTime: this.formData4.createTime
          ? this.formData4.createTime[1]
          : "",
        productId: this.formData4.productId
      }

      friendLibraryProductShootRecordList(data)
        .then((res) => {
          if (!res.retCode) {
            this.$message.success(res.retMsg)
          } else {
            this.$message({
              showClose: true,
              type: "error",
              message: res.retMsg,
            });
          }
        })
        .catch((err) => {
          this.$message({
            showClose: true,
            type: "error",
            message: "导出发生错误，请重试",
          });
        });
    },

    // 获取商品大类
    getCategoryList() {
      findDictList({
        type: 7,
      }).then((res) => {
        this.spuCategoryList = res.data.list;
      });
    },

    // 获取所属机构
    getMechanismList() {
      getMechanismList({
        codeType: "dept",
      })
        .then((resp) => {
          if (resp.retCode === 0) {
            resp.data.forEach((item) => {
              if (item.mechanismId) {
                this.mechanismOptions.push({
                  value: item.mechanismId,
                  label: item.mechanismName,
                });
              }
            });
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },

    // 搜索
    searchForm() {
      this.tableLoading = true;
      if (this.tabName == "first") {
        this.getCompletionList();
      } else if (this.tabName == "second") {
        this.getImportList();
      } else if (this.tabName == "third"){
        this.getPicComplementCrawlList();
      } else {
        this.getFriendLibraryProductShootRecordList();
      }
    },

    getCompletionList() {
      let param = Object.assign(
        {
          page: this.pageNum,
          limit: this.pageSize,
          createStartTime: this.formData.createTime
            ? this.formData.createTime[0]
            : "",
          createEndTime: this.formData.createTime
            ? this.formData.createTime[1]
            : "",
          auditOpinion: this.formData.auditOpinions.length ? this.formData.auditOpinions.join() : '',
          reviewStartTime: this.formData.auditTime
            ? this.formData.auditTime[0]
            : "",
          reviewEndTime: this.formData.auditTime
            ? this.formData.auditTime[1]
            : "",
        },
        this.formData
      );
      delete param.createTime;
      delete param.auditOpinions
      delete param.auditTime
      console.log(this.formData)
      if (this.formData.mechanism[0] === '全选') {
        param.mechanism.shift()
        param.mechanism = param.mechanism.join()
      } else {
        param.mechanism = param.mechanism.join()
      }
      if(param.auditOpinion.indexOf(0) !== -1 && !param.auditOpinionCustom) {
        this.$message.error('请输入自定义原因')
        this.tableLoading = false;
        return
      }
      getProductShootRecordList(param).then((res) => {
        if (!res.retCode) {
          this.tableData = res.data.list;
          this.total = res.data.total;
          this.tableLoading = false;
        } else {
          this.$message.error(res.retMsg);
        }
      });
    },

    getImportList() {
      let param = {
        page: this.pageNum,
        limit: this.pageSize,
      };
      getComplementUploadRecordList(param).then((res) => {
        if (!res.retCode) {
          this.tableData = res.data.list;
          this.total = res.data.total;
          this.tableLoading = false;
        } else {
          this.$message.error(res.retMsg);
        }
      });
    },

    getPicComplementCrawlList() {
      let param = Object.assign(
        {
          page: this.pageNum,
          limit: this.pageSize,
          startTime: this.formData1.createTime
            ? this.formData1.createTime[0]
            : "",
          endTime: this.formData1.createTime
            ? this.formData1.createTime[1]
            : "",
        },
        this.formData1
      );
      delete param.createTime;
      getPicComplementCrawlList(param).then((res) => {
        if (!res.retCode) {
          console.log(res.data.list);
          this.tableData = res.data.list;
          this.total = res.data.total;
          this.tableLoading = false;
        } else {
          this.$message.error(res.retMsg);
        }
      });
    },

    getFriendLibraryProductShootRecordList() {
      let param = Object.assign(
        {
          page: this.pageNum,
          limit: this.pageSize,
          startTime: this.formData4.createTime
            ? this.formData4.createTime[0]
            : "",
          endTime: this.formData4.createTime
            ? this.formData4.createTime[1]
            : "",
        },
        this.formData4
      );
      delete param.createTime;
      getFriendLibraryProductShootRecordList(param).then((res) => {
        if (!res.retCode) {
          console.log(res.data.list);
          this.tableData = res.data.list;
          this.total = res.data.total;
          this.tableLoading = false;
        } else {
          this.$message.error(res.retMsg);
        }
      });
    },

    /**
     * 查询按钮点击
     */
    btnSearchClick() {
      this.pageNum = 1;
      this.searchForm();
    },

    /**
     * 重置
     */
    btnResetClick() {
      if (this.tabName == "third") {
        this.formData1 = { createTime: "", createUser: "", applyUser: "" };
      } else if (this.tabName == "four") {
        this.formData4 = { createTime: "", productId: "" };
      } else {
        this.banAuditOpinionCustom = true
        this.formData = {
          createTime: "",
          mechanism: [],
          spuCategory: "",
          source: "",
          mixtureProduct: "",
          generalName: "",
          auditStatus: "",
          status: "",
          isDone: "",
          isPicture: "",
          auditOpinions:[],
          auditOpinionCustom:"",
          applyer: "",
          reviewUser:"",
          auditTime:""
        };
      }

      this.pageNum = 1;
      this.searchForm();
    },
    /**
     * pageSize 改变事件
     */
    handleSizeChange(pageSize) {
      this.pageNum = 1;
      this.pageSize = pageSize;
      this.searchForm();
    },

    /**
     * pageNum 改变事件
     */
    handleCurrentChange(currentPage) {
      this.pageNum = currentPage;
      this.searchForm();
    },
    /**
     * @description: 格式化时间参数
     * @param {date} time 时间戳
     * @return: string 格式化后的时间戳
     */
    hasPermission(str) {
      return hasPermission(str);
    },
    handleApply() {
      this.$refs.applyDialog.open();
    },
    handleCaptureApply() {
      this.$refs.captureApplyDialog.open();
    },
    submit(applyCode, type) {
      sumbitPicComplementCrawlList({
        applyCode,
        type,
      }).then((res) => {
        if (!res.retCode) {
          this.$message.success('提交成功');
          this.searchForm();
        } else {
          this.$message.error(res.retMsg);
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.task-to-be-film-container {
  /**
   * 查询表单
   */
  .search-form-wrap {
    width: 100%;
    padding: 15px;
    border-bottom: 1px dashed #e4e4eb;

    .el-row {
      flex-wrap: wrap;

      .el-col {
        display: flex;
        justify-content: flex-end;

        .el-form-item {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          width: 100%;
          margin-right: 0;
          margin-bottom: 15px;

          /deep/ {
            .el-form-item__label {
              width: 96px;
              line-height: normal;
              padding: 0 12px;
              color: #292933;
              font-weight: normal;
            }

            .el-form-item__content {
              flex: 1;

              .el-range-editor.el-input__inner {
                width: 100%;

                .el-range-separator {
                  width: 14px;
                  padding: 0;
                }
              }
            }
          }

          .el-input {
            width: 100%;
          }

          .el-select {
            width: 100%;
          }
        }
      }
    }
  }

  .tab-wrap {
    padding: 0 15px;
    margin-top: 15px;
  }
  .table-wrap {
    width: 100%;
    min-height: 440px;
    height: calc(100vh - 295px);
  }
}
.btn-wrap {
  padding: 15px 0 0 10px;
}
.radio-wrap {
  .title {
    padding-right: 20px;
  }
}
.active {
  color: #3b95a8;
}
.btn /deep/ {
  padding: 0 6px;
  .is-disabled {
    color: #c0c4cc;
  }
}
</style>
