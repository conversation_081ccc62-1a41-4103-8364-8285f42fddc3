<template>
  <div class="component-container">
    <div class="basic-info-title" @click="showForm = !showForm">
      扩展属性
      <i style="color: #3b95a8" :class="showForm ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
    </div>
    <!-- 说明书 -->
    <div v-show="showForm" class="border-bottom-dashed marbot20">
      <div class="title-container">
        <h4 class="detail-title martop0">说明书</h4>
        <el-button style="margin-bottom: 20px;" type="primary" size="mini" @click="reMatchs" v-if="urlParam.pageType != 'detail'" :loading="matching">{{ matching? '匹配中...' : '重新匹配' }}</el-button>
      </div>
    </div>

    <el-form :disabled="formDisable" v-show="showForm" :model="form" :rules="rules" ref="form" label-width="120px">
      <el-row class="border-bottom-dashed">
        <!-- 成份 -->
        <el-col :span="24">
          <el-form-item prop="ingredient" :class="['has-custom-label', { 'is-change': changeList.includes('ingredient') }]">
            <template slot="label">
              <div class="label-with-copy-vertical">
                <span>成份</span>
                <p
                  @click="copyToClipboard(form.ingredient, '成份')"
                  title="复制成份内容"
                  class="copy-btn-below"
                  v-if="shouldShowCopyButton"
                >
                  复制
                </p>
              </div>
            </template>
            <el-input
              type="textarea"
              placeholder="请输入内容"
              v-model="form.ingredient"
              rows="4"
              maxlength="500"
              show-word-limit
              :disabled="disabled"
            ></el-input>
          </el-form-item>
        </el-col>

        <!-- 适应症 -->
        <el-col :span="24">
          <el-form-item prop="indication" :class="['has-custom-label', { 'is-change': changeList.includes('indication') }]">
            <template slot="label">
              <div class="label-with-copy-vertical">
                <span>适应症/功能主治</span>
                <p
                  @click="copyToClipboard(form.indication, '适应症/功能主治')"
                  title="复制适应症/功能主治内容"
                  class="copy-btn-below"
                  v-if="shouldShowCopyButton"
                >
                  复制
                </p>
              </div>
            </template>
            <el-input
              type="textarea"
              placeholder="请输入内容"
              v-model="form.indication"
              rows="4"
              maxlength="5000"
              show-word-limit
              :disabled="disabled"
            ></el-input>
          </el-form-item>
        </el-col>

        <!-- 用法用量 -->
        <el-col :span="24">
          <el-form-item prop="usageDosage" :class="['has-custom-label', { 'is-change': changeList.includes('usageDosage') }]">
            <template slot="label">
              <div class="label-with-copy-vertical">
                <span>用法用量</span>
                <p
                  @click="copyToClipboard(form.usageDosage, '用法用量')"
                  title="复制用法用量内容"
                  class="copy-btn-below"
                  v-if="shouldShowCopyButton"
                >
                  复制
                </p>
              </div>
            </template>
            <el-input
              type="textarea"
              placeholder="请输入内容"
              v-model="form.usageDosage"
              rows="4"
              maxlength="5000"
              show-word-limit
              :disabled="disabled"
            ></el-input>
          </el-form-item>
        </el-col>

        <!-- 不良反应 -->
        <el-col :span="24">
          <el-form-item prop="adverseReaction" :class="['has-custom-label', { 'is-change': changeList.includes('adverseReaction') }]">
            <template slot="label">
              <div class="label-with-copy-vertical">
                <span>不良反应</span>
                <p
                  @click="copyToClipboard(form.adverseReaction, '不良反应')"
                  title="复制不良反应内容"
                  class="copy-btn-below"
                  v-if="shouldShowCopyButton"
                >
                  复制
                </p>
              </div>
            </template>
            <el-input
              type="textarea"
              placeholder="请输入内容"
              v-model="form.adverseReaction"
              rows="4"
              maxlength="5000"
              show-word-limit
              :disabled="disabled"
            ></el-input>
          </el-form-item>
        </el-col>

        <!-- 注意事项 -->
        <el-col :span="24">
          <el-form-item prop="precautions" :class="['has-custom-label', { 'is-change': changeList.includes('precautions') }]">
            <template slot="label">
              <div class="label-with-copy-vertical">
                <span>注意事项</span>
                <p
                  @click="copyToClipboard(form.precautions, '注意事项')"
                  title="复制注意事项内容"
                  class="copy-btn-below"
                  v-if="shouldShowCopyButton"
                >
                  复制
                </p>
              </div>
            </template>
            <el-input
              type="textarea"
              placeholder="请输入内容"
              v-model="form.precautions"
              rows="4"
              maxlength="5000"
              show-word-limit
              :disabled="disabled"
            ></el-input>
          </el-form-item>
        </el-col>

        <!-- 禁忌 -->
        <el-col :span="24">
          <el-form-item prop="taboos" :class="['has-custom-label', { 'is-change': changeList.includes('taboos') }]">
            <template slot="label">
              <div class="label-with-copy-vertical">
                <span>禁忌</span>
                <p
                  @click="copyToClipboard(form.taboos, '禁忌')"
                  title="复制禁忌内容"
                  class="copy-btn-below"
                  v-if="shouldShowCopyButton"
                >
                  复制
              </p>
              </div>
            </template>
            <el-input
              type="textarea"
              placeholder="请输入内容"
              v-model="form.taboos"
              rows="4"
              maxlength="5000"
              show-word-limit
              :disabled="disabled"
            ></el-input>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="24">
          <div class="bdTop">处方用法用量</div>
        </el-col> -->
        <!-- 处方用法用量 -->
        <el-col :span="12" style="display: flex">
          <el-form-item
            prop="medicationMethod"
            class="width100"
            :class="{
              'is-change': changeList.includes('medicationMethod') || changeList.includes('medicationFrequency'),
            }"
          >
            <template slot="label">
              <div class="label-with-copy-vertical">
                <span>处方用法用量</span>
                <p
                  @click="copyToClipboard(
                      options.medicMethod.find(item => item.id === form.medicationMethod).dictName + 
                      ',' + 
                      options.medicFrequency.find(item => item.id === form.medicationFrequency).dictName, 
                      '处方用法用量')"
                  title="复制处方用法用量"
                  class="copy-btn-below"
                  v-if="shouldShowCopyButton"
                >
                  复制
                </p>
              </div>
            </template>
            <el-select
              v-model="form.medicationMethod"
              placeholder="请选择用药方式"
              clearable
              :disabled="disabled"
              style="width: 48%; padding-right: 5px"
            >
              <el-option
                v-for="item in options.medicMethod"
                :key="item.id"
                :label="item.dictName"
                :value="item.id"
                :disabled="!item.isValid"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            class="nMr"
            label=""
            prop="medicationFrequency"
            :class="{
              'is-change': changeList.includes('medicationMethod') || changeList.includes('medicationFrequency'),
            }"
          >
            <el-select
              v-model="form.medicationFrequency"
              placeholder="请选择频次"
              clearable
              :disabled="disabled"
              style="width: 48%; padding-left: 5px"
            >
              <el-option
                v-for="item in options.medicFrequency"
                :key="item.id"
                :label="item.dictName"
                :value="item.id"
                :disabled="!item.isValid"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12" style="display: flex">
          <el-form-item
            ref="cinput"
            label="一次"
            prop="medicationDosage"
            class="width100"
            :class="{
              'is-change': changeList.includes('medicationDosage') || changeList.includes('miniUnit'),
            }"
          >
            <template slot="label">
              <div class="label-with-copy-vertical">
                <span>一次</span>
                <p
                  @click="copyToClipboard(
                      form.medicationDosage+ 
                      ',' + 
                      options.minimumUnit.find(item => item.id === form.miniUnit).dictName, 
                      '一次')"
                  title="复制一次"
                  class="copy-btn-below"
                  v-if="shouldShowCopyButton"
                >
                  复制
                </p>
              </div>
            </template>
            <el-input
              type="text"
              placeholder="请输入每次服用数量"
              v-model="form.medicationDosage"
              maxlength="7"
              :disabled="disabled || medicationDosageDisable"
              @change="(value) => value && verifyNumberIncludesFraction(value) && (form.medicationDosage = '')"
              style="width: 48%; padding-right: 5px"
            ></el-input>
          </el-form-item>
          <el-form-item
            class="nMr"
            label=""
            prop="miniUnit"
            :class="{
              'is-change': changeList.includes('medicationDosage') || changeList.includes('miniUnit'),
            }"
          >
            <el-select
              v-model="form.miniUnit"
              placeholder="请选择最小使用单位"
              clearable
              :disabled="disabled"
              @change="getMiniUnitTitel"
              style="width: 48%; padding-left: 5px"
            >
              <el-option
                v-for="item in options.minimumUnit"
                :key="item.id"
                :label="item.dictName"
                :value="item.id"
                :disabled="!item.isValid"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>


      </el-row>
      <!-- 用药指导 -->
      <h4 class="detail-title">用药指导</h4>
      <el-row>
        <!-- 治疗症状 -->
        <el-col :span="24">
          <el-button
            v-if="operationType != 'detail' && operationType != 'auditLevel2' && operationType != 'operate' && operationType != 'spuOperate'"
            size="medium"
            type="primary"
            icon="el-icon-plus"
            class="martop20"
            @click="openSymptomsDlg"
            >选择治疗症状</el-button
          >
          <div class="flex-text">
            <span>已选择症状：</span>
            <span>
              <el-tag
                v-for="tag in symptomsTags"
                :key="tag.treatId"
                :closable="!disabled"
                @close="handleCloseTag(tag)"
                style="margin-right: 10px; margin-bottom: 10px"
                >{{ tag.treatName }}</el-tag
              >
            </span>
          </div>
        </el-col>

        <!-- 是否中成药 -->
        <el-col :span="8">
          <el-form-item label="是否中成药" prop="patentMedicineType" :class="{ 'is-change': changeList.includes('patentMedicineType') }">
            <el-radio-group v-model="form.patentMedicineType" :disabled="disabled">
              <el-radio :label="1">中成药</el-radio>
              <el-radio :label="2">西药</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <!-- 是否国产 -->
        <el-col :span="8">
          <el-form-item label="是否国产" prop="madeType" :class="{ 'is-change': changeList.includes('madeType') }">
            <el-radio-group v-model="form.madeType" :disabled="disabled">
              <el-radio :label="1">国产</el-radio>
              <el-radio :label="2">进口</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <!-- 用药人群 -->
        <el-col :span="8">
          <el-form-item label="用药人群" prop="medicationCrowd" :class="{ 'is-change': changeList.includes('medicationCrowd') }">
            <el-radio-group v-model="form.medicationCrowd" :disabled="disabled">
              <el-radio :label="1">成人</el-radio>
              <el-radio :label="2">儿童</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- 治疗症状弹框 -->
    <el-dialog title="选择症状" :visible.sync="symptomsDlgVisible" width="800px" @close="closeSymptomsDlg">
      <el-form label-width="50px" :model="symptoms" ref="symptoms">
        <el-row class="border-bottom-dashed">
          <el-col :span="6">
            <el-form-item label="症状" prop="symptomName">
              <el-input v-model="symptoms.symptomName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="病种" prop="diseaseName">
              <el-input v-model="symptoms.diseaseName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="科室" prop="sectionOffice">
              <el-input v-model="symptoms.sectionOffice"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6" style="padding-left: 10px">
            <el-button size="medium" type="primary" @click="querySymptoms">查询</el-button>
            <el-button size="medium" @click="resetSymptoms">重置</el-button>
          </el-col>
        </el-row>
      </el-form>

      <!-- 选择 -->
      <el-button size="medium" type="primary" class="martop20 marbot20" @click="selectedSymptoms">选择</el-button>

      <!-- 治疗症状列表 -->
      <vxe-table
        border
        highlight-hover-row
        resizable
        height="320px"
        align="center"
        :loading="symptomsLoading"
        :tooltip-config="{ enterable: false }"
        :data="symptomsTableData"
        ref="symTable"
      >
        <vxe-table-column type="checkbox" width="60"></vxe-table-column>
        <vxe-table-column type="index" title="序号" width="60" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="symptomName" title="症状" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="diseaseName" title="病种" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="sectionOffice" title="科室" min-width="150" show-header-overflow show-overflow></vxe-table-column>
      </vxe-table>

      <!-- 分页 -->
      <el-row class="custom-pagination-wrap">
        <el-col>
          <el-pagination
            background
            :current-page.sync="symptoms.pageNum"
            :page-sizes="[20, 50, 100, 200]"
            :page-size.sync="symptoms.pageSize"
            :total="symptoms.total"
            layout="prev, pager, next, jumper,total, sizes"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </el-col>
      </el-row>
    </el-dialog>

    <!-- 用药指导-列表操作 -->
    <div
      v-show="showForm"
      class="marbot20"
      v-if="operationType != 'detail' && operationType != 'auditLevel2' && operationType != 'operate' && operationType != 'spuOperate'"
    >
      <el-button :disabled="formDisable" size="medium" type="primary" icon="el-icon-plus" @click="operationRow('新增')">新增行</el-button>
      <el-button :disabled="formDisable" size="medium" @click="operationRow('修改')">修改</el-button>
      <el-button :disabled="formDisable" size="medium" @click="operationRow('复制')">复制行</el-button>
      <el-button :disabled="formDisable" size="medium" @click="operationRow('删除')">删除</el-button>
    </div>

    <!-- 用药指导-列表数据 -->
    <vxe-table
      v-show="showForm"
      border
      highlight-hover-row
      highlight-current-row
      auto-resize
      resizable
      max-height="500"
      align="center"
      :radio-config="{ labelFiled: '', trigger: 'row' }"
      :tooltip-config="{ enterable: false }"
      :data="drugsGuideTableData"
      ref="drgTable"
      @current-change="currentChangeEvent"
    >
      <vxe-table-column
        v-if="operationType != 'detail' && operationType != 'auditLevel2' && operationType != 'operate'"
        type="radio"
        title="#"
        width="50"
      ></vxe-table-column>
      <vxe-table-column type="index" title="序号" width="60" show-header-overflow show-overflow></vxe-table-column>

      <vxe-table-column field="treatName" title="病种/症状" min-width="100" show-header-overflow show-overflow></vxe-table-column>

      <vxe-table-column
        field="medicationCrowd"
        title="人群"
        min-width="100"
        show-header-overflow
        show-overflow
        :formatter="medicationCrowdInit"
      ></vxe-table-column>

      <vxe-table-column field="usageDosage" title="用法用量" min-width="100" show-header-overflow show-overflow :formatter="usageDosageInit"></vxe-table-column>

      <vxe-table-column field="treatCourse" title="疗程" min-width="100" show-header-overflow show-overflow>
        <template v-slot="{ row }">{{ row.treatCourse ? row.treatCourse : "" }} 天</template>
      </vxe-table-column>

      <vxe-table-column field="usageDay" title="使用天数" min-width="100" show-header-overflow show-overflow>
        <template v-slot="{ row }">{{ row.usageDay ? row.usageDay : "" }} 天</template>
      </vxe-table-column>

      <vxe-table-column field="usageNote" title="使用注意" min-width="100" show-header-overflow show-overflow></vxe-table-column>

      <vxe-table-column field="takeCycle" title="服用周期" min-width="100" show-header-overflow show-overflow></vxe-table-column>

      <vxe-table-column field="forgetTake" title="忘记服用" min-width="100" show-header-overflow show-overflow></vxe-table-column>

      <vxe-table-column field="overdoseTake" title="过量服用" min-width="100" show-header-overflow show-overflow></vxe-table-column>

      <vxe-table-column field="usageFrequency" title="使用次数" min-width="100" show-header-overflow show-overflow></vxe-table-column>

      <vxe-table-column field="usageMethod" title="使用方法" min-width="100" show-header-overflow show-overflow></vxe-table-column>

      <vxe-table-column field="precautionsTaboos" title="注意事项和禁忌" min-width="130" show-header-overflow show-overflow></vxe-table-column>

      <vxe-table-column field="pharmacistSuggestions" title="药师建议" min-width="100" show-header-overflow show-overflow></vxe-table-column>

      <vxe-table-column field="lifeAdvice" title="生活建议" min-width="100" show-header-overflow show-overflow></vxe-table-column>
    </vxe-table>

    <!-- 新增-用药指导弹框 -->
    <el-dialog
      :title="operType + '-用药指导'"
      :visible.sync="drugsGuideDlgVisible"
      width="800px"
      class="drugs-guide"
      @close="closeDrugsGuideDlg"
      destroy-on-close
    >
      <el-form :model="drugsGuideForm" ref="drugsGuideForm" label-width="80px">
        <el-row class="drugs-guide-content">
          <!-- 病种/症状 -->
          <el-col :span="8">
            <el-form-item label="病种/症状" prop="treatId">
              <el-select v-model="drugsGuideForm.treatId" placeholder="请选择" @change="handleTreatName">
                <el-option v-for="item in options.diseaseSymptom" :value="item.treatId" :key="item.treatId" :label="item.treatName"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 疗程 -->
          <el-col :span="8">
            <el-form-item label="疗程" prop="treatCourse">
              <el-input
                v-model="drugsGuideForm.treatCourse"
                maxlength="4"
                @input="(val) => val && verifyInput(val, 'integer') && (drugsGuideForm.treatCourse = '')"
              >
                <template slot="append">天</template>
              </el-input>
            </el-form-item>
          </el-col>

          <!-- 使用天数 -->
          <el-col :span="8">
            <el-form-item label="使用天数" prop="usageDay">
              <el-input
                v-model="drugsGuideForm.usageDay"
                maxlength="4"
                @input="(val) => val && verifyInput(val, 'integer') && (drugsGuideForm.usageDay = '')"
              >
                <template slot="append">天</template>
              </el-input>
            </el-form-item>
          </el-col>

          <!-- 人群 -->
          <el-col :span="8">
            <el-form-item label="人群" prop="medicationCrowd">
              <el-select v-model="drugsGuideForm.medicationCrowd" placeholder="请选择">
                <el-option :value="'0'" :label="'全部'"></el-option>
                <el-option :value="'1'" :label="'成人'"></el-option>
                <el-option :value="'2'" :label="'儿童'"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 用法用量 -->
          <el-col :span="24">
            <!-- <el-form-item label="用法用量" prop="usageDosage">
              <el-input
                type="textarea"
                placeholder="请输入内容"
                v-model="drugsGuideForm.usageDosage"
                rows="4"
                maxlength="500"
                show-word-limit 
              ></el-input>
            </el-form-item> -->
            <!-- 处方用法用量 -->
            <div class="usageDosage-class">
              <el-form-item label="用法用量" prop="medicationMethod" class="width100">
                <el-select
                  v-model="drugsGuideForm.medicationMethod"
                  placeholder="请选择用药方式"
                  clearable
                  style="width: 48%; padding-right: 5px"
                >
                  <el-option v-for="item in options.medicMethod" :key="item.id" :label="item.dictName" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item class="nMr" label="" prop="medicationFrequency">
                <el-select v-model="drugsGuideForm.medicationFrequency" placeholder="请选择频次" clearable style="width: 48%; padding-left: 5px">
                  <el-option v-for="item in options.medicFrequency" :key="item.id" :label="item.dictName" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item ref="cinput" label="一次" prop="medicationDosage" class="width100">
                <el-input
                  type="text"
                  placeholder="请输入每次服用数量"
                  v-model="drugsGuideForm.medicationDosage"
                  maxlength="7"
                  style="width: 48%; padding-right: 5px"
                ></el-input>
              </el-form-item>
              <el-form-item class="nMr" label="" prop="miniUnit">
                <el-select
                  v-model="drugsGuideForm.miniUnit"
                  placeholder="请选择最小使用单位"
                  clearable
                  :disabled="disabled"
                  @change="drugsGuideFormGetMiniUnitTitel"
                  style="width: 48%; padding-left: 5px"
                >
                  <el-option
                    v-for="item in options.minimumUnit"
                    :key="item.id"
                    :label="item.dictName"
                    :value="item.id"
                    :disabled="!item.isValid"
                  ></el-option>
                </el-select>
              </el-form-item>
            </div>
          </el-col>

          <!-- 使用注意 -->
          <el-col :span="24">
            <el-form-item label="使用注意" prop="usageNote">
              <el-input
                type="textarea"
                placeholder="请输入内容"
                v-model="drugsGuideForm.usageNote"
                rows="4"
                maxlength="5000"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 服用周期 -->
          <el-col :span="24">
            <el-form-item label="服用周期" prop="takeCycle">
              <el-input
                type="textarea"
                placeholder="请输入内容"
                v-model="drugsGuideForm.takeCycle"
                rows="4"
                maxlength="5000"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 忘记服用 -->
          <el-col :span="24">
            <el-form-item label="忘记服用" prop="forgetTake">
              <el-input
                type="textarea"
                placeholder="请输入内容"
                v-model="drugsGuideForm.forgetTake"
                rows="4"
                maxlength="5000"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 过量服用 -->
          <el-col :span="24">
            <el-form-item label="过量服用" prop="overdoseTake">
              <el-input
                type="textarea"
                placeholder="请输入内容"
                v-model="drugsGuideForm.overdoseTake"
                rows="4"
                maxlength="500"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 使用次数 -->
          <el-col :span="24">
            <el-form-item label="使用次数" prop="usageFrequency">
              <el-input
                type="textarea"
                placeholder="请输入内容"
                v-model="drugsGuideForm.usageFrequency"
                rows="4"
                maxlength="5000"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 使用方法 -->
          <el-col :span="24">
            <el-form-item label="使用方法" prop="usageMethod">
              <el-input
                type="textarea"
                placeholder="请输入内容"
                v-model="drugsGuideForm.usageMethod"
                rows="4"
                maxlength="5000"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 注意事项和禁忌 -->
          <el-col :span="24">
            <el-form-item label="注意事项和禁忌" prop="precautionsTaboos">
              <el-input
                type="textarea"
                placeholder="请输入内容"
                v-model="drugsGuideForm.precautionsTaboos"
                rows="4"
                maxlength="5000"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 药师建议 -->
          <el-col :span="24">
            <el-form-item label="药师建议" prop="pharmacistSuggestions">
              <el-input
                type="textarea"
                placeholder="请输入内容"
                v-model="drugsGuideForm.pharmacistSuggestions"
                rows="4"
                maxlength="5000"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 生活建议 -->
          <el-col :span="24">
            <el-form-item label="生活建议" prop="lifeAdvice">
              <el-input
                type="textarea"
                placeholder="请输入内容"
                v-model="drugsGuideForm.lifeAdvice"
                rows="4"
                maxlength="5000"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button size="medium" @click="drugsGuideDlgVisible = false">取 消</el-button>
        <el-button size="medium" type="primary" @click="saveDrugsGuideData">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDictList, getTreatmentSymptoms, getExtendInfo, getExtendByGeneralName, reMatchExtend } from "../../api/product.js"
import {} from "@/api/product"
const InputRules = {
  decimal01: {
    pattern: /^(0|0\.[0-9]|[1-9]\d{0,3}|[1-9]\d{0,3}\.\d|[1-9]\d{0,3}\.\d{2}|0\.0[1-9]|0\.[1-9]\d?|[1-9]\d{0,3})$/,
    msg: "只能输入数字且不超过9999.99",
  },
  chinese: {
    pattern: /^[\u4e00-\u9fa5]+$/,
    msg: "只能输入汉字",
  },
  integer: {
    pattern: /^[1-9][0-9]{0,4}$/,
    msg: "请输入正确的天数",
  },
}

export default {
  name: "",
  components: {},
  filters: {},
  props: {
    modify: {
      type: String,
      default: "",
    },
    // 新版本的审批流
    newAudit: {
      type: Boolean,
      default: false,
    },
    // 商品修改改变字段
    changeList: {
      type: Array,
      required: false,
      default: () => [],
    },
    formDisable: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      medicationMethod: "",
      showForm: true,
      medicationDosageDisable: false,
      isInit: false, // 初始化
      originalObj: {}, // 原数据
      disabled: false,
      options: {
        diseaseSymptom: [], // 病种/症状
        medicMethod: [], // 用药方式
        medicFrequency: [], // 用药频次
        minimumUnit: [], // 最小使用单位
      },
      form: {
        ingredient: "", // 成份
        indication: "", // 适应症
        usageDosage: "", // 用法用量
        adverseReaction: "", // 不良反应
        precautions: "", // 注意事项
        taboos: "", // 禁忌
        medicationMethod: "", // 处方用法用量-用药方式
        medicationFrequency: "", // 处方用法用量-频次
        medicationDosage: "", // 一次-每次服用数量
        minimumUnit: "", // 一次-最小使用单位 (文本)
        miniUnit: "", // 一次-最小使用单位 (id)
        patentMedicineType: "", // 是否中成药
        madeType: "", // 是否国产
        medicationCrowd: "", // 用药人群
      },
      // 治疗症状-对话框
      symptomsDlgVisible: false,
      symptomsTableData: [],
      symptomsLoading: true,
      symptoms: {
        symptomName: "", // 症状
        diseaseName: "", // 病种
        sectionOffice: "", // 科室
        total: 0,
        pageNum: 1, // 页码
        pageSize: 10, // 条数
      },
      symptomsTags: [], // 选中症状
      // 用药指导-对话框
      drugsGuideDlgVisible: false,
      drugsGuideTableData: [],
      drugsGuideForm: {
        treatId: "", // 病种/症状
        treatName: "",
        treatCourse: "", // 疗程
        usageDay: "", // 使用天数
        usageDosage: "", // 用法用量
        usageNote: "", // 使用注意
        takeCycle: "", // 服用周期
        forgetTake: "", // 忘记服用
        overdoseTake: "", // 过量服用
        usageFrequency: "", // 使用次数
        usageMethod: "", // 使用方法
        precautionsTaboos: "", // 注意事项和禁忌
        pharmacistSuggestions: "", // 药师建议
        lifeAdvice: "", // 生活建议
        medicationCrowd: "0", //人群
        medicationMethod: "", //用药方法
        medicationFrequency: "", // 用药频次
        medicationDosage: "", //用量
        miniUnit: "", //最小使用单位id
        minimumUnit: "", //最小使用单位text
      },
      currentRow: null, // 用药指导列表 选中当前行数据
      operType: null, // 列表操作类型,
      rules: {
        ingredient: [{ required: true, message: "请输入成份", trigger: "blur" }],
        indication: [{ required: true, message: "请输入适应症", trigger: "blur" }],
        usageDosage: [{ required: true, message: "请输入用法用量", trigger: "blur" }],
        adverseReaction: [{ required: true, message: "请输入不良反应", trigger: "blur" }],
        precautions: [{ required: true, message: "请输入注意事项", trigger: "blur" }],
        taboos: [{ required: true, message: "请输入禁忌", trigger: "blur" }],
        medicationMethod: [
          {
            required: false,
            message: "请选择用药方式",
            trigger: "change",
          },
        ],
        medicationFrequency: [
          {
            required: false,
            message: "请选择频次",
            trigger: "change",
          },
        ],
        medicationDosage: [
          {
            required: false,
            message: "请输入每次服用数量",
            trigger: "blur",
          },
        ],
        miniUnit: [
          {
            required: false,
            message: "请选择最小使用单位",
            trigger: "change",
          },
        ],
      },
      matching: false,  // 匹配中
      spec: this.$store.state.product.spec,  //规格
      generalName: this.$store.state.product.generalName //通用名
    }
  },
  computed: {
    // 商品分类
    spuCategory() {
      return this.$store.getters.spuCategory
    },

    // 操作类型
    operationType() {
      return this.$store.getters.operationType
    },

    // 路由参数
    urlParam() {
      return this.$route.query
    },
    firstCategory() {
      return this.$store.state.product.firstCategory
    },
    // 新增：复制按钮显示条件
    shouldShowCopyButton() {
      // 主要条件：operationType为detail或edit
      if (this.operationType === 'detail' || this.operationType === 'edit') {
        return true;
      }
      
      // 备用条件：根据URL参数判断
      const urlParam = this.$route.query;
      
      // 如果是纠错类型或上架类型，显示复制按钮（这些通常是detail模式）
      if (urlParam.type === 'coorection' || urlParam.type === 'shelves') {
        return true;
      }
      
      // 如果URL中没有明确的新增标识，且有productCode，可能是编辑或详情模式
      if (urlParam.productCode && !urlParam.isAdd) {
        return true;
      }

      if(urlParam.procKey === '新品上报流程'){
        return true;
      }
      
      return false;
    }
  },
  watch: {
    firstCategory(val) {
      if (val == 100002) {
        Object.keys(this.rules).forEach((item) => {
          if (
            item == "medicationMethod" ||
            item == "medicationFrequency" ||
            // item == "medicationDosage" ||
            item == "miniUnit"
          ) {
            this.rules[item][0].required = true
          }
        })
      } else {
        Object.keys(this.rules).forEach((item) => {
          if (item == "medicationMethod" || item == "medicationFrequency" || item == "medicationDosage" || item == "miniUnit") {
            this.rules[item][0].required = false
          }
        })
      }
      this.$refs.form.clearValidate()
    },
    form: {
      handler: function (val) {
        if (this.operationType == "detail") return
        let result = _.isEqual(val, this.originalObj.form)
        this.$bus.$emit("productChange", !result)
        if (!result && !this.loading) {
          this.findDiffExtendedAttr(val)
        }
      },
      deep: true,
    },
    symptomsTags(val) {
      if (this.operationType == "detail") return
      // let result = _.isEqual(val, this.originalObj.symptomsTags);
      // console.log(val, this.originalObj.symptomsTags);
      let arr1 = val.map((item) => {
        return item.treatId
      })
      let arr2 = this.originalObj.symptomsTags.map((item) => {
        return item.treatId
      })
      function getArrDifference(arr1, arr2) {
        return [...arr1, ...arr2].filter(function (v, i, arr) {
          return arr.indexOf(v) === arr.lastIndexOf(v)
        })
      }
      let flag = getArrDifference(arr1, arr2).length ? true : false
      this.$bus.$emit("productChange", flag) // false 没变 true 变了
      // console.log(flag);
      // console.log(`扩展属性-symptomsTags：${flag ? "变了" : "没变"}`);
    },
    drugsGuideTableData(val) {
      if (this.operationType == "detail") return
      let newObj = val.map((item) => {
        delete item._XID
        return item
      })
      let result = _.isEqual(newObj, this.originalObj.drugsGuideTableData)
      // console.log(newObj, this.originalObj.drugsGuideTableData);
      this.$bus.$emit("productChange", !result)
      // console.log(`扩展属性-drugsGuideTableData：${result ? "没变" : "变了"}`);
    },
    // 修改商品分类
    spuCategory(newValue, oldValue) {
      // 1.4.2V 修改商品分类 不清空扩展属性值
      // 初始化完成后 常规切换分类 => 清空
      // if (
      //   this.isInit &&
      //   (this.operationType == "add" || this.operationType == "draft")
      // ) {
      //   this.clearExtendedAttr();
      // }
    },
    // 操作类型
    operationType(newValue) {
      // console.log(newValue, "newValue");
      if (newValue == "auditLevel1" || newValue == "auditLevel2" || newValue == "auditLevel3" || newValue == "RejectEdit") {
        this.getData("/api/spu/get/spuExtend/spuCode", {
          applyCode: this.urlParam.applyCode ? this.urlParam.applyCode : "",
          spuCode: this.urlParam.productCode ? this.urlParam.productCode : "",
        })
      }

      // 二审 扩展属性不能编辑
      if (this.operationType == "auditLevel2") {
        this.disabled = true
      }
    },
    "form.miniUnit"(val) {
      this.getMiniUnitTitel(val)
      if (this.form.minimumUnit === "适量") {
        this.form.medicationDosage = ""
        this.medicationDosageDisable = true
        this.rules.medicationDosage[0].required = false
        this.$refs.cinput.onFieldBlur()
      } else {
        this.medicationDosageDisable = false
        this.rules.medicationDosage[0].required = true
      }
    },
  },
  created() {
    // 用药方式
    getDictList(21).then((res) => {
      this.options.medicMethod = res
    })

    // 用药频次
    getDictList(20).then((res) => {
      this.options.medicFrequency = res
    })

    // 最小使用单位
    getDictList(25).then((res) => {
      this.options.minimumUnit = res
    })

    // 进入页面spu 加载完成触发
    this.$bus.$on("spuLoading", (loading) => {
      // console.log(loading, "spuLoading加载完成");
      if (loading) {
        this.isInit = true
      }
    })

    // 监听 复用事件 1.4.2V 复用不清空扩展属性值
    // this.$bus.$on("reuseEvent", data => {
    //  this.clearExtendedAttr();
    // });
    if (this.operationType == "add" || this.operationType == "draft") {
      this.originalObj = Object.assign(
        {},
        {
          form: _.cloneDeep(this.form),
          drugsGuideTableData: _.cloneDeep(this.drugsGuideTableData),
          symptomsTags: _.cloneDeep(this.symptomsTags),
        }
      )
    }

    // 获取扩展属性
    // console.log(this.operationType, this.urlParam);
    if (this.operationType == "draft") {
      this.getData("/api/spu/get/spuExtend/spuCode", {
        applyCode: this.urlParam.applyCode ? this.urlParam.applyCode : "",
      })
    }
    if (this.operationType == "edit" || this.operationType == "operate") {
      this.getData("/api/spu/get/spuExtend/spuCode", {
        spuCode: this.urlParam.productCode ? this.urlParam.productCode : "",
      })
      // 预首营 扩展属性不能编辑
      if (this.operationType == "operate") {
        this.disabled = true
      }
    }

    // 详情 扩展属性不能编辑
    if (
      this.operationType == "detail" ||
      this.operationType == "auditLevel1" ||
      this.operationType == "auditLevel2" ||
      this.operationType == "auditLevel3" ||
      this.operationType == "RejectEdit" ||
      this.newAudit
    ) {
      this.getData("/api/spu/get/spuExtend/businessCode", this.urlParam)
      if (this.operationType != "RejectEdit" && this.modify != 1) {
        this.disabled = true
      }
    }
  },
  mounted() {
    this.$bus.$on("generalNameChange", (generalName) => {
      if (generalName) {
        this.getGeneralNameData(generalName)
      }
    })
    this.$bus.$on('reMatchBySpec', (data) => {
      this.spec = data 
    })
    this.$bus.$on('reMatchByGaeneralName', (data) => {
      this.generalName = data 
    })
  },
  methods: {
    async reMatchs(){
      // console.log('====================================');
      // console.log(this.generalName, this.spec);
      // console.log('====================================');
      if(!this.generalName || !this.spec) {
        this.$message.error("请输入通用名及规格")
        return false
      }
      this.matching = true
      const params = {
        generalName: this.generalName,
        spec: this.spec,
      }
      try{
        const res = await reMatchExtend(params)
        if(res.retCode === 0){
          this.matching = false
          if(res.data){
            this.$set(this.form, "ingredient", res.data.ingredient)
            this.$set(this.form, "indication", res.data.indication)
            this.$set(this.form, "majorFunction", res.data.majorFunction)
            this.$set(this.form, "usageDosage", res.data.usageDosage)
            this.$set(this.form, "adverseReaction", res.data.adverseReaction)
            this.$set(this.form, "precautions", res.data.precautions)
            this.$set(this.form, "taboos", res.data.taboos)
            this.$set(this.form, "medicationMethod", res.data.medicationMethod)
            this.$set(this.form, "medicationFrequency", res.data.medicationFrequency)
            this.$set(this.form, "medicationDosage", res.data.medicationDosage)
            this.$set(this.form, "minimumUnit", res.data.minimumUnit)
            this.$set(this.form, "miniUnit", res.data.miniUnit)
          }else {
            this.$message.error("未找到匹配数据")
          }
        }else {
          this.$message.error(res.retMsg)
          this.matching = false
        }
      }catch(err){
        console.log(err, 'err');
        this.matching = false
      }
    },
    medicationCrowdInit(row) {
      if (row.row.medicationCrowd === null || row.row.medicationCrowd === undefined) {
        return "未知"
      }

      if (row.row.medicationCrowd.toString() === "0") {
        return "全部"
      } else if (row.row.medicationCrowd.toString() === "1") {
        return "成人"
      } else if (row.row.medicationCrowd.toString() === "2") {
        return "儿童"
      } else {
        return "未知"
      }
    },
    // 根据商品通用名获取扩展属性数据
    async getGeneralNameData(spuCode) {
      let res = await getExtendByGeneralName({ spuCode })
      if (res.data) {
        this.handleExtendData(res.data)
      }
    },
    // 获取扩展属性数据
    getData(url, paramsObj) {
      this.loading = true
      getExtendInfo(url, paramsObj).then((res) => {
        if (res) {
          this.loading = false
          this.handleExtendData(res)
        }
      })
    },

    // 暴露给父组件 所有数据 可通过ref
    getExtendData(isDraft) {
      let isValid = true
      if (!isDraft) {
        this.$refs.form.validate((valid) => {
          if (!valid) {
            isValid = false
            this.$message.warning("请完善扩展属性说明书信息")
          }
        })
      }
      if (!isValid) return false
      // 处理选中的治疗症状提交数据
      let symptomsArr = this.symptomsTags.map((item) => {
        let { department, diseases, symptom, treatId } = item
        return { department, diseases, symptom, treatId }
      })
      const allData = Object.assign({}, this.form, {
        productExtendGuidanceList: this.drugsGuideTableData,
        productExtendTreatList: symptomsArr,
      })

      // console.log(allData, "---扩展数据全部提交数据");
      allData.changeList = this.changeList
      return allData
    },

    // 处理props接收的数据
    handleExtendData(data) {
      let {
        productExtendGuidanceList,
        productExtendTreatList,
        ingredient,
        indication,
        usageDosage,
        adverseReaction,
        precautions,
        taboos,
        medicationMethod,
        medicationFrequency,
        medicationDosage,
        miniUnit,
        patentMedicineType,
        madeType,
        medicationCrowd,
      } = data

      let form = {
        ingredient,
        indication,
        usageDosage,
        adverseReaction,
        precautions,
        taboos,
        medicationMethod: medicationMethod ? medicationMethod : "",
        medicationFrequency: medicationFrequency ? medicationFrequency : "",
        medicationDosage,
        miniUnit: miniUnit === 0 ? "" : Number(miniUnit),
        patentMedicineType: patentMedicineType ? patentMedicineType : "",
        madeType: madeType ? madeType : "",
        medicationCrowd: medicationCrowd ? medicationCrowd : "",
      }
      let drugsGuideTableData = []
      if (productExtendGuidanceList.length) {
        drugsGuideTableData = productExtendGuidanceList.map((item) => {
          let {
            treatId,
            treatName,
            treatCourse, //疗程
            usageDay, //使用天数
            usageDosage, //用法用量
            usageNote, //使用注意
            takeCycle, //服用周期
            forgetTake, //忘记服用
            overdoseTake, //过量服用
            usageFrequency, //usageFrequency
            usageMethod, //使用方法
            precautionsTaboos, //注意事项和禁忌
            pharmacistSuggestions, //药师建议
            lifeAdvice, //生活建议
            medicationCrowd, //人群
            medicationMethod,
            medicationFrequency,
            medicationDosage,
            miniUnit,
            minimumUnit
          } = item
          return {
            treatId,
            treatName,
            treatCourse: treatCourse ? treatCourse : "", //疗程
            usageDay: usageDay ? usageDay : "", //使用天数
            usageDosage, //用法用量
            usageNote, //使用注意
            takeCycle, //服用周期
            forgetTake, //忘记服用
            overdoseTake, //过量服用
            usageFrequency, //usageFrequency
            usageMethod, //使用方法
            precautionsTaboos, //注意事项和禁忌
            pharmacistSuggestions, //药师建议
            lifeAdvice, //生活建议
            medicationCrowd, //人群
            medicationMethod,
            medicationFrequency,
            medicationDosage,
            miniUnit,
            minimumUnit
          }
        })
      }

      // 处理选中症状字段
      let symptomsTags = productExtendTreatList.map((item) => {
        let treatName = `${item.symptomName}-${item.diseasesName}-${item.departmentName}`
        let { treatId, department, diseases, symptom } = item
        return {
          treatId,
          treatName,
          department,
          diseases,
          symptom,
        }
      })

      if (
        this.operationType == "auditLevel1" ||
        this.operationType == "auditLevel2" ||
        this.operationType == "auditLevel3" ||
        this.operationType == "operate" ||
        this.operationType == "RejectEdit" ||
        this.operationType == "edit"
      ) {
        // 处理后的数据 拷贝一份为原数据
        this.originalObj = Object.assign(
          {},
          {
            form,
            drugsGuideTableData,
            symptomsTags,
          }
        )
      }

      this.form = _.cloneDeep(form)
      this.drugsGuideTableData = _.cloneDeep(drugsGuideTableData)
      this.symptomsTags = _.cloneDeep(symptomsTags)
      // 备份一份初始化完成时的数据
      this._initializedForm = _.cloneDeep(form)
    },

    // 父组件商品分类切换 暴露给外层 操作内部数据的方法 使用 this.$refs.sau.$children.clearExtendedAttr()
    clearExtendedAttr() {
      this.$refs["form"].resetFields()
      // 手动清空
      this.form.medicationFrequency = ""
      this.form.miniUnit = ""
      this.symptomsTags = []
      this.drugsGuideTableData = []
      this.currentRow = null

      this.originalObj = Object.assign(
        {},
        {
          form: _.cloneDeep(this.form),
          drugsGuideTableData: _.cloneDeep(this.drugsGuideTableData),
          symptomsTags: _.cloneDeep(this.symptomsTags),
        }
      )
    },

    // 打开治疗症状弹框
    openSymptomsDlg() {
      this.querySymptoms()
      this.symptomsDlgVisible = true
    },

    // 关闭治疗症状弹框
    closeSymptomsDlg() {
      this.$refs["symptoms"].resetFields()
      this.symptomsDlgVisible = false
    },

    // 查询
    querySymptoms() {
      this.symptomsLoading = true
      let { symptomName, diseaseName, sectionOffice } = this.symptoms
      getTreatmentSymptoms({ symptomName, diseaseName, sectionOffice }).then((res) => {
        this.symptomsLoading = false
        this.symptomsTableData = res.list
        this.symptoms.total = res.total
        // 接口 分页有问题
        // this.symptoms.pageNum = res.pageNum;
        // this.symptoms.pageSize = res.pageSize;
        // console.log(res, "接口：治疗症状查询列表");
      })
    },

    // 重置
    resetSymptoms() {
      this.symptoms = {
        diseaseName: "",
        sectionOffice: "",
        symptomName: "",
      }
      this.querySymptoms()
    },

    // pageSize 改变事件// 接口 分页有问题
    handleSizeChange(pageSize) {
      this.symptoms.pageNum = 1
      this.symptoms.pageSize = pageSize
      this.querySymptoms()
    },

    // pageNum 改变事件// 接口 分页有问题
    handleCurrentChange(currentPage) {
      this.symptoms.pageNum = currentPage
      this.resetSymptoms()
    },

    // 选择症状
    selectedSymptoms() {
      // 获取选中结果
      let selectedArr = this.$refs.symTable.getSelectRecords()
      if (selectedArr.length) {
        // 处理table列表选中数据
        if (this.symptomsTags.length == 0) {
          this.symptomsTags = this.handleCheckedSymptoms(selectedArr)
        } else {
          // 判断原数组中数据和当前选中数据是否存在相同
          let select = this.handleCheckedSymptoms(selectedArr)
          // 去重
          let result = [...select, ...this.symptomsTags].reduce(function (prev, next) {
            let str = JSON.stringify(next)
            prev.indexOf(str) == -1 && prev.push(str)
            return prev
          }, [])

          this.symptomsTags = result.map((item) => {
            return JSON.parse(item)
          })
        }

        this.symptomsDlgVisible = false
      } else {
        this.$message.error("请选择症状")
      }
    },

    // 处理table列表选中数据
    handleCheckedSymptoms(data) {
      let arr = data.map((item) => {
        let { symptomId, diseaseId, sectionOfficeId } = item
        let treatName = `${item.symptomName}-${item.diseaseName}-${item.sectionOffice}`
        return {
          treatId: item.id,
          treatName,
          department: sectionOfficeId,
          diseases: diseaseId,
          symptom: symptomId,
        }
      })
      return arr
    },

    // 移除选中症状标签
    handleCloseTag(tag) {
      // 校验
      let result = this.drugsGuideTableData.findIndex((item) => item.treatId == tag.treatId)
      if (result == -1) {
        this.symptomsTags = this.symptomsTags.filter((item) => {
          return item.treatId !== tag.treatId
        })
      } else {
        this.$message.error("请先删除列表数据，再操作")
      }
    },

    // 打开用药指导对话框
    openDrugsGuideDlg(type) {
      // 操作类型复制
      this.operType = type
      // 先判断是否已选治疗症状
      if (this.symptomsTags.length) {
        this.options.diseaseSymptom = this.symptomsTags
        // console.log(this.drugsGuideForm, type);
        if (type == "修改" || type == "复制") {
          this.drugsGuideForm = Object.assign({}, this.currentRow)
          this.drugsGuideForm.medicationCrowd = this.drugsGuideForm.medicationCrowd
            ? this.drugsGuideForm.medicationCrowd.toString()
            : this.drugsGuideForm.medicationCrowd
        } else {
          this.drugsGuideForm = Object.assign({}, this.drugsGuideForm)
        }
        this.drugsGuideDlgVisible = true
      } else {
        this.$message.error("请先选择治疗症状")
      }
    },

    // 关闭用药指导对话框
    closeDrugsGuideDlg() {
      this.drugsGuideForm = {
        treatId: "", // 病种/症状
        treatName: "",
        treatCourse: "", // 疗程
        usageDay: "", // 使用天数
        usageDosage: "", // 用法用量
        usageNote: "", // 使用注意
        takeCycle: "", // 服用周期
        forgetTake: "", // 忘记服用
        overdoseTake: "", // 过量服用
        usageFrequency: "", // 使用次数
        usageMethod: "", // 使用方法
        precautionsTaboos: "", // 注意事项和禁忌
        pharmacistSuggestions: "", // 药师建议
        lifeAdvice: "", // 生活建议
        medicationCrowd: "", //人群
        medicationMethod: "", //用药方法
        medicationFrequency: "", // 用药频次
        medicationDosage: "", //用量
        miniUnit: "", //最小使用单位id
        minimumUnit: "", //最小使用单位text
      }
      // this.$refs["drugsGuideForm"].resetFields();
      // this.drugsGuideForm.treatName = ""; // 不属于el-form 绑定字段 手动清空
    },

    // 处理key-val
    handleTreatName(val) {
      let result = this.options.diseaseSymptom.filter((item) => {
        return item.treatId == val
      })
      this.drugsGuideForm.treatName = result[0].treatName
    },

    // 保存用药指导数据
    saveDrugsGuideData() {
      // 校验 请至少填写一个字段
      let arr = Object.values(this.drugsGuideForm).filter((item) => {
        return item != ""
      })
      if (arr.length == 0) {
        this.$message.error("请至少填写一个字段")
        return
      }
      // console.log(this.drugsGuideForm, this.drugsGuideTableData);
      //判断当前数据是否已在列表存在
      let result = this.objComparator(
        this.drugsGuideForm, // 当前数据obj
        this.drugsGuideTableData // 列表数据arr
      )
      if (result.includes(true)) {
        let i = result.indexOf(true)
        this.$message.error(`用药指导信息不能重复：与（第${i + 1}行）重复`)
      } else {
        //console.log(this.drugsGuideForm._XID, this.operType);
        if (this.operType == "新增" || this.operType == "复制") {
          delete this.drugsGuideForm._XID
          let obj = JSON.stringify(this.drugsGuideForm)
          this.drugsGuideTableData.push(JSON.parse(obj))
        } else if (this.operType == "修改") {
          let index = this.drugsGuideTableData.findIndex((el) => el._XID == this.drugsGuideForm._XID)
          // console.log(index, "得到编辑的索引位置");
          this.$set(this.drugsGuideTableData, index, Object.assign({}, this.drugsGuideForm))
          this.currentRow = null
          this.$refs.drgTable.clearCurrentRow() // 取消选中
        }
        // console.log(this.drugsGuideTableData, "---列表数据");
        this.drugsGuideDlgVisible = false
      }
    },

    // 对象-数组包含关系
    objComparator(obj, original) {
      let currObj = _.cloneDeep(obj)
      let originalArr = _.cloneDeep(original)
      let resultArr = []
      delete currObj._XID
      originalArr.forEach((item) => {
        delete item._XID
        let res = _.isEqual(currObj, item)
        resultArr.push(res)
      })
      // return resultArr.includes(true) ? "重复" : "不重复";
      // console.log(resultArr, "---对比结果");
      return resultArr
    },

    // 行选中事件
    currentChangeEvent({ row }) {
      // 选中console.log(row, "行选中事件");
      this.currentRow = row
    },

    //操作行
    operationRow(type) {
      if (this.currentRow || type == "新增") {
        switch (type) {
          case "删除":
            this.delRow()
            break
          default:
            this.openDrugsGuideDlg(type)
            break
        }
      } else {
        this.$message.error(`请选择一行${type}`)
      }
    },

    // 删除
    delRow() {
      this.$confirm("是否确认删除", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // console.log(this.currentRow._XID,"删除行索引");
          let index = this.drugsGuideTableData.findIndex((el) => el._XID == this.currentRow._XID)
          this.drugsGuideTableData.splice(index, 1)
          this.currentRow = null
          this.$message.success("删除成功")
        })
        .catch(() => {})
    },

    // 输入自定义验证
    verifyInput(value, name) {
      if (!InputRules[name].pattern.test(value)) {
        this.$message.error(InputRules[name].msg)
        return true
      }
    },

    // 每次服用数量支出分数输入 1.4.1V
    verifyNumberIncludesFraction(value) {
      // console.log(value);
      if (
        !/^(0|0\.[0-9]|[1-9]\d{0,3}|[1-9]\d{0,3}\.\d|[1-9]\d{0,3}\.\d{2}|0\.0[1-9]|0\.[1-9]\d?|[1-9]\d{0,3})$|^(\d{1,3}\/\d{1,3}$)/.test(value)
      ) {
        this.$message.error("只能输入数字或分数（小数不超过2位，数值不超过9999.99，分子分母均不超过3位）")
        return true
      }
    },
    /**
     * @description: 查找扩展属性修改变动的key
     * @param {object} newValue 扩展属性 this.form 对象
     * @return:
     */
    findDiffExtendedAttr(newValue) {
      // console.log(newValue, this._initializedForm)
      if (
        this.operationType == "edit" ||
        this.operationType == "operate" ||
        // 审核及审核驳回修改时需要判断是否为修改商品
        (this.operationType == "auditLevel1" && this.$route.query.approvalProcess == 1) ||
        (this.operationType == "auditLevel2" && this.$route.query.approvalProcess == 1) ||
        // 判断是否为商品上架状态修改
        (this.operationType == "auditLevel2" && this.$route.query.type == "shelves") ||
        (this.operationType == "RejectEdit" && this.$route.query.approvalProcess == 1)
      ) {
        for (let key in newValue) {
          // initializedSpuModel 初始化后备份的SPU数据对象
          let res = _.isEqual(newValue[key], this._initializedForm[key])
          if (!res && !this.changeList.includes(key)) {
            this.changeList.push(key)
          }
        }
      }
    },
    getMiniUnitTitel(value) {
      for (let { dictName, id } of this.options.minimumUnit) {
        if (id == value) {
          this.form.minimumUnit = dictName
        }
      }
    },
    drugsGuideFormGetMiniUnitTitel(value) {
      for (let { dictName, id } of this.options.minimumUnit) {
        if (id == value) {
          this.drugsGuideForm.minimumUnit = dictName
        }
      }
    },
    usageDosageInit(row){
      const medicationMethod = this.options.medicMethod.find(item => {
        return item.id == row.row.medicationMethod
      })
      const medicFrequency = this.options.medicFrequency.find(item => {
        return item.id == row.row.medicationFrequency
      })
      if(!medicationMethod || !medicFrequency){
        return '';
      }
      let text = medicationMethod.dictName + medicFrequency.dictName+'一次'+row.row.medicationDosage+row.row.minimumUnit;
      return text
    },
    // 复制到剪贴板
    copyToClipboard(text, fieldName) {
      if (!text || text.trim() === '') {
        this.$message.warning(`${fieldName}内容为空，无法复制`)
        return
      }

      // 创建临时文本域元素
      const textarea = document.createElement('textarea')
      textarea.value = text
      document.body.appendChild(textarea)
      textarea.select()

      try {
        // 执行复制命令
        const successful = document.execCommand('copy')
        if (successful) {
          this.$message.success(`${fieldName}内容已复制到剪贴板`)
        } else {
          this.$message.error('复制失败，请手动复制')
        }
      } catch (err) {
        this.$message.error('复制失败，请手动复制')
      } finally {
        // 清理临时元素
        document.body.removeChild(textarea)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.component-container {
  padding-bottom: 80px;
  .basic-info-title {
    font-weight: 600;
    margin-bottom: 22px;
    border-bottom: 1px solid #e4e4eb;
    line-height: 50px;
    padding-left: 20px;
  }
  .el-form-item {
    &.nMr {
      /deep/ .el-form-item__content {
        margin-left: 0 !important;
        .el-select {
          width: 100% !important;
        }
      }
    }
    &.width100 {
      /deep/ .el-select {
        width: 100% !important;
      }
      /deep/ .el-input {
        width: 100% !important;
      }
    }
    /deep/ {
      .el-form-item__label {
        color: #292933;
        font-weight: normal;
      }
    }
  }
  .is-change {
    /deep/ {
      // 调整修改项lable样式
      .el-form-item__label {
        color: #f56c6c;
        font-weight: bold;
      }
    }
  }
  .border-bottom-dashed {
    border-bottom: 1px dashed #e4e4eb;
  }

  .title-container{
    display: flex;
    justify-content: start;
    align-items: center;
  }

  .detail-title {
    margin: 20px;
    font-size: 16px;
    color: #292933;
  }

  .martop0 {
    margin-top: 0;
  }
  .marbot20 {
    margin-bottom: 20px;
  }
  .martop20 {
    margin-top: 20px;
  }

  .flex-text {
    display: flex;
    color: #292933;
    font-weight: normal;
    margin: 20px 0 10px 0;
    > span:first-child {
      width: 130px;
      text-align: right;
    }
    > span:last-child {
      flex: 1;
    }
  }
  .el-dialog__wrapper /deep/ .el-dialog {
    top: 50% !important;
  }
  .el-dialog__wrapper.drugs-guide /deep/ .el-dialog .el-dialog__body {
    padding: 0;
  }
  .el-dialog__wrapper.drugs-guide /deep/ .el-dialog .el-dialog__footer {
    padding: 10px;
  }
  .drugs-guide-content {
    height: 600px;
    overflow-y: auto;
    padding: 20px 20px 0 20px;
  }
}
.usageDosage-class{
  display: flex;
  flex-direction: row;
}

// 自定义label样式 - 垂直布局
.label-with-copy-vertical {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;

  > span {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
  }

  .copy-btn-below {
    // 确保按钮不影响表单验证样式
    padding: 0 !important;
    margin: 0 !important;
    border: none !important;
    background: none !important;
    line-height: 1 !important;
    cursor: pointer;
    color: #4A95A9;
    &:focus {
      outline: none;
    }
  }
}

// 修复必填标识与label文本对齐问题
.el-form-item {
  // 针对使用自定义label模板的表单项
  &.is-required /deep/ .el-form-item__label {
    // 对于包含自定义label模板的情况，隐藏默认必填标识
    &:has(.label-with-copy-vertical)::before {
      display: none !important;
    }

    // 在自定义label的span前添加必填标识
    .label-with-copy-vertical > span::before {
      content: "*";
      color: #F56C6C;
      margin-right: 4px;
      display: inline-block;
      vertical-align: middle;
      font-weight: normal;
    }

    // 确保默认必填标识正确显示（针对普通label）
    &::before {
      display: inline-block !important;
      vertical-align: middle !important;
      margin-right: 4px !important;
      line-height: 1 !important;
    }
  }
}

// 兼容性更好的解决方案：为自定义label添加特定类名
.el-form-item.has-custom-label.is-required /deep/ .el-form-item__label {
  &::before {
    display: none !important;
  }
}

// .bdTop {
//   border-top: 1px dashed #e4e4eb;
//   padding: 20px;
//   font-size: 16px;
//   font-weight: bold;
//   color: #292933;
// }
</style>