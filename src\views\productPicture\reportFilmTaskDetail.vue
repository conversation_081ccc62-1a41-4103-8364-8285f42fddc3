<template>
  <div class="detail-container">
    <el-form :model="form" ref="form" label-width="130px">
      <el-row class="border-bottom-dashed">
        <el-col :span="24">
          <h4 class="detail-title">申请信息</h4>
        </el-col>

        <!-- 申请时间 -->
        <el-col :span="6">
          <el-form-item label="申请时间" prop="createTime">
            <el-input v-model="form.createTime" :disabled="true"></el-input>
          </el-form-item>
        </el-col>

        <!-- 单据编号 -->
        <el-col :span="6">
          <el-form-item label="单据编号" prop="billNo">
            <el-input v-model="form.billNo" :disabled="true"></el-input>
          </el-form-item>
        </el-col>

        <!-- 申请人 -->
        <el-col :span="6">
          <el-form-item label="申请人" prop="applicant">
            <el-input v-model="form.applicant" :disabled="true"></el-input>
          </el-form-item>
        </el-col>

        <!-- 申请人所属机构 -->
        <el-col :span="6">
          <el-form-item label="申请人所属机构" prop="applicantOrganization">
            <el-input v-model="form.applicantOrganization" :disabled="true"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <h4 class="detail-title">商品明细</h4>
        </el-col>

        <el-col :span="24" class="detail-query-condition">
          <!-- 查询条件 -->
          <span class="child">
            <el-checkbox v-model="query.isZero">只看图片版本数量不为0的商品</el-checkbox>
          </span>

          <!-- 查询条件 -->
          <span class="child">
            <el-input v-model="query.name" placeholder="商品名称/小包装条码/批准文号" style="width:100%"></el-input>
          </span>

          <!-- 查询条件 -->
          <span class="child">
            <el-button type="primary" size="medium" @click="queryList">查询</el-button>
          </span>
        </el-col>
      </el-row>

      <div class="table-wrap">
        <vxe-table
          border
          highlight-hover-row
          resizable
          min-height="500px"
          align="center"
          :tooltip-config="{enterable: false}"
          :data="tableData"
          :loading="tableLoading"
          ref="refVxeTable"
        >
          <vxe-table-column
            type="index"
            title="序号"
            width="60"
            show-header-overflow
            show-overflow
            fixed="left"
          ></vxe-table-column>

          <vxe-table-column
            field="historyVersionNum"
            title="图片版本数量"
            min-width="130"
            show-header-overflow
            show-overflow
            sortable
          ></vxe-table-column>

          <vxe-table-column
            type="seq"
            field="historyVersionDetailPicture"
            title="图片预览"
            min-width="100"
            show-header-overflow
            show-overflow
          >
            <template v-slot="{ row, seq }">
              <preview-img :row-data="{ row, seq }" :show-status="true" field="historyVersionDetailPicture" />
            </template>
          </vxe-table-column>

          <vxe-table-column
            field="productName"
            title="商品信息"
            min-width="300"
            show-header-overflow
            show-overflow
          >
            <template v-slot="{row}">
              <div>
                <div>{{ row.productName }}</div>
                <div>{{ row.spec }}</div>
                <div>{{ row.manufacturer }}</div>
              </div>
            </template>
          </vxe-table-column>

          <vxe-table-column
            field="brand"
            title="商品信息"
            min-width="200"
            show-header-overflow
            show-overflow
          >
            <template v-slot="{row}">
              <div>
                <div>{{ row.smallPackageCode }}</div>
                <div>{{ row.approvalNo }}</div>
                <div>{{ row.brand }}</div>
              </div>
            </template>
          </vxe-table-column>

          <vxe-table-column
            field="productCode"
            title="商品编码"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>

          <vxe-table-column
            field="productId"
            title="商品id"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>

          <vxe-table-column
            field="originalProductCode"
            title="原商品编码"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>

          <vxe-table-column
            field="reasonStatus"
            title="拍摄原因"
            min-width="150"
            show-header-overflow
            show-overflow
          >
            <!-- 拍摄原因(1:新品上架, 2:设计驳回, 3:原图半驳, 4:运营驳回, 5:精修图半驳 , 6:更换新老包装, 7:新品再上架) -->
            <template
              v-slot="{ row }"
            >{{ ['','新品上架','设计驳回', '原图半驳','运营驳回', '精修图半驳','更换新老包装','新品再上架'][row.reasonStatus] }}</template>
          </vxe-table-column>

          <vxe-table-column
            field="pictureDetailVersion"
            title="绑定图片版本"
            min-width="100"
            show-header-overflow
            show-overflow
          ></vxe-table-column>

          <vxe-table-column
            field="originalPictureNum"
            title="原图上传数量"
            min-width="130"
            show-header-overflow
            show-overflow
            sortable
          >
            <template v-slot="{ row, seq }">
              <preview-img
                type="slot"
                :row-data="{ row, seq }"
                field="currentVersionOriginalPicture"
              >
                <el-button
                  type="text"
                  :disabled="!row.originalPictureNum"
                >{{ row.originalPictureNum }}</el-button>
              </preview-img>
            </template>
          </vxe-table-column>

          <vxe-table-column
            field="detailPictureNum"
            title="精修图上传数量"
            min-width="130"
            show-header-overflow
            show-overflow
            sortable
          >
            <template v-slot="{ row, seq }">
              <preview-img type="slot" :row-data="{ row, seq }" field="currentVersionDetailPicture">
                <el-button type="text" :disabled="!row.detailPictureNum">{{ row.detailPictureNum }}</el-button>
              </preview-img>
            </template>
          </vxe-table-column>

          <vxe-table-column
            field="detailRejectPictureNum"
            title="运营删除图片数量"
            min-width="130"
            show-header-overflow
            show-overflow
          ></vxe-table-column>

          <!-- <vxe-table-column
            field="detailRejectPictureNum"
            title="精修图删除数量"
            min-width="130"
            show-header-overflow
            show-overflow
          ></vxe-table-column> -->

          <!-- 1.5.3 拍摄任务单报表明细 审核状态 -->
          <vxe-table-column
            field="auditStatus"
            title="审核状态"
            min-width="100"
            show-header-overflow
            show-overflow
          >
            <template v-slot="{ row }">
              <!-- 审核状态(0:待审核 1:通过 其他:不通过) -->
              <!-- <span v-if="row.auditStatus == 1">审核通过</span>
              <span v-else-if="row.auditStatus == 0">待审核</span>
              <span v-else>审核不通过</span> -->
                <!-- INIT((byte) 0, "待审核"),

                SUCCESS((byte) 1, "通过"),

                REJECT_ORIGINAL_ALL((byte) 2, "原图全驳"),

                REJECT_ORIGINAL_HALF((byte) 3, "原图半驳"),

                REJECT_DETAIL_ALL((byte) 4, "精修图全驳"),

                REJECT_DETAIL_HALF((byte) 5, "精修图半驳"),

                DELETE_ORIGINAL_ALL((byte) 6, "原图全删"),

                UN_AUDIT_SUCCESS((byte) 7, "提交前的通过"),

                UN_AUDIT_FAIL((byte) 8, "提交前的不通过"),

                PRE_AUDIT_INIT((byte) 10, "待预审核"),

                PRE_AUDIT_SUCCESS((byte) 11, "预审核通过"),

                PRE_AUDIT_FAIL((byte) 12, "预审核不通过"); -->
               {{["待审核","审核通过","设计驳回","原图半驳","运营驳回","精修图半驳","原图全删","","","转精修","","预审核通过","预审核不通过"][row.auditStatus]}}
            </template>
          </vxe-table-column>

          <vxe-table-column field title="驳回原因" min-width="130" show-header-overflow show-overflow>
            <template v-slot="{ row }">
              <!-- {{[ row.originalRejectReason,row.detailRejectReason].join(';')}} -->
              {{formatReason(row)}}
              <!-- {{ row.originalRejectReason }}
              <br />
              {{ row.detailRejectReason }} -->
            </template>
          </vxe-table-column>
        </vxe-table>
      </div>
    </el-form>
  </div>
</template>

<script>
import { getProductDetail } from "../../api/productPicture";

import previewImg from "@/components/uploadImg/preview";
import { parseTimestamp } from "@/utils";

export default {
  name: "",
  components: { previewImg },
  filters: {},
  props: {},
  created() {
    // 初始 数据
    this.queryList();
  },
  data() {
    return {
      tableLoading:false,
      form: {
        createTime: "",
        billNo: "",
        applicant: "",
        applicantOrganization: "",
      },
      query: {
        isZero: "",
        name: "",
      },
      tableData: [],
    };
  },
  computed: {
    applyCode() {
      return this.$route.query.applyCode;
    },
    applyStatus() {
      return this.$route.query.applyStatus;
    },
  },
  watch: {},
  mounted() {},
  methods: {
    // 获取任务单明细商品 - 数据、赋值
    getList(params) {
      console.log(params, "任务单报表明细--查询参数");
      this.tableLoading=true;
      getProductDetail(params).then((result) => {
        this.tableLoading=false;
        console.log(result, "任务单报表明细--返回数据");
        this.tableData = result;
        // console.log(this.tableData[0], "第一条数据----");
        let {
          createUser,
          applyCode,
          createUserMechanism,
          createTime,
        } = this.tableData[0];

        this.form = {
          createTime: parseTimestamp(createTime),
          billNo: applyCode,
          applicant: createUser,
          applicantOrganization: createUserMechanism,
        };
      });
    },
    // 条件 查询
    queryList() {
      let params = {
        applyCode: this.applyCode, // 单据编号
        productStatus: 5, //商品状态码(0:图片拍摄中1:原图已上传 2:图片精修中 3:精修图已上传 4:精修图审核中 5:审核完毕)
        haveVersion: this.query.isZero, // // true ? 1 : 0 图片版本数量是否为零的商品
        multipleInfo: this.query.name, // 混合查询字段
      };
      this.getList(params);
    },
    formatReason(row){
      // {{row.originalRejectReason ? row.originalRejectReason+";"+row.detailRejectReason : row.detailRejectReason}}
      if(row.originalRejectReason && row.detailRejectReason){
        return row.originalRejectReason + ";" + row.detailRejectReason
      }else if(row.originalRejectReason && !row.detailRejectReason){
        return row.originalRejectReason
      }else{
        return row.detailRejectReason
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.detail-container {
  padding: 20px;

  .el-form-item {
    /deep/ {
      .el-form-item__label {
        color: #292933;
        font-weight: normal;
      }
    }
  }

  .border-bottom-dashed {
    border-bottom: 1px dashed #e4e4eb;
  }

  .detail-title {
    margin: 20px;
    font-size: 16px;
    color: #292933;
  }
  .detail-query-condition {
    display: flex;
    line-height: 40px;
    margin-bottom: 15px;
    .child {
      margin-right: 10px;
    }
    .child:nth-child(2) {
      width: 400px;
    }
  }
}
</style>
