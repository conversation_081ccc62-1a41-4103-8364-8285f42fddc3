<template>
  <div class="container">
    <div class="productInfo">
      <div class="tabTitle">商品信息</div> 
      <div class="product-content">
        <vxe-table
          border
          highlight-hover-row
          auto-resize
          width="720px"
          resizable
          align="center"
          :data="productInfo"
          ref="table"
        >
          <vxe-table-column
            field="productId"
            title="标准库id"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="productCode"
            title="商品编码"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="originalProductCode"
            title="原商品编码"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="generalName"
            title="通用名"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="spuCategoryName"
            title="商品大类"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="manufacturerName"
            title="生产厂家"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="spec"
            title="规格型号"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="approvalNo"
            title="批准文号"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="smallPackageCode"
            title="小包装条码"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
        </vxe-table>
      </div>
    </div>
    <div
      class="partition-content"
      style="height: 20px; background: #f0f2f5; padding-left: -20px"
    ></div>
    <div class="disposeInfo">
      <div class="tabTitle">
        处理信息
        <el-button
          type="primary"
          size="mini"
          @click="handleAllPic(1)"
          :disabled="isMorePic"
          >获取所有图片</el-button>
          <el-button @click="openSearchDialog" size="mini" type="primary" style="margin-left:10px">搜索友商库</el-button>
      </div>
      <div class="dispose-content">
        <el-form
          :model="disposeForm"
          ref="disposeForm"
          :rules="rules"
          label-position="top"
        >
          <el-row>
            <!-- <el-col
              ><drag-img
                ref="dragImg"
                :imgList="imgList"
                :productInfo="productInfo[0]"
                :morePicList="morePicList"
                @changeNum="changeNum"
              ></drag-img
            ></el-col> -->
            <el-col
              ><drag-img
              ref="dragImg"
              :productInfo="productInfo[0]"
              :imgList="imgList"
              :morePicList="morePicList"
              :friendPhotoList="morePicList"
              :applyProductDispose='true'
            ></drag-img></el-col>
            <el-col>
              <el-form-item
                label="是否使用渠道上传图片"
                prop="usePictureStatus"
              >
                <el-radio-group v-model="disposeForm.usePictureStatus">
                  <el-radio :label="1"
                    >是</el-radio
                  >
                  <el-radio :label="2">否，并且上传图片</el-radio>
                  <el-radio :label="3">{{$route.query.page ? '否，并新建拍摄任务' : '否，并直接结束任务'}}</el-radio>否，不上传图片
                  <el-radio v-if="$route.query.page" :label="4">否，并直接结束任务</el-radio>
                </el-radio-group>
                 <div v-if="disposeForm.usePictureStatus == 2" class="usePictureStatus-tip">
                  注意：请将要保存的商品拖至上方，提交的为上方所有的图片！
                 </div>
                 <div v-else-if="disposeForm.usePictureStatus == 3" class="usePictureStatus-tip">
                  {{$route.query.page
                    ? "注意：选择该按钮，渠道上传的图片将被驳回，并且自动向百草发送拍图任务。"
                    : "注意：选择该按钮，渠道上传图片将被驳回，商品任务直接结束。"}}
                 </div>
                 <div v-else-if="disposeForm.usePictureStatus == 4" class="usePictureStatus-tip">
                  注意，选择该按钮，将直接结束拍图任务。
                 </div>
              </el-form-item>
            </el-col>
            <el-col v-if="disposeForm.usePictureStatus == 3 || disposeForm.usePictureStatus == 4">
              <el-form-item
                label="请选择驳回原因"
                prop="type"
                class="checkbox-wrap"
              >
                <el-checkbox-group
                  v-model="disposeForm.type"
                  @change="changeReasonType"
                >
                  <el-checkbox :label="1">商品图片与资料不符</el-checkbox>
                  <el-checkbox :label="2">主图是背面图</el-checkbox>
                  <el-checkbox :label="3">图片规格与资料不符</el-checkbox>
                  <el-checkbox :label="4">图片品牌与资料不符</el-checkbox>
                  <el-checkbox :label="5">图片资料不全</el-checkbox>
                  <el-checkbox :label="6">图片不清晰</el-checkbox>
                  <el-checkbox :label="0">自定义内容</el-checkbox>
                  <el-form-item
                    class="reason-wrap"
                    label=""
                    prop="reason"
                    :rules="reasonRule"
                  >
                    <el-input :disabled="banCustom" class="reason" v-model="disposeForm.reason" />
                  </el-form-item>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col v-if="disposeForm.usePictureStatus == 2">
              <el-form-item label="文件名称" prop="fileName">
                <el-input
                  v-model="disposeForm.fileName"
                  disabled=""
                  placeholder="请通过右侧按钮选择文件"
                >
                  <el-upload
                    slot="append"
                    class="upload-wrapper"
                    ref="refUpload"
                    :action="no"
                    :data="disposeForm"
                    :multiple="true"
                    accept=".jpg,.jpeg,.png,.gif,.bmp,.JPG,.JPEG,.PBG,.GIF,.BMP"
                    :show-file-list="false"
                    :auto-upload="false"
                    :on-change="uploadImage"
                  >
                    选择文件
                    <el-popover
                      placement="top-start"
                      title="提示"
                      width="300"
                      trigger="hover"
                    >
                      <span class="popover-content">
                        1、图片名称格式：商品编码+“-”+数字。<br />
                        2、图片支持jpg、png、jpeg、bmp格式，不支持pdf或其他非图片的文件格式。<br />
                      </span>
                      <i
                        class="el-icon-question el-icon--right icon-question"
                        slot="reference"
                      ></i>
                    </el-popover>
                  </el-upload>
                </el-input>
              </el-form-item>
              <el-form-item
                label="商品识别方式"
                prop="productRecognizeMethod"
                class=""
              >
                <el-radio-group v-model="disposeForm.productRecognizeMethod">
                  <!-- <el-radio :label="1">标准库id</el-radio> -->
                  <el-radio :label="2">商品编码</el-radio>
                  <!-- <el-radio :label="3">原商品编码</el-radio> -->
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col v-if="disposeForm.usePictureStatus == 2">
              <drag-img
                ref="dragBase64Img"
                :imgList="productPictureBase64List"
                :productInfo="productInfo[0]"
                :morePicList="[]"
              ></drag-img>
            </el-col>
            <el-col>
              <el-form-item label="图片质量" prop="pictureQualityStatus">
                <el-radio-group v-model="disposeForm.pictureQualityStatus">
                  <el-radio :label="1">需设计精修</el-radio>
                  <el-radio :label="2">可直接使用</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <div class="bottomBtn">
      <el-button type="" @click="handleBack" :disabled="btnDis">返回</el-button>
      <el-button type="primary" @click="submit" :disabled="btnDis"
        >提交</el-button
      >
    </div>
    <search-friend ref='searchfriend' @getSearchList="getSearchList"></search-friend>
  </div>
</template>
<script>
import searchFriend from "@/components/searchFriend"
import {
  getProductSupplementPictureDetail,
  productSupplementPictureReview,
  getAlternativePicture,
  pictureRetouch,
} from "@/api/productPicture.js";
import { getTempKey } from "@/api/follow.js"
import dragImg from "./dragImg";
const JSZip = require("jszip");
var COS = require("cos-js-sdk-v5")
export default {
  name: "applyProductDispose",
  components: { dragImg, searchFriend },
  data() {
    return {
      tempKey: {},
      cos: null,
      banCustom: true,
      moreListNum:null,
      firstmoreListNum:0,
      disposeForm: {
        usePictureStatus: "",
        pictureQualityStatus: 1,
        fileName: "",
        type: [],
        productRecognizeMethod: 2,
        reason:'',
      },
      rules: {
        pictureQualityStatus: [
          { required: true, message: "请选择图片质量", trigger: "change" },
        ],
        // fileName: [
        //   {
        //     required: true,
        //     message: "请先选择需要上传的文件",
        //     trigger: "change",
        //   },
        // ],
        usePictureStatus: [
          {
            required: true,
            message: "请选择是否使用渠道上传图片",
            trigger: "change",
          },
        ],
        type: [
          {
            required: true,
            message: "请选择驳回原因",
            trigger: "change",
          },
        ],
        reason: [
          {
            required: false,
            message: "请输入自定义原因",
            trigger: "blur",
          },
        ],
      },
      imgList: [],
      productPictureBase64List: [],
      productInfo: [],
      btnDis: false,
      isMorePic: false,
      morePicList: [],
    };
  },
  watch:{
    'disposeForm.type'(val) {
      if(val.indexOf(0) != -1) {
        this.banCustom = false
      } else {
        this.banCustom = true
        this.disposeForm.reason = ''
      }
    }
  },
  created() {
    getProductSupplementPictureDetail(this.$route.query.id).then((res) => {
      if (!res.retCode) {
        this.imgList = res.data ? res.data.productPictureList : [];
        this.productInfo.push(res.data);
        this.getListNum()
      } else {
        this.$message.error(res.retMsg);
      }
    });
    this.getTempKeyApi()
  },
  methods: {
    // 打开搜索友商库弹框
    openSearchDialog() {
      let selectedImageUrlList = []
      this.imgList.map(item => {
        if(item.sourcePictureUrl) {
          selectedImageUrlList.push(item.sourcePictureUrl)
        }
      })
      this.morePicList.map(item => {
        item.friendLibraryPictureDtos.map(item1 => {
          if(item1.sourcePictureUrl) {
            selectedImageUrlList.push(item1.sourcePictureUrl)
          }
        })
      })
      this.$refs.searchfriend.openDialog(this.productInfo[0], selectedImageUrlList)
    },
    // 获取搜索友商图片
    getSearchList(e) {
      console.log(e);
      this.morePicList = []
      let item = {
        sourceSiteName: '搜索友商库',
        friendLibraryPictureDtos: e,
        isToFinishing:0
      }
      this.handleAllPic(1, item)
    },
    changeNum(e) {
      this.moreListNum = e
    },

    uploadImage(file){
      const that = this;
      console.log('uploadfile changed', file)
      // this.productPictureBase64List = [];
      this.disposeForm.fileName = "";
      if(file.name.endsWith('.zip')){
        this.uploadOnChange(file)
      }else if(/\.(png|jpg|jpeg|bmp)$/.test(file.name)){
        // let base = zip.file(zip.files[key].name).async("base64"); // 将图片转化为base64格式
        let reader = new FileReader()
        reader.readAsDataURL(file.raw)
        reader.onload = ()=>{
          let img = new Image();
          img.src = reader.result.toString();
          img.onload = () => {
            let fileblobList = [];
            fileblobList.push({
              Bucket: that.tempKey.bucket,
              Region: that.tempKey.region,
              Key: `BMP/product/${file.name}`,
              Body: file.raw,
            })
            that.cos.uploadFiles(
                {
                  files: fileblobList,
                  SliceSize: 1024 * 1024 * 5 /* 触发分块上传的阈值，超过5MB使用分块上传，非必须 */,
                  onFileFinish: function (err, data, options) {
                    console.log(options.Key + " 上传" + (err ? "失败" : "完成"))
                  },
                },
                function (err, data) {
                  console.log("uploadFiles:", err || data)
                  if (err) {
                    that.$alert(`上传文件失败，错误 ${err}`, "错误", {
                      confirmButtonText: "确定",
                    })
                      .then(() => {})
                      .catch(() => {})
                  } else if (data && data.files) {
                    debugger
                    data.files.forEach((file) => {
                      that.productPictureBase64List.push({
                        pictureName: file.options.Key.split('/').reverse()[0],
                        pictureUrl:  window.tempKey.host +'/'+ file.options.Key,
                        pictureOrdinal:
                          file.options.Key
                            .split("-")[1]
                            .split(".")[0] * 1,
                        deleteStatus: 0,
                        pictureWidth: img.width,
                        pictureHeight: img.height,
                      })
                    })
                  }
                }
              )

            // this.productPictureBase64List.push({
            //   pictureName: file.name,
            //   pictureUrl:  img.src,//`data:image/png;base64,${res}`,
            //   pictureOrdinal:
            //     file.name
            //       .split("-")[1]
            //       .split(".")[0] * 1,
            //   deleteStatus: 0,
            //   pictureWidth: img.width,
            //   pictureHeight: img.height,
            // });
          };
        }
        reader.onerror = ()=>{
          //忽略
        }
      }else {
        this.$message.error("上传图片只能是jpg，png，jpeg，bmp格式！");
      }
    },

    // zip上传
    uploadOnChange(file) {
      // this.disposeForm.fileName = file.name;
      // this.productPictureBase64List = [];
      const jszip = new JSZip();
      jszip.loadAsync(file.raw).then((zip) => {
        // 读取zip
        for (let key in zip.files) {
          // 判断是否是目录
          if (!zip.files[key].dir) {
            if (/\.(png|jpg|jpeg|bmp)$/.test(zip.files[key].name)) {
              // 判断是否是图片格式
              let base = zip.file(zip.files[key].name).async("base64"); // 将图片转化为base64格式
              base.then((res) => {
                let img = new Image();
                img.src = `data:image/png;base64,${res}`;
                img.onload = () => {
                  this.productPictureBase64List.push({
                    pictureName: zip.files[key].name.split("/")[1],
                    pictureUrl: `data:image/png;base64,${res}`,
                    pictureOrdinal:
                      zip.files[key].name
                        .split("/")[1]
                        .split("-")[1]
                        .split(".")[0] * 1,
                    deleteStatus: 0,
                    pictureWidth: img.width,
                    pictureHeight: img.height,
                  });
                };
              });
            } else {
              this.$message.error("上传图片只能是jpg，png，jpeg，bmp格式！");
              // break
            }
          }
        }
      });
    },
    async submit() {
      const that = this;
      this.$refs.disposeForm.validate(async (valid) => {
        if (valid) {
          let obj = {};
          debugger

          if (this.disposeForm.usePictureStatus == 3 || this.disposeForm.usePictureStatus == 4) {
            debugger
            this.productSupplementPictureReview(2);
          } else {
            if (this.disposeForm.usePictureStatus == 2) {
              let arr = [
                { des: "标准库id", key: "productId" },
                { des: "商品编码", key: "productCode" },
                { des: "原商品编码", key: "originalProductCode" },
              ];
              debugger
              for (let item of that.imgList) { 
              // for (let item of that.productPictureBase64List) {
                // 根据运营要求去掉名称判断
                // if (
                //   item.pictureName.split("-")[0].trim() !=
                //   that.productInfo[0][
                //     arr[that.disposeForm.productRecognizeMethod - 1].key
                //   ].trim() && item.deleteStatus === 0 
                // ) {
                //   this.$message.error(
                //     `图片命名方式为:'${
                //       arr[this.disposeForm.productRecognizeMethod - 1].des
                //     }' + '-' + '序号',错误名称为：${item.pictureName}`
                //   );
                //   return;
                // }
                if(this.disposeForm.pictureQualityStatus == 2) {
                  // 图片直接使用需要校验宽度800 精修不需要
                  if (item.pictureOrdinal == 1) {
                    if (item.pictureWidth < 800 || item.pictureHeight < 800) {
                      this.$message.error(
                        `主图必须宽高都大于800px（含），错误名称为：${item.pictureName}`
                      );
                      return;
                    }
                  }
                  if (item.pictureOrdinal >= 2 && item.pictureOrdinal <= 5) {
                    if (item.pictureWidth < 800) {
                      this.$message.error(
                        `外包装图片必须宽大于800px（含），错误名称为：${item.pictureName}`
                      );
                      return;
                    }
                  }
                  if (item.pictureOrdinal > 5) {
                    if (item.pictureWidth < 800) {
                      this.$message.error(
                        `说明书图片必须宽大于800px（含），错误名称为：${item.pictureName}`
                      );
                      return;
                    }
                  }
                }
              }
              obj = this.$refs.dragImg.getSubmitData();
              if (!obj.isFlag) {
                return;
              }
            } else {
              obj = this.$refs.dragImg.getSubmitData();
              if (!obj.isFlag) {
                return;
              }
            }
            if (this.disposeForm.pictureQualityStatus == 1 && this.disposeForm.usePictureStatus == 1) {
             let res =  await pictureRetouch({
                id: this.$route.query.id,
                auditStatus:1,
                pictureQualityStatus: this.disposeForm.pictureQualityStatus,
                usePictureStatus: this.disposeForm.usePictureStatus,
                auditOpinion: this.disposeForm.type.join(","),
                auditOpinionCustom: this.disposeForm.reason,
                productRecognizeMethod: this.disposeForm.productRecognizeMethod,
                productPictureList: obj.imgList,
              })
              console.log('----bug resresres');
              console.log(res);
              if (res.retCode == 0) {
                this.$router.push({
                path: "/productPicture/dealphoto",
                query: {
                  page: '1',
                  id: res.data,
                  from: 'selfSupport'
                },
              });    
              }else{
                this.$message.error(res.retMsg);
              }
              
            }else {
              this.productSupplementPictureReview(1, obj.imgList);
            }
          }
        }
      });
    },

    backToList(){
      try {
              parent.CreateTab(
                `../static/dist/index.html#/productPicture/selfApplyProductList`,
                "已申请商品列表",
                true
              );
            } catch {
              this.$router.push({
                path: "/productPicture/selfApplyProductList",
                query: {
                  page: this.$route.query.page,
                  tab: 'second'
                },
              });
            }
      parent.CloseTab(
            "../static/dist/index.html#/productPicture/applyProductDispose" ///productPicture/applyProductDispose
          );
    },
    // 图片审核接口
    productSupplementPictureReview(auditStatus, imgList) {
      this.btnDis = true;
      productSupplementPictureReview({
        id: this.$route.query.id,
        auditStatus,
        pictureQualityStatus: this.disposeForm.pictureQualityStatus,
        usePictureStatus: this.disposeForm.usePictureStatus,
        auditOpinion: this.disposeForm.type.join(","),
        auditOpinionCustom: this.disposeForm.reason,
        productRecognizeMethod: this.disposeForm.productRecognizeMethod,
        productPictureList: imgList,
      }).then((res) => {
        debugger
        this.btnDis = false;
        if (!res.retCode) {
          this.$message.success("操作成功");
          let tab = 'second'
          if (!this.$route.query.page) {
            try {
              parent.CreateTab(
                `../static/dist/index.html#/productPicture/selfApplyProductList`,
                "已申请商品列表",
                true
              );
            } catch {
              this.$router.push({
                path: "/productPicture/selfApplyProductList",
                query: {
                  page: this.$route.query.page,
                  tab: 'second'
                },
              });
            }
          } else {
            try {
              parent.CreateTab(
                `../static/dist/index.html#/productPicture/selfApplyProductList`,
                "已申请商品列表",
                true
              );
            } catch {
              this.$router.push({
                path: "/productPicture/selfApplyProductList",
                query: {
                  page: this.$route.query.page,
                  tab: 'second'
                },
              });
            }
          }
          parent.CloseTab(
            "../static/dist/index.html#/productPicture/applyProductDispose" ///productPicture/applyProductDispose
          );
        } else {
          this.$message.error(res.retMsg);
        }
      });
    },
    handleBack() {
      try {
        parent.CreateTab(
          `../static/dist/index.html#/productPicture/selfApplyProductList`,
          "已申请商品列表",
          true
        );
      } catch {
        this.$router.push({
          path: "/productPicture/selfApplyProductList",
        });
      }
      parent.CloseTab(
        "../static/dist/index.html#/productPicture/applyProductDispose"
      );
    },
    changeReasonType(val) {
      if (val.indexOf(0) != -1) {
        this.rules.reason[0].required = true;
      } else {
        this.rules.reason[0].required = false;
      }
    },
    // 获取所有图片的条数
    async getListNum() {
      let res = await getAlternativePicture({
        applyCode: this.productInfo[0].applyCode,
        productCode: this.productInfo[0].productCode,
      })
      if(!res.retCode) {
        this.firstmoreListNum = res.data.length
        if(!res.data.length) {
          this.isMorePic = true;
        }
      }
    },
    handleAllPic(e, t) {//t搜索友商回调
      getAlternativePicture({
        applyCode: this.productInfo[0].applyCode,
        productCode: this.productInfo[0].productCode,
      }).then((res) => {
        if (!res.retCode) {
          this.isMorePic = true;
          console.log(res.data);
          this.morePicList = res.data;
          if (!res.data.length && !t) {
            this.$message.warning("无更多图片");
          }
          if(t) {
            this.morePicList.push(t)
          }
        } else {
          this.$message.error(res.retMsg);
        }
      });
    },

    // 初始化腾讯云插件
    async getTempKeyApi() {
      try {
        const { data } = await getTempKey()
        this.tempKey = data
        window.tempKey = data

        this.cos = new COS({
          // getAuthorization 必选参数
          getAuthorization: function (options, callback) {
            callback({
              TmpSecretId: window.tempKey.tmpSecretId,
              TmpSecretKey: window.tempKey.tmpSecretKey,
              SecurityToken: window.tempKey.sessionToken,
              // 建议返回服务器时间作为签名的开始时间，避免用户浏览器本地时间偏差过大导致签名错误
              StartTime: window.tempKey.startTime, // 时间戳，单位秒，如：1580000000
              ExpiredTime: window.tempKey.expiredTime, // 时间戳，单位秒，如：1580000000
            })
          },
        })
      } catch (error) {
        console.log(error)
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.container {
  padding-bottom: 80px;
  .tabTitle {
    line-height: 50px;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 22px;
    padding: 0 20px;
    border-bottom: 1px solid #e4e4eb;
  }
}
.productInfo {
  .product-content {
    padding: 0 40px 20px;
  }
}
.disposeInfo {
  .dispose-content /deep/ {
    width: 1000px;
    margin: 0 auto;
    .el-dialog__body {
      height: 500px;
      overflow-y: scroll;
    }
    .el-form-item {
      display: flex;
      height: 40px;
      padding: 0 50px;
      .el-form-item__content {
        width: 100%;
        position: relative;
        .usePictureStatus-tip {
          color: #f56c6c;
          position: absolute;
          top: 25px;
        }
      }
      .btn-wrap {
        width: 100%;
        display: flex;
        justify-content: center;
      }
    }
    .el-form-item__label {
      width: 200px;
    }
  }
}
.bottomBtn {
  background: #f0f2f5;
  width: 100%;
  position: fixed;
  bottom: 0;
  padding: 15px;
  z-index: 10;
  display: flex;
  justify-content: center;
}
.checkbox-wrap /deep/ {
  height: auto !important;
  .el-form-item__content {
    line-height: normal;
    .el-checkbox-group {
      display: flex;
      flex-wrap: wrap;
      .el-checkbox {
        padding: 10px 0;
      }
    }
  }
}
.reason-wrap /deep/ {
  padding: 0 10px !important;
  .el-input__inner {
    border-top: none;
    border-left: none;
    border-right: none;
  }
}
</style>

