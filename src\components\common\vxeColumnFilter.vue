<template>
  <div class='vxe-column-filter-container'>
    <el-button size="medium" @click="dialogOpenFunc">筛选列</el-button>

    <!-- dialog -->
    <el-dialog :visible.sync="dialogVisible">
      <span slot="title" class="dialog-title">筛选列</span>
      <el-row>
        <template v-for="(item,index) in columns">
          <template v-if="!['checkbox','radio'].includes(item.type)">
            <el-col :span="6" :key="index">
              <vxe-checkbox v-model="item.visible" :disabled="['left','right'].includes(item.fixed)">{{item.title}}</vxe-checkbox>
            </el-col>
          </template>
        </template>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogCancelFunc" size="medium">取消</el-button>
        <el-button @click="dialogSubmitFunc" size="medium" type="primary">确定</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
  export default {
    name: "",
    components: {},
    filters: {},
    props: {
      tableRefName: {
        type: String
      },
    },
    data() {
      return {
        dialogVisible: false,
        targetRef: void 0, //ref对象
        columns: []
      }
    },
    computed: {},
    watch: {},
    created() {},
    mounted() {
      this.$nextTick(() => {
        this.targetRef = this.$vnode.context.$refs[this.tableRefName];
        this.columns = this.targetRef.tableColumn;
      })
    },
    methods: {
      /**
       * 打开dialog
       */
      dialogOpenFunc() {
        this.dialogVisible = true;
      },

      /**
       * 取消
       */
      dialogCancelFunc() {
        this.dialogVisible = false;
      },

      /**
       * 确定
       */
      dialogSubmitFunc() {
        this.targetRef.refreshColumn();
        this.dialogVisible = false;
      }
    }
  }
</script>

<style lang="scss" scoped>
  .vxe-column-filter-container {
    display: inline-block;

    .el-dialog__wrapper {
      /deep/ {
        .el-dialog {
          width: 30%;
          min-width: 560px;

          .el-dialog__body {
            min-height: 200px;
            max-height: 500px;
            overflow-y: auto;

            .el-col {
              margin-bottom: 10px;
            }
          }
        }
      }
    }
  }
</style>
