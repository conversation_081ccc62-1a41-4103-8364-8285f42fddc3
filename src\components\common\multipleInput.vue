<template>
  <div class="el-form-item__content">
    <div class="multiple-input-container" :class="{ disabled: disabled }">
      <el-tag
        v-for="(tag, key) in vmodel"
        size="medium"
        :key="key"
        :disable-transitions="false"
        :closable="!disabled"
        :type="!disabled ? '' : 'info'"
        @close="handleClose(tag)"
      >
        {{ tag }}
      </el-tag>
      <el-input
        v-if="!disabled"
        type="text"
        class="input-new-tag"
        v-model="inputValue"
        ref="saveTagInput"
        size="small"
        @blur="handleInputConfirm"
      >
        <!-- <template slot="append">
          <span v-if="!disabled" @click="handleInputConfirm">添加</span>
        </template> -->
      </el-input>
      
    </div>
  </div>
</template>

<script>
// emitter: 自定义fome组件配合element校验需要
// import emitter from 'element-ui/src/mixins/emitter';
export default {
  name: "",
  // mixins: [emitter],
  components: {},
  filters: {},
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
    vmodel: {
      type: Array,
      default: () => [],
    },
    min: {
      type: Boolean,
      default: false,
    },
    standard: {
      type: Boolean,
      default: false,
    },
    validate: {
      type: Boolean,
      default: true,
    },
  },
  model: {
    prop: "vmodel", //父组件通过v-model向子组件传值，子组件这样获取
    event: "change", //子组件向父组件的v-model属性传值的$emit类型
  },
  data() {
    return {
      // dynamicTags: [],
      inputVisible: false,
      inputValue: "",
      tagNum: 0,
      tagChange: false
    };
  },
  computed: {},
  watch: {
    vmodel(newVal,oldVal) {
      if(!this.tagChange) {
        this.tagChange = true
        this.tagNum = newVal ? newVal.length : 0
      }
    }
  },
  created() {
    // this.dynamicTags = this.vmodel;
  },
  mounted() {},
  methods: {
    /**
     * 显示隐藏输入框
     */
    showInput() {
      this.inputVisible = true;
      this.$nextTick((_) => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },

    /**
     * @param {Object} tag  标签内容
     * 删除标签
     */
    handleClose(tag) {
      this.vmodel.splice(this.vmodel.indexOf(tag), 1);
      if(this.tagNum != 0) {
        this.tagNum --
      }
    },

    /**
     * 新增标签
     */
    handleInputConfirm() {
      let inputValue = this.inputValue.replace(/\s/g, "");
      let isNumber;
      if (this.validate) {
        // 如果是小包装条码
        if (this.min) {
          isNumber = /^0$|(^\d{8}$)|(^\d{12}$)|(^[1-9]{1}\d{12}$)/g.test(
            String(inputValue)
          );
        } else if (this.standard) {
          isNumber = /^\d{13}$|^\d{14}$/g.test(String(inputValue));
        } else {
          isNumber = /^\d+$/g.test(inputValue); //避免大数的科学计数法
        }
      } else {
        isNumber = true;
      }
      if (isNumber) {
        if(!this.vmodel){
          this.vmodel = [];
        }
        if (this.vmodel.includes(inputValue)) {
          this.$message.error("重复项");
        } else {
          this.vmodel[this.tagNum] = inputValue
          // this.vmodel.push(inputValue);
          this.$emit("change", this.vmodel);
          this.$emit("clearValidate", this.vmodel);
        }
      } else if(inputValue !== '') {
        this.$message.error("输入不符合规则请重新输入");
      }
      this.inputVisible = false;
      this.inputValue = "";
    },
  },
};
</script>

<style lang="scss" scoped>
.multiple-input-container {
  display: inline-block;
  box-sizing: border-box;
  background-image: none;
  border-radius: 4px;
  width: 100%;
  min-height: 40px;
  outline: 0;
  padding: 0px 10px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;

  .el-tag {
    margin: 4px 5px 2px 0;
  }

  .button-new-tag {
    // height: 32px;
    line-height: 25px;
    padding-top: 0;
    padding-bottom: 0;
    margin: 3px 5px 3px 0;
  }

  .input-new-tag {
    width: 200px;
    line-height: 1;
    vertical-align: middle;
    margin: 3px 5px 3px 0;
  }
}
.disabled {
  background: #f5f7fa;
}
</style>
