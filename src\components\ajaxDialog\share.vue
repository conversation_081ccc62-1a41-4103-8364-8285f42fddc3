<template>
  <transition name="fade">
    <div class="mask" v-if="show" @click="show = !show">
      <div class="content">
        <img src="../../assets/images/loginOut.png" alt="" />
        <span class="tip">登录失效请重新登录</span>
        <span class="dialog-btn" @click="loginOut">刷新</span>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  data() {
    return {
      show: false,
    };
  },
  components: {},
  methods: {
    loginOut() {
       window.parent.location.href = '/'; 
    },
  },
};
</script>
<style lang='scss' scoped>
// 渐变过渡
.fade-enter,
.fade-leave-active {
  opacity: 0;
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.35s;
}
.mask {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 99999;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.3);
  .content {
    width: 500px;
    padding: 50px 0;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #fff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .tip {
    color: #3b95a8;
    padding: 10px;
  }
  .dialog-btn {
    width: 120px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    background: #3b95a8;
    color: #fff;
    border-radius: 5px;
  }
}
</style>