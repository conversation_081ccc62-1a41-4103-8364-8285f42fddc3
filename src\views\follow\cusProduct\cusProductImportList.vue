
<template>
  <div class="task-to-be-film-container" v-loading="loading">
    <!-- 查询表单 -->
    <div class="search-form-wrap">
      <el-form :model="model" ref="refSearchForm">
        <el-row type="flex">
          <!-- 申请时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="申请时间" prop="applyTime">
              <el-date-picker
                v-model="formData.applyTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                clearable
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <!-- 处理状态 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="处理状态" prop="operateStatus">
              <el-select
                v-model="formData.operateStatus"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in operateStatusList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 来源 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="来源">
              <el-select
                v-model="formData.source"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" value=""></el-option>
                <el-option label="荷叶健康" :value="1"></el-option>
                <el-option label="SAAS智鹿" :value="2"></el-option>
                <el-option label="实施提报" :value="3"></el-option>
                <el-option label="POP" :value="4"></el-option>
                <el-option label="商品中台" :value="5"></el-option>
                <el-option label="百草" :value="6"></el-option>
                <el-option label="商采管理部" :value="7"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 小包装条码-->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="小包装条码">
              <el-input
                v-model="formData.smallPackageCode"
                placeholder=""
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 通用名-->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="通用名">
              <el-input
                v-model="formData.generalName"
                placeholder="模糊查询"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 生产厂家-->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="生产厂家">
              <el-input
                v-model="formData.manufacturerName"
                placeholder="模糊查询"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 规格型号-->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="规格型号">
              <el-input
                v-model="formData.spec"
                placeholder=""
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 药店编码-->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="药店编码">
              <el-input
                v-model="formData.customerCode"
                placeholder="模糊查询"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 药店商品编码-->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="药店商品编码">
              <el-input
                v-model="formData.customerProductCode"
                placeholder="模糊查询"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 标注内容-->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="标注内容">
              <el-select
                v-model="formData.markType"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" value=""></el-option>
                <el-option label="厂家不全" :value="2"></el-option>
                <el-option label="规格违法" :value="3"></el-option>
                <el-option label="品名不清" :value="4"></el-option>
                <el-option label="自定义内容" :value="5"></el-option>
                <el-option label="标准库ID" :value="6"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 导入单据编号 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="导入单据编号">
              <el-input
                v-model="formData.importCode"
                placeholder="模糊查询"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 按钮 -->
          <el-col :span="6" :offset="18">
            <!-- <el-button
              type="primary"
              size="medium"
              :disabled="tabName == 'second'"
              @click="
                () => {
                  isUnfold = !isUnfold;
                }
              "
              >{{ isUnfold ? "收起" : "展开" }}</el-button
            > -->
            <el-button type="primary" size="medium" @click="btnSearchClick"
              >查询</el-button
            >
            <el-button
              size="medium"
              :disabled="tabName == 'second'"
              @click="btnResetClick"
              >重置</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="btn-wrap">
      <excel-file-upload
        :scope="uploadConfiguration"
        :includeFileType="['xlsx']"
        :accept="'.xlsx'"
        @success="excelUpload"
        >导入客户商品</excel-file-upload
      >
      <el-button size="medium" @click="handleExport">导出EXCEL</el-button>
    </div>
    <!-- table表单 -->
    <!-- table -->
    <div class="tab-wrap">
      <el-tabs v-model="tabName" type="card">
        <el-tab-pane label="客户商品列表" name="first">
          <div class="table-wrap">
            <vxe-table
              border
              highlight-hover-row
              resizable
              height="100%"
              auto-resize
              size="small"
              align="center"
              @cell-dblclick="cellDBLClickEvent"
              :tooltip-config="{ enterable: false }"
              :loading="tableLoading"
              :data="tableData"
              :seq-config="{ startIndex: (pageNum - 1) * pageSize }"
              ref="refVxeTable"
            >
              <vxe-table-column
                type="seq"
                title="序号"
                width="60"
                show-header-overflow
                show-overflow
                fixed="left"
              ></vxe-table-column>
              <vxe-table-column
                field="sourceName"
                title="来源"
                width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                field="applyType"
                title="提交方式"
                width="120"
                show-header-overflow
                show-overflow
              >
                <template v-slot="{ row }">
                  {{ ["", "中台导入", "接口"][row.applyType] }}
                </template>
              </vxe-table-column>
              <vxe-table-column
                field="applyTime"
                title="申请时间"
                width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                field="applicantName"
                title="申请人"
                min-width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                field="applicantMechanism"
                title="所属机构"
                width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                field="operateStatus"
                title="处理状态"
                width="120"
                show-header-overflow
                show-overflow
              >
                <template v-slot="{ row }">
                  {{ getOperateStatusName(row.operateStatus) }}
                </template>
              </vxe-table-column>
              <vxe-table-column
                field="markText"
                title="标记内容"
                width="120"
                show-header-overflow
                show-overflow
              >
                <template v-slot="{ row }">
                  {{
                    row.markType
                      ? filterMarkTest(row.markType, row.markText)
                      : "-"
                  }}
                </template>
              </vxe-table-column>
              <vxe-table-column
                field="applyCode"
                title="任务单号"
                min-width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                field="customerCode"
                title="药店编码"
                width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                field="customerProductCode"
                title="药店商品编码"
                width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                field="smallPackageCode"
                title="小包装条码"
                min-width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                field="generalName"
                title="通用名"
                width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                field="spec"
                title="规格型号"
                min-width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                field="manufacturerName"
                title="生产厂家"
                min-width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                field="approvalNo"
                title="批准文号"
                width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                field="importCode"
                title="导入单据编号"
                width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                title="操作"
                width="120"
                show-header-overflow
                show-overflow
                fixed="right"
              >
                <template v-slot="{ row }">
                  <span class="table-btn" v-if="row.operateStatus == 1">
                    <el-link
                      :underline="false"
                      type="primary"
                      @click.stop="
                        receiveTask(row.applyCode, row.operateStatus)
                      "
                      >领取并处理</el-link
                    >
                  </span>
                  <span class="table-btn" v-if="row.operateStatus == 3">
                    <el-link
                      :underline="false"
                      type="primary"
                      @click.stop="receiveTask(row.applyCode)"
                      >领取新品上报</el-link
                    >
                  </span>
                  <span class="table-btn" v-if="row.operateStatus == 4">
                    <el-link
                      :underline="false"
                      type="primary"
                      @click.stop="receiveTask(row.applyCode)"
                      >领取补充条码</el-link
                    >
                  </span>
                  <span
                    class="table-btn"
                    v-if="
                      (row.operateStatus == 10 && row.submitType == 1) ||
                      (row.operateStatus == 11 && row.submitType == 1) ||
                      row.operateStatus == 7 ||
                      row.operateStatus == 8 ||
                      row.operateStatus == 9
                    "
                  >
                    <el-link
                      :underline="false"
                      type="primary"
                      @click.stop="handleDetail(row.applyCode)"
                      >查看</el-link
                    >
                  </span>
                  <span class="table-btn" v-if="row.operateStatus == 2">
                    <el-link
                      :underline="false"
                      :disabled="!row.receiveSelf"
                      type="primary"
                      @click.stop="deal(row.applyCode)"
                      >处理</el-link
                    >
                  </span>
                  <span class="table-btn" v-if="row.operateStatus == 5">
                    <el-link
                      :underline="false"
                      :disabled="!row.receiveSelf"
                      type="primary"
                      @click.stop="handleAddProduct(row.applyCode)"
                      >新品上报</el-link
                    >
                  </span>
                  <span class="table-btn" v-if="row.operateStatus == 6">
                    <el-link
                      :underline="false"
                      :disabled="!row.receiveSelf"
                      type="primary"
                      @click.stop="addBarcodeSubmit(row.applyCode)"
                      >补充条码</el-link
                    >
                  </span>
                  <span
                    class="table-btn"
                    v-if="row.operateStatus == 10 && row.submitType == 2"
                  >
                    <el-link
                      :underline="false"
                      type="primary"
                      @click.stop="handleSwitcher(row.applyCode)"
                      >停用</el-link
                    >
                  </span>
                  <span
                    class="table-btn"
                    v-if="row.operateStatus == 11 && row.submitType == 2"
                  >
                    <el-link
                      :underline="false"
                      type="primary"
                      @click.stop="handleSwitcher(row.applyCode)"
                      >启用</el-link
                    >
                  </span>
                </template>
              </vxe-table-column>
            </vxe-table>
          </div>
        </el-tab-pane>
        <el-tab-pane label="客户商品导入记录" name="second">
          <div class="table-wrap">
            <vxe-table
              border
              highlight-hover-row
              resizable
              height="100%"
              auto-resize
              size="small"
              align="center"
              :tooltip-config="{ enterable: false }"
              :loading="tableLoading"
              :data="tableData"
              :seq-config="{ startIndex: (pageNum - 1) * pageSize }"
              ref="refVxeTable"
            >
              <vxe-table-column
                type="seq"
                title="序号"
                width="60"
                show-header-overflow
                show-overflow
                fixed="left"
              ></vxe-table-column>
              <vxe-table-column
                field="applyDate"
                title="申请时间"
                width="120"
                show-header-overflow
                show-overflow
              >
                <template v-slot="{ row }">
                  {{ row.applyDate | parseTime }}
                </template>
              </vxe-table-column>
              <vxe-table-column
                field="applyer"
                title="申请人"
                width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                field="applyCode"
                title="单据编号"
                min-width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                field="applyTotalNum"
                title="提交商品数量"
                width="120"
                show-header-overflow
                show-overflow
              >
                <template v-slot="{ row }">
                  <a
                    :class="row.applyTotalNum != 0 ? 'active' : ''"
                    :href="row.originalFileUrl"
                    :download="row.originalFileName"
                    >{{ row.applyTotalNum }}</a
                  >
                </template>
              </vxe-table-column>
              <vxe-table-column
                field="applyAbnormalNum"
                title="异常商品数量"
                width="120"
                show-header-overflow
                show-overflow
              >
                <template v-slot="{ row }">
                  <a
                    :class="row.applyAbnormalNum != 0 ? 'active' : ''"
                    :href="row.abnormalFileUrl"
                    :download="row.abnormalFileName"
                    >{{ row.applyAbnormalNum }}</a
                  >
                </template>
              </vxe-table-column>
              <vxe-table-column
                field="analysisStatus"
                title="解析状态"
                width="120"
                show-header-overflow
                show-overflow
              >
                <template v-slot="{ row }">
                  {{ ["失败", "成功", "解析中"][row.analysisStatus] }}
                </template>
              </vxe-table-column>
              <vxe-table-column
                field="analysisEndDate"
                title="解析完成时间"
                min-width="120"
                show-header-overflow
                show-overflow
              >
                <template v-slot="{ row }">
                  {{ row.analysisEndDate | parseTime }}
                </template>
              </vxe-table-column>
            </vxe-table>
          </div></el-tab-pane
        >
      </el-tabs>
    </div>

    <!-- 分页 -->
    <el-row class="custom-pagination-wrap">
      <el-col>
        <el-pagination
          background
          :current-page.sync="pageNum"
          :page-sizes="[20, 50, 100, 200]"
          :page-size.sync="pageSize"
          :total="total"
          layout="prev, pager, next, jumper,total, sizes"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { hasPermission } from "@/utils/index.js";
import {
  getCustomerProductList,
  getAllOperateStatus,
  receiveCustomerProductTask,
  switcherCustomerProduct,
  addBarcodeSubmit,
  getCustomerProductExportList,
} from "@/api/follow.js";
import { getBatchModifyRecordList } from "@/api/common.js";
import excelFileUpload from "@/components/common/excelFileUpload";

export default {
  name: "",
  components: { excelFileUpload },
  filters: {},
  props: {},
  data() {
    return {
      loading: false,
      pageNum: 1,
      pageSize: 20,
      total: 0, //总条数
      formData: {
        createTime: "",
        operateStatus: "",
        source: "",
        smallPackageCode: "",
        generalName: "",
        manufacturerName: "",
        spec: "",
        customerCode: "",
        customerProductCode: "",
        markText: "",
        importCode: ""
      },
      tableLoading: false,
      tableData: [],
      operateStatusList: [],
      // isUnfold: false,
      tabName: "first",
      uploadConfiguration: {
        applyCode: "",
        disabled: false,
        type: "4",
        buttonType: "button",
        url: "api/customerProduct/add/batchUpload",
        downloadName: "客户商品导入模板.xlsx",
        downloadUrl: "../../../static/assets/excel/客户商品导入模板.xlsx",
      },
    };
  },
  computed: {},
  watch: {
    tabName(val) {
      this.pageNum = 1;
      this.searchForm();
    },
  },
  created() {
    this.searchForm();
    this.getAllOperateStatus();
  },
  mounted() {},
  methods: {
    getAllOperateStatus() {
      getAllOperateStatus().then((res) => {
        this.operateStatusList = res.data;
      });
    },
    // 搜索
    searchForm() {
      this.tableLoading = true;
      if (this.tabName == "first") {
        this.getCompletionList();
      } else {
        this.getImportList();
      }
    },
    // 获取客户商品列表
    getCompletionList() {
      let param = Object.assign(
        {
          page: this.pageNum,
          limit: this.pageSize,
          applyStartTime: this.formData.applyTime
            ? this.formData.applyTime[0]
            : "",
          applyEndTime: this.formData.applyTime
            ? this.formData.applyTime[1]
            : "",
        },
        this.formData
      );
      delete param.applyTime;
      getCustomerProductList(param).then((res) => {
        if (!res.retCode) {
          this.tableData = res.data.list;
          this.total = res.data.total;
          this.tableLoading = false;
        } else {
          this.$message.error(res.retMsg);
        }
      });
    },
    // 获取导入记录列表
    getImportList() {
      getBatchModifyRecordList({
        page: this.pageNum,
        limit: this.pageSize,
        sortList: [
          {
            order: 0,
            columnName: "applyDate",
            lift: "DESC", //(升序-ASC, 降序-DESC)
          },
        ],
        modifyType: 17,
      }).then((res) => {
        if (!res.retCode) {
          this.tableData = res.data.list;
          this.total = res.data.total;
          this.tableLoading = false;
        } else {
          this.$message.error(res.retMsg);
        }
      });
    },
    // 导出客户商品
    handleExport() {
      let param = Object.assign(
        {
          page: this.pageNum,
          limit: this.pageSize,
          applyStartTime: this.formData.applyTime
            ? this.formData.applyTime[0]
            : "",
          applyEndTime: this.formData.applyTime
            ? this.formData.applyTime[1]
            : "",
        },
        this.formData
      );
      delete param.applyTime;
      getCustomerProductExportList(param).then((res) => {
        if (!res.retCode) {
          this.$message.success(res.retMsg);
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      });
    },

    // 查询按钮点击
    btnSearchClick() {
      this.pageNum = 1;
      this.searchForm();
    },

    // 重置
    btnResetClick() {
      this.formData = {
        createTime: "",
        operateStatus: "",
        source: "",
        smallPackageCode: "",
        generalName: "",
        manufacturerName: "",
        spec: "",
        customerCode: "",
        customerProductCode: "",
        markText: "",
        importCode: ""
      };
      this.pageNum = 1;
      this.searchForm();
    },
    // pageSize 改变事件
    handleSizeChange(pageSize) {
      this.pageNum = 1;
      this.pageSize = pageSize;
      this.searchForm();
    },

    // pageNum 改变事件
    handleCurrentChange(currentPage) {
      this.pageNum = currentPage;
      this.searchForm();
    },
    // 权限
    hasPermission(str) {
      return hasPermission(str);
    },
    // 表格获取操作状态
    getOperateStatusName(state) {
      let stateName = "";
      this.operateStatusList.forEach((item) => {
        if (item.key == state) {
          stateName = item.value;
          return;
        }
      });
      return stateName;
    },
    // 启用停用
    handleSwitcher(applyCode) {
      switcherCustomerProduct({ applyCode }).then((res) => {
        if (!res.retCode) {
          this.$message.success("操作成功");
          this.searchForm();
        } else {
          this.$message.error(res.retMsg);
        }
      });
    },
    // 补充条码提交
    addBarcodeSubmit(applyCode) {
      addBarcodeSubmit({ applyCode }).then((res) => {
        if (!res.retCode) {
          this.$message.success("操作成功");
          this.searchForm();
        } else {
          this.$message.error(res.retMsg);
        }
      });
    },
    // 领取任务
    receiveTask(applyCode, state) {
      receiveCustomerProductTask({ applyCode }).then((res) => {
        if (!res.retCode) {
          this.$message.success("领取成功");
          this.searchForm();
          if (state == 1) {
            try {
              parent.CreateTab(
                `../static/dist/index.html#/follow/disposeCusProduct?applyCode=${applyCode}&isEdit=${true}`,
                "客户商品处理"
              );
            } catch {
              this.$router.push({
                path: "/follow/disposeCusProduct",
                query: {
                  applyCode,
                  isEdit: true
                },
              });
            }
          }
        } else {
          this.$message.error(res.retMsg);
        }
      });
    },
    // 处理
    deal(applyCode) {
      try {
        parent.CreateTab(
          `../static/dist/index.html#/follow/disposeCusProduct?applyCode=${applyCode}&isEdit=${true}`,
          "客户商品处理"
        );
      } catch {
        this.$router.push({
          path: "/follow/disposeCusProduct",
          query: {
            applyCode,
            isEdit: true
          },
        });
      }
    },
    // 新品上报
    handleAddProduct(applyCode) {
      try {
        parent.CreateTab(
          "../static/dist/index.html#/product/addProduct?applyCode=" +
            applyCode +
            "&type=present",
          "商品新增",
          true
        );
      } catch (error) {
        this.$router.push({
          name: "addProduct",
          query: {
            applyCode,
            type: "present",
          },
        });
      }
    },
    // 双击查看详情
    cellDBLClickEvent({ row }) {
      this.handleDetail(row.applyCode);
    },
    // 查看详情
    handleDetail(applyCode) {
      try {
        parent.CreateTab(
          `../static/dist/index.html#/follow/disposeCusProduct?applyCode=${applyCode}`,
          "客户商品处理详情"
        );
      } catch {
        this.$router.push({
          path: "/follow/disposeCusProduct",
          query: {
            applyCode,
          },
        });
      }
    },
    // 格式化标记内容
    filterMarkTest(type, text) {
      let result = [];
      type.split(",").forEach((item) => {
        if (item == 2) {
          result.push("厂家不全");
        } else if (item == 3) {
          result.push("规格违法");
        } else if (item == 4) {
          result.push("品名不清");
        } else {
          return;
        }
      });
      if (text) {
        result.push(text);
      }
      return result.join(",");
    }
  },
};
</script>
<style lang="scss" scoped>
.task-to-be-film-container {
  /**
   * 查询表单
   */
  .search-form-wrap {
    width: 100%;
    padding: 15px;
    border-bottom: 1px dashed #e4e4eb;

    .el-row {
      flex-wrap: wrap;

      .el-col {
        display: flex;
        justify-content: flex-end;

        .el-form-item {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          width: 100%;
          margin-right: 0;
          margin-bottom: 15px;

          /deep/ {
            .el-form-item__label {
              width: 96px;
              line-height: normal;
              padding: 0 12px;
              color: #292933;
              font-weight: normal;
            }

            .el-form-item__content {
              flex: 1;

              .el-range-editor.el-input__inner {
                width: 100%;

                .el-range-separator {
                  width: 14px;
                  padding: 0;
                }
              }
            }
          }

          .el-input {
            width: 100%;
          }

          .el-select {
            width: 100%;
          }
        }
      }
    }
  }

  .tab-wrap {
    padding: 0 15px;
    margin-top: 15px;
  }
  .table-wrap {
    width: 100%;
    min-height: 440px;
    height: calc(100vh - 295px);
  }
}
.btn-wrap /deep/ {
  padding: 15px 0 0 10px;
  .el-button {
    margin-bottom: 15px;
    margin-left: 0;
    margin-right: 15px;
  }
}
.radio-wrap {
  .title {
    padding-right: 20px;
  }
}
.active {
  color: #3b95a8;
}
.table-btn /deep/ {
  padding: 0 6px;
  .is-disabled {
    color: #c0c4cc;
  }
}
</style>
