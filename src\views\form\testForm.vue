<template>
<div class="test">
    <component :is="test" :selectValue="formSelectValue" @changeForm="changeForm"></component>
</div>
</template>
<script>
import formeA from "@/components/form/form1"
import formeB from "@/components/form/form2"
export default {
    data() {
        return {
            test:formeA,
            formSelectValue:"1"
        }
    },
    components:{
        formeA,
        formeB
    },
    methods: {
        changeForm(formName){
            this.formSelectValue=formName;
            switch (formName) {
                case "1":
                    this.test=formeA;
                    break;
                case "2":
                    this.test=formeB;
                    break;
                default:
                    break;
            }
        }
    }
}
</script>
<style scoped lang="scss">
.test{
  padding: 30px;
}
</style>