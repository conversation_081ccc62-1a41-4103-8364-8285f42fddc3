<template>
  <transition name="viewer-fade">
    <div tabindex="-1" ref="el-image-viewer__wrapper"
      :class="{ 'el-image-viewer__wrapper_max': max, 'el-image-viewer__wrapper': !max }" :style="imgStyleWapper"
      @wheel="handleWheelEvent" @scroll.stop.prevent @touchmove.stop.prevent>
      <div class="el-image-viewer__mask"></div>
      <!-- CLOSE -->
      <span class="el-image-viewer__btn el-image-viewer__close" :class="{ 'with-ocr-panel': enableOCR && showOcrPanel }"
        @click="hide">
        <i class="el-icon-circle-close"></i>
      </span>
      <!-- ARROW -->
      <template v-if="!isSingle">
        <span class="el-image-viewer__btn el-image-viewer__prev" :class="{
          'is-disabled': !infinite && isFirst,
          'with-ocr-panel': enableOCR && showOcrPanel
        }" @click="prev">
          <i class="el-icon-arrow-left" />
        </span>
        <span class="el-image-viewer__btn el-image-viewer__next" :class="{
          'is-disabled': !infinite && isLast,
          'with-ocr-panel': enableOCR && showOcrPanel
        }" @click="next">
          <i class="el-icon-arrow-right" />
        </span>
      </template>
      <!-- ACTIONS -->
      <div class="el-image-viewer__btn el-image-viewer__actions"
        :class="{ 'with-ocr-panel': enableOCR && showOcrPanel }">
        <div class="el-image-viewer__actions__inner">
          <i class="el-icon-zoom-out" @click="handleActions('zoomOut')"></i>
          <i class="el-image-viewer__actions__divider"></i>
          <i class="el-icon-zoom-in" @click="handleActions('zoomIn')"></i>
          <i class="el-image-viewer__actions__divider"></i>
          <i class="el-icon-refresh-left" @click="handleActions('anticlocelise')"></i>
          <i class="el-icon-refresh-right" @click="handleActions('clocelise')"></i>
          <!-- <i class="el-image-viewer__actions__divider"></i> -->
          <!-- <i :class="{'el-icon-full-screen': max,'el-icon-c-scale-to-original': !max}" @click="toggleMode"></i> -->
          <!-- OCR按钮 -->
          <template v-if="enableOCR">
            <i class="el-image-viewer__actions__divider"></i>
            <!-- 提取中状态 -->
            <span v-if="currentOcrResult.loading" class="ocr-extract-btn loading" title="正在提取文字">
              提取中
            </span>
            <!-- 可点击状态 -->
            <span v-else-if="!currentOcrResult.extracted" class="ocr-extract-btn clickable"
              @click="extractCurrentImageText" title="提取图片文字">
              提取图片文字
            </span>
            <!-- 已提取状态 -->
            <span v-else class="ocr-extract-btn extracted" @click="showRepeatExtractTip" title="已成功提取图片文字">
              已提取文字
            </span>
          </template>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="el-image-viewer__content">
        <!-- 图片显示区域 -->
        <div class="el-image-viewer__canvas" :class="{ 'with-ocr-panel': enableOCR && showOcrPanel }">
          <img v-for="(url, i) in urlList" v-show="i === index" ref="img" class="el-image-viewer__img"
            :key="url ? (url.mediaName || url.name || url.uid || i) : i" :src="currentImg.mediaUrl || currentImg.url"
            :style="imgStyle" @load="handleImgLoad" @error="handleImgError"
            @mousedown="handleMouseDown($event, transform)">
        </div>

        <!-- OCR结果展示面板 -->
        <div v-if="enableOCR && showOcrPanel" class="ocr-results-panel">
          <div class="ocr-panel-header">
            <div class="ocr-panel-title">识别结果</div>
            <div class="ocr-panel-controls">
              <span class="close-panel-btn" @click="hideOcrPanel" title="关闭面板">
                <i class="el-icon-close"></i>
              </span>
            </div>
          </div>
          <div class="ocr-panel-content" ref="ocrPanelContent">
            <div v-if="currentOcrResult.extracted && currentOcrResult.fields.length > 0" class="ocr-result-fields">
              <div v-for="field in currentOcrResult.fields" :key="field.id" class="ocr-result-field">
                <div class="field-content">
                  <span class="field-label">{{ field.label }}：</span>
                  <span class="field-value">{{ field.value }}</span>
                </div>
                <div class="field-copy">
                  <span class="copy-btn" @click="copyFieldValue(field.value)" title="复制">
                    复制
                  </span>
                </div>
              </div>
            </div>
            <div v-else class="no-ocr-result">
              <p>暂无识别结果</p>
              <p class="tip">点击上方"提取图片文字"按钮开始识别</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
import { on, off } from 'element-ui/src/utils/dom';
import { rafThrottle, isFirefox } from 'element-ui/src/utils/util';

const Mode = {
  CONTAIN: {
    name: 'contain',
    icon: 'el-icon-full-screen'
  },
  ORIGINAL: {
    name: 'original',
    icon: 'el-icon-c-scale-to-original'
  }
};

const mousewheelEventName = isFirefox() ? 'DOMMouseScroll' : 'mousewheel';

export default {
  name: 'imagePreviewWithOCR',
  props: {
    urlList: {
      type: Array,
      default: () => []
    },
    zIndex: {
      type: Number,
      default: 2000
    },
    onSwitch: {
      type: Function,
      default: () => { }
    },
    onClose: {
      type: Function,
      default: () => { }
    },
    initialIndex: {
      type: Number,
      default: 0
    },
    // OCR相关props
    enableOCR: {
      type: Boolean,
      default: false
    },
    ocrResults: {
      type: Object,
      default: () => ({})
    },
    extractTextFromImage: {
      type: Function,
      default: () => { }
    },
    copyInfo: {
      type: Function,
      default: () => { }
    }
  },
  data() {
    return {
      max: true,
      index: this.initialIndex,
      isShow: false,
      infinite: true,
      loading: false,
      mode: Mode.CONTAIN,
      transform: {
        scale: 1,
        deg: 0,
        offsetX: 0,
        offsetY: 0,
        enableTransition: false
      },
      transformWrapper: {
        offsetX: 0,
        offsetY: 0,
      },
      // OCR相关数据
      showOcrPanel: false
    };
  },
  computed: {
    isSingle() {
      return this.urlList.length <= 1;
    },
    isFirst() {
      return this.index === 0;
    },
    isLast() {
      return this.index === this.urlList.length - 1;
    },
    currentImg() {
      return this.urlList[this.index];
    },
    // 当前图片的OCR结果
    currentOcrResult() {
      if (!this.currentImg || !this.enableOCR) {
        return { loading: false, extracted: false, fields: [] };
      }
      const key = this.currentImg.uid || this.currentImg.mediaUrl || this.currentImg.url;
      return this.ocrResults[key] || { loading: false, extracted: false, fields: [] };
    },
    imgStyle() {
      const { scale, deg, offsetX, offsetY, enableTransition } = this.transform;
      const style = {
        transform: `scale(${scale}) rotate(${deg}deg)`,
        transition: enableTransition ? 'transform .3s' : '',
        'margin-left': `${offsetX}px`,
        'margin-top': `${offsetY}px`
      };

      // 移除图片尺寸限制，允许图片自由缩放
      if (this.mode) {
        style.maxWidth = style.maxHeight = '100%';
      }

      return style;
    },
    imgStyleWapper() {
      return {
        zIndex: this.zIndex
      };
    }
  },
  watch: {
    index: {
      handler: function (val) {
        this.resetImagePosition();
        this.onSwitch(val);
        // 切换图片时，如果新图片有OCR结果则显示面板
        if (this.enableOCR && this.currentOcrResult.extracted) {
          this.showOcrPanel = true;
        }
      }
    },
    showOcrPanel: {
      handler: function (newVal, oldVal) {
        // OCR面板显示/隐藏时，重新居中图片
        if (newVal !== oldVal) {
          this.centerImage();
        }
      }
    }
  },
  mounted() {
    this.deviceSupportInstall();

    // 添加窗口大小变化监听器
    this._resizeHandler = () => {
      // 窗口大小变化时，重新计算图片居中位置
      if (this.enableOCR) {
        this.centerImage();
      }
    };
    window.addEventListener('resize', this._resizeHandler);

    // 如果当前图片有OCR结果，则显示面板
    if (this.enableOCR && this.currentOcrResult.extracted) {
      this.showOcrPanel = true;
      // 初始化时如果显示OCR面板，确保图片居中
      this.$nextTick(() => {
        this.centerImage();
      });
    }
  },

  beforeDestroy() {
    this.deviceSupportUninstall();

    // 移除窗口大小变化监听器
    if (this._resizeHandler) {
      window.removeEventListener('resize', this._resizeHandler);
      this._resizeHandler = null;
    }
  },
  methods: {
    // OCR相关方法
    async extractCurrentImageText() {
      if (!this.currentImg || !this.extractTextFromImage) {
        return;
      }

      try {
        // 调用父组件传入的OCR提取方法
        await this.extractTextFromImage(this.currentImg, this.index);

        // 提取成功后显示OCR面板
        this.showOcrPanel = true;
        // 显示面板时会通过 watch 自动触发 centerImage()
      } catch (error) {
        console.error('OCR提取失败:', error);
      }
    },

    showRepeatExtractTip() {
      this.$message.info('已成功提取图片文字，勿重复操作');
      // 显示OCR面板以查看结果
      this.showOcrPanel = true;
      // 显示面板时会通过 watch 自动触发 centerImage()
    },

    hideOcrPanel() {
      this.showOcrPanel = false;
      // 关闭面板时会通过 watch 自动触发 centerImage()
    },



    copyFieldValue(value) {
      if (this.copyInfo) {
        this.copyInfo(value);
      }
    },

    // 检查OCR面板滚动边界，防止滚动穿透
    checkOcrScrollBoundary(element, deltaY) {
      if (!element) {
        // console.log('OCR边界检测: 元素不存在');
        return false;
      }

      const { scrollTop, scrollHeight, clientHeight } = element;

      // 调试信息
      // console.log('OCR边界检测:', {
      //   scrollTop,
      //   scrollHeight,
      //   clientHeight,
      //   deltaY,
      //   needsScroll: scrollHeight > clientHeight
      // });

      // 如果内容高度不超过容器高度，说明不需要滚动，直接阻止穿透
      if (scrollHeight <= clientHeight) {
        // console.log('OCR边界检测: 内容不需要滚动，阻止穿透');
        return true;
      }

      const isAtTop = scrollTop <= 1; // 允许1px的误差
      const isAtBottom = (scrollTop + clientHeight) >= (scrollHeight - 1); // 允许1px的误差

      // console.log('OCR边界检测: 边界状态', { isAtTop, isAtBottom, deltaY });

      // 向上滚动且已在顶部，或向下滚动且已在底部时，需要阻止事件传播
      if ((deltaY < 0 && isAtTop) || (deltaY > 0 && isAtBottom)) {
        // console.log('OCR边界检测: 在边界，阻止穿透');
        return true; // 需要阻止传播
      }

      // console.log('OCR边界检测: 允许正常滚动');
      return false; // 允许正常滚动
    },

    // 处理模态框级别的滚轮事件
    handleWheelEvent(e) {
      // console.log('滚轮事件触发:', e.target.className, e.target.tagName);

      // 检查事件是否来自OCR面板内部
      const ocrPanel = e.target.closest('.ocr-results-panel');
      const ocrPanelContent = e.target.closest('.ocr-panel-content');

      // console.log('OCR面板检测:', {
      //   hasOcrPanel: !!ocrPanel,
      //   hasOcrPanelContent: !!ocrPanelContent,
      //   targetClass: e.target.className
      // });

      if (ocrPanel || ocrPanelContent) {
        // console.log('检测到OCR面板内滚动');

        // 获取OCR面板内容容器
        const contentElement = this.$refs.ocrPanelContent;
        // console.log('contentElement引用:', !!contentElement);

        if (contentElement) {
          const deltaY = e.deltaY || e.detail || (e.wheelDelta ? -e.wheelDelta : 0);
          // console.log('deltaY值:', deltaY);

          // 检查是否在滚动边界，如果是则阻止事件传播
          if (this.checkOcrScrollBoundary(contentElement, deltaY)) {
            // console.log('边界检测: 阻止事件');
            e.preventDefault();
            e.stopPropagation();
            return;
          }

          // 如果不在边界，允许正常滚动，只阻止冒泡到外层，不阻止默认行为
          // console.log('边界检测: 允许滚动，只阻止冒泡');
          e.stopPropagation();
          return;
        }

        // 如果无法获取到contentElement，保守处理：只阻止冒泡
        // console.log('无法获取contentElement，只阻止冒泡');
        e.stopPropagation();
        return;
      }

      // 其他区域的滚轮事件用于图片缩放，阻止默认行为和冒泡
      // console.log('非OCR区域滚动，用于图片缩放');
      e.preventDefault();
      e.stopPropagation();

      const delta = e.deltaY || e.detail || e.wheelDelta;
      if (delta < 0) {
        this.handleActions('zoomIn', {
          zoomRate: 0.015,
          enableTransition: false
        });
      } else {
        this.handleActions('zoomOut', {
          zoomRate: 0.015,
          enableTransition: false
        });
      }
    },

    // 以下是继承自原imagePreview组件的方法
    hide() {
      this.deviceSupportUninstall();
      this.onClose();
    },

    deviceSupportInstall() {
      this._keyDownHandler = rafThrottle(e => {
        const keyCode = e.keyCode;
        switch (keyCode) {
          // ESC
          case 27:
            this.hide();
            break;
          // SPACE
          case 32:
            this.toggleMode();
            break;
          // LEFT_ARROW
          case 37:
            this.prev();
            break;
          // UP_ARROW
          case 38:
            this.handleActions('zoomIn');
            break;
          // RIGHT_ARROW
          case 39:
            this.next();
            break;
          // DOWN_ARROW
          case 40:
            this.handleActions('zoomOut');
            break;
        }
      });
      this._mouseWheelHandler = rafThrottle(e => {
        // 阻止事件冒泡和默认行为，防止背景页面滚动
        e.preventDefault();
        e.stopPropagation();

        const delta = e.wheelDelta ? e.wheelDelta : -e.detail;
        if (delta > 0) {
          this.handleActions('zoomIn', {
            zoomRate: 0.015,
            enableTransition: false
          });
        } else {
          this.handleActions('zoomOut', {
            zoomRate: 0.015,
            enableTransition: false
          });
        }
      });
      on(document, 'keydown', this._keyDownHandler);
      on(document, mousewheelEventName, this._mouseWheelHandler);
    },

    deviceSupportUninstall() {
      off(document, 'keydown', this._keyDownHandler);
      off(document, mousewheelEventName, this._mouseWheelHandler);
      this._keyDownHandler = null;
      this._mouseWheelHandler = null;
    },

    handleImgLoad() {
      this.loading = false;
    },

    handleImgError(e) {
      this.loading = false;
      e.target.alt = '加载失败';
    },

    handleMouseDown(e, transform) {
      if (this.loading || e.button !== 0) return;

      const { offsetX, offsetY } = transform;
      const startX = e.pageX;
      const startY = e.pageY;
      this._dragHandler = rafThrottle(ev => {
        transform.offsetX = offsetX + ev.pageX - startX;
        transform.offsetY = offsetY + ev.pageY - startY;
      });
      on(document, 'mousemove', this._dragHandler);
      on(document, 'mouseup', () => {
        off(document, 'mousemove', this._dragHandler);
      });

      e.preventDefault();
    },

    reset() {
      this.transform = {
        scale: 1,
        deg: 0,
        offsetX: 0,
        offsetY: 0,
        enableTransition: false
      };
    },

    // 计算图片居中的目标偏移位置
    calculateCenterOffset() {
      let targetOffsetX = 0;

      // 如果OCR面板显示，需要计算图片在剩余区域的居中位置
      if (this.enableOCR && this.showOcrPanel) {
        // 获取容器总宽度
        const containerWidth = window.innerWidth;
        let imageAreaWidth;

        // 根据屏幕尺寸计算图片区域宽度
        if (containerWidth <= 768) {
          // 小屏幕：图片区域 = 总宽度 - 300px
          imageAreaWidth = containerWidth - 300;
        } else if (containerWidth <= 1200) {
          // 中等屏幕：图片区域为60%宽度
          imageAreaWidth = containerWidth * 0.6;
        } else {
          // 大屏幕：图片区域为70%宽度
          imageAreaWidth = containerWidth * 0.7;
        }

        // 计算图片应该向左偏移的距离，使其在剩余区域居中
        // 剩余区域的中心位置 = (图片区域宽度 / 2) - (容器总宽度 / 2)
        targetOffsetX = (imageAreaWidth / 2) - (containerWidth / 2);
      }

      return {
        offsetX: targetOffsetX,
        offsetY: 0
      };
    },

    // 重置图片位置（用于图片切换时）
    resetImagePosition() {
      // 使用 nextTick 确保DOM更新完成后再执行重置
      this.$nextTick(() => {
        // 计算正确的居中位置
        const centerOffset = this.calculateCenterOffset();

        this.transform = {
          scale: 1,
          deg: 0, // 重置旋转角度
          offsetX: centerOffset.offsetX, // 根据OCR面板状态设置正确的居中位置
          offsetY: centerOffset.offsetY,
          enableTransition: true // 启用过渡动画
        };

        // 短暂延迟后关闭过渡动画，避免影响后续操作
        setTimeout(() => {
          if (this.transform) {
            this.transform.enableTransition = false;
          }
        }, 300);
      });
    },

    // 图片居中（用于OCR面板开关时）
    centerImage() {
      if (!this.transform) return;

      // 使用 nextTick 确保DOM更新完成后再执行居中
      this.$nextTick(() => {
        // 计算正确的居中位置
        const centerOffset = this.calculateCenterOffset();

        // 设置图片位置：只重置位置，保持当前的缩放和旋转
        this.transform.offsetX = centerOffset.offsetX;
        this.transform.offsetY = centerOffset.offsetY;
        this.transform.enableTransition = true;

        // 短暂延迟后关闭过渡动画
        setTimeout(() => {
          if (this.transform) {
            this.transform.enableTransition = false;
          }
        }, 300);
      });
    },



    toggleMode() {
      if (this.loading) return;

      const modeNames = Object.keys(Mode);
      const modeValues = Object.values(Mode);
      const index = modeValues.indexOf(this.mode);
      const nextIndex = (index + 1) % modeNames.length;
      this.mode = Mode[modeNames[nextIndex]];
      this.reset();
      this.max = !this.max;
    },

    prev() {
      if (this.isFirst && !this.infinite) return;
      const len = this.urlList.length;
      this.index = (this.index - 1 + len) % len;
    },

    next() {
      if (this.isLast && !this.infinite) return;
      const len = this.urlList.length;
      this.index = (this.index + 1) % len;
    },

    handleActions(action, options = {}) {
      if (this.loading) return;
      const { zoomRate, rotateDeg, enableTransition } = {
        zoomRate: 0.2,
        rotateDeg: 90,
        enableTransition: true,
        ...options
      };
      const { transform } = this;
      switch (action) {
        case 'zoomOut':
          if (transform.scale > 0.2) {
            transform.scale = parseFloat((transform.scale - zoomRate).toFixed(3));
          }
          break;
        case 'zoomIn':
          if (transform.scale < 5) {
            transform.scale = parseFloat((transform.scale + zoomRate).toFixed(3));
          }
          break;
        case 'clocelise':
          transform.deg += rotateDeg;
          break;
        case 'anticlocelise':
          transform.deg -= rotateDeg;
          break;
      }
      transform.enableTransition = enableTransition;
    }
  }
};
</script>

<style lang="scss" scoped>
.viewer-fade-enter-active {
  animation: viewer-fade-in .3s;
}

.viewer-fade-leave-active {
  animation: viewer-fade-out .3s;
}

@keyframes viewer-fade-in {
  0% {
    transform: translate3d(0, -20px, 0);
    opacity: 0;
  }

  100% {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}

@keyframes viewer-fade-out {
  0% {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }

  100% {
    transform: translate3d(0, -20px, 0);
    opacity: 0;
  }
}

.el-image-viewer__wrapper {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 30%;
  height: 40%;
  min-width: 500px;
  min-height: 400px;
  overflow: hidden;
  margin: 15% auto 0;
  z-index: 99999;
  pointer-events: auto; // 确保主容器可以接收事件
}

.el-image-viewer__wrapper_max {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  z-index: 99999;
  position: fixed;
  top: 0;
  left: 0;
  transform: none;
  pointer-events: auto; // 确保最大化模式下也可以接收事件
}

.el-image-viewer__btn {
  position: absolute;
  z-index: 15; // 设置按钮z-index高于OCR面板(10)和图片容器(1)
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  opacity: .8;
  cursor: pointer;
  box-sizing: border-box;
  user-select: none;
  pointer-events: auto; // 确保按钮可以接收点击事件
}

.el-image-viewer__close {
  top: 40px;
  right: 40px;
  width: 40px;
  height: 40px;
  font-size: 24px;
  color: #fff;
  background-color: #606266;
  transition: right 0.3s ease;
}

// 当OCR面板显示时，关闭按钮向左移动
.el-image-viewer__close.with-ocr-panel {
  right: calc(30% + 60px); // OCR面板宽度 + 原始边距 + 额外间距
}

.el-image-viewer__canvas {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 1; // 设置图片容器的z-index较低
  pointer-events: auto; // 图片容器允许接收事件（用于图片拖拽等）
}

.el-image-viewer__actions {
  position: absolute;
  left: 50%;
  bottom: 30px;
  transform: translateX(-50%);
  width: auto;
  height: 44px;
  padding: 0 23px;
  background-color: #606266;
  border-color: #fff;
  border-radius: 22px;
  transition: left 0.3s ease, transform 0.3s ease;
  z-index: 15; // 设置操作栏z-index高于OCR面板(10)和图片容器(1)
  pointer-events: auto; // 确保操作栏可以接收点击事件
}

// 当OCR面板显示时，底部操作栏向左移动
.el-image-viewer__actions.with-ocr-panel {
  left: 35%; // 调整到图片显示区域的中心
  transform: translateX(-50%);
}

.el-image-viewer__actions__inner {
  width: 100%;
  height: 100%;
  text-align: justify;
  cursor: default;
  font-size: 23px;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-around;
  pointer-events: auto; // 确保内部元素可以接收点击事件

  // 确保所有图标按钮可以点击
  i {
    cursor: pointer;
    pointer-events: auto;
    transition: opacity 0.3s;

    &:hover {
      opacity: 0.7;
    }
  }

  // 确保OCR按钮可以点击
  .ocr-extract-btn {
    cursor: pointer;
    pointer-events: auto;
  }
}

.el-image-viewer__actions__divider {
  width: 1px;
  height: 20px;
  background-color: #fff;
  margin: 0 8px;
}

.el-image-viewer__prev {
  top: 50%;
  transform: translateY(-50%);
  left: 40px;
  width: 44px;
  height: 44px;
  font-size: 24px;
  color: #fff;
  background-color: #606266;
  border-color: #fff;
  transition: left 0.3s ease;
}

// 当OCR面板显示时，上一张按钮保持在图片显示区域内
.el-image-viewer__prev.with-ocr-panel {
  left: 20px; // 距离左边缘20px，确保在图片显示区域内
}

.el-image-viewer__next {
  top: 50%;
  transform: translateY(-50%);
  right: 40px;
  width: 44px;
  height: 44px;
  font-size: 24px;
  color: #fff;
  background-color: #606266;
  border-color: #fff;
  text-indent: 2px;
  transition: right 0.3s ease;
}

// 当OCR面板显示时，下一张按钮向左移动，避免被OCR面板遮挡
.el-image-viewer__next.with-ocr-panel {
  right: calc(30% + 20px); // OCR面板宽度 + 20px边距，确保在图片显示区域内
}

.el-image-viewer__prev.is-disabled,
.el-image-viewer__next.is-disabled {
  color: #c0c4cc;
  cursor: not-allowed;
}

.el-image-viewer__mask {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  opacity: .5;
  background: #000;
  z-index: 0; // 确保遮罩层在最底层
  pointer-events: none; // 遮罩层不拦截点击事件
}

.el-image-viewer__content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  pointer-events: none; // 内容容器不拦截事件，让子元素自行处理
}

.el-image-viewer__img {
  display: block;
  width: auto;
  height: auto;
  max-width: 100%;
  max-height: 100%;
  cursor: grab;
  pointer-events: auto; // 图片允许接收事件（用于拖拽）
}

// 图片尺寸现在通过JavaScript动态控制，不再需要CSS约束

.el-image-viewer__img:active {
  cursor: grabbing;
}

// OCR按钮样式
.ocr-extract-btn {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  text-align: center;
  line-height: 1.2;
  border: 1px solid;
  transition: all 0.3s;
  user-select: none;
  white-space: nowrap;

  &.clickable {
    background-color: #409eff;
    border-color: #409eff;
    color: #ffffff;
    cursor: pointer;

    &:hover {
      background-color: #66b1ff;
      border-color: #66b1ff;
    }
  }

  &.loading {
    background-color: #c0c4cc;
    border-color: #c0c4cc;
    color: #ffffff;
    cursor: not-allowed;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      left: 4px;
      top: 50%;
      transform: translateY(-50%);
      width: 10px;
      height: 10px;
      border: 1px solid transparent;
      border-top: 1px solid #ffffff;
      border-radius: 50%;
      animation: loading-rotate 1s linear infinite;
    }

    padding-left: 20px;
  }

  &.extracted {
    background-color: #67c23a;
    border-color: #67c23a;
    color: #ffffff;
    cursor: pointer;

    &:hover {
      background-color: #85ce61;
      border-color: #85ce61;
    }
  }
}

@keyframes loading-rotate {
  0% {
    transform: translateY(-50%) rotate(0deg);
  }

  100% {
    transform: translateY(-50%) rotate(360deg);
  }
}

// OCR结果面板样式
.ocr-results-panel {
  position: absolute;
  top: 0;
  right: 0;
  width: 30%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  border-left: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  z-index: 10; // 设置OCR面板的z-index高于图片容器
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15); // 添加阴影增强层级效果
  pointer-events: auto; // OCR面板允许接收事件

  .ocr-panel-header {
    padding: 15px 20px;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f8f9fa;

    .ocr-panel-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }

    .ocr-panel-controls {
      .close-panel-btn {
        color: #909399;
        cursor: pointer;
        padding: 4px;
        border-radius: 3px;
        transition: all 0.3s;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;

        &:hover {
          background-color: #f56c6c;
          color: white;
        }

        i {
          font-size: 14px;
        }
      }
    }
  }

  .ocr-panel-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;
    min-height: 0; // 确保flex子元素可以正确收缩
    max-height: calc(100vh - 120px); // 设置最大高度，确保有滚动空间

    // 始终显示滚动条，确保滚动功能正常
    scrollbar-width: thin;
    scrollbar-color: rgba(193, 193, 193, 0.6) transparent;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.05);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(193, 193, 193, 0.6);
      border-radius: 3px;
      transition: background 0.3s ease;

      &:hover {
        background: rgba(168, 168, 168, 0.8);
      }
    }

    // 悬停时显示更明显的滚动条
    &:hover {
      scrollbar-color: rgba(168, 168, 168, 0.8) transparent;

      &::-webkit-scrollbar-thumb {
        background: rgba(168, 168, 168, 0.8);
      }
    }

    // 确保内容可以正常滚动
    &:focus {
      outline: none;
    }

    // 确保鼠标滚轮事件能正确处理
    pointer-events: auto;

    .ocr-result-fields {
      .ocr-result-field {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .field-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 4px;

          .field-label {
            font-size: 13px;
            color: #666;
            font-weight: 500;
          }

          .field-value {
            font-size: 14px;
            color: #333;
            word-break: break-all;
            line-height: 1.5;
            background-color: #f8f9fa;
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #e9ecef;
          }
        }

        .field-copy {
          margin-left: 12px;
          margin-top: 20px;

          .copy-btn {
            color: #409eff;
            cursor: pointer;
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 3px;
            transition: all 0.3s;
            border: 1px solid #409eff;
            background-color: transparent;

            &:hover {
              background-color: #409eff;
              color: white;
            }

            &:focus {
              outline: none;
            }
          }
        }
      }
    }

    .no-ocr-result {
      text-align: center;
      padding: 40px 20px;
      color: #909399;

      p {
        margin: 0 0 8px 0;
        font-size: 14px;

        &.tip {
          font-size: 12px;
          color: #c0c4cc;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .ocr-results-panel {
    width: 40%;
  }

  // 中等屏幕下的按钮位置调整
  .el-image-viewer__close.with-ocr-panel {
    right: calc(40% + 60px);
  }

  .el-image-viewer__actions.with-ocr-panel {
    left: 30%;
  }

  .el-image-viewer__prev.with-ocr-panel {
    left: 20px;
  }

  .el-image-viewer__next.with-ocr-panel {
    right: calc(40% + 20px);
  }
}

@media (max-width: 768px) {
  .ocr-results-panel {
    width: 300px;
  }

  // 小屏幕下的按钮位置调整
  .el-image-viewer__close.with-ocr-panel {
    right: 320px; // OCR面板宽度 + 边距
  }

  .el-image-viewer__actions.with-ocr-panel {
    left: calc(50% - 150px); // 向左偏移避免被OCR面板遮挡
  }

  .el-image-viewer__prev.with-ocr-panel {
    left: 20px; // 保持在左侧可见区域
  }

  .el-image-viewer__next.with-ocr-panel {
    right: 320px; // 避免被OCR面板遮挡
  }
}
</style>
