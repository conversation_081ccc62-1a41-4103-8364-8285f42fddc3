import Layout from '@/layout'
import Iframe from '@/iframe'
const productRouters = {
  path: '/product',
  name: 'product',
  // component: Layout,
  component: Iframe,
  redirect: 'noRedirect',
  meta: { title: '', icon: 'nested' },
  children: [
    {
      path: 'addProduct',
      name: 'addProduct',
      component: () => import('@/views/product/addProduct'),
      meta: { title: '新增商品' },
      hidden: true
    },
    {
      path: 'addMainProduct',
      name: 'addMainProduct',
      component: () => import('@/views/product/addMainProduct'),
      meta: { title: '新增主商品' },
      hidden: true
    },
    {
      path: 'addDeputyProduct',
      name: 'addDeputyProduct',
      component: () => import('@/views/product/addDeputyProduct'),
      meta: { title: '新增副商品' },
      hidden: true
    },
    {
      path: 'deputyProductDetail',
      name: 'deputyProductDetail',
      component: () => import('@/views/product/deputyProductDetail'),
      meta: { title: '副商品详情' },
      hidden: true
    },
    {
      path: 'editProduct',
      name: 'editProduct',
      component: () => import('@/views/product/editProduct'),
      meta: { title: '修改商品' },
      hidden: true
    },
    {
      path: 'detailProduct',
      name: 'detailProduct',
      component: () => import('@/views/product/detailProduct'),
      meta: { title: '商品详情' },
      hidden: true
    },
    {
      path: 'draftProduct',
      name: 'draftProduct',
      component: () => import('@/views/product/draftProduct'),
      meta: { title: '草稿编辑' },
      hidden: true
    },
    {
      path: 'reviewProduct',
      name: 'reviewProduct',
      component: () => import('@/views/product/reviewProduct'),
      meta: { title: '商品审核明细页' },
      hidden: true
    },
    {
      path: 'mergeProductDetail',
      name: 'mergeProductDetail',
      component: () => import('@/views/product/mergeProductDetail'),
      meta: { title: '商品审核明细页' },
      hidden: true
    },

    {
      path: 'extendedAttr',
      name: 'extendedAttr',
      component: () => import('@/views/product/extendedAttr'),
      meta: { title: '扩展属性' }
    },
    {
      path: 'qualificationAttr',
      name: 'qualificationAttr',
      component: () => import('@/views/product/qualificationAttr'),
      meta: { title: '资质属性' }
    },
    {
      path: 'salesAttr',
      name: 'salesAttr',
      component: () => import('@/views/product/salesAttr'),
      meta: { title: '销售属性' }
    },
    {
      path: 'sau',
      name: 'sau',
      component: () => import('@/views/product/sau'),
      meta: { title: '属性管理' }
    },
    {
      path: 'modifyRecord',
      name: 'modifyRecord',
      component: () => import('@/views/product/modifyRecord'),
      meta: { title: '修改记录' }
    },
    {
      path: 'productList',
      name: 'productList',
      component: () => import('@/views/product/productList'),
      meta: { title: '商品列表' }
    },
    {
      path: 'productCompletetList',
      name: 'productCompletetList',
      component: () => import('@/views/product/productComplete/productCompletetList'),
      meta: { title: '商品完整度' }
    },
    {
      path: 'spuList',
      name: 'spuList',
      component: () => import('@/views/product/spu/spuList'),
      meta: { title: 'spu列表' }
    },
    {
      path: 'mergeSpu',
      name: 'mergeSpu',
      component: () => import('@/views/product/spu/mergeSpu'),
      meta: { title: '移动商品' }
    },
    {
      path: 'batchModifyList',
      name: 'batchModifyList',
      component: () => import('@/views/product/batchModifyList'),
      meta: { title: '批量修改记录' }
    }
  ]
}

export default productRouters
