<template>
  <div class="container-receive">
    <div class="search-form-wrap">
      <el-form :model="model" ref="refSearchForm">
        <el-row type="flex">
          <!-- 来源 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="来源">
              <el-select
                v-model="formData.source"
                placeholder="请选择"
                clearable
              >
                <el-option label="爬虫" :value="20"></el-option>
                <el-option label="百草" :value="1"></el-option>
                <el-option label="系统上传" :value="2"></el-option>
                <el-option label="荷叶健康" :value="3"></el-option>
                <!-- <el-option label="智慧脸商城" :value="4"></el-option>
                <el-option label="POP" :value="5"></el-option>
                <el-option label="EC" :value="6"></el-option>
                <el-option label="客服" :value="7"></el-option>
                <el-option label="供多多" :value="8"></el-option>
                <el-option label="智鹿" :value="9"></el-option>
                <el-option label="神农" :value="10"></el-option> -->
                <el-option label="新品上报" :value="21"></el-option>
                <el-option label="友商库-自动创建" :value="22"></el-option>
                <el-option label="友商库-运营创建" :value="23"></el-option>
                <el-option label="荷叶积分" :value="25"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 申请人 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="申请人" prop="applyer">
              <el-input
                v-model="formData.applyer"
                placeholder="请输入申请人"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 是否含图 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="是否含图">
              <el-select
                v-model="formData.containPictureStatus"
                placeholder="请选择"
                clearable
              >
                <el-option label="是" :value="1"></el-option>
                <el-option label="否" :value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 申请时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="申请时间" prop="time">
              <el-date-picker
                v-model="formData.time"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                clearable
              ></el-date-picker>
            </el-form-item>
          </el-col>


          <!-- 是否自营 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="是否自营">
              <el-select
                v-model="formData.businessType"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" :value='null'></el-option>
                <el-option label="自营" :value="1"></el-option>
                <el-option label="非自营" :value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 是否高优 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="是否高优">
              <el-select
                v-model="formData.whetherHighQualityGoods"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" :value='null'></el-option>
                <el-option label="高优" :value="1"></el-option>
                <el-option label="非高优" :value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 商品编码 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="商品编码" prop="mixtureProduct">
              <el-input
                v-model="formData.mixtureProduct"
                placeholder="输入商品ID，原商品编码，商品编码搜索"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 按钮 -->
          <el-col :span="6" :offset="18">
            <el-button size="medium" @click="btnResetClick">重置</el-button>
            <el-button type="primary" size="medium" @click="btnSearchClick"
              >查询</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="recieve-wrap">
      <div class="recieve">
        <div class="content">
          <div class="title">任务总数量：{{ total }}个</div>
          <!--<el-select v-model="ReceiveNum" placeholder="请选择"> -->
            <el-select v-model="ReceiveNum" @blur="selectBlur" filterable placeholder="请选择">
            <el-option label="25" :value="25"> </el-option>
            <el-option label="50" :value="50"> </el-option>
            <el-option label="100" :value="100"> </el-option>
            <el-option label="200" :value="200"> </el-option>
          </el-select>
          <el-button
            type="primary"
            class="btn"
            size="mimi"
            @click="handleReceive"
            >批量领取</el-button
          >
        </div>
        <div class="tip">注意：选择数量小于任务总数，将领取剩余的任务数量</div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getProductSupplementPictureCount,
  receivePictureZiYing,
} from "@/api/productPicture.js";

export default {
  name: "prepareReceive",
  data() {
    return {
      formData: {
        source: 20,
        applyer: "",
        mixtureProduct: "",
        containPictureStatus: "",
        time: "",
        whetherHighQualityGoods: null,
        businessType: null
      },
      ReceiveNum: 25,
      total: 0,
    };
  },
  created() {
    // this.btnSearchClick();
  },
  methods: {
   selectBlur(e) {
      this.ReceiveNum = +e.target.value === 0 || isNaN(+e.target.value) ? '' : +e.target.value
    },
    handleReceive() {
      if (!this.total) {
        this.$message.warning("当前筛选条件无任务");
        return;
      }
      if (!this.ReceiveNum) {
        this.$message.warning("请选择领取数量");
        return;
      }
      const loading = this.$loading({
        lock: true,
        text: "任务领取中...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      let param = Object.assign({}, this.formData);
      param.createStartTime = param.time ? param.time[0] : "";
      param.createEndTime = param.time ? param.time[1] : "";
      param.planClaimNum = this.ReceiveNum;
      param.businessType = param.businessType ? param.businessType : null;
      delete param.time;
      receivePictureZiYing(param).then((res) => {
        if (!res.retCode) {
          loading.close();
          this.$emit("changeTab", "second");
          this.btnSearchClick();
          this.ReceiveNum = "";
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      });
    },
    btnSearchClick() {
      if(this.formData.businessType === '请选择'){
        this.$message({
            showClose: true,
            type: "error",
            message: '请选择是否自营',
          });
        return
      }
      if(this.formData.whetherHighQualityGoods === '请选择') {
        this.$message({
            showClose: true,
            type: "error",
            message: '请选择是否高优',
            });
        return
      }
      let param = Object.assign({}, this.formData);
      param.createStartTime = param.time ? param.time[0] : "";
      param.createEndTime = param.time ? param.time[1] : "";
      delete param.time;
      getProductSupplementPictureCount(param).then((res) => {
        if (!res.retCode) {
          this.total = res.data;
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      });
    },
    btnResetClick() {
      this.formData = {
        businessType: null,
        whetherHighQualityGoods: null,
        source: 20,
        applyer: "",
        mixtureProduct: "",
        containPictureStatus: "",
        time: "",
      };
      this.total = 0,
      this.btnSearchClick();
    },
  },
};
</script>

<style lang="scss" scoped>
.search-form-wrap {
  width: 100%;
  padding-bottom: 15px;
  border-bottom: 1px dashed #e4e4eb;
  .el-row {
    flex-wrap: wrap;
    .el-col {
      display: flex;
      justify-content: flex-end;
      .el-form-item {
        flex: 1;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        width: 100%;
        margin-right: 0;
        margin-bottom: 15px;
        /deep/ {
          .el-form-item__label {
            width: 96px;
            line-height: normal;
            padding: 0 12px;
            color: #292933;
            font-weight: normal;
          }
          .el-form-item__content {
            flex: 1;
            .el-range-editor.el-input__inner {
              width: 100%;

              .el-range-separator {
                width: 14px;
                padding: 0;
              }
            }
          }
        }
        .el-input {
          width: 100%;
        }
        .el-select {
          width: 100%;
        }
      }
    }
  }
}
.recieve-wrap {
  padding-top: 15px;
}
.recieve {
  width: 625px;
  padding-top: 50px;
  padding-left: 40px;
  background: #e9f7fe;
  margin: 0 auto;
  .content {
    display: flex;
    align-items: center;
    .title {
      padding-right: 50px;
      color: #3b95a8;
    }
    .btn {
      margin-left: 15px;
    }
  }
  .tip {
    font-size: 12px;
    color: #bababa;
    padding: 40px 0;
  }
}
</style>
