<template>
  <div class="container-receive">
    <!-- 查询表单 -->
    <div class="search-form-wrap">
      <el-form ref="refSearchForm">
        <el-row>审核流程</el-row>
        <el-row type="flex">
          <!-- 工作流 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="工作流">
              <el-select
                filterable
                v-model="searchFormData.procKey"
                placeholder="请选择"
                @change="changeProcKey"
              >
                <el-option label="全部" :value="null"></el-option>
                <el-option
                  v-for="item in procKeyList"
                  :key="item.procKey"
                  :label="item.procKeyName"
                  :value="item.procKey"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 批量类型 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="searchFormData.procKey === 'meBatchModify'">
            <el-form-item label="批量类型">
              <el-select v-model="searchFormData.batchType" placeholder="请选择">
                <el-option label="全部" :value="null"></el-option>
                <el-option label="基础信息" :value="1"></el-option>
                <el-option label="sku信息" :value="25"></el-option>
                <el-option label="税率" :value="14"></el-option>
                <el-option label="标签信息" :value="24"></el-option>
                <el-option label="扩展信息" :value="2"></el-option>
                <el-option label="用药指导" :value="6"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-show="!isFold">发起人</el-row>
        <el-row v-show="!isFold">
          <!-- 发起人 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="发起人">
              <el-input v-model="searchFormData.taskCreater"></el-input>
            </el-form-item>
          </el-col>
          <!-- 所属机构 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="所属机构" prop="mechanism">
             <div class="pop-tip" v-show="showTip">{{nameStr}}</div>
              <el-input @mouseover.native="showTip1"  @mouseleave.native="hideTip" v-model="nameStr"
                @click.native="showTree = !showTree; isShowPeople = false" readonly clearable>
                <el-button slot="append" :icon="showTree ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
                @click.stop="showTree = !showTree; isShowPeople = false"></el-button>
              </el-input>
              <el-tree v-if="showTree" class="tree-box" :data="deptList" :props="defaultProps" :default-expanded-keys="['1']" node-key="lev">
                <span slot-scope="{ data }">
                  <span class="line mr10">{{ data.dptName }}</span>
                  <el-link v-if="+data.lev > 1" type="primary" class="lh40" @click="showPeople(data.dptCode)">选人</el-link>
                </span>
              </el-tree>
              <div class="people-box" v-if="isShowPeople">
                <el-select
                  ref="autoSelect"
                  multiple
                  collapse-tags
                  v-model="currentPeopleList"
                  placeholder="请选择"
                  @remove-tag="removeTag1"
                  @change="changeSelect1"
                >
                  <el-option label="全选" value="全选" @click.native="selectAll1"></el-option>
                  <el-option v-for="item in peopleList" :key="item.oaId" :label="item.realname" :value="item.oaId"></el-option>
                </el-select>
                <div style="margin-top:130px;text-align:center;">请展开下拉框选人</div>
              </div>
            </el-form-item>
          </el-col>
          <!-- 发起时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="发起时间">
              <el-date-picker
                v-model="searchFormData.createTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <!-- 角色类型 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="角色类型">
              <el-select
              filterable
                v-model="searchFormData.taskCreateRoleCode"
                placeholder="请选择"
              >
                <el-option label="全部" :value="null"></el-option>
                <el-option
                  v-for="item in roleList"
                  :key="item.roleCode"
                  :label="item.roleName"
                  :value="item.roleCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-show="!isFold">任务</el-row>
        <el-row>
          <!-- 任务来源 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="任务来源">
              <el-select
              filterable
                :value="searchFormData.taskSourceList"
                placeholder="请选择"
                multiple
                @input="changeTaskList"
              >
               <el-option label="全部" value="xxx"></el-option>
                <el-option
                  v-for="item in sourceList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 商品编码 -->
          <el-col v-show="!isFold" :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="商品编码">
              <el-input v-model="searchFormData.productCode"></el-input>
            </el-form-item>
          </el-col>
          <!-- 通用名 -->
          <el-col v-show="!isFold" :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="通用名">
              <el-input v-model="searchFormData.generalName"></el-input>
            </el-form-item>
          </el-col>
          <!-- 批准文号 -->
          <el-col v-show="!isFold" :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="批准文号">
              <el-input v-model="searchFormData.approvalNo"></el-input>
            </el-form-item>
          </el-col>
          <!-- 生产厂家 -->
          <el-col v-show="!isFold" :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="生产厂家">
              <el-input v-model="searchFormData.manufacturer"></el-input>
            </el-form-item>
          </el-col>
          <!-- 版本号 -->
          <el-col v-show="!isFold" :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="版本号">
              <el-input v-model="searchFormData.pictureVersion"></el-input>
            </el-form-item>
          </el-col>
          <!-- 规格 -->
          <el-col v-show="!isFold" :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="规格">
              <el-input v-model="searchFormData.spec"></el-input>
            </el-form-item>
          </el-col>
          <!-- 小包装条码 -->
          <el-col v-show="!isFold" :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="小包装条码">
              <el-input v-model="searchFormData.smallPackageCode"></el-input>
            </el-form-item>
          </el-col>
          <!-- 最后更新时间 -->
          <el-col v-show="!isFold" :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="最后更新时间">
              <el-date-picker
                v-model="searchFormData.updateTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="商品业务编码">
              <el-input v-model="searchFormData.outProductCode"></el-input>
            </el-form-item>
          </el-col>
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="复制品编码" class="green">
              <el-input v-model="searchFormData.barcodes" >
                <el-button slot="append" @click="searchCodes">查询原商品编码</el-button>
                <!-- <template slot="append"  ><span @click="searchCodes">查询原商品编码</span></template> -->
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <!-- 按钮 -->
          <el-col :span="6" :offset="18">
            <el-button type="primary" size="medium" @click="isFold = !isFold"
              >展开/收起</el-button
            >
            <el-button type="primary" size="medium" @click="btnSearchClick(1)"
              >查询</el-button
            >
            <el-button size="medium" @click="btnResetClick">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="recieve-wrap">
      <div class="recieve">
        <div class="content">
          <div class="title">任务总数量：{{ total }}个</div>
          <el-select v-model="ReceiveNum" placeholder="请选择" @blur="selectBlur" filterable>
            <el-option label="25" :value="25"> </el-option>
            <el-option label="50" :value="50"> </el-option>
            <el-option label="100" :value="100"> </el-option>
            <el-option label="200" :value="200"> </el-option>
          </el-select>
          <el-button
            type="primary"
            class="btn"
            size="mimi"
            @click="handleReceive"
            >批量领取</el-button
          >
        </div>
        <div class="tip">注意：选择数量小于任务总数，将领取剩余的任务数量</div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from "axios"
import { userApprovalProcessList, queryOriginBarcodes,userRoleList, taskSourceList, preClaimTaskNum, batchClaimTask,
getOrganizeList, getEmployee } from "@/api/workManage"
let setTime = 0
let tt = false
export default {
  name: "",
  props: {},
  data() {
    return {
      showTip: false,
      isShowPeople: false,
      peopleList:[],
      currentPeopleList:[],
      nameList:[],
      showTree: false,
      ReceiveNum:'',
      isFold:true,
      searchFormData: {
        procKey:null,//工作流
        batchType: null, //批量类型
        createTime:[],//发起时间
        taskCreater:'',//发起人
        taskPostCodeList:[],//所属机构 入参
        taskCreateRoleCode:null,//角色类型
        taskSourceList:[],//任务来源
        updateTime:[],//最后更新时间
        generalName:'',//通用名
        approvalNo:'',//批准文号
        manufacturer:'',//生产厂家
        pictureVersion:'',//版本号
        spec:'',//规格
        smallPackageCode:'',//小包装条码
        productCode:'', //商品编码
        outProductCode: '', //商品业务编码
        barcodes:"",//复制品编码
      },
      procKeyList:[], //工作流列表
      roleList:[],//角色类型列表
      sourceList:[],//任务来源列表
      deptList:[],//所属机构列表
      total: 0
    };
  },
  computed: {
    nameStr() {
      return this.nameList.join()
    }
  },
  created() {
    this.getUserApprovalProcessList()
    this.getUserRoleList()
    this.getTaskSourceList()
    this.getDeptList()
    this.btnSearchClick();
  },
  methods: {
    changeTaskList(val) {
      console.log(val);

      if (val.some(str => str == 'xxx') && this.searchFormData.taskSourceList.every(str => str != 'xxx')) {
        this.searchFormData.taskSourceList = ["xxx"];
      } else if (this.searchFormData.taskSourceList.some(str => str == 'xxx') && val.some(str => str == 'xxx')) {
        this.searchFormData.taskSourceList = val.filter(str => str != 'xxx');
      } else {
        this.searchFormData.taskSourceList = val;
      }
    },
    searchCodes(){
      let dataNum=this.searchFormData.barcodes.split(",")
      if(dataNum.length>200){
        this.$message.error("最多输入200条");
        return
      }
      let url="" //https://pop-admin.test.ybm100.com/inner/query/queryOriginBarcodes
      if(process.env.NODE_ENV=="development"||process.env.NODE_ENV=="test"){
        url="https://pop-admin.test.ybm100.com/inner/query/queryOriginBarcodes"
      }else if(process.env.NODE_ENV=="production"){
        url="https://pop-admin.ybm100.com/inner/query/queryOriginBarcodes"
      }
      axios({
        url:`${url}?barcodes=${this.searchFormData.barcodes}`,
        method:"post"
      }).then(
        res=>{
          res=res.data
          if(res.code==0){
            this.searchFormData.outProductCode=Array.isArray(res.data)?res.data.join(","):""
          }else{
            this.$message.error(res.message);
          }
        }
      )
    },
    // 修改工作流查询条件
    changeProcKey(e) {
      if (e !== "meBatchModify") {
        this.searchFormData.batchType = null
      }
    },
    showTip1() {
      tt = true
      clearTimeout(setTime)
      this.showTip = true
    },
    hideTip() {
      this.showTip = false
      /* tt = false
      setTime = setTimeout(() => {
        if (!tt) {
          this.showTip = false
          tt = false
        }
      }, 1500); */
    },
    async showPeople(e) {
      try {
        this.currentPeopleList = this.currentPeopleList.filter(item => {
          return item !== "全选"
        })
        this.isShowPeople = true
        const res = await getEmployee(e)
        this.peopleList = res.data
        this.$refs.autoSelect.toggleMenu()
        let temp = []
        this.peopleList.map((item, index) => {
          if (this.searchFormData.taskPostCodeList.indexOf(item.oaId) !== -1) {
            temp.push(index)
          }
        })
        this.currentPeopleList = []
        temp.map(item => {
          this.currentPeopleList.push(this.peopleList[item].oaId)
        })
        if (this.currentPeopleList.length === this.peopleList.length) {
          this.currentPeopleList.unshift("全选")
        }
      } catch (error) {
        console.log(error);
      }
    },
    selectBlur(e) {
      this.ReceiveNum = +e.target.value === 0 || isNaN(+e.target.value) ? '' : +e.target.value
    },
    // 获取工作流列表
    async getUserApprovalProcessList (){
      try{
        const res = await userApprovalProcessList()
        if(res.retCode === 0) {
          this.procKeyList = res.data
        }
      }
      catch(error) {
        console.log(error);
      }
    },
    // 获取角色类型列表
    async getUserRoleList() {
      try{
        const res = await userRoleList()
        if(res.retCode === 0) {
          this.roleList = res.data
        }
      }
      catch(error) {
        console.log(error);
      }
    },
    // 获取任务来源列表
    async getTaskSourceList() {
      try{
        const res = await taskSourceList()
        this.searchFormData.taskSourceList = [];
        if(res.retCode === 0) {
          this.sourceList = res.data
          res.data.forEach(item => {
            if (item.isSelected == 1) {
              this.searchFormData.taskSourceList.push(item.key);
            }
          })
        }
      }
      catch(error) {
        console.log(error);
      }
    },
    changeSelect1(val) {
      // 追加所有已经选中的人
      let temp = []
      let deleteList = []
      let deleteNameList = []
      let tempNameList = []
      this.peopleList.map(item => {
        if (this.currentPeopleList.indexOf(item.oaId) !== -1) {
          temp.push(item.oaId)
          tempNameList.push(item.realname)
        } else {
          deleteList.push(item.oaId)
          deleteNameList.push(item.realname)
        }
      })
      temp = temp.concat(this.searchFormData.taskPostCodeList)
      this.searchFormData.taskPostCodeList = Array.from(new Set(temp))
      tempNameList = tempNameList.concat(this.nameList)
      this.nameList = Array.from(new Set(tempNameList))
      let deleteIndexList = []
      let deleteNameIndexList = []
      deleteList.map(item => {
        if (this.searchFormData.taskPostCodeList.indexOf(item) !== -1) {
          deleteIndexList.push(this.searchFormData.taskPostCodeList.indexOf(item))
        }
      })
      for (let i = deleteIndexList.length - 1; i >= 0; i--) {
        this.searchFormData.taskPostCodeList.splice(deleteIndexList[i], 1)
      }
      deleteNameList.map(item => {
        if (this.nameList.indexOf(item) !== -1) {
          deleteNameIndexList.push(this.nameList.indexOf(item))
        }
      })
      for (let i = deleteNameIndexList.length - 1; i >= 0; i--) {
        this.nameList.splice(deleteNameIndexList[i], 1)
      }
      // 单点到全选
      if (!val.includes("全选") && val.length === this.peopleList.length) {
        this.currentPeopleList.unshift("全选")
      } else if (val.includes("全选") && val.length - 1 < this.peopleList.length) { //全选点到不全选
        // 当前下拉框移除全选
        this.currentPeopleList = this.currentPeopleList.filter(item => {
          return item !== "全选"
        })
      }
    },
    // 下拉框移除值
    removeTag1(val) {
      if (val === "全选") {
        let temp = []
        this.peopleList.map((item, index) => {
          if (this.searchFormData.taskPostCodeList.indexOf(item.oaId) !== -1) {
            temp.push(index)
          }
        })
        for (let i = temp.length; index >= 0; i--) {
          this.searchFormData.taskPostCodeList.splice(temp[i], 1)
          this.nameList.splice(temp[i], 1)
        }
        this.currentPeopleList = []
      } else {
        this.searchFormData.taskPostCodeList.map((item, i) => {
          if (item === val) {
            this.searchFormData.taskPostCodeList.splice(i, 1)
            this.nameList.splice(i, 1)
          }
        })
      }
    },
    selectAll1() {
      let temp = []
      // 全选
      if (this.currentPeopleList.length < this.peopleList.length) {
        this.currentPeopleList = []
        this.searchFormData.taskPostCodeList = []
        this.peopleList.map(item => {
          temp.push(item.realname)
          this.currentPeopleList.push(item.oaId)
          this.searchFormData.taskPostCodeList.push(item.oaId)
        })
        this.currentPeopleList.unshift("全选")
        this.searchFormData.taskPostCodeList = Array.from(new Set(this.searchFormData.taskPostCodeList))
        this.nameList = this.nameList.concat(temp)
        this.nameList = Array.from(new Set(this.nameList))
        // this.searchFormData.taskPostCodeList.unshift("全选")
      } else {
        //取消全选
        this.currentPeopleList.shift()
        this.peopleList.map(item => {
          for (let index = this.currentPeopleList.length -1; index >= 0; index--) {
            if (this.currentPeopleList[index] === item.oaId) {
              temp.push(item.oaId)
              this.currentPeopleList.splice(index, 1)
              this.nameList.splice(index, 1)
            }
          }
        })
        let deleteIndexList = []
        temp.map(item => {
          if (this.searchFormData.taskPostCodeList.indexOf(item) !== -1) {
            deleteIndexList.push(this.searchFormData.taskPostCodeList.indexOf(item))
          }
        })
        for (let i = deleteIndexList.length - 1; i >= 0; i--) {
          this.searchFormData.taskPostCodeList.splice(deleteIndexList[i], 1)
        }
      }
    },
    // 获取所属机构列表
    async getDeptList() {
      try{
        const res = await getOrganizeList()
        this.deptList = res.data
      }
      catch(error) {
        console.log(error);
      }
    },
    // 领取任务
    handleReceive() {
      if (!this.total) {
        this.$message.warning("当前筛选条件无任务");
        return;
      }
      if (!this.ReceiveNum) {
        this.$message.warning("请选择领取数量");
        return;
      }
      const loading = this.$loading({
        lock: true,
        text: "任务领取中...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      let param = Object.assign({}, this.searchFormData);
      param.planClaimNum = this.ReceiveNum;
      if(param.createTime && param.createTime.length) {
        param.startTimeStart = param.createTime[0]
        param.startTimeEnd = param.createTime[1]
      }
      if(param.updateTime && param.updateTime.length) {
        param.updateTimeStart = param.updateTime[0]
        param.updateTimeEnd = param.updateTime[1]
      }
      param.taskSourceList = param.taskSourceList.filter(str => str != 'xxx');
      delete param.createTime
      delete param.updateTime
      batchClaimTask(param).then((res) => {
        if (!res.retCode) {
          loading.close();
          this.$emit("changeTab", "second");

          this.btnSearchClick();
          this.ReceiveNum = "";
        } else {
          loading.close();
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      });
    },
    btnSearchClick() {
      const outProductCode = this.searchFormData.outProductCode;
      if (outProductCode && outProductCode.split(',').length > 100) {
        this.$message.error("商品业务编码搜索最大支持100个");
        return;
      }
      let param = Object.assign({}, this.searchFormData);
      if(param.createTime && param.createTime.length) {
        param.startTimeStart = param.createTime[0]
        param.startTimeEnd = param.createTime[1]
      }
      if(param.updateTime && param.updateTime.length) {
        param.updateTimeStart = param.updateTime[0]
        param.updateTimeEnd = param.updateTime[1]
      }
      if (param.taskPostCodeList[0] === '全选') {
        param.taskPostCodeList.shift()
      }
      param.taskSourceList = param.taskSourceList.filter(str => str != "xxx");
      delete param.createTime
      delete param.updateTime
      preClaimTaskNum(param).then((res) => {
        if (!res.retCode) {
          this.total = res.data;
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      });
    },
    btnResetClick() {
      this.nameList = []
      this.currentPeopleList = []
      this.searchFormData = {
        procKey:null,//工作流
        batchType: null, //批量类型
        createTime:[],//发起时间
        taskCreater:'',//发起人
        taskPostCodeList:[],//所属机构 入参
        taskCreateRoleCode:null,//角色类型
        taskSourceList:[],//任务来源
        updateTime:[],//最后更新时间
        generalName:'',//通用名
        approvalNo:'',//批准文号
        manufacturer:'',//生产厂家
        pictureVersion:'',//版本号
        spec:'',//规格
        smallPackageCode:'',//小包装条码
        productCode:'', //商品编码
        outProductCode: '', //商品业务编码
        barcodes:"",//复制品编码
      };
      this.total = 0,
      this.btnSearchClick();
    },
  },
};
</script>

<style lang="scss" scoped>
   /**
   * 查询表单
   */
  .search-form-wrap {
    width: 100%;
    padding: 15px;
    border-bottom: 1px dashed #e4e4eb;

    .el-row {
      flex-wrap: wrap;

      .el-col {
        display: flex;
        justify-content: flex-end;

        .el-form-item {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          width: 100%;
          margin-right: 0;
          margin-bottom: 15px;

          /deep/ {
            .el-form-item__label {
              width: 116px;
              line-height: normal;
              padding: 0 12px;
              color: #292933;
              font-weight: normal;
            }

            .el-form-item__content {
              flex: 1;

              .el-range-editor.el-input__inner {
                width: 100%;

                .el-range-separator {
                  width: 14px;
                  padding: 0;
                }
              }
            }
          }

          .el-input {
            width: 100%;
          }

          .el-select {
            width: 100%;
          }
        }
      }
    }
  }
.recieve-wrap {
  padding-top: 15px;
}
.recieve {
  width: 625px;
  padding-top: 50px;
  padding-left: 40px;
  background: #e9f7fe;
  margin: 0 auto;
  .content {
    display: flex;
    align-items: center;
    .title {
      padding-right: 50px;
      color: #3b95a8;
    }
    .btn {
      margin-left: 15px;
    }
  }
  .tip {
    font-size: 12px;
    color: #bababa;
    padding: 40px 0;
  }
}
.tree-box{
  width:100%;
  height:40px;
  z-index:20;
  overflow:auto;
  height:330px;
  position:absolute;
  top:60px;
  border-radius:4px;
  border: 1px solid #DCDFE6;
}
.people-box{
  position:absolute;
  left:100%;
  top:60px;
  background:#a19f9f;
  width:100%;
  height:330px;
  z-index:20;
}
.mr10 {
  margin-right: 10px;
}
.lh40 {
  /deep/ .el-link--inner{
    line-height: 40px;
  }
}
/deep/ .el-tree-node {
  background: #fff;
  .el-tree-node__content{
    font-size: 14px;
    padding: 0 20px;
    position: relative;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #606266;
    height: 34px;
    line-height: 34px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    cursor: pointer;
  }
}
.pop-tip {
  position:absolute;
  z-index:30;
  top:-110px;
  width:230%;
  height:100px;
  overflow: hidden;
  background:#fff;
  border-radius:5px;
  border:1px solid #DCDFE6;
  line-height: 20px;
}
</style>
<style>
.green .el-input-group__append{
background-color: rgb(59,149,168);
color:white;
cursor: pointer;
}
</style>
