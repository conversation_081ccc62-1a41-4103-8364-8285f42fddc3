<template>
  <div class="four-level-classification-container">
    <el-row>
      <el-col :span="24">
        <el-button plain>全部展开</el-button>
        <el-button plain>修改记录</el-button>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-tree
          :data="data"
          node-key="id"
          draggable
          default-expand-all
          :expand-on-click-node="false"
        >
        <span class="custom-tree-node" slot-scope="{ node, data }">
          <span v-show="!data.isEdit">{{ data.label}}</span>
          <el-input
            v-model="data.label"
            v-if="data.isEdit"
            @blur="editLabel(data)"
            :ref="`editLabelInput${data.id}`"
          ></el-input>
          <span>
            <el-tooltip class="item" effect="dark" :open-delay="300" content="新增" placement="top">
              <el-button
                type="text"
                @click="append(node,data)"
                icon="el-icon-circle-plus-outline"
                circle
              ></el-button>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" :open-delay="300" content="编辑" placement="top">
              <el-button 
                type="text" 
                @click="edit(node,data)" 
                icon="el-icon-edit-outline" 
                circle
              ></el-button>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" :open-delay="300" content="禁用" placement="top">
              <el-button
                type="text"
                @click="remove(node, data)"
                icon="el-icon-folder-remove"
                circle
              ></el-button>
            </el-tooltip>
          </span>
        </span>
        </el-tree>
      </el-col>
    </el-row>
  </div>
</template>
<script>
let id = 1000;
export default {
  name: "",
  components: {},
  filters: {},
  props: {},
  data() {
    return {
      data: [
        {
          id: 1,
          label: "一级 1",

          children: [
            {
              id: 4,
              label: "二级 1-1",

              children: [
                {
                  id: 9,
                  label: "三级 1-1-1"
                },
                {
                  id: 10,
                  label: "三级 1-1-2"
                }
              ]
            }
          ]
        },
        {
          id: 2,
          label: "一级 2",

          children: [
            {
              id: 5,
              label: "二级 2-1"
            },
            {
              id: 6,
              label: "二级 2-2"
            }
          ]
        },
        {
          id: 3,
          label: "一级 3",

          children: [
            {
              id: 7,
              label: "二级 3-1"
            },
            {
              id: 8,
              label: "二级 3-2"
            }
          ]
        }
      ]
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    append(node, data) {
      const newChild = {
        id: id++,
        label: "testtest",
        children: []
      };
      if (!data.children) {
        this.$set(data, "children", []);
      }
      data.children.push(newChild);
    },
    edit(node, data) {
      this.$set(data, "isEdit", true);
      this.$nextTick(() => {
        console.log(this.$refs);
        this.$refs[`editLabelInput${data.id}`].focus();
      });
    },
    editLabel(data) {
      data.isEdit = false;
    },
    remove(node, data) {
      const parent = node.parent;
      const children = parent.data.children || parent.data;
      const index = children.findIndex(d => d.id === data.id);
      children.splice(index, 1);
    }
  }
};
</script>
<style lang="scss">
.four-level-classification-container {
  .custom-tree-node {
    .el-button.is-circle {
      padding: 0;
    }
    .el-button + .el-button {
      margin-left: 0px;
    }
    .el-input__inner {
      height: 30px;
      line-height: 30px;
    }
  }
  .el-tree-node__content {
    height: 30px;
  }
}
</style>
<style lang="scss" scoped>
.four-level-classification-container {
  padding: 20px;
  .el-row {
    margin-bottom: 20px;
  }
}
</style>