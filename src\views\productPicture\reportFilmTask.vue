<template>
  <div class="report-film-task-container">
    <!-- 查询表单 -->
    <div class="search-form-wrap">
      <el-form :model="model" ref="refSearchForm">
        <el-row type="flex">
          <!-- 版本上线时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="版本上线时间">
              <el-date-picker
                v-model="model.taskFinishAuditPassTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>

          <!-- 任务状态 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="任务状态">
              <el-select v-model="model.operateStatus" placeholder="请选择">
                <el-option value="" label="全部"></el-option>
                <el-option value="0" label="待审核"></el-option>
                <el-option value="1" label="通过"></el-option>
                <el-option value="2" label="设计驳回"></el-option>
                <el-option value="3" label="运营驳回"></el-option>
                <el-option value="4" label="待线下精修"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 任务ID -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="任务ID">
              <el-input v-model="model.applyCode" placeholder="模糊查询"></el-input>
            </el-form-item>
          </el-col>

          <!-- 所属机构 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="所属机构" prop="mechanism">
              <el-select 
                multiple
                collapse-tags
                v-model="model.mechanism"
                placeholder="请选择"
                @remove-tag="removeTag1"
                @change="changeSelect1"
                clearable
                >

                <el-option
                  label="全选"
                  value="全选"
                  @click.native="selectAll1"
                ></el-option>

                <el-option
                  v-for="item in model.mechanismOptions"
                  :key="'key_mechanismOptions_'+item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 申请时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="申请时间" prop="createTime">
              <el-date-picker
                v-model="model.createTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>

          <!-- 申请人 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="申请人">
              <el-input v-model="model.createUser" placeholder="模糊查询"></el-input>
            </el-form-item>
          </el-col>

          <!-- 初审审核时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="初审审核时间">
              <el-date-picker
                v-model="model.preAuditTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>

          <!-- 初审审核人 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="初审审核人">
              <el-input v-model="model.preReceiveUser" placeholder="模糊查询"></el-input>
            </el-form-item>
          </el-col>

          <!-- 线上精修上传时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="线上精修上传时间">
              <el-date-picker
                v-model="model.designAuditTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>

          <!-- 线上精修人 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="线上精修人">
              <el-input v-model="model.designReceiveUser" placeholder="模糊查询"></el-input>
            </el-form-item>
          </el-col>

          <!-- 线下精修申请时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="线下精修申请时间">
              <el-date-picker
                v-model="model.offlineDesignApplyTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>

          <!-- 线下精修人 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="线下精修人">
              <el-input v-model="model.offlineDesignReceiveUser" placeholder="模糊查询"></el-input>
            </el-form-item>
          </el-col>

          <!-- 商品编码 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="商品编码">
              <el-input v-model="model.productCode" placeholder="输入商品ID，原商品编码，商品编码搜索"></el-input>
            </el-form-item>
          </el-col>

          <!-- 是否自营 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="是否自营">
              <el-select v-model="model.businessType" placeholder="请选择">
                <el-option value="" label="全部"></el-option>
                <el-option value="1" label="自营"></el-option>
                <el-option value="2" label="非自营"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 是否存在关联任务 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="是否存在关联任务">
              <el-select v-model="model.hasRelationalTask" placeholder="请选择">
                <el-option :value="null" label="全部"></el-option>
                <el-option value="1" label="是"></el-option>
                <el-option value="0" label="否"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 按钮 -->
          <el-col :span="6" :offset="18" style="height:fit-content">
            <el-button type="primary" size="medium" @click="btnSearchClick">查询</el-button>
            <el-button size="medium" @click="btnResetClick">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- table上方按钮组 -->
    <el-row class="btn-group-wrap">
      <el-col>
        <el-button size="medium" @click="exportExcel">导出excel</el-button>
      </el-col>
    </el-row>

    <!-- table -->
        <div class="table-wrap">
          <vxe-table
            border
            highlight-hover-row
            resizable
            height="100%"
            auto-resize
            size="small"
            align="center"
            :tooltip-config="{enterable: false}"
            :loading="tableLoading_first"
            :data.sync="tableData_first"
            @cell-dblclick="cellDBLClickEvent"
          >
            <vxe-table-column
              field="index"
              title="序号"
              width="60"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <vxe-table-column
              field="mechanismName"
              title="申请人所属机构"
              min-width="120"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <vxe-table-column
              field="createTime"
              title="申请时间"
              min-width="120"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <vxe-table-column
              field="createUser"
              title="申请人"
              min-width="120"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <vxe-table-column
              field="applyCode"
              title="任务ID"
              min-width="120"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <vxe-table-column
              field="relationalTaskNum"
              title="关联任务数量"
              min-width="120"
              show-header-overflow
              show-overflow
            ><template v-slot="{ row }">
              <span style="margin-left:5px">
                <el-link
                  :underline="false"
                  type="primary"
                  @click.stop="openDetail(row)"
                  >{{ row.relationalTaskNum }}</el-link
                >
              </span>
              </template></vxe-table-column>
            <vxe-table-column
              title="任务状态"
              min-width="120"
              show-header-overflow
              show-overflow
            ><template v-slot="{ row }">
              {{ ['待审核','通过','设计驳回','运营驳回'][row.operateStatus] }}
              </template>
            </vxe-table-column>
            <vxe-table-column
              field="preReceiveUser"
              title="初审审核人"
              min-width="120"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <vxe-table-column
              field="preAuditTime"
              title="初审审核时间"
              min-width="120"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <vxe-table-column
              field="designAuditTime"
              title="线上精修提交时间"
              min-width="140"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <vxe-table-column
              field="designReceiveUser"
              title="线上精修提交人"
              min-width="120"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <vxe-table-column
              field="offlineDesignApplyTime"
              title="线下精修申请时间"
              min-width="140"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <vxe-table-column
              field="offlineDesignReceiveUser"
              title="线下精修人"
              min-width="120"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <vxe-table-column
              field="taskFinishAuditPassTime"
              title="版本上线时间"
              min-width="120"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <vxe-table-column
              field="preRejectReason"
              title="运营驳回原因"
              min-width="120"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <vxe-table-column
              field="designRejectReason"
              title="设计驳回原因"
              min-width="120"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <vxe-table-column
              field="preReceivePictureQuantity"
              title="原图上传张数"
              min-width="120"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <vxe-table-column
              field="preDeletePictureQuantity"
              title="运营删除数量"
              min-width="120"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <vxe-table-column
              field="designDeletePictureQuantity"
              title="精修删除数量"
              min-width="120"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <vxe-table-column
              field="productName"
              title="商品名称"
              min-width="120"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <vxe-table-column
              field="productCode"
              title="商品编码"
              min-width="120"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <vxe-table-column
              field="businessTypeName"
              title="是否自营"
              min-width="120"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <!-- 	单据状态(0:图片拍摄中1:原图已上传 2:图片精修中 3:精修图已上传 4:精修图审核中 5:审核完毕, 6:单据已拆分, 7:原图已上传) -->
            <!-- <vxe-table-column title="单据状态" min-width="120" show-header-overflow show-overflow>
              <template
                v-slot="{ row }"
              >{{ ['','原图已上传','图片精修中','','','流程完毕','', '精修图已领取',"预审核中","预审核完毕"][row.applyStatus] }}</template>
            </vxe-table-column> -->
          </vxe-table>
        </div>
        <!-- 分页 -->
        <el-row class="custom-pagination-wrap">
          <el-col>
            <el-pagination
              background
              :current-page.sync="pageNum_first"
              :page-sizes="[20, 50, 100, 200]"
              :page-size.sync="pageSize_first"
              :total="total_first"
              layout="prev, pager, next, jumper,total, sizes"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            ></el-pagination>
          </el-col>
        </el-row>
        <!-- 关联任务详情弹框 -->
        <el-dialog
          style="padding:0"
          class="search-form-wrap"
          fullscreen
          title="预览"
          :close-on-click-modal="false"
          :visible.sync="dialogFormVisible"
        >
          <vxe-table
            border
            highlight-hover-row
            resizable
            auto-resize
            size="small"
            align="center"
            :tooltip-config="{ enterable: false }"
            :data="tableData1"
            ref="previewTable"
          >
            <vxe-table-column
              title="序号"
              min-width="140"
              show-header-overflow
              show-overflow
            >
            <template v-slot="{ rowIndex }">
                <span>{{ rowIndex + 1 }}</span>
              </template>
            </vxe-table-column>
            <vxe-table-column
              field="bindMechanism"
              title="申请人所属机构"
              min-width="120"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <vxe-table-column
              field="createUser"
              title="申请人"
              min-width="120"
              show-header-overflow
              show-overflow
            ></vxe-table-column>

            <vxe-table-column
              field="createTime"
              title="申请时间"
              min-width="120"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <vxe-table-column
              field="applyCode"
              title="任务ID"
              min-width="120"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <vxe-table-column
              field="productCode"
              title="商品编码"
              min-width="120"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
          </vxe-table>
        </el-dialog>
  </div>
</template>

<script>
import {
  getMechanismList,
  getPagePictureTask,
  exportPictureTask,
  getRelationalTask
} from "@/api/productPicture.js";
import { dictSearchTypeAndName } from "@/api/dict.js";
import { parseTimestamp } from "@/utils/index.js";
export default {
  name: "",
  components: {},
  filters: {},
  props: {},
  data() {
    return {
      dialogFormVisible: false,
      tableData1: [],
      pageNum_first: 1,
      pageSize_first: 20,
      total_first: 0,
      model: {
        businessType:"",
        hasRelationalTask: null, //是否存在关联任务
        taskFinishAuditPassTime: [], // 版本上线时间
        operateStatus: '', // 任务状态
        applyCode: '', // 任务ID
        mechanism: [], // 所属机构
        createTime: [], //申请时间
        createUser: '', // 申请人
        preAuditTime: [], // 初始审核时间
        preReceiveUser: '', //初审审核人
        designAuditTime: [], // 线上精修上传时间
        designReceiveUser: '', // 线上精修人
        offlineDesignApplyTime: [], // 线下精修申请时间
        offlineDesignReceiveUser: '', // 线下精修人
        productCode: "", // 商品编码
        mechanismOptions: [],
      },
      tableLoading_first: false,
      tableData_first: [],
      activeTab: "first",
    };
  },
  computed: {},
  watch: {},
  created() {
    this.getMechanismList()
    /**
     * 查询生产厂家
     */
    this.remoteMethod("药");

    this.searchForm();
  },
  mounted() {},
  methods: {

     changeSelect1(val) {
      if (!val.includes("全选") && val.length === this.model.mechanismOptions.length) {
        this.model.mechanism.unshift("全选");
      } else if (
        val.includes("全选") &&
        val.length - 1 < this.model.mechanismOptions.length
      ) {
        console.log(this.model.mechanism)
        this.model.mechanism = this.model.mechanism.filter((item) => {
          return item !== "全选";
        });
      }
    },
    removeTag1(val) {
      if (val === "全选") {
        this.model.mechanism = [];
      }
    },
    selectAll1() {
      if (this.model.mechanism.length < this.model.mechanismOptions.length) {
        this.model.mechanism = [];
        this.model.mechanismOptions.map((item) => {
          this.model.mechanism.push(item.value);
        });
        this.model.mechanism.unshift("全选");
      } else {
        this.model.mechanism = [];
      }
    },

    // 所属机构列表查询
    getMechanismList() {
      getMechanismList({
        codeType: "dept",
      }).then((resp) => {
          if (resp.retCode === 0) {
            resp.data.forEach((item) => {
              if (item.mechanismId) {
                this.model.mechanismOptions.push({
                  value: item.mechanismId,
                  label: item.mechanismName,
                });
              }
            });
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    //查询
    searchForm() {
      this.searchTaskList()
    },

    /**
     * 生产厂家模糊搜索
     */
    remoteMethod(query) {
      let dictName = query || "药";
      dictSearchTypeAndName({
        dictName: dictName,
        type: "12",
      })
        .then((resp) => {
          if (resp.list && resp.list.length) {
            let list = [];
            resp.list.forEach((item, index) => {
              list.push({
                label: item.dictName,
                value: item.id,
              });
            });
            this.model.manufacturerOptions = list;
          }
        })
        .catch((err) => {
          this.$message({
            showClose: true,
            type: "error",
            message: "请求发生错误，请重试",
          });
        });
    },

    /**
     * 生产厂家清空
     */
    clearMethod() {
      // this.model.manufacturerOptions = [];
    },

    /**
     * 查询单据列表
     */
    searchTaskList() {
      this.tableLoading_first = true;
      let params = Object.assign({},this.getSearchParams())
      if (params.mechanism[0] === '全选') {
        params.mechanism.shift()
        params.mechanism = params.mechanism.join()
      } else {
        params.mechanism = params.mechanism.join()
      }
      getPagePictureTask(
        Object.assign({}, params, {
          page: this.pageNum_first,
          limit: this.pageSize_first
        })
      )
        .then((resp) => {
          if (resp.retCode === 0) {
            this.total_first = resp.data.total;
            this.tableData_first = resp.data.list
            this.tableData_first.map((item, index) => {
              item.index = index + 1 + (this.pageNum_first - 1) * this.pageSize_first
              item.createTime = parseTimestamp(item.createTime)
              item.preAuditTime = parseTimestamp(item.preAuditTime)
              item.designAuditTime = parseTimestamp(item.designAuditTime)
              item.offlineDesignApplyTime = parseTimestamp(item.offlineDesignApplyTime)
              item.taskFinishAuditPassTime = parseTimestamp(item.taskFinishAuditPassTime)
            });
          } else {
            this.$message({
              showClose: true,
              type: "error",
              message: resp.retMsg,
            });
          }
        })
        .catch((err) => {
          this.$message({
            showClose: true,
            type: "error",
            message: "请求发生错误，请重试",
          });
        })
        .finally(() => {
          this.tableLoading_first = false;
        });
    },

    /**
     * 获取搜索条件
     */
    getSearchParams() {
      return {
        taskFinishAuditPassTimeStart: this.model.taskFinishAuditPassTime
          ? this.model.taskFinishAuditPassTime.length
            ? this.model.taskFinishAuditPassTime[0]
            : null
          : null,
        taskFinishAuditPassTimeEnd: this.model.taskFinishAuditPassTime
          ? this.model.taskFinishAuditPassTime.length
            ? this.model.taskFinishAuditPassTime[1]
            : null
          : null,
        createStartTime: this.model.createTime
          ? this.model.createTime.length
            ? this.model.createTime[0]
            : null
          : null,
        createEndTime: this.model.createTime
          ? this.model.createTime.length
            ? this.model.createTime[1]
            : null
          : null,
        preAuditTimeStart: this.model.preAuditTime
          ? this.model.preAuditTime.length
            ? this.model.preAuditTime[0]
            : null
          : null,
        preAuditTimeEnd: this.model.preAuditTime
          ? this.model.preAuditTime.length
            ? this.model.preAuditTime[1]
            : null
          : null,
        designAuditTimeStart: this.model.designAuditTime
          ? this.model.designAuditTime.length
            ? this.model.designAuditTime[0]
            : null
          : null,
        designAuditTimeEnd: this.model.designAuditTime
          ? this.model.designAuditTime.length
            ? this.model.designAuditTime[1]
            : null
          : null,
        offlineDesignApplyTimeStart: this.model.offlineDesignApplyTime
          ? this.model.offlineDesignApplyTime.length
            ? this.model.offlineDesignApplyTime[0]
            : null
          : null,
        offlineDesignApplyTimeEnd: this.model.offlineDesignApplyTime
          ? this.model.offlineDesignApplyTime.length
            ? this.model.offlineDesignApplyTime[1]
            : null
          : null,
        operateStatus:this.model.operateStatus,
        applyCode:this.model.applyCode,
        mechanism:this.model.mechanism || null,
        createUser:this.model.createUser,
        preReceiveUser:this.model.preReceiveUser,
        designReceiveUser:this.model.designReceiveUser,
        offlineDesignReceiveUser:this.model.offlineDesignReceiveUser,
        productCode: this.model.productCode,
        businessType:this.model.businessType || null,
        hasRelationalTask: this.model.hasRelationalTask
      };
    },

    /**
     * 点击查询按钮
     */
    btnSearchClick() {
      this.searchForm();
    },

    /**
     * 点击重置按钮
     */
    btnResetClick() {
      this.model = {
        businessType:"",
        hasRelationalTask: null,
        taskFinishAuditPassTime: [], // 版本上线时间
        operateStatus: '', // 任务状态
        applyCode: '', // 任务ID
        mechanism: [], // 所属机构
        createTime: [], //申请时间
        createUser: '', // 申请人
        preAuditTime: [], // 初始审核时间
        preReceiveUser: '', //初审审核人
        designAuditTime: [], // 线上精修上传时间
        designReceiveUser: '', // 线上精修人
        offlineDesignApplyTime: [], // 线下精修申请时间
        offlineDesignReceiveUser: '', // 线下精修人
        productCode: "", // 商品编码
        mechanismOptions: [],
      },
      this.pageNum_first = 1;
      this.searchForm();
      this.getMechanismList()
      this.remoteMethod("药");
    },

    /**
     * pageSize 改变事件
     */
    handleSizeChange(pageSize) {
      this.pageNum_first = 1;
      this.pageSize_first = pageSize;
      this.searchForm();
    },

    /**
     * pageNum 改变事件
     */
    handleCurrentChange(currentPage) {
      this.pageNum_first = currentPage
      this.searchForm();
    },

    /**
     * 导出excel
     */
    exportExcel() {
      let params = this.getSearchParams();
      if (params.mechanism[0] === '全选') {
        params.mechanism.shift()
        params.mechanism = params.mechanism.join()
      } else {
        params.mechanism = params.mechanism.join()
      }
       //导出单据
      exportPictureTask(
        Object.assign({}, params)
      ).then((res) => {
        if (!res.retCode) {
          this.$message.success(res.retMsg)
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          })

        }
      })
      .catch((err) => {
        this.$message({
          showClose: true,
          type: "error",
          message: "导出发生错误，请重试",
        });
      });
    },

    /**
     * 单据列表，双击进详情
     */
    cellDBLClickEvent({ row, rowIndex, column, columnIndex }) {
      // todo
      // window.parent.CreateTab(
      //   "../static/dist/index.html#/productPicture/reportFilmTaskDetail?applyCode=" +
      //     row.applyCode +
      //     "&applyStatus=" +
      //     row.applyStatus,
      //   "任务单明细页",
      //   true
      // );
    },
    // 关联任务详情
    async openDetail(e) {
      // console.log(e);
      if(e.relationalTaskNum) {
        this.tableData1 = []
        this.dialogFormVisible = true
        try {
          let res = await getRelationalTask({ applyCode:e.applyCode, productCode:e.productCode })
          console.log(res);
          if(res.retCode === 0) {
            this.tableData1 = res.data
            this.tableData1.map(item => {
              item.createTime = parseTimestamp(item.createTime)
            })
          } else {
            this.$message.error(res.retMsg)
          }        
        } catch (error) {
          console.log(error);
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.report-film-task-container {
  /**
   * 查询表单
   */
  .search-form-wrap {
    width: 100%;
    padding: 15px;
    border-bottom: 1px dashed #e4e4eb;

    .el-row {
      flex-wrap: wrap;

      .el-col {
        display: flex;
        justify-content: flex-end;

        .el-form-item {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          width: 100%;
          margin-right: 0;
          margin-bottom: 15px;

          /deep/ {
            .el-form-item__label {
              width: 122px;
              line-height: normal;
              padding: 0 12px;
              color: #292933;
              font-weight: normal;
            }

            .el-form-item__content {
              flex: 1;

              .el-range-editor.el-input__inner {
                width: 100%;

                .el-range-separator {
                  width: 14px;
                  padding: 0;
                }
              }
            }
          }

          .el-input {
            width: 100%;
          }

          .el-select {
            width: 100%;
          }
        }
      }
    }
  }

  /**
     * 按钮组
     */
  .btn-group-wrap {
    font-size: 0;
    padding: 15px;

    .el-col {
      .el-button,
      .vxe-column-filter-container {
        margin-right: 10px;
      }
    }
  }

  /**
     * el-tabs
     */
  .el-tabs {
    padding: 0 15px;
    /deep/ {
      .el-tabs__header {
        padding: 0 15px;

        .el-tabs__nav {
          border-radius: 0;

          .el-tabs__item {
            background-color: #f4f4f4;
            border-bottom: 1px solid #e4e7ed;
          }

          .is-active {
            background-color: #fff;
            border-bottom: 0;
          }
        }
      }
    }
  }

  /**
     * table
     */
  .table-wrap {
    width: 100%;
    //min-height: 440px;
    height: calc(100vh - 421px);
    padding: 0 0 0 15px;
  }
}
</style>
