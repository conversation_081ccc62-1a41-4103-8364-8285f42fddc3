import router from './router'
import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import { getToken } from '@/utils/auth' // get token from cookie
import getPageTitle from '@/utils/get-page-title'
import { asyncRoutes } from '@/router'
import { getUserPermission } from "@/api/user.js";
NProgress.configure({ showSpinner: false }) // NProgress Configuration

const whiteList = ['/login',"/basicDatabase/sauList"] // no redirect whitelist

router.beforeEach(async(to, from, next) => {
  // start progress bar
  NProgress.start()
  // set page title
  document.title = getPageTitle(to.meta.title);
   // 获取用户权限
  const res = await getUserPermission();
  store.commit('user/SET_PERMISSIONLIST',res);
  next();
  NProgress.done()
 
})

router.afterEach(() => {
  // finish progress bar
  NProgress.done()
})
