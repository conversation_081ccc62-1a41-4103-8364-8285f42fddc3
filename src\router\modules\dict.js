/*
 * @Descripttion: 
 * @Version: 1.0
 * @Author: zyh
 * @Date: 2025-02-06 13:53:24
 * @LastEditors: zyh
 * @LastEditTime: 2025-02-07 09:52:20
 */
import Iframe from '@/iframe'
const dictRouters = {
  path: '/dict',
  name: 'dict',
  component: Iframe,
  redirect: 'noRedirect',
  meta: { title: '', icon: 'nested' },
  children: [
    {
      path: 'basic',
      name: 'basic',
      component: () => import('@/views/dict/basic/index'),
      meta: { title: '基础属性' }
    },
    {
        path: 'medicineExpand',
        name: 'medicineExpand',
        component: () => import('@/views/dict/medicineExpand/index'),
        meta: { title: '中药拓展管理' }
      }
  ]
}

export default dictRouters
