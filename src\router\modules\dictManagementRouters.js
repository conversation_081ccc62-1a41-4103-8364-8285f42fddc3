import Layout from '@/layout'

const dictManagementRouters = {
    path: '/dictManagement',
    name: 'dictManagement',
    component: Layout,
    redirect: 'noRedirect',
    meta: { title: '字典管理', icon: 'nested' ,roles: ['admin','editor']},
    children: [
        {
            path: 'basicProperty',
            name: 'basicProperty',
            component: () => import('@/views/dictManagement/basicProperty'),
            meta: { title: '基础属性', icon: 'example' ,roles: ['admin','editor']}
        },
        {
            path: 'fourLevelClassification',
            name: 'fourLevelClassification',
            component: () => import('@/views/dictManagement/fourLevelClassification'),
            meta: { title: '四级分类', icon: 'example',roles: ['admin','editor'] }
        }
    ]
}

export default dictManagementRouters
