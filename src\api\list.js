import request from '@/utils/request'
import qs from "qs" // 请求数据 转成form data 形式
import { _Message } from '@/components/common/resectMessage';
// 新品上报列表数据
export function findProductPresentList(data) {
    return request({
        url: `/api/present/findProductPresentList`,
        data,
        method: 'POST'
    })
}

/** 
 * author:caoshuwen
 * date: 2021-05-27
 * description:导出新品上报
 * **/
export function exportProductPresentList(data) {
    return request({
        url: `/api/present/exportProductPresentList `,
        data,
        method: 'POST'
    })
}

// 领取新品上报
export function presentReceive(data) {
    return request({
        url: `/api/present/update/updateReceive`,
        data: qs.stringify(data),
        method: 'POST'
    })
}

// 商品纠错记录列表
export function correctionList(data) {
    return request({
        url: `/api/correction/findProductCorrectionList`,
        data: qs.stringify(data),
        headers: { 'content-Type': 'application/json;charset=UTF-8' },
        data,
        method: 'POST'
    })
}

// 商品纠错记录列表
export function correctionReveive(data) {
    return request({
        url: `/api/correction/update/updateReceive`,
        data: qs.stringify(data),
        // headers: {'content-Type': 'application/json;charset=UTF-8'},
        method: 'POST'
    })
}
// 商品上架列表
export function shelvesList(data) {
    return request({
        url: `/api/shelves/findProductShelvesList`,
        data,
        headers: { 'content-Type': 'application/json;charset=UTF-8' },
        method: 'POST'
    })
}

// 商品上架列表
export function shelvesReveive(data) {
    return request({
        url: `/api/shelves/update/updateReceive`,
        data: qs.stringify(data),
        method: 'POST'
    })
}

// 已申请商品列表
export function productShootRecordList(data) {
    return request({
        url: '/api/productShootRecord/page/myTaskListFzy',
        data: data,
        headers: { 'content-Type': 'application/json;charset=UTF-8' },
        method: 'POST'
    })
}

// 已申请商品列表数据处理
export function picCorrection(data) {
    return request({
        url: '/api/picture/picCorrection',
        data: data,
        headers: { 'content-Type': 'application/json;charset=UTF-8' },
        method: 'POST'
    })
}

// 已申请商品列表数据处理
export function rejectProductCorrection(data) {
    return request({
        url: '/api/picture/rejectProductCorrection',
        data: data,
        headers: { 'content-Type': 'application/json;charset=UTF-8' },
        method: 'POST'
    })
}

// 图片上传
// -/api/picture/imageUploadShootRecord?id=1&handle=1&quality=1&message=你好
// ------------------
// id     列表主键
// handle    1.未找到网图 2.已找到网图
// quality    1.需设计精修 2.可直接使用
// message  备注留言

// 查询SKU列表数据
export function skuList(data) {
    return request({
        url: `/api/sku/find/skuList`,
        data: data,
        headers: { 'content-Type': 'application/json;charset=UTF-8' },
        method: 'POST'
    })
}

/** 
 * author:caoshuwen
 * date: 2021-10-20
 * description:友商库图片补全申请记录导出
 * **/
export function getSkuList(data) {
    return request({
        url: `/api/sku/find/sku/spuCode?spuCode=${data}`,
        method: 'post'
    })
}
/**
 * author:xiaohan
 * date: 2024-06-09
 * description:查询商品业务编码
 * **/
export function queryOriginBarcodes(data) {
    return request({
        url: `/api/inner/query/queryOriginBarcodes?barcodes=${data.barcodes}`,
        method: 'post'
    })
}