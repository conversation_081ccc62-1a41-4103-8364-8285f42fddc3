<template>
  <div class="container">
    <div class="basic-info">
      <div class="card-title">
        <span>申请信息</span>
        <div class="btn-wrap">
          <el-button type="warning" size="small" v-if="routeInfo.type != 'detail'" @click="handleRefuse"
            >驳回</el-button
          >
          <el-button size="small" @click="handleBack">返回</el-button>
          <el-button type="primary" size="small" v-if="routeInfo.type != 'detail'" @click="handleSubmit"
            >提交</el-button
          >
        </div>
      </div>
      <el-row class="card-content">
        <el-col :lg="6" :md="6" :xs="24" :sm="12" :xl="6"
          >申请时间 ：{{ samegroupDetail.submitTime | parseTime }}</el-col
        >
        <el-col :lg="6" :md="6" :xs="24" :sm="12" :xl="6"
          >单据编号 ：{{ samegroupDetail.orderCode }}</el-col
        >
        <el-col :lg="6" :md="6" :xs="24" :sm="12" :xl="6"
          >申请人 ：{{ samegroupDetail.receiverName }}</el-col
        >
        <el-col :lg="6" :md="6" :xs="24" :sm="12" :xl="6"
          >所属机构 ：{{ samegroupDetail.receiverMechanism }}</el-col
        >
      </el-row>
    </div>
    <div
      class="partition-content"
      style="height: 20px; background: #f0f2f5"
    ></div>
    <div class="product-list">
      <div class="card-title">商品信息</div>
      <div class="table-wrap">
        <vxe-table
          border
          highlight-hover-row
          resizable
          height="100%"
          auto-resize
          size="small"
          align="center"
          :tooltip-config="{ enterable: false }"
          :loading="tableLoading"
          :data="tableData"
          :seq-config="{ startIndex: (pageNum - 1) * pageSize }"
          ref="refVxeTable"
          @radio-change="radioChangeEvent"
        >
          <vxe-table-column
            type="radio"
            width="120"
            fixed="left"
            v-if="routeInfo.type != 'detail'"
          >
            <template v-slot:header>
              <span class="ng">选择主商品</span>
            </template>
          </vxe-table-column>
          <vxe-table-column
            field="isRemove"
            title="是否移除"
            width="120"
            show-header-overflow
            show-overflow
            v-if="routeInfo.type == 'detail'"
            ><template v-slot="{ row }">
              <span>{{ ['否','是'][row.isRemove] }}</span>
            </template></vxe-table-column
          >
          <vxe-table-column
            field="spuCategory"
            title="商品大类"
            width="120"
            show-header-overflow
            show-overflow
            ><template v-slot="{ row }">
              <span>{{ row.spuCategory | filterSpuCategory }}</span>
            </template></vxe-table-column
          >
          <vxe-table-column
            field="productCode"
            title="商品编码"
            width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="productId"
            title="商品id"
            width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="generalName"
            title="通用名"
            width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="generalName"
            title="商品名"
            width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="spec"
            title="规格型号"
            width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="approvalNo"
            title="批准文号"
            width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="manufacturerName"
            title="生产厂家"
            width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="attributeText"
            title="属性"
            width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="dosageForm"
            title="剂型"
            width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="spuCode"
            title="spu编码"
            width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="skuCode"
            title="sku编码"
            width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="packageUnit"
            title="包装单位"
            width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="prescriptionCategory"
            title="处方分类"
            width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="smallPackageCode"
            title="小包装条码"
            width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="brand"
            title="品牌商标"
            width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="validity"
            title="有效期"
            width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="delegationProduct"
            title="是否委托生产"
            width="120"
            show-header-overflow
            show-overflow
            ><template v-slot="{ row }">
              <span>{{ row.delegationProduct | filterDelegationProduct }}</span>
            </template></vxe-table-column
          >
          <vxe-table-column
            field="delegationProductVender"
            title="受托生产厂家"
            width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            title="操作"
            width="80"
            show-header-overflow
            show-overflow
            fixed="right"
            v-if="routeInfo.type != 'detail'"
          >
            <template v-slot="{ row, $rowIndex }">
              <span>
                <el-link
                  :underline="false"
                  type="danger"
                  @click.stop="handleRemove(row, $rowIndex)"
                  >移除</el-link
                >
              </span>
            </template>
          </vxe-table-column>
        </vxe-table>
      </div>
    </div>
  </div>
</template>
<script>
import {
  getSamegroupDetail,
  rejectSamegroup,
  submitSamegroup,
} from "@/api/follow";

export default {
  data() {
    return {
      samegroupDetail: {},
      tableLoading: false,
      tableData: [],
      routeInfo: this.$route.query,
      selectRow: {},
      deleteList: [],
    };
  },
  filters: {
    filterDelegationProduct(val) {
      switch (val * 1) {
        case 0:
          return "否";
        case 1:
          return "是";
        default:
          return "否";
      }
    },
  },
  created() {
    const orderCode = this.$route.query.orderCode;
    this.tableLoading = true;
    getSamegroupDetail({ orderCode }).then((res) => {
      this.tableLoading = false;
      this.samegroupDetail = res.data;
      this.tableData = res.data.detailPageList;
    });
  },
  methods: {
    radioChangeEvent({ row }) {
      this.selectRow = row;
    },

    handleRemove(row, index) {
      if (!!this.selectRow.id && this.selectRow.id == row.id) {
        this.selectRow = {};
      }
      this.deleteList.push(row);
      this.tableData.splice(index, 1);
    },

    handleBack() {
      try {
        parent.CreateTab(
          `../static/dist/index.html#/follow/repetition`,
          "六要素商品去重"
        );
        parent.CloseTab("../static/dist/index.html#/follow/repetitionDetail");
      } catch {
        this.$router.push({
          path: "/follow/repetition",
        });
      }
    },

    handleSubmit() {
      if (this.tableData.length < 2) {
        this.$message.warning("提交合并商品流程至少需要2条商品");
        return;
      }
      if (!this.selectRow.id) {
        this.$message.warning("请选择主商品");
        return;
      }
      const removeList = this.deleteList.map((item) => item.id);
      this.$prompt("申请原因", "提交", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPattern: /\S/,
        inputErrorMessage: "请输入申请原因",
      })
        .then(({ value }) => {
          submitSamegroup({
            orderCode: this.samegroupDetail.orderCode,
            primaryProductId: this.selectRow.id,
            removeList: removeList.join(","),
            submitReason: value,
          }).then((res) => {
            if (!res.retCode) {
              this.$message.success("操作成功");
              try {
                parent.CreateTab(
                  `../static/dist/index.html#/follow/repetition`,
                  "六要素商品去重",
                  true
                );
                parent.CloseTab(
                  "../static/dist/index.html#/follow/repetitionDetail"
                );
              } catch {
                this.$router.push({
                  path: "/follow/repetition",
                });
              }
            } else {
              this.$message.error(res.retMsg);
            }
          });
        })
        .catch(() => {});
    },

    handleRefuse() {
      this.$prompt("驳回原因", "驳回", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPattern: /\S/,
        inputErrorMessage: "请输入驳回原因",
      })
        .then(({ value }) => {
          rejectSamegroup({
            orderCode: this.samegroupDetail.orderCode,
            submitReason: value,
          }).then((res) => {
            if (!res.retCode) {
              this.$message.success("操作成功");
              try {
                parent.CreateTab(
                  `../static/dist/index.html#/follow/repetition`,
                  "六要素商品去重",
                  true
                );
                parent.CloseTab(
                  "../static/dist/index.html#/follow/repetitionDetail"
                );
              } catch {
                this.$router.push({
                  path: "/follow/repetition",
                });
              }
            } else {
              this.$message.error(res.retMsg);
            }
          });
        })
        .catch(() => {});
    },
  },
};
</script>
<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  .card-title {
    line-height: 50px;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 22px;
    padding: 0 20px;
    border-bottom: 1px solid #e4e4eb;
    display: flex;
    justify-content: space-between;
    .btn-wrap {
      padding-right: 20px;
    }
  }
  .card-content {
    padding: 0 40px 20px;
  }
  .product-list {
    padding-bottom: 20px;
    .table-wrap {
      height: 400px;
      padding: 0 40px;
    }
  }
}
.ng {
  line-height: 26px !important;
}
</style>