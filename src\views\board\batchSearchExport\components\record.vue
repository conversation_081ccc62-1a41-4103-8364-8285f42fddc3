<template>
  <div class="task-to-be-film-container" v-loading="loading">
    <div class="loading" v-loading="uploadLoading" v-show="uploadLoading"></div>
    <!-- 查询表单 -->
    <div class="search-form-wrap">
      <el-form :model="model" ref="refSearchForm">
        <el-row type="flex">
          <!-- 操作时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="操作时间" prop="applyDate">
              <el-date-picker
                v-model="formData.applyDate"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                clearable
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <!-- 申请人 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="申请人" prop="applyer">
              <el-input
                v-model="formData.applyer"
                placeholder="请输入申请人"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- 操作结果 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="操作结果">
              <el-select
                v-model="formData.analysisStatus"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" :value="null"></el-option>
                <el-option label="导出失败" :value="0"></el-option>
                <el-option label="导出成功" :value="1"></el-option>
                <el-option label="正在导出" :value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 按钮 -->
          <el-col :span="6" :offset="18">
            <el-button type="primary" size="medium" @click="btnSearchClick">查询</el-button>
            <el-button size="medium" @click="btnResetClick">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- table -->
    <div class="table-wrap">
      <vxe-table
        border
        highlight-hover-row
        resizable
        height="100%"
        auto-resize
        size="small"
        align="center"
        :tooltip-config="{ enterable: false }"
        :loading="tableLoading"
        :seq-config="{ startIndex: (pageNum - 1) * pageSize }"
        ref="refVxeTable"
        :data="tableData"
      >
        <vxe-table-column
          type="seq"
          title="序号"
          width="60"
          show-header-overflow
          show-overflow
          fixed="left"
        ></vxe-table-column>
        <vxe-table-column
          field="applyDate"
          title="操作时间"
          min-width="150"
          show-header-overflow
          show-overflow
          ><template v-slot="{ row }">
            <span>{{ row.applyDate  | parseTimestamp }}</span>
          </template></vxe-table-column
        >
         <vxe-table-column
          field="applyer"
          title="申请人"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="analysisStatus"
          title="导出结果"
          min-width="120"
          show-header-overflow
          show-overflow
        ><template v-slot="{ row }">
            <span>{{ row.analysisStatus | filterAnalysisStatus }}</span>
          </template></vxe-table-column>
        <vxe-table-column
          title="导入文件"
          min-width="120"
          show-header-overflow
          show-overflow
        ><template v-slot="{ row}">
            <span>
              <el-link
                :underline="false"
                type="primary"
                @click.stop="downloadImport(row)"
                >{{ row.applyAbnormalNum }}</el-link
              >
            </span>
          </template></vxe-table-column>
        <vxe-table-column
          title="导出文件"
          min-width="120"
          show-header-overflow
          show-overflow
        ><template v-slot="{ row}">
            <span>
              <el-link
                :underline="false"
                type="primary"
                @click.stop="downloadExport(row)"
                >{{ row.analysisStatus === 1 ? row.applyTotalNum : '' }}</el-link
              >
            </span>
          </template></vxe-table-column>
        <vxe-table-column
          field="exportCount"
          title="导出次数"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
      </vxe-table>
    </div>

    <!-- 分页 -->
    <el-row class="custom-pagination-wrap">
      <el-col>
        <el-pagination
          background
          :current-page.sync="pageNum"
          :page-sizes="[20, 50, 100, 200]"
          :page-size.sync="pageSize"
          :total="total"
          layout="prev, pager, next, jumper,total, sizes"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { parseTimestamp } from '@/utils/index.js'
import { createDownloadElement } from "@/utils/index.js";
import {
  batchModifyRecordList,
  exportCount
} from "@/api/board.js";

export default {
  name: "",
  components: { },
  filters: {
    filterAnalysisStatus: function (value) {
      switch (value * 1) {
        case 0:
          return "导出失败";
        case 1:
          return "导出成功";
        case 2:
          return "正在导出";
        default:
          return "-";
      }
    },
  },
  props: {},
  data() {
    return {
      loading: false,
      pageNum: 1,
      pageSize: 20,
      total: 0, //总条数
      formData: {
        applyDate: "", //申请时间
        applyer: "", //申请人
        analysisStatus: null //操作结果
      },
      tableLoading: false,
      tableData: []
    };
  },
  computed: {},
  watch: {},
  created() {
    this.searchForm()
  },
  mounted() {},
  methods: {
    // 下载导入文件
    downloadImport(e) {
      if(e.abnormalFileUrl) {
        createDownloadElement(e.abnormalFileUrl, e.abnormalFileName)
      }
    },
    // 下载导出文件
    async downloadExport(e) {
      console.log(e);
      if(e.abnormalFileUrl) {
        createDownloadElement(e.originalFileUrl, e.originalFileName)
        const res = await exportCount(e.id)
        console.log(res);
        if(res.retCode === 0) {
          this.searchForm()
        } else {
          this.$message.error(res.retMsg)
        }
      }
    },
    queryList() {
      this.searchForm()
    },
    /**
     * 查询table数据
     */
    async searchForm() {
      try {
        this.tableLoading = true;
        let param = Object.assign({}, this.formData);
        param.page = this.pageNum;
        param.limit = this.pageSize;
        param.modifyType = 23
        param.applyStartDate  = param.applyDate ? param.applyDate[0] : null;
        param.applyEndDate = param.applyDate ? param.applyDate[1] : null;
        param.applyer = param.applyer ? param.applyer : null
        delete param.applyDate;

        let res = await batchModifyRecordList(param);
        this.tableLoading = false;
        if (!res.retCode) {
        this.tableData = res.data.list;
        this.total = res.data.total;
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      } catch (error) {
        console.error(error);
      }
    },
    /**
     * 查询按钮点击
     */
    btnSearchClick() {
      this.pageNum = 1;
      this.searchForm();
    },

    /**
     * 重置
     */
    btnResetClick() {
      this.formData = {
        applyDate: "", //申请时间
        applyer: "", //申请人
        analysisStatus: null //操作结果
      };
      this.pageNum = 1;
      this.searchForm();
    },

    /**
     * pageSize 改变事件
     */
    handleSizeChange(pageSize) {
      this.pageNum = 1;
      this.pageSize = pageSize;
      this.searchForm();
    },

    /**
     * pageNum 改变事件
     */
    handleCurrentChange(currentPage) {
      this.pageNum = currentPage;
      this.searchForm();
    }
  },
};
</script>
<style lang="scss" scoped>
.task-to-be-film-container {
  .loading {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
  }
  /**
   * 查询表单
   */
  .search-form-wrap {
    width: 100%;
    padding: 15px;
    border-bottom: 1px dashed #e4e4eb;

    .el-row {
      flex-wrap: wrap;

      .el-col {
        display: flex;
        justify-content: flex-end;

        .el-form-item {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          width: 100%;
          margin-right: 0;
          margin-bottom: 15px;

          /deep/ {
            .el-form-item__label {
              width: 96px;
              line-height: normal;
              padding: 0 12px;
              color: #292933;
              font-weight: normal;
            }

            .el-form-item__content {
              flex: 1;

              .el-range-editor.el-input__inner {
                width: 100%;

                .el-range-separator {
                  width: 14px;
                  padding: 0;
                }
              }
            }
          }

          .el-input {
            width: 100%;
          }

          .el-select {
            width: 100%;
          }
        }
      }
    }
  }
  /**
     * table
     */
  .table-wrap {
    width: 100%;
    min-height: 440px;
    height: calc(100vh - 295px);
    padding: 0 15px;
  }
}
.el-link.el-link--primary.is-disabled {
  color: #bababa;
}
</style>