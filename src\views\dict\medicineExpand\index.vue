<!--
 * @Descripttion: 
 * @Version: 1.0
 * @Author: zyh
 * @Date: 2025-02-07 09:51:07
 * @LastEditors: zyh
 * @LastEditTime: 2025-02-19 13:40:13
-->

<template>
    <div class="task-to-be-film-container">
        <!-- 查询表单 -->
        <div class="search-form-wrap">
            <el-form :model="model"
                     ref="refSearchForm"
                     label-width="96x">
                <el-row type="flex"
                        :gutter="20">
                    <el-col :lg="8"
                            :xs="24"
                            :sm="12"
                            :xl="6">
                        <el-form-item label="商品大类">
                            <el-select v-model="formData.categoryCode"
                                       placeholder="请选择"
                                       disabled
                                       clearable>
                                <el-option v-for="item in categoryCodeList"
                                           :key="item.id"
                                           :label="item.dictName"
                                           :value="item.id"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="8"
                            :xs="24"
                            :sm="12"
                            :xl="6">
                        <el-form-item label="通用名">
                            <el-input v-model.trim="formData.commonName"
                                      placeholder="通用名"
                                      clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="8"
                            :xs="24"
                            :sm="12"
                            :xl="6">
                        <el-form-item label="拓展字段">
                            <el-select v-model="formData.dictIdList"
                                       placeholder="请选择"
                                       multiple
                                       filterable
                                       clearable>
                                <el-option v-for="item in dictIdSourceList"
                                           :key="item.id"
                                           :label="item.value"
                                           :value="item.id"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6"
                            :offset="18">
                        <el-button type="primary"
                                   size="medium"
                                   @click="btnSearchClick">查询</el-button>
                        <el-button size="medium"
                                   @click="btnResetClick">重置</el-button>
                    </el-col>
                </el-row>
            </el-form>
        </div>
        <div class="btn-wrap">
            <el-button type="primary"
                       size="medium"
                       @click="expandDict">拓展字典配置</el-button>
            <el-button size="medium"
                       type="primary"
                       @click="expandCommonName">拓展值配置</el-button>
        </div>
        <!-- table -->
        <div class="table-wrap">
            <vxe-table border
                       highlight-hover-row
                       resizable
                       height="100%"
                       auto-resize
                       size="small"
                       align="center"
                       :tooltip-config="{ enterable: false }"
                       :loading="tableLoading"
                       :data="tableData"
                       :seq-config="{ startIndex: (pageNum - 1) * pageSize }"
                       ref="refVxeTable">
                <vxe-table-column type="seq"
                                  title="序号"
                                  width="60"
                                  show-header-overflow
                                  show-overflow
                                  fixed="left"></vxe-table-column>
                <vxe-table-column field="categoryName"
                                  title="商品大类"
                                  width="120"
                                  show-header-overflow
                                  show-overflow></vxe-table-column>
                <vxe-table-column field="commonName"
                                  title="通用名"
                                  width="120"
                                  show-header-overflow
                                  show-overflow>
                </vxe-table-column>
                <vxe-table-column field="extValues"
                                  title="拓展值"
                                  width="320"
                                  show-header-overflow>
                    <template v-slot="{ row }">
                        <div class="expand-box">
                            <div class="expand-box-item"
                                 v-for="(item,index) in row.extValues"
                                 :key="index">
                                <span class="left-title">{{item.value}}: </span>
                                <span class="right-box">
                                    <span v-for="(name,j) in item.detailValueList"
                                          :key="j">{{name}}</span>
                                </span>
                            </div>
                        </div>
                    </template></vxe-table-column>
                <vxe-table-column field="createUser"
                                  title="创建人"
                                  width="120"
                                  show-header-overflow
                                  show-overflow>
                </vxe-table-column>
                <vxe-table-column field="createTime"
                                  title="创建时间"
                                  min-width="120"
                                  show-header-overflow
                                  show-overflow>
                    <template v-slot="{ row }">
                        {{ parseTime(row.createTime) }}
                    </template>
                </vxe-table-column>
                <vxe-table-column field="updateUser"
                                  title="最后更新人"
                                  min-width="120"
                                  show-header-overflow
                                  show-overflow>
                </vxe-table-column>
                <vxe-table-column field="updateTime"
                                  title="最后更新时间"
                                  min-width="120"
                                  show-header-overflow
                                  show-overflow><template v-slot="{ row }">
                        {{ parseTime(row.updateTime) }}
                    </template></vxe-table-column>
                <vxe-table-column title="操作"
                                  width="180"
                                  show-header-overflow
                                  fixed="right">
                    <template v-slot="{ row }">
                        <div class="action-box">
                            <div class="btn">
                                <el-link :underline="false"
                                         type="primary"
                                         @click.stop="edit(row)">拓展值编辑</el-link>
                            </div>
                            <div class="btn">
                                <el-link :underline="false"
                                         type="primary"
                                         @click.stop="handleRecord(row)">修改记录</el-link>
                            </div>
                        </div>
                    </template>
                </vxe-table-column>
            </vxe-table>
        </div>
        <!-- 分页 -->
        <el-row class="custom-pagination-wrap">
            <el-col>
                <el-pagination background
                               :current-page.sync="pageNum"
                               :page-sizes="[20, 50, 100]"
                               :page-size.sync="pageSize"
                               :total="total"
                               layout="prev, pager, next, jumper,total, sizes"
                               @size-change="handleSizeChange"
                               @current-change="handleCurrentChange">
                </el-pagination>
            </el-col>
        </el-row>
        <div>
            <ExpandDictConfig ref="expandDictConfig"
                              @close=reload />
            <EditRecords ref="editRecord" />
            <DictCommonName ref="dictCommonName" />
            <ExpandFieldConfig ref="expandFieldConfig" />
        </div>
    </div>
</template>

<script>
import { parseTimestamp, hasPermission } from "@/utils/index.js";
import { apiQueryList, apiGetCategoryDict, apiSelectDictList } from "@/api/dict.js";
import EditRecords from "./components/EditRecords.vue";
import ExpandDictConfig from "./components/ExpandDictConfig.vue";
import DictCommonName from "./components/DictCommonName.vue";
import ExpandFieldConfig from './components/ExpandFieldConfig.vue'
export default {
    name: "dictList",
    components: { EditRecords, ExpandDictConfig, DictCommonName, ExpandFieldConfig },
    data () {
        return {
            pageNum: 1,
            pageSize: 20,
            total: 0, //总条数
            categoryCodeList: [],
            dictIdSourceList: [],//拓展字典下拉
            formData: {
                categoryCode: 2,//商品大类 默认中药2
                commonName: "",//通用名
                dictIdList: [],//拓展字段
            },
            tableLoading: false,
            tableData: [],
        };
    },
    created () {
        this.getCategoryDict();
        this.getSelectDictList();
        this.searchForm();
    },
    methods: {
        reload () {
            this.getSelectDictList();
        },
        async getCategoryDict () {
            const res = await apiGetCategoryDict();
            if (res.retCode == 0) {
                this.categoryCodeList = res.data;
            } else {
                this.$message({
                    showClose: true,
                    type: "error",
                    message: res.retMsg,
                });
            }
        },
        async getSelectDictList () {
            const res = await apiSelectDictList({ type: 1 });
            if (res.retCode == 0) {
                this.dictIdSourceList = res.data.list;
            } else {
                this.$message({
                    showClose: true,
                    type: "error",
                    message: res.retMsg,
                });
            }
        },
        async searchForm () {
            try {
                this.tableLoading = true;
                let param = Object.assign({}, this.formData);
                param.page = this.pageNum;
                param.limit = this.pageSize;
                let res = await apiQueryList(param);
                this.tableLoading = false;
                if (res.retCode == 0) {
                    this.tableData = res.data.list;
                    this.total = res.data.total;
                } else {
                    this.$message({
                        showClose: true,
                        type: "error",
                        message: res.retMsg,
                    });
                }
            } catch (error) {
                console.error(error);
            }
        },
        btnSearchClick () {
            this.pageNum = 1;
            this.searchForm();
        },

        btnResetClick () {
            this.formData = {
                categoryCode: 2,//商品大类 默认中药2
                commonName: "",//通用名
                dictIdList: [],//拓展字段
            };
            this.pageNum = 1;
            this.searchForm();
        },
        parseTime (time) {
            return parseTimestamp(time);
        },
        handleSizeChange (pageSize) {
            this.pageNum = 1;
            this.pageSize = pageSize;
            this.searchForm();
        },
        handleCurrentChange (currentPage) {
            this.pageNum = currentPage;
            this.searchForm();
        },
        hasPermission (str) {
            return hasPermission(str);
        },
        // 拓展字段配置
        expandDict () {
            this.$refs.expandDictConfig.open();
        },
        //拓展值配置
        expandCommonName () {
            this.$refs.dictCommonName.open();
        },
        // 修改记录
        handleRecord (row) {
            this.$refs.editRecord.openLogList(row);
        },
        // 修改
        edit (row) {
            this.$refs.expandFieldConfig.open(row);
        },
    },
};
</script>
<style lang="scss" scoped>
.expand-box {
    .expand-box-item {
        &:not(:last-child) {
            margin-bottom: 10px;
        }
        display: flex;
        .left-title {
            font-weight: 500;
            color: #3c96a8;
            font-size: 15px;
            margin-right: 6px;
        }
        .right-box {
            flex: 1;
            display: flex;
            flex-wrap: wrap;
            gap: 8px; /* 设置元素间距，可选 */
            span {
                border: 1px #3c96a8 solid;
                padding: 1px 4px;
                border-radius: 3px;
            }
        }
    }
}
.task-to-be-film-container {
    .search-form-wrap {
        width: 100%;
        padding: 15px;
        border-bottom: 1px dashed #e4e4eb;
        .el-row {
            flex-wrap: wrap;
            .el-col {
                display: flex;
                justify-content: flex-end;
                .el-form-item {
                    flex: 1;
                    display: flex;
                    justify-content: flex-end;
                    align-items: center;
                    width: 100%;
                    margin-right: 0;
                    margin-bottom: 15px;
                    /deep/ {
                        .el-form-item__label {
                            padding: 0 12px;
                            color: #292933;
                            font-weight: normal;
                        }
                        .el-form-item__content {
                            flex: 1;
                            margin-left: 0 !important;
                            .el-range-editor.el-input__inner {
                                width: 100%;

                                .el-range-separator {
                                    width: 14px;
                                    padding: 0;
                                }
                            }
                        }
                    }
                    .el-input {
                        width: 100%;
                    }

                    .el-select {
                        width: 100%;
                    }
                    .el-cascader {
                        width: 100%;
                    }
                }
            }
        }
    }

    /**
     * table
     */
    .table-wrap {
        width: 100%;
        min-height: 440px;
        height: calc(100vh - 346px);
        padding: 0 15px;
    }
}
.btn {
    padding: 0 10px;
}
.btn-wrap /deep/ {
    padding: 15px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    .el-button {
        margin-bottom: 15px;
        margin-left: 0;
        margin-right: 15px;
    }
    .vxe-toolbar {
        flex: 1;
        .vxe-button--wrapper {
            display: none;
        }
        .vxe-tools--operate {
            margin-top: 2px;
            display: flex;
            justify-content: flex-end;
        }
    }
}
</style>