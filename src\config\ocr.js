/**
 * OCR API 配置
 * 根据不同环境自动切换OCR接口地址
 */

// OCR API 配置
const OCR_CONFIG = {
  // 开发环境
  development: {
    baseURL: 'https://xyy-ocr.test.ybm100.com',
    apiPath: '/ocr/identify',
    proxyPath: '/ocr/identify'
  },
  // 测试环境
  test: {
    baseURL: 'https://xyy-ocr.test.ybm100.com',
    apiPath: '/ocr/identify',
    proxyPath: '/api/ocr/identify'
  },
  // 预发布环境
  staging: {
    baseURL: 'https://xyy-ocr.ybm100.com',
    apiPath: '/ocr/identify',
    proxyPath: '/api/ocr/identify'
  },
  // 生产环境
  production: {
    baseURL: 'https://xyy-ocr.ybm100.com',
    apiPath: '/ocr/identify',
    proxyPath: '/api/ocr/identify'
  }
}

/**
 * 获取当前环境的OCR配置
 * @returns {Object} OCR配置对象
 */
export function getOcrConfig() {
  const env = process.env.NODE_ENV || 'development'
  const config = OCR_CONFIG[env] || OCR_CONFIG.development
  
  return {
    ...config,
    fullURL: config.baseURL + config.apiPath,
    env: env
  }
}

/**
 * 获取OCR API的完整URL（用于直接调用）
 * @returns {string} 完整的OCR API URL
 */
export function getOcrApiUrl() {
  const config = getOcrConfig()
  return config.fullURL
}

/**
 * 获取OCR API的代理路径（用于通过代理调用）
 * @returns {string} 代理路径
 */
export function getOcrProxyPath() {
  const config = getOcrConfig()
  return config.proxyPath
}

/**
 * 获取当前环境信息
 * @returns {Object} 环境信息
 */
export function getEnvironmentInfo() {
  const config = getOcrConfig()
  return {
    environment: config.env,
    ocrApiUrl: config.fullURL,
    proxyPath: config.proxyPath,
    isDevelopment: config.env === 'development',
    isTest: config.env === 'test',
    isStaging: config.env === 'staging',
    isProduction: config.env === 'production'
  }
}

export default {
  getOcrConfig,
  getOcrApiUrl,
  getOcrProxyPath,
  getEnvironmentInfo
}
