
<template>
  <div class="component-container">
    <div
      class="loading"
      v-loading="productLoading"
      v-show="productLoading"
    ></div>
    <el-tabs type="border-card">
      <el-tab-pane label="基础属性">
        <approval-process-new :approvalData="approvalData" :boardData="urlParam"></approval-process-new>
        <spu
          ref="spu"
          @isShowBtn="isShowBtn"
          :spuData="spuData"
          :skuData="skuData"
          @reuseSpu="reuseSpu"
        ></spu>
        <sku ref="sku" :skuData="skuData" :loadForm="false"></sku>
        <label-attr ref="labelAttr" :skuData="skuData" :spuData="spuData" :loadForm="false"
        :showEmpty="spuCategory.type == 'GENERAL_MEDICINE' || spuCategory.type == 'TRADITIONAL_MEDICINE' ? false : true"></label-attr>
        <extended-attr ref="extend" v-if="spuCategory.type == 'GENERAL_MEDICINE'" :newAudit="true" :modify="1"></extended-attr>
        <extended-attr2 ref="extend" v-if="spuCategory.type == 'TRADITIONAL_MEDICINE'" :newAudit="true" :modify="1"></extended-attr2>
      </el-tab-pane>
    </el-tabs>
    <el-row class="btns" :style="{ top: scrollTop < 40 ? '40px' : '0px' }">
      <el-col :span="24" class="bottom-btn-wrap">
        <el-button
          type="primary"
          v-if="operationType == 'update'"
          @click="productReject"
          >驳回</el-button
        >
        <el-button
          type="primary"
          v-if="operationType == 'present' && urlParam.uniqueCode"
          @click="productPresent"
          :loading="presentLoading"
          >{{ presentLoading ? '匹配中' : '标品查重' }}</el-button
        >
        <el-button
          type="primary"
          v-if="operationType == 'present' && urlParam.uniqueCode"
          @click="() => (dialogVisibleForPresent = true)"
          >驳回</el-button
        >
        <!-- 实施提报暂时不显示保存草稿 -->
        <el-button v-show="showDraftBtn" @click="submit('draft')"
          >保存草稿</el-button
        >
        <el-button :disabled="banSubmmit" type="primary" @click="submit()">提交商品信息</el-button>
      </el-col>
    </el-row>
    <el-dialog
      title="新品上报驳回"
      :visible.sync="dialogVisibleForPresent"
      width="80%"
      @close="resetRejectDialog"
    >
      <el-form
        ref="formPresent"
        :model="formPresent"
        :rules="rulesPresent"
        label-width="100px"
      >
        <p>商品已存在:</p>
          <el-checkbox-group v-model="checkList1" style="width:50%">
            <el-checkbox label="商品已存在，提报的商品已有标准库信息——标准库ID">提报的商品已有标准库信息</el-checkbox>
            <el-input
            :disabled="checkList1.length === 0 ? true : false"
            style="width:200px;margin-left:40px"
            v-model="formPresent.productId"
            placeholder="请输入标准库ID"
          ></el-input>
          </el-checkbox-group>
          <p>商品信息不完整，请实施修改并提供商品清晰完整套图:</p>
          <el-checkbox-group v-model="checkList2">
            <el-checkbox label="商品信息不完整，请实施修改并提供商品清晰完整套图——没有条码">没有条码</el-checkbox>
            <el-checkbox label="商品信息不完整，请实施修改并提供商品清晰完整套图——规格/型号不完整">规格/型号不完整</el-checkbox>
            <el-checkbox label="商品信息不完整，请实施修改并提供商品清晰完整套图——厂家不完整">厂家不完整</el-checkbox>
            <el-checkbox label="商品信息不完整，请实施修改并提供商品清晰完整套图——没有批文">没有批文</el-checkbox>
          </el-checkbox-group>
          <p>商品信息错误，请实施修改并提供商品清晰完整套图:</p>
          <el-checkbox-group v-model="checkList2">
            <el-checkbox label="商品信息错误，请实施修改并提供商品清晰完整套图——通用名与条码网不符">通用名与条码网不符</el-checkbox>
            <el-checkbox label="商品信息错误，请实施修改并提供商品清晰完整套图——生产厂家与条码网不符">生产厂家与条码网不符</el-checkbox>
            <el-checkbox label="商品信息错误，请实施修改并提供商品清晰完整套图——规格与条码网不符">规格与条码网不符</el-checkbox>
            <el-checkbox label="商品信息错误，请实施修改并提供商品清晰完整套图——品牌与条码网不符">品牌与条码网不符</el-checkbox>
            <el-checkbox label="商品信息错误，请实施修改并提供商品清晰完整套图——通用名与药监网不符">通用名与药监网不符</el-checkbox>
            <el-checkbox label="商品信息错误，请实施修改并提供商品清晰完整套图——生产厂家与药监网不符">生产厂家与药监网不符</el-checkbox>
            <el-checkbox label="商品信息错误，请实施修改并提供商品清晰完整套图——图片与资料不符">图片与资料不符</el-checkbox>
          </el-checkbox-group>
          <p>商品信息无法核实，请实施修改并提供商品清晰完整套图:</p>
          <el-checkbox-group v-model="checkList2">
            <el-checkbox label="商品信息无法核实，请实施修改并提供商品清晰完整套图——条码无法核实">条码无法核实</el-checkbox>
            <el-checkbox label="商品信息无法核实，请实施修改并提供商品清晰完整套图——规格无法核实">规格无法核实</el-checkbox>
            <el-checkbox label="商品信息无法核实，请实施修改并提供商品清晰完整套图——厂家无法核实">厂家无法核实</el-checkbox>
            <el-checkbox label="商品信息无法核实，请实施修改并提供商品清晰完整套图——批文无法核实">批文无法核实</el-checkbox>
            <el-checkbox label="商品信息无法核实，请实施修改并提供商品清晰完整套图——图片模糊">图片模糊</el-checkbox>
          </el-checkbox-group>
          <el-input
          style="margin-top:20px"
            v-model="formPresent.customReason"
            placeholder="请输入自定义驳回原因"
          ></el-input>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleForPresent = false">取 消</el-button>
        <el-button type="primary" @click="newReject">确 定</el-button>
      </span>
    </el-dialog>
    <product-duplicate-check
      ref="productDuplicateCheck"
      @reject-product="handleProductReject"
      @productDetail="handleProductDetail"
    ></product-duplicate-check>
  </div>
</template>

<script>
// 商品基础共用信息混入对象
import { productMixinBase } from "@/mixin/productMixinBase.js";
import {
  getProductData, //获取商品信息
  getGoodsDataDetail, // 同步待提交数据
  // productAdd, // 商品添加
  productRejectUpdate, //同步待提交商品状态
  presentReject, //新品上报商品驳回
  checkProductId,
  checkSpuSingleApi, //检查SPU唯一性
  productPresentDetail, // 新品上报详情
} from "@/api/product";
import { getCustomerProductReportProduct } from "@/api/follow";
import { getAddProcessData, newPresentReject, auditPassPresentProduct, checkRepeat } from "@/api/workManage"
import { isNumber } from "@/utils"
import productDuplicateCheck from "./productDuplicateCheck.vue";

export default {
  name: "",
  components: {
    productDuplicateCheck,
  },
  mixins: [productMixinBase],
  watch: {
    "formPresent.statusCode": function (newValue, oldValue) {
      if (newValue == 2) {
        this.rulesPresent.productId[0].required = true;
      } else {
        this.rulesPresent.productId[0].required = false;
      }
    },
    checkList1(e) {
      if(e.length !== 0) {
        this.checkList2 = []
        this.statusMsgCodeList = []
        this.formPresent.customReason = ""
        this.formPresent.statusCode = 2
      }
    },
    checkList2(e) {
      // 如果正在设置默认值，跳过干扰
      if (this.isSettingDefaultValues) return;

      this.statusMsgCodeList = []
      e.map(item => {
        if (item === '商品信息不完整，请实施修改并提供商品清晰完整套图——没有批文') {
          this.statusMsgCodeList.push(2)
        } else if (item === '商品信息不完整，请实施修改并提供商品清晰完整套图——没有条码') {
          this.statusMsgCodeList.push(3)
        } else if (item === '商品信息不完整，请实施修改并提供商品清晰完整套图——规格/型号不完整') {
          this.statusMsgCodeList.push(4)
        } else if (item === '商品信息错误，请实施修改并提供商品清晰完整套图——品牌与条码网不符') {
          this.statusMsgCodeList.push(5)
        } else if (item === '商品信息错误，请实施修改并提供商品清晰完整套图——通用名与药监网不符') {
          this.statusMsgCodeList.push(6)
        } else if (item === '商品信息错误，请实施修改并提供商品清晰完整套图——生产厂家与药监网不符') {
          this.statusMsgCodeList.push(7)
        } else if (item === '商品信息错误，请实施修改并提供商品清晰完整套图——通用名与条码网不符') {
          this.statusMsgCodeList.push(8)
        } else if (item === '商品信息不完整，请实施修改并提供商品清晰完整套图——厂家不完整') {
          this.statusMsgCodeList.push(10)
        } else if (item === '商品信息错误，请实施修改并提供商品清晰完整套图——生产厂家与条码网不符') {
          this.statusMsgCodeList.push(11)
        } else if (item === '商品信息错误，请实施修改并提供商品清晰完整套图——规格与条码网不符') {
          this.statusMsgCodeList.push(12)
        } else if (item === '商品信息错误，请实施修改并提供商品清晰完整套图——图片与资料不符') {
          this.statusMsgCodeList.push(13)
        } else if (item === '商品信息无法核实，请实施修改并提供商品清晰完整套图——条码无法核实') {
          this.statusMsgCodeList.push(14)
        } else if (item === '商品信息无法核实，请实施修改并提供商品清晰完整套图——规格无法核实') {
          this.statusMsgCodeList.push(15)
        } else if (item === '商品信息无法核实，请实施修改并提供商品清晰完整套图——厂家无法核实') {
          this.statusMsgCodeList.push(16)
        } else if (item === '商品信息无法核实，请实施修改并提供商品清晰完整套图——批文无法核实') {
          this.statusMsgCodeList.push(17)
        } else if (item === '商品信息无法核实，请实施修改并提供商品清晰完整套图——图片模糊') {
          this.statusMsgCodeList.push(18)
        }
      })
      if(e.length !== 0) {
        this.checkList1 = []
        this.formPresent.productId = ''
        this.formPresent.statusCode = 3
      }
    },
    'formPresent.customReason'(e){
      // 如果正在设置默认值，跳过干扰
      if (this.isSettingDefaultValues) return;

      if(e || e === 0){
        this.checkList1 = []
        this.formPresent.productId = ''
        this.formPresent.statusCode = 3
      }
    }
  },
  data() {
    return {
      banSubmmit: false, //防抖节流标识
      scrollTop: 0,
      checkList1: [],
      checkList2: [],
      statusMsgCodeList:[],
      hiddenBtn: false,
      dialogVisibleForPresent: false,
      dialogVisible: false,
      // 存储当前驳回的数据
      currentRejectData: null,
      // 标志是否正在设置默认值，防止watch干扰
      isSettingDefaultValues: false,
      // 实时提报时，驳回原因选项
      rejectOptions: [
        {
          value: 3,
          label: "商品信息不完整，请实施修改后提报",
        },
        {
          value: 2,
          label: "商品无需匹配标准库",
        },
        {
          value: 4,
          label: "提报的商品已有标准库信息",
        },
      ],
      rejectState: 3,
      formPresent: {
        productId: "",
        statusCode: "",
        customReason: ""
      },
      rulesPresent: {
        productId: [
          { required: false, message: "请输入正确的商品ID", trigger: "change" },
        ],
        statusCode: [
          { required: true, message: "请选择驳回原因", trigger: "change" },
        ],
      },
      presentLoading: false,  // 标品查重按钮加载
    };
  },
  computed: {
    showDraftBtn: function () {
      // 同步待提交 || 新品上报不显示草稿保存
      if (
        this.urlParam.source == "implement" ||
        this.urlParam.type == "present"
      ) {
        return false;
      } else {
        return true;
      }
    },
    spuCategory() {
      return this.$store.state.product.spuCategory;
    },
  },
  async created() {
    window.onscroll = () => {
      this.scrollTop =
        document.documentElement.scrollTop || document.body.scrollTop;
    };
    // 设置商品操作类型为新增
    this.$store.commit("product/SET_OPERATION_TYPE", "add");
    // 新品上报
    await this.presentProduct();
    // 同步待跟进处理
    await this.updatePorduct();
    // 获取审批流信息
    await this.getApplyInfo();
    // 检测页面数据值修改变化
    this.$bus.$on("productChange", (res) => {
      this.editState = res;
    });
  },
  methods: {
    handleProductDetail(duplicate){
      try {
        parent.CreateTab(
          "../static/dist/index.html#/product/" + (duplicate.productType == 2 ? "detailProduct" : "deputyProductDetail") + "?productCode=" +
          duplicate.productCode +
          "&productType=" +
          duplicate.productType +
          "&spuCode=" +
          (duplicate.spuCode ? duplicate.spuCode : '') +
          "&skuCode=" +
          (duplicate.skuCode ? duplicate.skuCode : '') +
          "&applyCode=" +
          (duplicate.applyCode ? duplicate.applyCode : '') +
          "&dataType=" +
          (duplicate.dataType ? duplicate.dataType : '') +
          "&detailType=self" +
          "&pageType=detail",
          duplicate.productType == 2 ? "商品详情" : "副商品详情"
        );
      } catch (err){
        console.log(err);
        
        this.$router.push({
          path: duplicate.productType == 2 ? "/product/detailProduct" : "/product/deputyProductDetail",
          query: {
            productCode: duplicate.productCode,
            productType: duplicate.productType,
            spuCode: duplicate.spuCode ? duplicate.spuCode : '',
            skuCode: duplicate.skuCode ? duplicate.skuCode : '',
            applyCode: duplicate.applyCode ? duplicate.applyCode : '',
            dataType: duplicate.dataType ? duplicate.dataType : '',
            detailType: "self",
            pageType: "detail",
          },
        });
      }
    },
    async productPresent(){
      this.presentLoading = true
      
      // 从表单组件获取最新的数据，而不是使用 this.spuData 和 this.skuData
      let spu = await this.$refs.spu.getSpuData(true); // false表示不是草稿
      let spuData = spu.data;
      let skuData = this.$refs.sku.getSkuData(true);
      
      if (!spu.state || !skuData) {
        this.$message.error('请完善商品信息');
        this.presentLoading = false;
        return;
      }
      
      const productInfo = {
        smallPackageCode: skuData[0].smallPackageCodeList.join(','),
        approvalNumber: spuData.approvalNo,
        generalName: spuData.generalName,
        manufacturer: spuData.manufacturerName,
        spec: skuData[0].spec,
        brand: skuData[0].brand || '',
        originPlace: skuData[0].originPlace || '',
        entrustedManufacturer: skuData[0].entrustedManufacturer || '',
        spuCategory: spuData.spuCategory,
      }
      
      // if(this.spuData.spuCategory == 2){
      //   delete productInfo.approvalNumber
      // }
      // console.log('====================================');
      // console.log(productInfo, 'productInfo');
      // console.log('====================================');
      try {
        const res = await checkRepeat(productInfo)
        if(res.retCode === 0){
          this.presentLoading = false
          // console.log('====================================');
          // console.log(res.data);
          // console.log('====================================');
          res.data.forEach(item => { 
            item.approvalNo = item.approvalNumber
          })
          this.$refs.productDuplicateCheck.open(spuData, skuData[0], res.data)
          // if(res.data.length > 0){
          //   this.$refs.productDuplicateCheck.open(spuData, skuData[0], res.data)
          // }else {
          //   this.$message.error('未查询到重复标品')
          // }
        }else {
          this.$message.error(res.retMsg)
          this.presentLoading = false
        }
      }catch (err) {
        console.log(err);
        this.presentLoading = false
      }
    },
    close() {
      let tab = "second"
      parent.CreateTab(
        `../static/dist/index.html#/workManage/receiveDeal?tab=${tab}`,
        "领取与处理",
        true
      );
      parent.CloseTab(
        "../static/dist/index.html#/workManage/newGoodsReportDeal"
      );
    },
    // 新的驳回
    async newReject() {
      let statusMsg = ''
      let rejectOpinionNumber = ''
      if(this.formPresent.statusCode === 2) {
        statusMsg = this.checkList1.join()
        rejectOpinionNumber = '1'
        if (!this.formPresent.productId) {
          this.$message.error("请输入重复数据标准库ID！")
          return
        } else if(!isNumber(this.formPresent.productId)) {
          this.$message.error("标准库ID录入错误！")
          return
        }
      } else if(this.formPresent.statusCode === 3) {
        if(!this.formPresent.customReason && this.formPresent.customReason !== 0) {
          statusMsg = this.checkList2.join()
          rejectOpinionNumber = this.statusMsgCodeList.join()
        } else {
          let temp = [...this.checkList2]
          temp.push(this.formPresent.customReason)
          statusMsg = temp.join()
          let temp2 = [...this.statusMsgCodeList]
          temp2.push(9)
          rejectOpinionNumber = temp2.join()
        }
      }
      let param = {
        productId:this.formPresent.productId,
        applyCode:this.urlParam.applyCode,
        comment:this.formPresent.customReason,
        procInstId:this.urlParam.procInstId,
        procKey:this.urlParam.procKey,
        taskId:this.urlParam.taskId,
        uniqueCode:this.urlParam.taskId,
        statusCode:this.formPresent.statusCode,
        statusMsg,
        rejectOpinionNumber,
        uniqueCode:this.urlParam.uniqueCode
      }
      if(!rejectOpinionNumber) {
        this.$message.error('驳回原因不能为空')
        return
      }
      console.log(param)
      const res = await newPresentReject(param)
      console.log(res);
      if(res.retCode === 0) {
        this.$message.success('驳回成功');

        // 如果是从标品查重组件触发的驳回，关闭标品查重弹窗
        if (this.currentRejectData) {
          this.$refs.productDuplicateCheck.close();
          this.currentRejectData = null;
        }

        this.close()
      } else {
        this.$message.error(res.retMsg)
      }
    },
    /**
     * @description:同步待提交
     * @param {string} spucode 复用的spucode
     * @return:
     */
    async updatePorduct() {
      if (this.urlParam.type != "followup") {
        // 判断是否为同步待跟进数据
        return false;
      }
      this.productLoading = true;
      // 设置商品操作类型为 同步待提交
      this.$store.commit("product/SET_OPERATION_TYPE", "update");
      let productData = await getGoodsDataDetail({ id: this.urlParam.id });
      this.productLoading = false
      this.spuData = Object.freeze(productData.data.spu);
      this.skuData = Object.freeze(productData.data.sku);
      // 同步待提交是默认有数据的，隐藏可以直接保存草稿
      this.editState = true;
    },
    /**
     * @description: 新品上报数据处理
     * @param {type}
     * @return {type}
     */
    async presentProduct() {
      if (this.urlParam.type != "present") {
        // 判断是否为新品上报
        return false;
      }
      this.productLoading = true;
      // 设置商品操作类型为新品上报
      this.$store.commit("product/SET_OPERATION_TYPE", "present");
      let res = {};
      if (this.urlParam.uniqueCode) {
        res = await productPresentDetail({
          uniqueCode: this.urlParam.uniqueCode,
        });
        this.productLoading = false
      } else {
        res = await getCustomerProductReportProduct({
          applyCode: this.urlParam.applyCode,
        });
        this.productLoading = false
      }

      if (res.success) {
        let productData = res.data;
        this._presentOrigin = productData.productSource;
        this.spuData = {
          ...productData,
          // 税率
          approvalImgList: productData.approvalImgList
            ? productData.approvalImgList
            : [], //批件图片
          inRate: productData.inRate === 0 || productData.inRate ? Number(productData.inRate) : productData.inRate,
          outRate: productData.outRate === 0 || productData.outRate ? Number(productData.outRate) : productData.outRate,
        };
        this.$store.getters.selectOptions.packageUnitOptions.forEach((item) => {
          if (item.dictName == productData.packageUnitName) {
            productData.packageUnitName = item.id;
            return;
          }
        });
        if (typeof productData.packageUnitName == "string") {
          productData.packageUnitName = "";
        }
        this.skuData = [
          {
            tasteName: productData.tasteName || "", //口味
            sizeName: productData.sizeName || "", // 尺码
            colorName: productData.colorName || "", // 颜色
            storageCond:productData.storageCond || "", //存储条件
            skuName: productData.productName, // 商品名
            standardCodes: productData.standardCodes
              ? productData.standardCodes
              : "", //本位码
            spec: productData.spec,
            packageUnit: productData.packageUnitName, // 包装单位-文本类型需要转换
            prescriptionCategory: productData.prescriptionCategory,
            prescriptionCategoryName: productData.prescriptionCategoryName,
            smallPackageCodeList: productData.smallPackageCode && productData.smallPackageCode !== '-'
              ? [productData.smallPackageCode]
              : [],
            brand: productData.brand,
            qualityStandard: productData.qualityStandard,
            validity: productData.validity, // number
            outPackageImgList: productData.outPackageImgList
              ? productData.outPackageImgList
              : [],
            directionImgList: productData.directionImgList
              ? productData.directionImgList
              : [],
            piecePackageCodeList: productData.piecePackageCode
              ? [productData.piecePackageCode]
              : [], //件包装条码
            mediumPackageCodeList: productData.mediumPackageCode
              ? [productData.mediumPackageCode]
              : [], //中包装条码
            delegationProduct: productData.delegationProduct ? productData.delegationProduct : 0, // 是否委托生产
            noSmallPackageCode: productData.noSmallPackageCode == 1 ? 1 : 0, // 无小包装条码
            originPlace: productData.originPlace, // 产地
            entrustedManufacturer: productData.entrustedManufacturerName, //受托生产厂家
            netContent: productData.netContent || "",
            netContentUnit: productData.netContentUnit || "",
          },
        ];
        this.sauData = [
          {
            outPackageImgList: productData.outPackageImgList
              ? productData.outPackageImgList
              : [],
            directionImgList: productData.directionImgList
              ? productData.directionImgList
              : [],
          },
        ];
        if (productData.sku) {
          this.skuData = [...this.skuData, ...productData.sku];
        }
        if (productData.sau) {
          this.sauData = [...this.sauData, ...productData.sau];
        }
        // 同步待提交是默认有数据的，隐藏可以直接保存草稿
        this.editState = true;
      } else {
        this.$message.error(res.retMsg);
      }
    },

    /**
     * @description:spu复用
     * @param {string} spucode 复用的spucode
     * @return:
     */
    async reuseSpu(spuCode) {
      // console.log(spucode);
      this.productLoading = true;
      // 设置商品操作类型为复用
      this.$store.commit("product/SET_OPERATION_TYPE", "reuse");
      // 查询下属所有
      let spuInfo = await getProductData({ spuCode }, "all");
      // console.log(spuInfo);
      this.spuData = Object.freeze(spuInfo.data.spu);
      this.skuData = Object.freeze(spuInfo.data.sku);
      // this.sauData = Object.freeze(spuInfo.data.sau);
    },
    /**
     * @description:商品数据提交
     * @param {string} isDraft 存在，保存草稿，不存在，提交
     * @param {string} checkSpuSingle 是否为检查SPU唯一触发
     * @return:
     */
    async submit(isDraft, checkSpuSingle) {
      console.log(this.urlParam);
      if (!this.editState && isDraft) {
        this.$message.error("请至少维护一个字段，再保存草稿");
        return false;
      }
      let spu = await this.$refs.spu.getSpuData(isDraft);
      let spuData = spu.data;
      let skuData = this.$refs.sku.getSkuData(isDraft);
      let sau = []
      let extendData = {};
      if (this.spuCategory.type == "GENERAL_MEDICINE" || this.spuCategory.type == "TRADITIONAL_MEDICINE") {
        extendData = this.$refs.extend.getExtendData(isDraft);
      }
      if (!spu.state || !skuData || !extendData) {
        // 未通过 SPU表单校验
        return;
      }
      if(skuData[0].smallPackageCodeList && skuData[0].smallPackageCodeList.length > 1) {
        this.$message.error('一个商品最多包含1个小包装条码！')
        return
      } else if(skuData[0].mediumPackageCodeList && skuData[0].mediumPackageCodeList.length > 1) {
        this.$message.error('一个商品最多包含1个中包装条码！')
        return
      } else if(skuData[0].piecePackageCodeList && skuData[0].piecePackageCodeList.length > 1) {
        this.$message.error('一个商品最多包含1个件包装条码！')
        return
      }
      this.banSubmmit = true
      // 验证spu唯一性
      if ((spuData.spuCategory == 3 || spuData.spuCategory == 4) && !isDraft) {
        this.productLoading = true;
        let res = await checkSpuSingleApi(spuData);
        this.productLoading = false;
        this.banSubmmit = false
        if (!res.success) {
          this.$message.error(res.retMsg);
          return false;
        }
      }
      if (checkSpuSingle) {
        this.productLoading = true;
        let res = await checkSpuSingleApi(spuData);
        this.banSubmmit = false
        if (res.success) {
          this.$message.success("spu唯一性校验通过");
        } else {
          this.$message.error(res.retMsg);
        }
        this.productLoading = false;
        return false;
      }
      // 设置新增时的商品来源
      this.setProductSpurce(spuData, skuData, sau);
      // formatSubmitData 函数在 productMixinBase.js中定义
      let submitData = this.formatSubmitData(
        _.cloneDeep(spuData),
        _.cloneDeep(skuData),
        _.cloneDeep(sau),
        _.cloneDeep(extendData)
      );
      //操作类型,
      // 0:保存草稿,
      // 1:新增spu,
      // 2:属性修改,
      // 3:商品合并,
      // 4:商品停用,
      // 5:三方同步,
      // 6:批量修改,
      // 7:草稿删除,
      // 8:预首营修改)
      if (isDraft) {
        //保存草稿
        submitData.operType = 0;
      } else {
        //新增
        submitData.operType = 1;
      }
      this.productLoading = true;
      // 如果是实施提报，携带SAAS saasUniqueCode
      if (this.urlParam.source && this.urlParam.source == "implement") {
        submitData.saasUniqueCode = this.skuData[0].saasUniqueCode;
      }
      // 如果是新品上报，携带uniqueCode
      if (this.urlParam.type && this.urlParam.type == "present") {
        submitData.uniqueCode = this.urlParam.uniqueCode;
      }
      if (this.urlParam.applyCode) {
        submitData.applyCode = this.urlParam.applyCode;
      }
      submitData.comment='新品上报审核通过',
      submitData.procInstId=this.urlParam.procInstId
      submitData.procKey=this.urlParam.procKey
      submitData.taskId=this.urlParam.taskId
      let res = await auditPassPresentProduct(submitData);
      this.banSubmmit = false
      // 更新同步待跟进数据状态
      if (this.urlParam.type == "followup") {
        await this.productRejectUpdate(3);
      }
      this.productLoading = false;
      if (res.success) {
        this.$message.success("操作成功");
        this.close()
      } else {
        this.$message.error(res.retMsg);
      }
    },
    /**
     * @description: 设置新增商品的商品来源：实施提报 || 新品上报
     * @param {object} spu SPU数据
     * @param {array} sku sku数据
     * @param {array} sau  sau数据
     * @return:
     */
    setProductSpurce(spu, sku, sau) {
      // 实施提报处理
      if (this.urlParam.source && this.urlParam.source == "implement") {
        this.setOrigin(spu, sku, sau, "实施提报");
      }
      // 新品上报处理
      if (this.urlParam.type && this.urlParam.type == "present") {
        this.setOrigin(spu, sku, sau, this._presentOrigin);
      }
    },
    /**
     * @description: 设置新增商品的商品来源：实施提报 || 新品上报
     * @param {object} spu SPU数据
     * @param {array} sku sku数据
     * @param {array} sau  sau数据
     * @param {string} origin  来源
     * @return:
     */
    setOrigin(spu, sku, sau, origin) {
      // spu 增加来源标识
      spu.spuSource = origin;
      for (let skuItem of sku) {
        if (!skuItem.skuCode || !skuItem.productId) {
          // 新增的SKU增加来源标识
          skuItem.skuSource = origin;
        }
      }
      for (let sauItem of sau) {
        if (!sauItem.skuCode || !sauItem.productId || !sauItem.sauCode) {
          // 新增的Sau增加来源标识
          sauItem.sauSource = origin;
        }
      }
    },
    async getApplyInfo() {
    let param = {
        procDefId: this.urlParam.procDefId,
        procInstId: this.urlParam.procInstId
      };
      let res = await getAddProcessData(param);
      console.log(res);
      if (res.success) {
        this.approvalData = res.data;
        // if (this.urlParam.source && this.urlParam.source == "implement") {
        //   this.approvalData.applyAttribute.productSource = "实施提报";
        // }
        // // 新品上报
        // if (this.urlParam.type && this.urlParam.type == "present") {
        //   this.approvalData.applyAttribute.productSource = this._presentOrigin;
        // }
      }
    },
    // 同步待跟进驳回
    productReject() {
      if (this.urlParam.source == "implement") {
        this.dialogVisible = true;
      } else {
        productRejectUpdate(2);
      }
    },
    /**
     * @description: 新品上报表单处理
     * @param {type}
     * @return {type}
     */
    presentHandle() {
      // console.log(this.checkList2)
      // return
      this.productRejectforPresent();
      // this.$refs.formPresent.validate((valid) => {
      //   if (valid) {
      //     this.productRejectforPresent();
      //   } else {
      //     console.log("error submit!!");
      //   }
      // });
    },
    /**
     * @description: 新品上报驳回
     */
    async productRejectforPresent() {
      try {
        if (this.formPresent.statusCode == 2) {
          let checkProduct = await checkProductId({
            productId: this.formPresent.productId,
          });
          if (!checkProduct.success) {
            this.$message.error(checkProduct.retMsg);
            return;
          }
        }
        // console.log(this.formPresent.statusCode)
        // return
        let statusMsg = ''
        if(this.formPresent.statusCode === 2) {
          statusMsg = this.checkList1.join()
        } else if(this.formPresent.statusCode === 3) {
          if(!this.formPresent.customReason && this.formPresent.customReason !== 0) {
            statusMsg = this.checkList2.join()
          } else {
            let temp = [...this.checkList2]
            temp.push(this.formPresent.customReason)
            statusMsg = temp.join()
          }
        }
        if(!this.formPresent.customReason && this.formPresent.customReason!==0 && this.checkList2.length === 0 && !this.formPresent.productId) {
          this.$message.error('请选择驳回原因')
          return
        }
        let res = await presentReject({
          uniqueCode: this.urlParam.uniqueCode,
          statusCode: this.formPresent.statusCode,
          statusMsg,
          productId: this.formPresent.productId,
        });
        if (res.success) {
          parent.CreateTab(
            "../static/dist/index.html#/list/newProductSubmit",
            "新品上报",
            true
          );
          parent.CloseTab("../static/dist/index.html#/product/addProduct");
        }
      } catch (error) {
        console.error(error);
      }
    },
    /**
     * @description: 更新同步待跟进商品状态
     * @param {type} status 2 关闭， 3 更新
     * @param {type} rejectReason 驳回原因
     * @return:
     */
    // 同步待提交商品状态更新
    async productRejectUpdate(status, rejectReason) {
      this.productLoading = true;
      let res = await productRejectUpdate({
        id: Number(this.urlParam.id),
        statusCode: status,
        reviewOpinion: rejectReason ? rejectReason : "",
      });
      this.productLoading = false;
      if (res && res.retCode == 0 && res.data) {
        parent.CloseTab("../static/dist/index.html#/product/addProduct");
      } else {
        this.$message.error(res.retMsg);
      }
    },
    // isShowBtn(businessScopeList, spuCategory, id) {
    //   if (spuCategory == 4) {
    //      this.hiddenBtn = true;
    //   } else {
    //     this.hiddenBtn = false;
    //   }
    // },
    isShowBtn(businessScopeList, spuCategory, id) {
      if (spuCategory == 4) {
        let flag = false;
        businessScopeList.forEach((item) => {
          item.forEach((list) => {
            if (list == id) {
              flag = true;
              return;
            }
          });
          if (flag) {
            return;
          }
        });
        if (flag) {
          this.hiddenBtn = true;
        } else {
          this.hiddenBtn = false;
        }
      } else {
        this.hiddenBtn = false;
      }
    },

    // 处理标品查重组件的驳回事件
    handleProductReject(rejectData) {
      // console.log('收到标品查重驳回事件:', rejectData);

      // 设置标志，防止watch干扰
      this.isSettingDefaultValues = true;

      // 清空之前的选择
      this.checkList1 = [];
      this.checkList2 = [];
      this.statusMsgCodeList = [];
      this.formPresent.customReason = '';

      // 设置驳回表单的默认值
      this.formPresent.statusCode = 2; // 默认为"提报的商品已有标准库信息"
      this.formPresent.productId = rejectData.productId || '';

      // 默认勾选"提报的商品已有标准库信息" - 使用正确的label值
      this.checkList1 = ['商品已存在，提报的商品已有标准库信息——标准库ID'];

      // 打开驳回弹窗
      this.dialogVisibleForPresent = true;

      // 存储当前驳回的数据，用于驳回成功后的处理
      this.currentRejectData = rejectData;

      // 使用nextTick确保弹窗完全渲染后再设置默认值
      this.$nextTick(() => {
        // 再次确保设置正确的默认值
        this.formPresent.statusCode = 2;
        this.formPresent.productId = rejectData.productId || '';
        this.checkList1 = ['商品已存在，提报的商品已有标准库信息——标准库ID'];

        // 清除标志
        this.isSettingDefaultValues = false;

        // console.log('nextTick后的表单状态:', {
        //   statusCode: this.formPresent.statusCode,
        //   productId: this.formPresent.productId,
        //   checkList1: this.checkList1
        // });
      });
    },

    // 重置驳回弹窗状态
    resetRejectDialog() {
      this.isSettingDefaultValues = true;
      this.checkList1 = [];
      this.checkList2 = [];
      this.statusMsgCodeList = [];
      this.formPresent = {
        productId: "",
        statusCode: "",
        customReason: ""
      };
      this.currentRejectData = null;
      this.isSettingDefaultValues = false;
    }
  },
};
</script>

<style lang="scss" scoped>
.component-container {
  padding-top: 0px;
  .loading {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
  }
  /deep/ .el-tabs__header {
    border-bottom: none;
    background-color: #f2f2f2;
  }
  /deep/.el-tabs__content {
    // padding: 0 !important;
    padding-top:72px!important;
  }
  .el-tabs--border-card {
    border: none;
    box-shadow: none;
    /deep/ .el-tabs__content {
      padding-bottom: 80px;
      padding-left: 0;
      padding-right: 0;
    }
  }
  .bottom-btns {
    background: #f0f2f5;
    width: 100%;
    position: fixed;
    bottom: 0;
    padding: 15px;
    z-index: 10;
  }
}
.bottom-btn-wrap {
  display: flex;
  justify-content: flex-end;
}
.btns {
    background: #fff;
    width: 100%;
    position: fixed;
    top: 40px;
    padding: 15px;
    // z-index: 3100;
  }
</style>
