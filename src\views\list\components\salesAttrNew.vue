<template>
  <div class="component-container">    
    <!-- 销售属性 -->
    <vxe-table
      border
      highlight-hover-row
      auto-resize
      resizable
      width="100%"
      align="center"
      :tooltip-config="{ enterable: false }"
      :data="tableData"
      ref="table"
    >
      <vxe-table-column
        type="index"
        title="序号"
        width="60"
        show-header-overflow
        show-overflow
      ></vxe-table-column>
      <vxe-table-column field="businessCode" title="商品编码" min-width="100">
      </vxe-table-column>
      <vxe-table-column
        field="pictureVersion"
        title="版本号"
        min-width="100"
      ></vxe-table-column>
      <vxe-table-column
        field="detailPictureList"
        title="精修主图"
        min-width="100"
      >
        <template v-slot="{ row }">
          <div class="img-preview-container">
            <img
              v-for="(img, index) in row.detailPictureList"
              :key="index"
              :src="img.url"
              :alt="img.name"
              class="img-s readonly-img"
              @click="previewImages(row.detailPictureList, index)"
              :title="`点击预览 ${img.name}`"
            />
            <div v-if="!row.detailPictureList || !row.detailPictureList.length" class="no-image">
              <i class="el-icon-picture-outline"></i>
              <span>暂无图片</span>
            </div>
          </div>
        </template>
      </vxe-table-column>
      <vxe-table-column
        field="detailPictureNum"
        title="图片数量"
        min-width="100"
      ></vxe-table-column>
      <vxe-table-column field="productStatus" title="图片状态" min-width="100">
        <!-- 停启用状态,1启用,0停用 -->
        <template v-slot="{ row }">
          <el-tag :type="row.pictureEnable ? 'success' : 'danger'">
            {{row.pictureEnable ? "启用" : "停用"}}
          </el-tag>
        </template>
      </vxe-table-column>
      <vxe-table-column
        field="mechanismNum"
        title="已绑定机构数量"
        min-width="130"
      >
        <template v-slot="{ row }">
          <el-tooltip class="item" effect="dark" placement="top">
            <template v-slot:content>
              <p v-for="(item, key) in row.mechanismList" :key="key">
                {{ item }}
              </p>
            </template>
            <span>{{ row.mechanismNum }}</span>
          </el-tooltip>
        </template>
      </vxe-table-column>
    </vxe-table>

    <!-- 图片预览对话框 -->
    <el-dialog
      title="图片预览"
      :visible.sync="previewDialogVisible"
      width="80%"
      center
    >
      <div class="preview-container">
        <img :src="currentPreviewImg" alt="预览图片" class="preview-img" />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="previewDialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { parseTimestamp } from "@/utils/index.js";

export default {
  name: "SalesAttrNew",
  components: {},
  filters: {
    dateTime: function (value) {
      if (!value) return "";
      return parseTimestamp(value);
    },
  },
  props: {
    imgTitle: {
      type: String,
      default: ""
    },
    showChange: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      tableData: [],
      previewDialogVisible: false,
      currentPreviewImg: ""
    };
  },
  computed: {
    // 路由参数
    urlParam() {
      return this.$route.query;
    },
    // 商品操作类型
    operationType: function () {
      return this.$store.getters.operationType;
    },
  },
  watch: { },
  created() {
    this.initMockData();
  },
  mounted() {},
  methods: {
    // 初始化模拟数据
    initMockData() {
      // 模拟销售属性数据
      this.tableData = [
        {
          id: 1,
          businessCode: "P001",
          pictureVersion: "V1.0",
          detailPictureList: [
            {
              name: "主图1",
              url: ""
            },
            {
              name: "主图2", 
              url: ""
            },
            {
              name: "主图3",
              url: ""
            }
          ],
          detailPictureNum: 3,
          pictureEnable: true,
          mechanismNum: 5,
          mechanismList: [
            "华润医药集团",
            "国药控股",
            "上海医药",
            "九州通医药",
            "华东医药"
          ]
        },
        {
          id: 2,
          businessCode: "P001",
          pictureVersion: "V1.1",
          detailPictureList: [
            {
              name: "精修图1",
              url: ""
            },
            {
              name: "精修图2",
              url: ""
            }
          ],
          detailPictureNum: 2,
          pictureEnable: false,
          mechanismNum: 3,
          mechanismList: [
            "华润医药集团",
            "国药控股", 
            "上海医药"
          ]
        },
        {
          id: 3,
          businessCode: "P001",
          pictureVersion: "V2.0",
          detailPictureList: [
            {
              name: "最新主图",
              url: ""
            }
          ],
          detailPictureNum: 1,
          pictureEnable: true,
          mechanismNum: 8,
          mechanismList: [
            "华润医药集团",
            "国药控股",
            "上海医药",
            "九州通医药",
            "华东医药",
            "广药集团",
            "康美药业",
            "步长制药"
          ]
        }
      ];
    },

    // 预览图片
    previewImages(imageList, index) {
      if (imageList && imageList[index]) {
        this.currentPreviewImg = imageList[index].url;
        this.previewDialogVisible = true;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.component-container {
  padding: 20px;
}

.button-wrap {
  padding-bottom: 10px;
}

.img-s {
  width: 80px;
  height: 80px;
  margin: 2px;
  cursor: pointer;
  border-radius: 4px;
  object-fit: cover;
  border: 1px solid #ddd;

  &:hover {
    border-color: #409EFF;
    transform: scale(1.05);
    transition: all 0.3s ease;
  }

  &.readonly-img {
    cursor: pointer;

    &:hover {
      border-color: #409EFF;
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
    }
  }
}

.img-preview-container {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.preview-container {
  text-align: center;
}

.preview-img {
  max-width: 100%;
  max-height: 500px;
  object-fit: contain;
}

.no-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border: 1px dashed #ddd;
  border-radius: 4px;
  color: #999;
  font-size: 12px;

  i {
    font-size: 24px;
    margin-bottom: 4px;
  }
}
</style>
