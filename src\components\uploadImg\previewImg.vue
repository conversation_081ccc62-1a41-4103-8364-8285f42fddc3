<template>
  <div>
    <!-- 图片预览对话框 -->
    <el-dialog :before-close="closeDlg" :visible.sync="dlgVisible" fullscreen class="text-lf" :modal="false">
      <div slot="title" class="dlg-title">
        <span>{{ imgTitle }}</span>
        <div>
          <el-button
            :disabled="imgList[currImgIndex].addBack"
            v-if="imgList[currImgIndex] && imgList[currImgIndex].pictureUrl"
            @click="addBackground(imgList[currImgIndex].pictureUrl)"
            size="mini"
            >{{ !imgList[currImgIndex].addBack ? "一键加白底" : "已填充白底" }}</el-button
          >
          <span class="cc" v-if="!!imgList[currImgIndex] && imgList[currImgIndex].hasOwnProperty('pictureWidth')"
            >宽高：{{ imgList[currImgIndex].pictureWidth + "px * " + imgList[currImgIndex].pictureHeight + "px" }}</span
          >
        </div>
      </div>
      <div class="top">
        <div class="productInfo" v-if="!!productInfo">
          <div class="btnBox" v-if="!webList.productPictureList">
            <el-button
              type="danger"
              size="mini"
              v-if="finishingAudit && !!imgList[currImgIndex] && imgList[currImgIndex].isToFinishing == 0"
              @click="handleToFinish(true)"
              >转精修</el-button
            >
            <el-button
              type="info"
              size="mini"
              v-if="finishingAudit && !!imgList[currImgIndex] && imgList[currImgIndex].isToFinishing == 1"
              @click="handleToFinish(false)"
              >已标记转精修</el-button
            >
            <el-button
              type="danger"
              size="mini"
              v-if="!!imgList[currImgIndex] && imgList[currImgIndex].deleteStatus == 0"
              @click="handleDelete(true)"
              >删除</el-button
            >
            <el-button
              type="info"
              size="mini"
              v-else-if="!!imgList[currImgIndex] && imgList[currImgIndex].hasOwnProperty('deleteStatus')"
              @click="handleDelete(false)"
              >取消删除</el-button
            >
          </div>
          <div class="title">商品资料：</div>
          <div class="list">
            <span class="label">通用名：</span>
            <span class="content">{{ productInfo.generalName || "无" }}</span>
          </div>
          <div class="list">
            <span class="label">商品名：</span>
            <span class="content">{{ productInfo.skuName || "无" }}</span>
          </div>
          <!-- <div class="list">
            <span class="label">商品大类：</span>
            <span class="content">{{ productInfo.spuCategoryName }}</span>
          </div> -->
          <div class="list">
            <span class="label">规格型号：</span>
            <span class="content">{{ productInfo.spec || "无" }}</span>
          </div>
          <div class="list">
            <span class="label">批准文号：</span>
            <span class="content">{{ productInfo.approvalNo || "无" }}</span>
          </div>
          <div class="list">
            <span class="label">生产厂家：</span>
            <span class="content">{{ productInfo.manufacturerName || "无" }}</span>
          </div>
          <div class="list">
            <span class="label">小包装条码：</span>
            <span class="content">{{ productInfo.smallPackageCode || "无" }}</span>
          </div>
          <div class="list" v-if="imgList[currImgIndex]">
            <span class="label">品牌：</span>
            <span class="content">{{ productInfo.brand || "无" }}</span>
          </div>
          <div class="list">
            <span class="label">处方分类：</span>
            <span class="content">{{ productInfo.prescriptionCategoryName || "无" }}</span>
          </div>
          <!-- <div class="list" v-if="imgList[currImgIndex]">
            <span class="label">是否委托生产：</span>
            <span class="content">{{ productInfo.delegationProduct != 0 ? '是' : '否'}}</span>
          </div> -->
          <div class="list" v-if="imgList[currImgIndex]">
            <span class="label">受托生产厂家：</span>
            <span class="content">{{ productInfo.entrustedManufacturerName || "无" }}</span>
          </div>
          <div class="list">
            <span class="label">管理属性：</span>
            <span class="content" v-if="!productInfo.sauAttrMap">无</span>
            <p class="content" v-if="productInfo.sauAttrMap && productInfo.sauAttrMap.key1">
              {{ productInfo.sauAttrMap.key1 }}:{{ productInfo.sauAttrMap.value1 }}
            </p>
            <p class="content" v-if="productInfo.sauAttrMap && productInfo.sauAttrMap.key2">
              {{ productInfo.sauAttrMap.key2 }}:{{ productInfo.sauAttrMap.value2 }}
            </p>
            <p class="content" v-if="productInfo.sauAttrMap && productInfo.sauAttrMap.key3">
              {{ productInfo.sauAttrMap.key3 }}:{{ productInfo.sauAttrMap.value3 }}
            </p>
            <p class="content" v-if="productInfo.sauAttrMap && productInfo.sauAttrMap.key4">
              {{ productInfo.sauAttrMap.key4 }}:{{ productInfo.sauAttrMap.value4 }}
            </p>
          </div>
          <div class="list" v-if="imgList[currImgIndex]">
            <span class="label">可用精修图版本数量：</span>
            <span class="content">{{ productInfo.detailPictureVersionCount || "无" }}</span>
          </div>
          <div class="list">
            <span class="label">自营状态：</span>
            <span class="content">{{ productInfo.preOperateStatusName || "无" }}</span>
          </div>
          <div class="list" v-if="webList.productPictureList && webList.productPictureList[currImgIndex]">
            <span class="label">爬取搜索关键词：</span>
            <span class="content">{{ webList.productPictureList[currImgIndex].keywords || "无" }}</span>
          </div>
          <div class="list" v-if="webList.productPictureList && webList.productPictureList[currImgIndex]">
            <span class="label">来源网站：</span>
            <span class="content">{{
              webList.productPictureList[currImgIndex] ? webList.productPictureList[currImgIndex].sourceSiteName : "无"
            }}</span>
          </div>
          <div class="list" v-if="webList.productPictureList && webList.productPictureList[currImgIndex]">
            <span class="label">图片地址：</span>
            <span class="content">{{ webList.productPictureList[currImgIndex].productSource || "无" }}</span>
          </div>
          <div class="list" v-if="!webList.productPictureList && imgList[currImgIndex]">
            <span class="label">爬取搜索关键词：</span>
            <span class="content">{{ imgList[currImgIndex].keywords || "无" }}</span>
          </div>
          <div class="list" v-if="!webList.productPictureList && imgList[currImgIndex]">
            <span class="label">来源网站：</span>
            <span class="content">{{ imgList[currImgIndex].sourceSiteName || "无" }}</span>
          </div>
          <div class="list" v-if="!webList.productPictureList && imgList[currImgIndex]">
            <span class="label">图片地址：</span>
            <span class="content">{{ imgList[currImgIndex].productSource || "无" }}</span>
          </div>
          <div class="list">
            <span class="label">产地：</span>
            <span class="content">{{ productInfo.originPlace || "无" }}</span>
          </div>
        </div>
        <div class="preview-box">
          <span class="circle circle-lf" v-if="currImgIndex != 0" @click="handlePicturePreview(currImgIndex - 1)">
            <i class="el-icon-arrow-left"></i>
          </span>
          <div class="img-l">
            <vue-cropper
              ref="cropper"
              :img="
                imgList[currImgIndex] ? (imgList[currImgIndex].base64 ? imgList[currImgIndex].base64 : imgList[currImgIndex].pictureUrl) : ''
              "
              :mosaic="false"
            ></vue-cropper>
          </div>

          <span class="circle circle-rt" v-if="currImgIndex != imgList.length - 1" @click="handlePicturePreview(currImgIndex + 1)">
            <i class="el-icon-arrow-right"></i>
          </span>
        </div>
      </div>
      <div class="text-center">
        {{ currImgIndex + 1 }} / {{ imgList.length }}
        <el-button @click="markPending(1)" v-show="isSearchPending && !marked" class="mark-btn" type="primary" size="mini"
          >标记为待定图</el-button
        >
        <el-button @click="markPending(0)" v-show="isSearchPending && marked" class="mark-btn" size="mini">已标记({{ markIndex }})</el-button>
      </div>
      <el-row class="fot-btn">
        <el-col :span="6">
          <el-button-group>
            <el-button size="medium" @click="downloadImg">下载</el-button>
            <el-button size="medium" @click="downloadOriginalImg">下载原图</el-button>
            <el-button size="medium" icon="el-icon-refresh-right" title="旋转" @click="toRevolve"></el-button>
          </el-button-group>
        </el-col>
      </el-row>
    </el-dialog>
    <el-dialog :visible.sync="dlgVisible2" append-to-body title="请确认" :close-on-click-modal="false">
      <p style="font-size:16px;text-align:center;margin:20px;">请确认是否保存当前的白底图片？</p>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="info" @click="dlgVisible2 = false">取 消</el-button>
        <el-button size="mini" type="danger" @click="cancelSave">不保存</el-button>
        <el-button size="mini" type="primary" @click="confirmSave">保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getPictureSizeInfo, addBackground, saveAddBackground } from "@/api/common"
import vueCropper from "@/components/vue-cropper/src/vue-cropper"
export default {
  props: {
    webList: {
      type: Array,
      required: false,
      default: () => []
    },
    productInfo: {
      type: Object,
      required: false,
      default: () => {}
    },
    finishingAudit: {
      type: Boolean,
      default: false
    },
    // 待定图片
    pendingImgList: {
      type: Array,
      required: false,
      default: () => []
    },
    // 是否显示待定图片操作按钮
    isSearchPending: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    async currImgIndex(e) {
      this.marked = false
      if (this.imgList[e] && !this.imgList[e].pictureWidth) {
        let res = await getPictureSizeInfo({
          pictureUrl: this.imgList[e].pictureUrl
        })
        console.log(res)
        if (res.retCode === 0) {
          this.imgList[e].pictureWidth = res.data.width
          this.imgList[e].pictureHeight = res.data.height
        } else {
          this.$message.error(res.retMsg)
        }
      }
      this.pendingImgList.map((item, i) => {
        if (this.imgList[e] && item.id === this.imgList[e].id) {
          this.marked = true
          this.markIndex = i + 1
        }
      })
    }
  },
  components: {
    vueCropper
  },
  computed: {},
  created() {},
  mounted() {},
  data() {
    return {
      close: null,
      continueIndex: null,
      originUrl: "",
      dlgVisible2: false,
      imgTitle: "",
      scale: 1,
      rotate: 0,
      dlgVisible: false,
      imgList: [],
      currImgIndex: null,
      smallPackageCode: "",
      marked: false, //是否标记为待定图
      markIndex: "" //当前的图片是被标记的第几张图片
    }
  },
  methods: {
    // 切换转精修
    handleToFinish(e) {
      if (e) {
        this.imgList[this.currImgIndex].isToFinishing = 1
      } else {
        this.imgList[this.currImgIndex].isToFinishing = 0
      }
    },
    // 打开弹框
    openDlg(list, index, str) {
      this.imgList = list
      this.currImgIndex = null
      this.currImgIndex = index
      this.smallPackageCode = str
      this.scale = 1
      this.rotate = 0
      this.dlgVisible = true
      this.changeTitle()
    },
    closeDlg() {
      if (this.imgList[this.currImgIndex].addBack && !this.imgList[this.currImgIndex].isAddBack) {
        this.dlgVisible2 = true
        this.continueIndex = null
        this.close = null
      } else {
        this.currImgIndex = null
        this.dlgVisible = false
      }
    },
    // 预览图片
    handlePicturePreview(index) {
      if (this.imgList[this.currImgIndex].addBack && !this.imgList[this.currImgIndex].isAddBack) {
        this.dlgVisible2 = true
        this.continueIndex = index
        this.close = 1
      } else {
        if (index >= 0 && index < this.imgList.length) {
          this.currImgIndex = index
        }
        this.changeTitle()
      }
    },
    // 修改标题
    changeTitle() {
      if (this.smallPackageCode) {
        this.imgTitle = `${this.smallPackageCode}-${this.imgList[this.currImgIndex].pictureName}`
      } else {
        this.imgTitle = `${this.imgList[this.currImgIndex].pictureName}`
      }
    },
    getBase64(src) {
      var _this = this
      return new Promise(function (resolve, reject) {
        let image = new Image()
        // 处理缓存
        if (src.indexOf('?')<0) {
          image.src = src + "?v=" + Math.random()
        } else {
           image.src = src
        }

        // 支持跨域图片
        image.crossOrigin = "*"
        image.onload = function () {
          let base64 = _this.transBase64FromImage(image)
          resolve(base64)
        }
      })
    },
    // 将网络图片转换成base64格式
    transBase64FromImage(image) {
      let canvas = document.createElement("canvas")
      canvas.width = image.width
      canvas.height = image.height
      let ctx = canvas.getContext("2d")
      ctx.drawImage(image, 0, 0, image.width, image.height)
      // 可选其他值 image/jpeg
      return canvas.toDataURL("image/png")
    },
    // 下载原图
    downloadOriginalImg() {
      this.getBase64(this.imgList[this.currImgIndex].pictureUrl).then(data=>{
        let a = document.createElement('a')
        a.download = this.title
        a.href = data
        a.click()
      })
    },
    // 下载图片
    downloadImg() {
      var aLink = document.createElement("a")
      aLink.download = this.imgTitle
      this.$refs.cropper.getCropBlob(data => {
        this.downImg = window.URL.createObjectURL(data)
        aLink.href = window.URL.createObjectURL(data)
        aLink.click()
      })
    },
    // 旋转图片
    toRevolve() {
      this.$refs.cropper.rotateRight()
    },
    handleDelete(isFlag) {
      if (isFlag) {
        this.imgList[this.currImgIndex].deleteStatus = 1
      } else {
        this.imgList[this.currImgIndex].deleteStatus = 0
      }
      this.$forceUpdate()
    },
    // 标记/取消标记待定图片
    markPending(e) {
      if (this.pendingImgList.length > 99) {
        this.$message.error("最多标记100张图片")
        return
      }
      this.$emit("changePendingList", this.imgList[this.currImgIndex], e)
      this.marked = e ? true : false
      if (e) {
        this.markIndex = this.pendingImgList.length
      }
    },
    // 图片加白底
    async addBackground(e) {
      console.log(e)
      this.originUrl = e
      try {
        const res = await addBackground({ pictureUrl: e })
        if (res.retCode === 0) {
          console.log(res)
          this.imgList[this.currImgIndex].pictureUrl = res.data.pictureUrl
          this.imgList[this.currImgIndex].base64 = res.data.base64
          this.imgList[this.currImgIndex].addBack = true
        } else {
          this.$message.error(res.retMsg)
        }
      } catch (error) {
        this.$message.error(res.retMsg)
      }
    },
    // 取消加白底
    cancelSave() {
      this.imgList[this.currImgIndex].pictureUrl = this.originUrl
      this.imgList[this.currImgIndex].addBack = false
      this.dlgVisible2 = false
      if (this.close) {
        this.handlePicturePreview(this.continueIndex)
      } else {
        this.closeDlg()
      }
    },
    // 保存加白底
    async confirmSave() {
      this.originUrl = ""
      this.imgList[this.currImgIndex].isAddBack = true
      this.dlgVisible2 = false
      try {
        if (this.imgList[this.currImgIndex].pictureId) {
          const res = await saveAddBackground({
            pictureId: this.imgList[this.currImgIndex].pictureId,
            pictureUrl: this.imgList[this.currImgIndex].pictureUrl
          })
          if (res.retCode !== 0) {
            this.$message.error(res.retMsg)
          } else {
            if (this.close) {
              this.handlePicturePreview(this.continueIndex)
            } else {
              this.closeDlg()
            }
          }
        } else {
          if (this.close) {
            this.handlePicturePreview(this.continueIndex)
          } else {
            this.closeDlg()
          }
        }
      } catch (error) {
        this.$message.error(res.retMsg)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.el-upload-list {
  display: none;
}
.el-dialog__wrapper /deep/ .el-dialog .el-dialog__body {
  padding: 0 0 20px 0;
}
.el-dialog__wrapper /deep/ .el-dialog {
  // max-height: 100%;
}
.text-center {
  text-align: center;
  margin-top: 10px;
  .mark-btn {
    position: absolute;
    right: 30px;
  }
}
.text-lf {
  text-align: left;
}
.text-rt {
  text-align: right;
}
.img-slot:hover {
  cursor: pointer;
}
.fot-btn {
  margin: 0 20px;
}
.img-box {
  position: relative;
  display: inline-block;
  border-radius: 5px;
  width: 80px;
  height: 80px;
}
.img-s {
  border-radius: 5px;
  width: 80px;
  height: 80px;
}
.img-status {
  position: absolute;
  width: 100px;
  height: 100px;
  top: 10px;
  right: 10px;
  z-index: 200;
  img {
    width: 100%;
  }
}
.img-l {
  position: relative;
  width: 100%;
  height: 600px;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  img {
    user-select: none;
  }
}

.img-actions {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  border-radius: 5px;
  cursor: pointer;
  text-align: center;
  opacity: 0;
  font-size: 24px;
  color: #fff;
  background-color: rgb(0, 0, 0, 0.5);
  line-height: 80px;
  transition: opacity 0.3s;
}
.img-actions:hover {
  opacity: 0.5;
}

.circle {
  position: absolute;
  z-index: 2000;
  top: calc(50% - 15px);
  width: 30px;
  height: 30px;
  border-radius: 50%;
  opacity: 0.5;
  background-color: #ccc;
  text-align: center;
  font-size: 22px;
  line-height: 30px;
  transition: opacity 0.3s;
}
.circle-lf {
  left: 20px;
}
.circle-rt {
  right: 20px;
}
.circle:hover {
  opacity: 1;
}
.top {
  position: relative;
  display: flex;
  .btnBox {
    position: absolute;
    right: 30px;
    top: 30px;
    z-index: 999;
  }
  .productInfo {
    min-width: 400px;
    max-width: 500px;
    padding: 20px;
    line-height: 32px;
    .title {
      font-size: 18px;
      font-weight: bold;
      color: #333;
    }
    .list {
      .label {
        font-weight: bold;
        color: #333;
      }
      .content {
        text-indent: 10px;
      }
    }
  }
  .preview-box {
    position: relative;
    flex: 1;
    padding-top: 10px;
  }
}
.dlg-title {
  display: flex;
  justify-content: space-between;
  .cc {
    padding: 0 50px;
  }
}
</style>
