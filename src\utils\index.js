import store from '@/store'
/**
 * Created by ranliang on 2019.12.27
 */

/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string}
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
      time = parseInt(time)
    }
    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value] }
    if (result.length > 0 && value < 10) {
      value = '0' + value
    }
    return value || 0
  })
  return time_str
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
  if (('' + time).length === 10) {
    time = parseInt(time) * 1000
  } else {
    time = +time
  }
  const d = new Date(time)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return parseTime(time, option)
  } else {
    return (
      d.getMonth() +
      1 +
      '月' +
      d.getDate() +
      '日' +
      d.getHours() +
      '时' +
      d.getMinutes() +
      '分'
    )
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = url.split('?')[1]
  if (!search) {
    return {}
  }
  return JSON.parse(
    '{"' +
    decodeURIComponent(search)
      .replace(/"/g, '\\"')
      .replace(/&/g, '","')
      .replace(/=/g, '":"')
      .replace(/\+/g, ' ') +
    '"}'
  )
}

/**
 * 时间戳序列化为{y}-{m}-{d} {h}:{i}:{s}
 */
export function parseTimestamp(timestamp) {
  let ret = timestamp;
  let date = null;
  if (timestamp) {
    try {
      date = new Date(timestamp);
      ret = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, 0)}-${date.getDate().toString().padStart(2, 0)} ${date.getHours().toString().padStart(2, 0)}:${date.getMinutes().toString().padStart(2, 0)}:${date.getSeconds().toString().padStart(2, 0)}`;
      date = null;
    } catch (err) {
      console.log('Error:utils/index.js parseTimestamp ' + err);
    }
  }
  return ret;
}
/**
 * 时间戳序列化为{y}-{m}-{d}
 */
export function parseDate(timestamp) {
  let ret = timestamp;
  let date = null;
  if (timestamp) {
    try {
      date = new Date(timestamp);
      ret = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, 0)}-${date.getDate().toString().padStart(2, 0)}`;
      date = null;
    } catch (err) {
      console.log('Error:utils/index.js parseTimestamp ' + err);
    }
  }
  return ret;
}


/**
 * 导出excel公用
 */
export function utils_export_excel(blobBulider, fileName) {
  let blob = new Blob([blobBulider]);
  let blobUrl = window.URL.createObjectURL(blob);
  let a = document.createElement('a');
  a.style.display = 'none';
  a.download = fileName;
  a.href = blobUrl;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}

String.prototype.getByteLength = function (value, length) {
  var len = 0;
  var sliceIndex = 0;
  for (var i = 0; i < this.length; i++) {
    if (this.charCodeAt(i) > 127 || this.charCodeAt(i) == 94) {
      len += 2;
    } else {
      len++;
    }
    // 存储超出时截取的下标
    if (len <= length) {
      sliceIndex++;
    }
  }
  return {
    len,
    sliceIndex
  };
}

/*
 * 根据字节长度设置可输入最大值
 * val 当前文本对象
 * length 设置的长度值
 */
export function setMaxLengthByByte(val, length) {
  let byte = val.getByteLength(val, length);
  if (byte.len > length) {
    return val.slice(0, byte.sliceIndex)
  } else {
    return val
  }
}
/*
 * 通过名称查找选项ID
 * val 查询的字符
 * name 当前选项名称
 * Options 选项数组
 */
export function findIdByOptions(val, name, Options) {
  let arr = Options.filter(item => item[name] == val);
  if (arr.length > 0) {
    return arr[0].id;
  } else {
    return "";
  }
}
/*
 * 通过id查找名称
 * id查询的字符
 * key 当前选项名称
 * Options 选项数组
 */
export function findNameByOptions(val, key, Options) {
  try {
    let arr = Options.filter(item => item[key] == val);
    if (arr.length > 0) {
      return arr[0].dictName;
    } else {
      return "";
    }
  } catch (e) {
    console.log(e)
    // handle the exception
  }

}

/*
 * 有效期转换，规则：-1 对应 - ;0 对应 *;
 * value 转换值
 */
export function validityTransition(value) {
  // -1 对应 - ;0 对应 *;
  switch (value) {
    case -1:
      return '-';
      break;
    case 0:
      return '*';
      break;
    default:
      return value;
      break;
  }
}
/*
 * 全角转半角;
 * str 转换值
 */
export function toCDB(str) {
  var tmp = "";
  for (var i = 0; i < str.length; i++) {
    if (str.charCodeAt(i) > 65248 && str.charCodeAt(i) < 65375) {
      tmp += String.fromCharCode(str.charCodeAt(i) - 65248);
    } else {
      tmp += String.fromCharCode(str.charCodeAt(i));
    }
  }
  return tmp
}
/*
 * 半角转全角;
 * txtstring 转换值
 */
export function toDBC(txtstring) {
  var tmp = "";
  for (var i = 0; i < txtstring.length; i++) {
    if (txtstring.charCodeAt(i) == 32) {
      tmp = tmp + String.fromCharCode(12288);
    } else if (txtstring.charCodeAt(i) < 127) {
      tmp = tmp + String.fromCharCode(txtstring.charCodeAt(i) + 65248);
    }
  }
  return tmp;
}
/**
 * @description: 防抖函数
 * @param {number} delay 执行的延时 
 * @param {function} fn 延时处理的函数
 * @return: 
 */
export function debounce(fn, delay) {
  let timeId;
  return function (...args) {
    clearTimeout(timeId)
    timeId = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}
/**
 * @description: 节流函数
 * @param {function} fn 节流处理的函数
 * @return: 
 */
export function throttle(fn) {
  let locked = false;
  return function (...args) {
    if (locked) return;
    locked = true;
    window.requestAnimationFrame(_ => {
      fn.apply(this, args);
      locked = false;
    });
  };
}

/**
 * @description: 递归遍历获取所属经营范围父级ID
 * @param {array} _arr 遍历的选项
 * @param {array} value 获取到的值
 */
export function getId(_arr, value) {
  for (let obj of _arr) {
    if (obj.disable && obj.id) {
      value.push(obj.id)
      if (obj.children && obj.children.length > 0) {
        getId(obj.children, value);
      }
    }
  }
}

/**
 * @description: 创建一个下载链接
 * @param {string} url 下载链接值
 * @param {string} name 下载名称
 */
export function createDownloadElement(url, name) {

  const a = document.createElement('a');
  a.style.display = 'block';
  a.download = name;
  a.href = url;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}
// 验证数组是否有重复项
export function isRepeat(arr) {
  let hash = {};
  for (let i in arr) {
    if (hash[arr[i]]) {
      return true;
    }
    hash[arr[i]] = true;
  }
  return false;
}

// 判断是否有权限
export function hasPermission(permission) {
  let permissionList = store.getters.permissionList,
    result = false;
  for (let item of permissionList) {
    if (item.title == permission) {
      result = true;
      break;
    }
  };
  return result;
}

// 获取近*天日期范围
// range天数，例如7则为最近一周
export function getDateRange(range) {
  const end = new Date();
  const start = new Date();
  start.setTime(start.getTime() - 3600 * 1000 * 24 * range);
  return [parseTime(start, '{y}-{m}-{d}'), parseTime(end,'{y}-{m}-{d}')]
}

// 判断一个字符串是否是数字
export function isNumber(val) {
  var regPos = /^\d+(\.\d+)?$/; //非负浮点数
  var regNeg = /^(-(([0-9]+\.[0-9]*[1-9][0-9]*)|([0-9]*[1-9][0-9]*\.[0-9]+)|([0-9]*[1-9][0-9]*)))$/; //负浮点数
  if(regPos.test(val) || regNeg.test(val)) {
      return true;
      } else {
      return false;
      }
  }

