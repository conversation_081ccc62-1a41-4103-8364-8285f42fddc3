<template>
  <div class="task-to-be-film-container">
    <!-- 查询表单 -->
    <div class="search-form-wrap">
      <el-form :model="model" ref="refSearchForm">
        <el-row type="flex" :gutter="20">
          <!-- 申请时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="申请时间" prop="createTime">
              <el-date-picker
                v-model="formData.createTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                clearable
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <!-- 单据编号 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="单据编号">
              <el-input v-model="formData.applyCode" placeholder="单据编号" clearable></el-input>
            </el-form-item>
          </el-col>
          <!-- 申请人 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="申请人">
              <el-input v-model="formData.applyer" placeholder="申请人" clearable></el-input>
            </el-form-item>
          </el-col>
          <!-- 所属机构 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="所属机构">
              <el-input v-model="formData.mechanism" placeholder="所属机构" clearable></el-input>
            </el-form-item>
          </el-col>
          <!-- 申请类型 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="申请类型">
              <el-select v-model="formData.batchType" placeholder="请选择" clearable>
                <el-option label="全部" :value="null"></el-option>
                <el-option label="基础信息" :value="1"></el-option>
                <el-option label="sku信息" :value="25"></el-option>
                <el-option label="税率" :value="14"></el-option>
                <el-option label="标签信息" :value="24"></el-option>
                <el-option label="扩展信息" :value="2"></el-option>
                <el-option label="用药指导" :value="6"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 申请原因 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="申请原因">
              <el-input v-model="formData.applyReason" placeholder="申请原因" clearable></el-input>
            </el-form-item>
          </el-col>
          <!-- 商品编码 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="商品编码">
              <el-input v-model="formData.productInfo" placeholder="商品编码/sku编码/spu编码/原商品编码" clearable></el-input>
            </el-form-item>
          </el-col>
          <!-- 审核状态 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="审核状态">
              <el-select v-model="formData.auditStatus" placeholder="请选择" clearable>
                <el-option label="全部" value=""></el-option>
                <el-option label="运营中心审核中" value="0"></el-option>
                <el-option label="质管总部审核中" value="1"></el-option>
                <el-option label="审核驳回" value="2"></el-option>
                <el-option label="审核通过" value="3"></el-option>
                <el-option label="审核不通过" value="4"></el-option>
                <el-option label="财务部审核中" value="5"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 解析状态 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="解析状态">
              <el-select v-model="formData.analysisStatus" placeholder="请选择" clearable>
                <el-option label="全部" value=""></el-option>
                <el-option label="处理失败" value="0"></el-option>
                <el-option label="处理完成" value="1"></el-option>
                <el-option label="处理中" value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 审核人 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="审核人">
              <el-input v-model="formData.auditer" placeholder="审核人" clearable></el-input>
            </el-form-item>
          </el-col>
          <!-- 审核时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="审核时间" prop="auditTime">
              <el-date-picker
                v-model="formData.auditTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                clearable
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <!-- 按钮 -->
          <el-col :span="6" :offset="18">
            <el-button size="medium" @click="handleExport">导出EXCEL</el-button>
            <el-button type="primary" size="medium" @click="searchForm">查询</el-button>
            <el-button size="medium" @click="btnResetClick">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="btn-wrap">
      <el-form>
        <el-form-item label="批量修改:" label-width="80px">
          <el-radio-group v-model="batchType">
            <el-radio v-if="hasPermission('商品列表-新增商品')" :label="1" border>基础信息</el-radio>
            <el-radio v-if="hasPermission('商品列表-新增商品')" :label="25" border>sku信息</el-radio>
            <el-radio v-if="hasPermission('商品列表-新增商品')" :label="14" border>税率</el-radio>
            <el-radio v-if="hasPermission('商品列表-新增商品')" :label="24" border>标签信息</el-radio>
            <el-radio v-if="hasPermission('商品列表-新增商品')" :label="2" border>扩展属性</el-radio>
            <el-radio v-if="hasPermission('商品列表-新增商品')" :label="6" border>用药指导</el-radio>
          </el-radio-group>
          <el-button @click="downloadTemplate" :disabled="!batchType" style="margin:0 10px">下载模板</el-button>
        </el-form-item>
      </el-form>
      <div class="file-name-shower">
        <el-upload
          accept=".xlsx"
          :data="{ batchType }"
          ref="refUpload"
          class="upload-demo"
          :action="action"
          :on-change="uploadOnChange"
          :show-file-list="false"
          :auto-upload="false"
          :multiple="false"
          :limit="1"
          :disabled="uploadDisabled"
          :on-success="uploadOnSuccess"
          :on-error="uploadOnError"
        >
          <p style="width:260px">{{ selectedFileName || "请点击选择本地文件" }}</p>
          <i @click.stop="removeFile" v-show="uploadDisabled" class="el-icon-circle-close"></i>
          <el-popover placement="top" width="200" trigger="hover" content="只能上传表格文件，且不超过1000条">
            <i v-show="!uploadDisabled" class="info-suffix-icon el-icon-question" slot="reference"></i>
          </el-popover>
        </el-upload>
        <el-button @click="enterUpload" style="margin:0 10px" type="primary" :disabled="!batchType || !selectedFileName">确认上传</el-button>
      </div>
      <!-- <vxe-toolbar custom style="display: inline-block"></vxe-toolbar> -->
    </div>
    <el-tabs @tab-click="changeTab" v-model="tabName" type="card" style="padding-left:15px">
      <el-tab-pane label="单据列表" name="first"> </el-tab-pane>
      <el-tab-pane label="商品列表" name="second"> </el-tab-pane>
    </el-tabs>
    <!-- table -->
    <div class="table-wrap">
      <vxe-table
        v-show="tabName === 'first'"
        border
        highlight-hover-row
        highlight-current-row
        resizable
        height="100%"
        auto-resize
        size="small"
        align="center"
        :tooltip-config="{ enterable: false }"
        :loading="tableLoading"
        :data="tableData"
        :seq-config="{ startIndex: (page - 1) * limit }"
        ref="refVxeTable"
        @sort-change="sortQueryList"
      >
        <vxe-table-column type="seq" title="序号" width="60" show-header-overflow show-overflow fixed="left"></vxe-table-column>
        <vxe-table-column field="applyDate" title="申请时间" min-width="120" show-header-overflow show-overflow sortable>
          <template v-slot="{ row }">
            {{ parseTime(row.applyDate) }}
          </template>
        </vxe-table-column>
        <vxe-table-column field="applyer" title="申请人" min-width="120" show-header-overflow show-overflow sortable></vxe-table-column>
        <vxe-table-column field="mechanism" title="所属机构" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="applyCode" title="单据编号" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="applyReason" title="申请原因" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column title="提交商品数量" min-width="120" show-header-overflow show-overflow>
          <template v-slot="{ row }">
            <el-link :underline="false" type="primary" @click.stop="downloadApply(row, 1)">{{ row.applyTotalNum }}</el-link>
          </template>
        </vxe-table-column>
        <vxe-table-column title="异常商品数量" min-width="120" show-header-overflow show-overflow>
          <template v-slot="{ row }">
            <el-link :underline="false" type="primary" @click.stop="downloadApply(row, 0)">{{ row.applyAbnormalNum }}</el-link>
          </template>
        </vxe-table-column>
        <vxe-table-column title="解析状态" min-width="120" show-header-overflow show-overflow>
          <template v-slot="{ row }">
            {{ row.analysisStatus | filterAnalysisStatus }}
          </template>
        </vxe-table-column>
        <vxe-table-column field="remark" title="解析说明" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column title="解析完成时间" min-width="120" show-header-overflow show-overflow>
          <template v-slot="{ row }">
            {{ parseTime(row.analysisEndDate) }}
          </template>
        </vxe-table-column>
        <vxe-table-column field="auditStatusStr" title="审核状态" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="auditer" title="审核人" min-width="120" show-header-overflow show-overflow sortable></vxe-table-column>
        <vxe-table-column field="auditDate" title="审核时间" width="120" show-header-overflow show-overflow sortable>
          <template v-slot="{ row }">
            {{ parseTime(row.auditDate) }}
          </template>
        </vxe-table-column>
      </vxe-table>
      <vxe-table
        v-show="tabName === 'second'"
        border
        highlight-hover-row
        highlight-current-row
        resizable
        height="100%"
        auto-resize
        size="small"
        align="center"
        :tooltip-config="{ enterable: false }"
        :loading="tableLoading"
        :data="tableData2"
        :seq-config="{ startIndex: (page - 1) * limit }"
        ref="refVxeTable2"
        @sort-change="sortQueryList"
      >
        <vxe-table-column type="seq" title="序号" width="60" show-header-overflow show-overflow fixed="left"></vxe-table-column>
        <vxe-table-column field="applyDate" title="申请时间" min-width="120" show-header-overflow show-overflow sortable>
          <template v-slot="{ row }">
            {{ parseTime(row.applyDate) }}
          </template>
        </vxe-table-column>
        <vxe-table-column field="applyer" title="申请人" min-width="120" show-header-overflow show-overflow sortable></vxe-table-column>
        <vxe-table-column field="mechanism" title="所属机构" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="applyCode" title="单据编号" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="applyContent" title="申请内容" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="spuCategoryName" title="商品大类" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="sauCode" title="商品编码" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="skuCode" title="sku编码" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="spuCode" title="spu编码" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="originalProductCode" title="原商品编码" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="productId" title="商品id" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="generalName" title="通用名" width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="productName" title="商品名" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="spec" title="规格/型号" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="manufacturer" title="生产厂家" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="packageUnit" title="包装单位" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="approvalNo" title="批准文号" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="firstCategory" title="一级分类" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="secondCategory" title="二级分类" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="thirdCategory" title="三级分类" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="fourthCategory" title="四级分类" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="fiveCategory" title="五级分类" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="sixCategory" title="六级分类" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="prescriptionCategory" title="处方分类" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="taxCategoryCode" title="税务分类" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="inRate" title="进项税率" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="outRate" title="销项税率" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="smallPackageCode" title="小包装条码" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="mediumPackageCode" title="中包装条码" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="piecePackageCode" title="件包装条码" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column title="有效期" min-width="120" show-header-overflow show-overflow>
          <template v-slot="{ row }">{{ row | filterValidity }}</template>
        </vxe-table-column>
        <vxe-table-column field="originPlace" title="产地" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="storageCond" title="存储条件" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="specialAttr" title="特殊属性" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="brand" title="品牌/商标" min-width="120" show-header-overflow show-overflow></vxe-table-column>
      </vxe-table>
    </div>
    <!-- 分页 -->
    <el-row class="custom-pagination-wrap">
      <el-col>
        <el-pagination
          background
          :current-page.sync="page"
          :page-sizes="[20, 50, 100, 200]"
          :page-size.sync="limit"
          :total="total"
          layout="prev, pager, next, jumper,total, sizes"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { parseTimestamp, hasPermission, utils_export_excel } from "@/utils"
import { fileDownLoad } from "@/api/productPicture"
import {
  getBatchModifyRecordList,
  getBatchModifyDetailList,
  downloadBatchModifyTemplate,
  exportBatchModifyDetail,
  exportBatchModifyRecord
} from "@/api/product"
export default {
  name: "batchModifyList",
  components: {},
  filters: {
    // 格式化解析状态
    filterAnalysisStatus(e) {
      switch (e) {
        case 0:
          return "处理失败"
        case 1:
          return "处理完成"
        case 2:
          return "处理中"
      }
    },
    // 格式化审核状态
    filterAuditStatus(e) {
      switch (e) {
        case 0:
          return "运营中心审核中"
        case 1:
          return "质管总部审核中"
        case 2:
          return "审核驳回"
        case 3:
          return "审核通过"
        case 4:
          return "审核不通过"
        case 5:
          return "财务部审核中"
      }
    },
    // 格式化有效期
    filterValidity(e) {
      switch (e.validity) {
        case -1:
          return "-"
        case 0:
          return "*"
        case "-":
          return "-"
        case "*":
          return "*"
        default:
          var listStr = ""
          if (e.validityUnit == 1) {
            listStr = e.validity + " " + "日"
          } else if (e.validityUnit == 2) {
            listStr = e.validity + " " + "月"
          } else if (e.validityUnit == 3) {
            listStr = e.validity + " " + "年"
          }
          return listStr
      }
    }
  },
  props: {},
  data() {
    return {
      action: process.env.VUE_APP_BASE_API + "/api/modifyRecord/add/batchModifyRecord",
      uploadDisabled: false,
      selectedFileName: "",
      batchType: "",
      page: 1,
      limit: 20,
      total: 0, //总条数
      formData: {},
      sortList: [],
      tableLoading: false,
      tableData: [],
      tableData2: [],
      tabName: "first"
    }
  },
  computed: {},
  watch: {},
  created() {
    this.getRecordList()
  },
  methods: {
    // 查询
    searchForm(e) {
      if (e !== 1) {
        this.page = 1
      }
      if (this.tabName === "first") {
        this.getRecordList()
      } else {
        this.getProductList()
      }
    },
    // 重置
    btnResetClick() {
      this.formData = {}
      this.sortList = []
      this.page = 1
      this.searchForm()
    },
    //   获取单据列表
    async getRecordList() {
      try {
        let param = Object.assign(this.formData)
        param.page = this.page
        param.limit = this.limit
        param.sortList = this.sortList
        param.applyStartDate = param.createTime ? param.createTime[0] : null
        param.applyEndDate = param.createTime ? param.createTime[1] : null
        param.modifyTypes = [1,2,6,14,24,25]
        this.tableLoading = true
        const res = await getBatchModifyRecordList(param)
        this.tableLoading = false
        this.tableData = res.data.list
        this.total = res.data.total
      } catch (error) {
        console.log(error)
      }
    },
    //   获取商品列表
    async getProductList() {
      try {
        let param = Object.assign(this.formData)
        param.page = this.page
        param.limit = this.limit
        param.sortList = this.sortList
        // param.sortList = [{ order: 0, columnName: "applyDate", lift: "DESC" }]
        this.tableLoading = true
        const res = await getBatchModifyDetailList(param)
        this.tableLoading = false
        this.tableData2 = res.data.list
        this.total = res.data.total
      } catch (error) {
        console.log(error)
      }
    },
    // 下载 e:信息 t:1正常数量 0异常数量
    async downloadApply(e, t) {
      if (t) {
        if (e.applyTotalNum) {
          try {
            const res = await fileDownLoad(e.originalFileUrl, e.originalFileName)
            utils_export_excel(res, e.originalFileName)
          } catch (error) {
            console.log(error)
          }
        }
      } else {
        if (e.applyAbnormalNum) {
          try {
            const res = await fileDownLoad(e.abnormalFileUrl, e.abnormalFileName)
            utils_export_excel(res, e.abnormalFileName)
          } catch (error) {
            console.log(error)
          }
        }
      }
    },
    // 切换tab
    changeTab(e) {
      this.page = 1
      this.sortList = []
      this.searchForm()
    },
    // 排序查询
    sortQueryList({ property, order }) {
      this.sortList = [{ columnName: property, lift: order }]
      this.page = 1
      this.searchForm()
    },
    // limit 改变事件
    handleSizeChange(pageSize) {
      this.page = 1
      this.limit = pageSize
      this.searchForm()
    },

    // page 改变事件
    handleCurrentChange(currentPage) {
      this.page = currentPage
      this.searchForm(1)
    },
    //   时间格式化
    parseTime(e) {
      return parseTimestamp(e)
    },
    hasPermission(str) {
      return hasPermission(str)
    },
    // 移除选择的文件
    removeFile() {
      this.selectedFileName = ""
      this.uploadDisabled = false
      this.$refs.refUpload.clearFiles()
    },
    /**
     * 选择上传文件
     */
    uploadOnChange(file, fileList) {
      //只在添加文件时触发
      if (file.status === "ready") {
        this.uploadDisabled = true
        this.selectedFileName = file.name
        this.uploadBtnDisabled = false
        if (fileList.length > 1) {
          fileList.splice(0, fileList.length - 1)
        }
      }
    },
    // 上传成功回调
    uploadOnSuccess(e) {
      if (e.retCode === 0) {
        this.removeFile()
        this.batchType = ""
        this.$message.success("上传成功")
      } else {
        this.$message.error(e.retMsg || "上传失败")
      }
    },
    // 上传失败回调
    uploadOnError() {
      this.$message.error(e.retMsg || "上传失败")
    },
    // 确定上传
    enterUpload() {
      this.$refs.refUpload.submit()
    },
    // 下载模板
    async downloadTemplate() {
      try {
        const res = await downloadBatchModifyTemplate(this.batchType)
        utils_export_excel(res, "批量修改记录模板.xlsx")
      } catch (error) {
        console.log(error)
      }
    },
    // 导出excel
    async handleExport() {
      let param = Object.assign(this.formData)
      param.sortList = this.sortList
      const res = this.tabName === "first" ? await exportBatchModifyDetail(param) : await exportBatchModifyRecord(param)
       if (!res.retCode) {
          this.$message.success(res.retMsg);
        } else {
          this.$message.error(res.retMsg);
        }
    }
  }
}
</script>
<style lang="scss" scoped>
.task-to-be-film-container {
  /**
   * 查询表单
   */
  .search-form-wrap {
    width: 100%;
    padding: 15px;
    border-bottom: 1px dashed #e4e4eb;

    .el-row {
      flex-wrap: wrap;

      .el-col {
        display: flex;
        justify-content: flex-end;
        .el-form-item {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          width: 100%;
          margin-right: 0;
          margin-bottom: 15px;

          /deep/ {
            .el-form-item__label {
              padding: 0 12px;
              color: #292933;
              font-weight: normal;
            }

            .el-form-item__content {
              flex: 1;
              margin-left: 0 !important;
              .el-range-editor.el-input__inner {
                width: 100%;

                .el-range-separator {
                  width: 14px;
                  padding: 0;
                }
              }
            }
          }
          .el-input {
            width: 100%;
          }

          .el-select {
            width: 100%;
          }
          .el-cascader {
            width: 100%;
          }
        }
      }
    }
  }
  /**
     * 按钮组
     */
  .btn-group-wrap {
    font-size: 0;
    padding: 15px;
    .el-col {
      .el-button,
      .vxe-column-filter-container {
        margin-right: 10px;
      }
    }
  }
  /**
     * table
     */
  .table-wrap {
    width: 100%;
    min-height: 440px;
    height: calc(100vh - 346px);
    padding: 0 15px;
    /deep/ .vxe-table .vxe-body--row.row--current {
      background: #f5f7fa;
      color: #606266;
    }
  }
}
.btn {
  padding: 0 10px;
}
.btn-wrap /deep/ {
  padding: 15px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  .el-form {
    .el-form-item {
      margin: 0;
    }
  }
  .el-button {
    margin-bottom: 15px;
    margin-left: 0;
    margin-right: 15px;
  }
  .vxe-toolbar {
    flex: 1;
    .vxe-button--wrapper {
      display: none;
    }
    .vxe-tools--operate {
      margin-top: 2px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
.el-radio {
  margin: 0;
}
.file-name-shower {
  width: 300px;
  height: 40px;
  line-height: 40px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 0 15px;
  text-align: left;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .upload-demo {
    margin-right: 10px;
    /deep/ .el-upload {
      display: flex !important;
      justify-content: space-between;
      align-items: center;
      p {
        width: 200px;
        position: relative;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        text-align: left;
      }
    }
  }
}
.upload {
  width: 96px;
  line-height: 40px;
  padding: 0 12px;
  color: #292933;
  font-size: 14px;
  font-weight: normal;
}
</style>
