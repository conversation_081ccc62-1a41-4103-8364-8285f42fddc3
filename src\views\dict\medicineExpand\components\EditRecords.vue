<!--
 * @Descripttion: 
 * @Version: 1.0
 * @Author: zyh
 * @Date: 2025-02-07 14:36:29
 * @LastEditors: zyh
 * @LastEditTime: 2025-02-18 14:02:45
-->

<template>
    <div class="container">
        <el-dialog title="修改记录"
                   :visible.sync="dialogVisible"
                   width="1000px">
            <div class="table-wrap">
                <vxe-table border
                           highlight-hover-row
                           resizable
                           auto-resize
                           size="small"
                           align="center"
                           :tooltip-config="{ enterable: false }"
                           :loading="tableLoading"
                           :data="tableData"
                           :seq-config="{ startIndex: (pageNum - 1) * pageSize }"
                           ref="refVxeTable">
                    <vxe-table-column type="seq"
                                      title="序号"
                                      width="60"
                                      show-header-overflow
                                      show-overflow></vxe-table-column>
                    <vxe-table-column field="createTime"
                                      title="修改时间"
                                      min-width="120"
                                      show-header-overflow
                                      show-overflow><template v-slot="{ row }">
                            {{ parseTime(row.createTime) }}
                        </template></vxe-table-column>
                    <vxe-table-column field="createUser"
                                      title="修改人"
                                      width="120"
                                      show-header-overflow
                                      show-overflow>
                    </vxe-table-column>
                    <vxe-table-column field="dictValue"
                                      title="拓展字段"
                                      width="120"
                                      show-header-overflow
                                      show-overflow>
                    </vxe-table-column>
                    <vxe-table-column field="oldExtDetailValue"
                                      title="原拓展值"
                                      show-header-overflow
                                      show-overflow>
                    </vxe-table-column>
                    <vxe-table-column field="extDetailValue"
                                      title="新拓展值"
                                      show-header-overflow
                                      show-overflow></vxe-table-column>
                    <vxe-table-column field="statusName"
                                      title="启停用"
                                      width="80"
                                      show-header-overflow
                                      show-overflow></vxe-table-column>
                </vxe-table>
                <el-row class="custom-pagination-wrap">
                    <el-col>
                        <el-pagination background
                                       :current-page.sync="pageNum"
                                       :page-sizes="[20, 50, 100, 200]"
                                       :page-size.sync="pageSize"
                                       :total="total"
                                       layout="prev, pager, next, jumper,total, sizes"
                                       @size-change="handleSizeChange"
                                       @current-change="handleCurrentChange">
                        </el-pagination>
                    </el-col>
                </el-row>
            </div>
            <span slot="footer"
                  class="dialog-footer">
                <el-button @click="dialogVisible = false">关闭</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import { apiQueryLogList } from "@/api/dict.js";
import { parseTimestamp } from "@/utils/index.js";
export default {
    name: "",
    data () {
        return {
            pageNum: 1,
            pageSize: 20,
            total: 0, //总条数
            dialogVisible: false,
            tableData: [],
            tableLoading: false,
        };
    },
    methods: {
        openLogList (row) {
            this.dialogVisible = true;
            this.queryLogList(row);
        },
        async queryLogList (row) {
            this.tableLoading = true;
            let res = await apiQueryLogList({
                limit: this.pageSize,
                page: this.pageNum,
                commonName: row.commonName,
                categoryCode: row.categoryCode,
            });
            this.tableLoading = false;
            if (res.retCode == 0) {
                this.tableLoading = false;
                this.tableData = res.data.list;
                this.total = res.data.total;
            } else {
                this.$message({
                    showClose: true,
                    type: "error",
                    message: res.retMsg,
                });
            }
        },
        handleSizeChange (pageSize) {
            this.pageNum = 1;
            this.pageSize = pageSize;
            this.queryLogList();
        },
        handleCurrentChange (currentPage) {
            this.pageNum = currentPage;
            this.queryLogList();
        },
        parseTime (time) {
            return parseTimestamp(time);
        },
    },
};
</script>
<style lang="scss">
.vxe-table--tooltip-wrapper {
    z-index: 3000 !important;
}
</style>