<template>
  <div class="component-container">
    <div
      class="loading"
      v-loading="productLoading"
      v-show="productLoading"
    ></div>
    <approval-process
      :approvalData="approvalData"
      :processInfo="processInfo"
    ></approval-process>
    <el-row
      class="btns"
      v-if="showBtn"
      :style="{ top: scrollTop < 40 ? '40px' : '0px' }"
    >
      <el-col :span="24" class="text-rt">
        <el-button
          type="primary"
          v-if="processInfo.showHang && !processInfo.hangStatus"
          @click="hang()"
          >挂起</el-button
        >
        <el-button @click="review(true)">审核通过</el-button>
        <el-button @click="review(false)">审核不通过</el-button>
      </el-col>
    </el-row>
    <div class="table-contnt" v-show="!productLoading">
      <div class="table-title">主商品信息(合并商品)</div>
      <vxe-table
        border
        highlight-hover-row
        auto-resize
        resizable
        align="center"
        :data="mergeData"
        ref="table"
      >
        <vxe-table-column
          field="productCode"
          title="商品编码"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="productId"
          title="商品ID"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="spuCategory"
          title="商品大类"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="generalName"
          title="通用名"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="skuName"
          title="商品名"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="spec"
          title="规格/型号"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="approvalNo"
          title="批准文号/注册证号"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="manufacturer"
          title="生产厂家"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="spuCode"
          title="spu编码"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="dosageForm"
          title="剂型"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="attributeText"
          title="属性"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="skuCode"
          title="sku编码"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="packageUnit"
          title="包装单位"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="prescriptionCategory"
          title="处方分类"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="smallPackageCode"
          title="小包装条码"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="brand"
          title="品牌/商标"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="validityDate"
          title="有效期"
          min-width="120"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">
            <span v-if="row.validityUnit == 1">{{ row.validity + "日" }}</span>
            <span v-else-if="row.validityUnit == 2">{{
              row.validity + "月"
            }}</span>
            <span else>{{ row.validity + "年" }}</span>
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="delegationProduct"
          title="是否委托生产"
          min-width="120"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">{{
            row.delegationProduct ? "是" : "否"
          }}</template>
        </vxe-table-column>
        <vxe-table-column
          field="entrustedManufacturerName"
          title="受托生产厂家"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
      </vxe-table>
      <div class="table-title bTitle">副商品信息(被合并商品)</div>
      <vxe-table
        border
        highlight-hover-row
        auto-resize
        resizable
        align="center"
        :data="bMergeData"
        ref="table"
      >
        <vxe-table-column
          field="productCode"
          title="商品编码"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="productId"
          title="商品ID"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="spuCategory"
          title="商品大类"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="generalName"
          title="通用名"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="skuName"
          title="商品名"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="spec"
          title="规格/型号"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="approvalNo"
          title="批准文号/注册证号"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="manufacturer"
          title="生产厂家"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="spuCode"
          title="spu编码"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="dosageForm"
          title="剂型"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="attributeText"
          title="属性"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="skuCode"
          title="sku编码"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="packageUnit"
          title="包装单位"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="prescriptionCategory"
          title="处方分类"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="smallPackageCode"
          title="小包装条码"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="brand"
          title="品牌/商标"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="validityDate"
          title="有效期"
          min-width="120"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">
            <span v-if="row.validityUnit == 1">{{ row.validity + "日" }}</span>
            <span v-else-if="row.validityUnit == 2">{{
              row.validity + "月"
            }}</span>
            <span else>{{ row.validity + "年" }}</span>
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="delegationProduct"
          title="是否委托生产"
          min-width="120"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">{{
            row.delegationProduct ? "是" : "否"
          }}</template>
        </vxe-table-column>
        <vxe-table-column
          field="entrustedManufacturerName"
          title="受托生产厂家"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
      </vxe-table>
    </div>

    <!-- 审核弹框 -->
    <el-dialog
      title="审核"
      width="500px"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisible"
      :close-on-press-escape="false"
      :show-close="false"
      @close="dialogClose"
    >
      <el-form :model="reviewForm" :rules="rules" ref="reviewForm">
        <el-form-item label="审核状态">
          <el-input
            v-model="reviewForm.state"
            disabled
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="审核意见" prop="value">
          <el-input
            type="textarea"
            placeholder="超过100的长度，不能提交"
            v-model="reviewForm.value"
            autocomplete="off"
            maxlength="100"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="save()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  singleReview,
  mergeProductDetail,
  mergeDetail,
  review,
  hang,
} from "@/api/worksubstitution";

import approvalProcess from "@/views/product/approvalProcess";

export default {
  name: "",
  components: { approvalProcess },
  computed: {
    // 路由参数
    urlParam() {
      return this.$route.query;
    },
  },
  data() {
    return {
      approvalData: {},
      processInfo: {},
      mergeData: [],
      bMergeData: [],
      dialogFormVisible: false,
      reviewForm: {},
      showBtn: false,
    };
  },
  created() {
    this.init();
    this.showBtn = this.urlParam.detailType == "edit" ? true : false;
  },
  methods: {
    init() {
      this.productLoading = true;
      Promise.all([
        singleReview({
          id: this.urlParam.id,
        }),
        mergeProductDetail({
          applyCode: this.urlParam.applyCode,
          productCode: this.urlParam.productCode,
          productType: this.urlParam.productType,
        }),
        mergeDetail({
          applyCode: this.urlParam.applyCode,
        }),
      ]).then((res) => {
        this.productLoading = false;
        this.approvalData = res[1].data;
        this.processInfo = res[0].data;
        this.mergeData.push(res[2].data.chiefProduct);
        this.bMergeData = res[2].data.tmpSauList;
      });
    },
    review(flag) {
      this.dialogFormVisible = true;
      if (flag) {
        this.reviewForm.state = "审核通过";
      } else {
        this.reviewForm.state = "审核不通过";
      }
    },
    save() {
      review({
        id: this.processInfo.id,
        applyCode: this.processInfo.applyCode,
        applyUserScope: this.processInfo.applyUserScope,
        approvalProcess: this.processInfo.approvalProcess,
        rejectStatus: this.reviewForm.state == "审核通过" ? 1 : 0,
        reviewOpinion: this.reviewForm.value,
        reviewStatus: this.processInfo.reviewStatus,
      }).then((res) => {
        this.dialogFormVisible = false;
        if (res.retCode == 0) {
          this.$message({
            message: "操作成功！",
            type: "success",
          });
          if (this.processInfo.hangStatus) {
            parent.CreateTab("/api/worksubstitution/to/hang", "挂起事项", true);
          } else {
            parent.CreateTab(
              "../static/dist/index.html#/workbench/prepare",
              "待办事项",
              true
            );
          }
          parent.CloseTab(
            "../static/dist/index.html#/product/mergeProductDetail"
          );
        } else {
          this.$message.error(res.retMsg);
        }
      });
    },
    hang() {
      hang({
        hangStatus: 1,
        id: this.processInfo.id,
        reviewStatus: this.processInfo.reviewStatus,
      }).then((res) => {
        if (res.retCode == 0) {
          this.$message({
            message: "操作成功！",
            type: "success",
          });
          parent.CreateTab(
            "../static/dist/index.html#/workbench/prepare",
            "待办事项",
            true
          );
          parent.CloseTab(
            "../static/dist/index.html#/product/mergeProductDetail"
          );
        } else {
          this.$message.error(res.retMsg);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.component-container {
  padding-top: 40px;
  .loading {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
  }
  /deep/.el-tabs__header {
    border-bottom: none;
    background-color: #f2f2f2;
  }
  /deep/ .el-tabs__content {
    padding-top: 72px;
  }
  .el-tabs--border-card {
    border: none;
    box-shadow: none;
  }
  .btns {
    background: #fff;
    width: 100%;
    position: fixed;
    top: 40px;
    padding: 15px;
    z-index: 10;
  }
}
.table-contnt {
  padding: 0 20px;
  .table-title {
    padding: 10px 0 20px;
    font-size: 16px;
    font-weight: bold;
    &.bTitle {
      padding-top: 30px;
    }
  }
}
</style>