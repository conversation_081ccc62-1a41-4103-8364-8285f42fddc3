<template>
  <div class="container">
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="1000px"
      @close="handleClose"
      :destory-on-close="true"
    >
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="80px"
        class="ruleForm"
      >
        <el-form-item label="公告标题" prop="noticeTitle">
          <el-input maxlength="100" v-model="ruleForm.noticeTitle" clearable></el-input>
        </el-form-item>
        <el-form-item label="是否置顶" prop="toppingStatus">
          <el-radio-group v-model="ruleForm.toppingStatus">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item class="tinymce-wrap" label="" prop="noticeDetail">
          <div class="title">公告内容</div>
          <tinymce
            ref="tinymce"
            v-model="ruleForm.noticeDetail"
            :height="300"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="sure">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { addNotice, editNotice } from "@/api/workbench.js";
import Tinymce from "@/components/Tinymce";
import {
  dictListSearch,
  saveDict,
  getTotalDictionaryTree,
} from "@/api/dict.js";
export default {
  name: "",
  props: {},
  components: { Tinymce },
  data() {
    return {
      title: "新增公告",
      dialogVisible: false,
      content: "",
      ruleForm: {
        toppingStatus: 0,
      },
      rules: {
        noticeTitle: [{ required: true, message: "标题必填", trigger: "blur" }],
      },
    };
  },
  methods: {
    open({ title, obj }) {
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.title = title;
        if (obj) {
          this.ruleForm = obj;
          this.$refs.tinymce.setContent(obj.noticeDetail);
        }
      });
    },
    handleClose() {
      this.ruleForm = {
        toppingStatus: 0,
      };
      this.$refs.tinymce.setContent("");
    },
    sure() {
      this.$refs.ruleForm.validate((valid) => {
        if (!valid) return;
        if (!this.ruleForm.noticeDetail) {
          this.$message.warning("请输入公告内容");
          return;
        }
        if (this.title == "新增公告") {
          addNotice(this.ruleForm).then((res) => {
            if (!res.retCode) {
              this.$message.success("操作成功");
              this.$emit("refresh", true);
              this.dialogVisible = false;
            } else {
              this.$message({
                showClose: true,
                type: "error",
                message: res.retMsg,
              });
            }
          });
        } else {
          editNotice(this.ruleForm).then((res) => {
            if (!res.retCode) {
              this.$message.success("操作成功");
              this.$emit("refresh");
              this.dialogVisible = false;
            } else {
              this.$message({
                showClose: true,
                type: "error",
                message: res.retMsg,
              });
            }
          });
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.container /deep/ {
  .el-dialog__body {
    height: 300px;
    padding-bottom: 0;
    overflow-y: scroll;
  }
  .el-dialog__footer {
    padding-top: 10px;
  }

  .mnemonicCode-box {
    width: 100%;
    height: 40px;
    display: flex;
    .mnemonicCode-list {
      flex: 1;
      border: 1px solid #dcdfe6;
      display: flex;
      align-items: center;
      .box {
        display: flex;
        position: relative;
        .mnemonicCode-input {
          height: 30px;
          width: 60px;
          margin: 0 5px;
          display: flex;
          .el-input__inner {
            height: 100%;
          }
        }
        .el-icon-error {
          position: absolute;
          top: -2px;
          right: 0;
        }
      }
    }
    .el-icon-plus {
      height: 100%;
      width: 40px;
      background: #3b95a8;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
.ruleForm /deep/ {
  .el-select {
    width: 100%;
  }
  .el-form-item__label {
    text-align: left;
  }
}
.tinymce-wrap /deep/ {
  .el-form-item__content {
    margin-left: 0 !important;
  }
  .title {
    font-weight: bold;
  }
}
</style>