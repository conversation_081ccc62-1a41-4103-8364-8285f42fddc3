<template>
    <!-- 预览弹框 -->
    <el-dialog
      style="padding:0"
      class="search-form-wrap"
      fullscreen
      title="搜索友商库-图片下载记录"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisible"
      :close-on-press-escape="false"
    >
      <vxe-table
        height="90%"
        border
        highlight-hover-row
        resizable
        auto-resize
        size="small"
        align="center"
        :tooltip-config="{ enterable: false }"
        :data="tableData"
        :seq-config="{ startIndex: (searchFormData.pageNum - 1) * searchFormData.pageSize }"
        ref="previewTable"
      >
        <vxe-table-column
          type="seq"
          title="序号"
          width="60"
          show-header-overflow
          show-overflow
          fixed="left"
        ></vxe-table-column>
        <vxe-table-column
          title="申请时间"
          min-width="120"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">
            <span>{{ row.applyDate  | parseTimestamp }}</span>
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="applyer"
          title="申请人"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="applyTotalNum"
          title="图片数量"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="downloadStatusName"
          title="处理状态"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="downloadsCount"
          title="下载次数"
          min-width="120"
          show-header-overflow
          show-overflow
        ><template v-slot="{ row }">
            <span>
              <el-link
                :underline="false"
                :type="row.downloadFileUrl ? 'primary' : 'info'"
                :diabled="row.downloadFileUrl"
                @click.stop="downloadImg(row)"
                >{{ row.downloadsCount }}</el-link
              >
            </span>
          </template>
        </vxe-table-column>
      </vxe-table>

      <!-- 分页 -->
    <el-row class="custom-pagination-wrap">
        <el-col>
            <el-pagination
            background
            :current-page.sync="searchFormData.pageNum"
            :page-sizes="[20, 50, 100, 200]"
            :page-size.sync="searchFormData.pageSize"
            :total="total"
            layout="prev, pager, next, jumper,total, sizes"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            >
            </el-pagination>
        </el-col>
    </el-row>
    </el-dialog>
</template>

<script>
import { parseTimestamp } from '@/utils/index.js'
import { getDownloadImagesRecord, downloadStatistics } from '@/api/searchFriend'
import { createDownloadElement } from "@/utils/index.js";
export default {
  name: "",
  props: {},
  data() {
    return {
        dialogFormVisible:false,
        searchFormData: {
            pageNum:1,
            pageSize:20
        },
        tableData:[],
        total:0,
    };
  },
  watch: {},
  created() {

  },
  methods: {
    //   获取下载记录
    async getRecordData() {
        try {
            const res = await getDownloadImagesRecord(this.searchFormData)
            console.log(res);
            if (res.retCode === 0) {
                this.tableData = res.data.list
                this.total = res.data.total
            } else {
                this.$message.error(res.retMsg)
            }
        } catch (error) {
            console.log(error);
        }
    },
    openDlg() {
        this.dialogFormVisible = true
        this.tableData = []
        this.total = 0
        this.getRecordData()
    },
    /**
     * pageSize 改变事件
     */
    handleSizeChange(pageSize) {
      this.searchFormData.pageNum = 1;
      this.searchFormData.pageSize = pageSize;
      this.getRecordData();
    },

    /**
     * pageNum 改变事件
     */
    handleCurrentChange(currentPage) {
      this.searchFormData.pageNum = currentPage;
      this.getRecordData();
    },
    // 下载图片并统计
    async downloadImg(e) {
      if(e.downloadFileUrl) {
        createDownloadElement(e.downloadFileUrl)
        try {
          const res = await downloadStatistics({applyCode: e.applyCode})
          if (res.retCode !== 0) {
            this.$message.error(res.retMsg)
          } else{
            // 下载成功并统计后刷新表格数据
            this.getRecordData()
          }             
        } catch (error) {
          console.log(error);
        }
      }
    },
  }
};
</script>

<style lang="scss" scoped>
</style>