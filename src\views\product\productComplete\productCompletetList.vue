
<template>
  <div class="task-to-be-film-container">
    <!-- 查询表单 -->
    <div class="search-form-wrap">
      <el-form :model="model" ref="refSearchForm">
        <el-row type="flex">
          <!-- 商品大类 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="商品大类">
              <el-select
                v-model="formData.spuCategory"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" value></el-option>
                <el-option
                  v-for="item in spuCategoryList"
                  :key="item.id"
                  :label="item.dictName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 六级分类 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="六级分类">
              <el-cascader
                :options="categoryList"
                :props="{
                  checkStrictly: true,
                  value: 'id',
                  label: 'name',
                  lazy: true,
                  lazyLoad: lazyLoad,
                }"
                clearable
                v-model="category"
                @change="categoryChange"
              ></el-cascader>
            </el-form-item>
          </el-col>
          <!-- 完整度颗粒度 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="完整度颗粒度" label-width="120px">
              <el-select
                v-model="formData.wzdkld"
                placeholder="请选择"
                clearable
              >
                <el-option label="1%" value="1%"></el-option>
                <el-option label="2%" value="2%"></el-option>
                <el-option label="5%" value="5%"></el-option>
                <el-option label="10%" value="10%"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 按钮 -->
          <el-col :span="6" :offset="18">
            <el-button type="primary" size="medium" @click="btnSearchClick"
              >查询</el-button
            >
            <el-button size="medium" @click="btnResetClick">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="btn-wrap">
      <el-button size="medium" @click="handleExport">导出EXCEL</el-button>
    </div>
    <!-- table表单 -->
    <!-- table -->
    <div class="tab-wrap">
      <el-tabs v-model="tabName" type="card">
        <el-tab-pane label="商品完整度列表" name="first">
          <div class="table-wrap">
            <vxe-table
              border
              highlight-hover-row
              resizable
              height="100%"
              auto-resize
              size="small"
              align="center"
              :tooltip-config="{ enterable: false }"
              :data="tableData"
              :seq-config="{ startIndex: (pageNum - 1) * pageSize }"
              ref="refVxeTable"
              @cell-dblclick="cellDBLClickEvent"
            >
              <vxe-table-column
                type="seq"
                title="序号"
                width="60"
                show-header-overflow
                show-overflow
                fixed="left"
              ></vxe-table-column>
              <vxe-table-column
                field="spuCategoryName"
                title="商品大类"
                min-width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                field="integrity"
                title="商品完整度"
                min-width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                field="wzdkld"
                title="完整度颗粒度"
                min-width="120"
                show-header-overflow
                show-overflow
              >
              </vxe-table-column>
              <vxe-table-column
                field="num"
                title="包含商品数量"
                min-width="120"
                show-header-overflow
                show-overflow
              >
                <template v-slot="{ row }">
                  <el-link
                    :underline="false"
                    type="primary"
                    @click.stop="handleDeatilExport(row)"
                    >{{ row.num }}</el-link
                  >
                </template>
              </vxe-table-column>
            </vxe-table>
          </div>
          <!-- 分页 -->
          <!-- <el-row class="custom-pagination-wrap">
            <el-col>
              <el-pagination
                background
                :current-page.sync="pageNum"
                :page-sizes="[20, 50, 100, 200]"
                :page-size.sync="pageSize"
                :total="total"
                layout="prev, pager, next, jumper,total, sizes"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              >
              </el-pagination>
            </el-col>
          </el-row> -->
        </el-tab-pane>
        <el-tab-pane label="商品完整度统计" name="second">
          <div id="myChart" style="height: 400px"></div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <product-complete-detail ref="productCompleteDetail"></product-complete-detail>
  </div>
</template>

<script>
import { findDictList, categoryList } from "@/api/dict.js";
import { hasPermission } from "@/utils/index.js";
import productCompleteDetail from "./productCompleteDetail";
import {
  getProductCompleteList,
  exportProductCompleteList,
  exportProductCompleteDetail,
} from "@/api/product.js";

export default {
  name: "",
  components: { productCompleteDetail },
  filters: {},
  props: {},
  data() {
    return {
      pageNum: 1,
      pageSize: 20,
      total: 0, //总条数
      formData: {
        wzdkld: "5%",
      },
      tableData: [],
      echartsData: {},
      tabName: "first",
      categoryList: [],
      category: [],
      spuCategoryList: [],
      myChart: null,
    };
  },
  computed: {},
  watch: {
    tabName(val) {
      if (val == "second") {
        this.$nextTick(() => {
          if (!this.myChart) {
            this.drawLine();
          }
        });
      }
    },
  },
  created() {
    this.searchForm();
    this.getCategoryList();
    this.getFixCategoryList({
      isValid: 1,
      level: 1,
      parentId: "",
    });
  },
  mounted() {},
  methods: {
    // 获取商品大类
    getCategoryList() {
      findDictList({
        type: 7,
      }).then((res) => {
        this.spuCategoryList = res.data.list.filter((item) => item.id != 5);
      });
    },

    // 获取六级分类
    getFixCategoryList(data) {
      categoryList(data).then((res) => {
        if (!res.retCode) {
          this.categoryList = res.data;
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      });
    },

    // 搜索
    searchForm() {
      let param = Object.assign({}, this.formData);
      param.page = this.pageNum;
      param.limit = this.pageSize;
      this.getProductCompleteList();
    },

    getProductCompleteList() {
      let param = Object.assign(
        {
          page: this.pageNum,
          limit: this.pageSize,
        },
        this.formData
      );
      let loading = this.$loading({
        lock: true,
        text: "加速获取数据中...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      getProductCompleteList(param).then((res) => {
        if (!res.retCode) {
          this.tableData = res.data.filter((item) => item.num > 0);
          this.filterEchartData(res.data);
          if (this.myChart) {
            this.myChart.clear();
            this.drawLine();
          }
          loading.close();
        } else {
          this.$message.error(res.retMsg);
        }
      });
    },

    filterEchartData(data) {
      let obj = { legendData: [], xAxisData: [], seriesData: {} };
      data.forEach((item) => {
        if (obj.legendData.indexOf(item.spuCategoryName) == -1) {
          obj.legendData.push(item.spuCategoryName);
        }
        if (obj.xAxisData.indexOf(item.integrity) == -1) {
          obj.xAxisData.push(item.integrity);
        }
        if (!obj.seriesData[item.spuCategoryName]) {
          obj.seriesData[item.spuCategoryName] = [];
        }
        obj.seriesData[item.spuCategoryName].push(item.num);
      });
      this.echartsData = obj;
    },

    // 动态加载六级分类
    lazyLoad(node, resolve) {
      categoryList({
        isValid: 1,
        level: node.level + 1,
        parentId: node.value,
      }).then((res) => {
        if (!res.retCode) {
          if (node.level == 5) {
            res.data.forEach((item) => {
              item.leaf = 6;
            });
          }
          resolve(res.data);
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      });
    },

    // 六级分类改变
    categoryChange(e) {
      this.formData.firstCategory = e[0] ? e[0] : "";
      this.formData.secondCategory = e[1] ? e[1] : "";
      this.formData.thirdCategory = e[2] ? e[2] : "";
      this.formData.fourthCategory = e[3] ? e[3] : "";
      this.formData.fiveCategory = e[4] ? e[4] : "";
      this.formData.sixCategory = e[5] ? e[5] : "";
    },

    drawLine() {
      // 基于准备好的dom，初始化echarts实例
      this.myChart = this.$echarts.init(document.getElementById("myChart"));
      window.addEventListener("resize", () => {
        this.myChart.resize();
      });
      this.setOption();
    },
    filterSeriesData(data) {
      let arr = [];
      Object.keys(data).forEach((item) => {
        arr.push({
          name: item,
          type: "bar",
          stack: "数量",
          data: data[item],
        });
      });
      return arr;
    },
    setOption() {
      let seriesData = this.filterSeriesData(this.echartsData.seriesData);
      // 绘制图表
      this.myChart.setOption({
        tooltip: {
          trigger: "axis",
          formatter: function (params, ticket, callback) {
            let res = `<div><span style="font-weight: bold;color: #333;">完整度：</span>${params[0].axisValue}</div>`;
            params.forEach((item) => {
              res += `<div><div><span style="font-weight: bold;color: ${item.color};">${item.seriesName}：</span>${item.value} </div></div>`;
            });
            return res;
          },
        },
        legend: {
          data: this.echartsData.legendData,
        },
        xAxis: {
          name: "完整度",
          type: "category",
          data: this.echartsData.xAxisData,
        },
        yAxis: {
          name: "数量",
          type: "value",
        },
        series: seriesData,
      });
    },

    handleExport() {
      let param = Object.assign({}, this.formData);
      exportProductCompleteList(param).then((res) => {
        if (!res.retCode) {
          this.$message.success(res.retMsg);
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      });
    },

    handleDeatilExport(row) {
      let param = Object.assign({}, this.formData);
      param.min = row.min;
      param.max = row.max;
      param.spuCategory = row.spuCategory;
      exportProductCompleteDetail(param).then((res) => {
        if (!res.retCode) {
          this.$message.success(res.retMsg);
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      });
    },

    cellDBLClickEvent({ row }) {
      let param = Object.assign({}, this.formData);
      param.min = row.min;
      param.max = row.max;
      param.spuCategory = row.spuCategory;
      this.$refs.productCompleteDetail.show(param);
    },

    /**
     * 查询按钮点击
     */
    btnSearchClick() {
      this.pageNum = 1;
      this.searchForm();
    },

    /**
     * 重置
     */
    btnResetClick() {
      this.formData = {
        spuCategory: "",
        wzdkld: "5%",
      };
      this.category = [];
      this.pageNum = 1;
      this.searchForm();
    },
    /**
     * pageSize 改变事件
     */
    handleSizeChange(pageSize) {
      this.pageNum = 1;
      this.pageSize = pageSize;
      this.searchForm();
    },

    /**
     * pageNum 改变事件
     */
    handleCurrentChange(currentPage) {
      this.pageNum = currentPage;
      this.searchForm();
    },
    /**
     * @description: 格式化时间参数
     * @param {date} time 时间戳
     * @return: string 格式化后的时间戳
     */
    hasPermission(str) {
      return hasPermission(str);
    },
  },
};
</script>
<style lang="scss" scoped>
.task-to-be-film-container {
  /**
   * 查询表单
   */
  .search-form-wrap {
    width: 100%;
    padding: 15px;
    border-bottom: 1px dashed #e4e4eb;

    .el-row {
      flex-wrap: wrap;

      .el-col {
        display: flex;
        justify-content: flex-end;

        .el-form-item {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          width: 100%;
          margin-right: 0;
          margin-bottom: 15px;

          /deep/ {
            .el-form-item__label {
              width: 96px;
              line-height: normal;
              padding: 0 12px;
              color: #292933;
              font-weight: normal;
            }

            .el-form-item__content {
              flex: 1;
              margin-left: 0 !important;
              .el-range-editor.el-input__inner {
                width: 100%;

                .el-range-separator {
                  width: 14px;
                  padding: 0;
                }
              }
            }
          }

          .el-input {
            width: 100%;
          }

          .el-select {
            width: 100%;
          }

          .el-cascader {
            width: 100%;
          }
        }
      }
    }
  }

  .tab-wrap {
    padding: 0 15px;
    margin-top: 15px;
  }
  .table-wrap {
    width: 100%;
    min-height: 440px;
    height: calc(100vh - 295px);
    margin-bottom: 20px;
  }
}
.btn-wrap {
  padding: 15px 0 0 10px;
}
.radio-wrap {
  .title {
    padding-right: 20px;
  }
}
.active {
  color: #3b95a8;
}
</style>
