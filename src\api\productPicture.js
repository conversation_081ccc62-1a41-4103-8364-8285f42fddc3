import request from '@/utils/request'
import qs from "qs"
/**
 * 查询所属机构
 */
export function getMechanismList(data) {
  return request({
    url: '/api/picture/get/mechanismList',
    method: 'post',
    data: qs.stringify(data)
  })
}

/** 
 * author:caoshuwen
 * date: 2021-05-24
 * description:待拍摄商品列表查询
 * **/
export function productShootListFind(data) {
  return request({
    url: '/api/productPictureShoot/find/list',
    method: 'post',
    data
  })
}

/** 
 * author:caoshuwen
 * date: 2021-05-24
 * description:待拍摄商品列表导出
 * **/
export function productShootListExport(data) {
  return request({
    url: '/api/productPictureShoot/export/list',
    method: 'post',
    data
  })
}

/**
 * 新增、操作拍摄任务
 */
export function applyOperate(data) {
  return request({
    url: '/api/picture/applyOperate',
    method: 'post',
    data
  })
}


/**
 * 待拍摄列表单据查询
 */
export function getproductPictureShootList(data) {
  return request({
    url: '/api/productPictureShoot/page/list',
    method: 'post',
    data
  })
}

/** 
 * author:caoshuwen
 * date: 2021-06-24
 * description:拍摄单据报表查询
 * **/
export function getPagePictureTask(data) {
  return request({
    url: '/api/picture/task/pagePictureTask',
    method: 'post',
    data
  })
}

/** 
 * author:caoshuwen
 * date: 2021-06-24
 * description:拍摄单据报表导出
 * **/
export function exportPictureTask(data) {
  return request({
    url: '/api/picture/task/exportPictureTask',
    method: 'post',
    data
  })
}

/**
 * 拍摄单据列表查询
 */
export function productShootTaskListFind(data) {
  return request({
    url: '/api/picture/find/productShootTaskList',
    method: 'post',
    data
  })
}

/**
 * 
 * 拍摄单据列表导出
 */
export function productShootTaskListExport(data) {
  return request({
    url: '/api/picture/export/productShootTaskList',
    method: 'post',
    needResponseHeader: true,
    data
  });
}

/**
 * 任务单商品明细列表查询
 */
export function productShootTaskDetailListFind(data) {
  return request({
    url: '/api/picture/find/productShootTaskDetailList',
    method: 'post',
    data
  })
}

/**
 * 任务单商品明细列表导出
 */
export function productShootTaskDetailListExport(data) {
  return request({
    url: '/api/picture/export/productShootTaskDetailList',
    method: 'post',
    needResponseHeader: true,
    data
  })
}

/**
 *  待拍摄任务明细页
 */

// 明细页-商品 数据
export function getProductDetail(data) {
  /**
   * @description: 
   * @param {type} 
   * @return: 
   */

  return request({
    url: '/api/picture/find/productShootTaskDetailList',
    method: 'post',
    data: data
  }).then(res => res.data.list)
}

// 明细页-商品 提交
export function submitDetailProduct(data) {
  return request({
    url: '/api/picture/applyOperate',
    method: 'post',
    data
  })
}

// 补充图片 - 原图
export function supplementPicture(data) {
  return request({
    url: '/api/picture/add/uploadOriginalPicture',
    method: 'post',
    data
  })
}

// 绑定到本机构
export function bindOrgan(applyCode, productCode, pictureDetailVersion) {
  return request({
    url: '/api/picture/bindDetailVersion',
    method: 'post',
    data: {
      applyCode,   // 单据编号
      productCode, // 商品编码
      pictureDetailVersion // 要绑定的精修图版本
    }
  })
}

// 删除图片 - 原图
export function deletePicture(applyCode, productCode, pictureId) {
  return request({
    url: '/api/picture/delete/deleteOriginalPicture',
    method: 'post',
    data: {
      applyCode,    // 单据编号
      productCode,  // 商品编码
      pictureId  // 图片id
    }
  })
}

// 审核商品明细 - 通过、不通过
export function auditProductDetail(data) {
  return request({
    url: '/api/picture/auditProductShootTaskDetails',
    method: 'post',
    data
  })
}

/**
 * 改名下载文件
 */
export function fileDownLoad(filePath, fileName) {
  return request({
    url: `/api/base/download?filePath=${filePath}&fileName=${fileName}`,
    responseType: 'blob',
    method: 'get'
  })
}




// 1.5.1 
// 领取
export function goToReceive({ operation, applyCode }) {
  return request({
    url: '/api/picture/applyOperate',
    method: 'post',
    data: {
      operation,
      applyCode	//	操作类型（- 1新建任务单, 0上传原图, 5领取精修图, 1下载原图, 2上传精修图, 3领取, 4提交审核）	是
    }
  })
}

// 预览原图
export function getOriginalProduct(applyCode) {
  return request({
    url: '/api/picture/find/previewPictureList',
    method: 'post',
    data: qs.stringify({ applyCode })
  }).then(res => res.data)
}

// 驳回原图 
export function postRejectOriginal(data) {
  return request({
    url: '/api/picture/rejectOriginalPicture',
    method: 'post',
    data
  })
}

// 设计通过原图 
export function approvedOriginalPicture(data) {
  return request({
    url: 'api/picture/approvedOriginalPicture',
    method: 'post',
    data
  })
}

// 审核精修图
export function postReviewPictures(data) {
  return request({
    url: '/api/picture/rejectDetailPicture',
    method: 'post',
    data
  })
}
// 另选商品
export function taskDetailChangeProduct(data) {
  return request({
    url: '/api/picture/taskDetailChangeProduct',
    method: 'post',
    data
  })
}

// 图片回收记录查询
export function picRecyclingRecordList(data) {
  return request({
    url: '/api/picture/find/recycleBinTaskList',
    method: 'post',
    data
  })
}

// 图片回收记录 导出excel
export function picRecyclingRecordExport(data) {
  return request({
    url: '/api/picture/export/recycleBinTaskList',
    method: 'post',
    needResponseHeader: true,
    data
  })
}

// 库外待精商品列表修创建任务单
export function createTaskByRecord(data) {
  return request({
    url: '/api/picture/createTaskByRecord',
    method: 'post',
    // headers: {'content-Type': 'application/json;charset=UTF-8'},
    // data:qs.stringify(data),
    data
  })
}

// 库外待精修任务列表清除领取 {"applyCodeList":["AS303","",""]}
export function clearRecipientUser(data) {
  return request({
    url: '/api/picture/clearRecipientUser',
    method: 'post',
    // headers: {'content-Type': 'application/json;charset=UTF-8'},
    // data:qs.stringify(data),
    data
  })
}
// 库外已申请商品列表处理接口-未找到网图时使用
export function imageUploadShootRecord(data) {
  return request({
    url: '/api/picture/imageUploadShootRecordNoFile',
    method: 'post',
    // headers: {'content-Type': 'application/json;charset=UTF-8'},
    data: qs.stringify(data),
    // data
  })
}


//销售属性图片管理排序
export function sortPicture(data) {
  return request({
    url: '/api/picture/sortPicture',
    method: 'post',
    // headers: {'content-Type': 'application/json;charset=UTF-8'},
    data
    // data
  })
}

// 自营已申请商品列表

// 自营已申请商品列表获取待领取任务数量
export function getProductSupplementPictureCount(data) {
  return request({
    url: '/api/productShootRecord/page/listCount',
    method: 'post',
    data
  })
}

/** 
 * author:caoshuwen
 * date: 2021-05-07
 * description:待精修任务总数量
 * **/
export function getRetouchTaskNotReceivedCount(data) {
  return request({
    url: '/api/picture/retouch/getRetouchTaskNotReceivedCount',
    method: 'post',
    data
  })
}

/** 
 * author:caoshuwen
 * date: 2021-05-08
 * description:领取图片精修任务
 * **/
export function receiveRetouchTask(data) {
  return request({
    url: '/api/picture/retouch/receiveRetouchTask',
    method: 'post',
    data
  })
}

/** 
 * author:caoshuwen
 * date: 2021-05-08
 * description:查询已领取的图片精修任务列表
 * **/
export function pageRetouchTaskReceived(data) {
  return request({
    url: '/api/picture/retouch/pageRetouchTaskReceived',
    method: 'post',
    data
  })
}

/** 
 * author:caoshuwen
 * date: 2021-05-10
 * description:查询需线下精修的图片列表
 * **/
export function pageRetouchTaskOfflinePicture(data) {
  return request({
    url: '/api/picture/retouch/pageRetouchTaskOfflinePicture',
    method: 'post',
    data
  })
}

/** 
 * author:caoshuwen
 * date: 2021-05-11
 * description:一键抠图
 * **/
export function digPhotoApi(data) {
  return request({
    url: '/api/picture/retouch/getRetouchTaskOneClickMatting',
    method: 'post',
    data
  })
}

/** 
 * author:caoshuwen
 * date: 2021-05-11
 * description:审核图片精修任务
 * **/
export function auditRetouchTaskReceived(data) {
  return request({
    url: '/api/picture/retouch/auditRetouchTaskReceived',
    method: 'post',
    data
  })
}

/** 
 * author:caoshuwen
 * date: 2021-05-10
 * description:批量导入精修图
 * **/
export function uploadRetouchTaskOfflineRetouchPicture(data) {
  return request({
    url: '/api/picture/retouch/uploadRetouchTaskOfflineRetouchPicture',
    method: 'post',
    data
  })
}

/** 
 * author:caoshuwen
 * date: 2021-05-10
 * description:待精修批量下载图图片
 * **/
export function downloadRetouchTaskOfflinePicture(ids) {
  return request({
    url: `/api/picture/retouch/downloadRetouchTaskOfflinePicture?ids=${ids}`,
    method: 'get',
    responseType: 'blob'
  })
}

/** 
 * author:caoshuwen
 * date: 2021-05-10
 * description:查询单个已领取的图片精修任务详情
 * **/
export function getRetouchTaskReceivedDetail(ids) {
  return request({
    url: `/api/picture/retouch/getRetouchTaskReceivedDetail/${ids}`,
    method: 'get'
  })
}

// 非自营已申请商品列表获取待领取任务数量
export function getProductPictureComplementCount(data) {
  return request({
    url: '/api/productShootRecord/page/productPictureComplementListCount',
    method: 'post',
    data
  })
}

// 自营已申请商品列表批量领取任务
export function receivePictureZiYing(data) {
  return request({
    url: '/api/productShootRecord/submit/batchReceivePictureZiYing',
    method: 'post',
    data
  })
}

// 非自营已申请商品列表批量领取任务
export function receivePictureFeiZiYing(data) {
  return request({
    url: '/api/productShootRecord/submit/batchReceivePictureFeiZiYing',
    method: 'post',
    data
  })
}

// 自营已申请商品列表获取我的任务列表
export function getProductSupplementPictureList(data) {
  return request({
    url: '/api/productShootRecord/page/myTaskList',
    method: 'post',
    data
  })
}

// 已申请列表获取数据详情
export function getProductSupplementPictureDetail(id) {
  return request({
    url: `/api/productShootRecord/get/detail/${id}`,
    method: 'get'
  })
}

// 已申请商品列表审核图片
export function productSupplementPictureReview(data) {
  return request({
    url: '/api/productShootRecord/audit/picture',
    method: 'post',
    data
  })
}

// 已申请商品列表创建拍摄任务
export function productSupplementPictureShootTask(data) {
  return request({
    url: '/api/productShootRecord/create/shootTask',
    method: 'post',
    data
  })
}

// 已申请商品列表创建精修任务
export function productSupplementPictureDetailTask(data) {
  return request({
    url: '/api/productShootRecord/create/detailTask',
    method: 'post',
    data
  })
}

// 已申请商品列表领取任务
export function productSupplementPictureGetTask(data) {
  return request({
    url: '/api/productShootRecord/receive/picture',
    method: 'post',
    data: qs.stringify(data)
  })
}

// 已申请商品处理爬虫来源获取备选图
export function getAlternativePicture(data) {
  return request({
    url: '/api/productShootRecord/get/alternativePicture',
    method: 'post',
    data: data
  })
}

/** 
 * author:caoshuwen
 * date: 2021-05-26
 * description:获取待领取图片预审核任务数量
 * **/
export function getOriginalTaskCount(data) {
  return request({
    url: '/api/picture/original/getOriginalTaskNotReceivedCount',
    method: 'post',
    data
  })
}

/** 
 * author:caoshuwen
 * date: 2021-05-26
 * description:领取图片预审核任务
 * **/
export function receiveOriginalTask(data) {
  return request({
    url: '/api/picture/original/receiveOriginalTask',
    method: 'post',
    data
  })
}

/** 
 * author:caoshuwen
 * date: 2021-05-28
 * description:查询已领取的图片预审核任务列表
 * **/
export function getAuditTaskList(data) {
  return request({
    url: '/api/picture/original/pageOriginalTaskReceived',
    method: 'post',
    data
  })
}

/** 
 * author:caoshuwen
 * date: 2021-06-01
 * description:查询单个已领取的图片预审核任务详情
 * **/
export function getOriginalTaskReceivedDetail(data) {
  return request({
    url: `/api/picture/original/getOriginalTaskReceivedDetail/${data}`,
    method: 'get'
  })
}

/** 
 * author:caoshuwen
 * date: 2021-06-01
 * description:审核图片预审核任务
 * **/
export function auditTaskReceived(data) {
  return request({
    url: '/api/picture/original/auditOriginalTaskReceived',
    method: 'post',
    data
  })
}

/** 
 * author:shilang
 * date: 2021-07-20
 * description:友商库图片补全申请记录导出
 * **/
 export function friendLibraryProductShootRecordList(data) {
  return request({
    url: '/api/productShootRecord/export/friendLibraryProductShootRecordList',
    method: 'post',
    data
  })
}

/** 
 * author:caoshuwen
 * date: 2021-10-20
 * description:友商库图片补全申请记录导出
 * **/
export function getRelationalTask(params) {
  return request({
    url: '/api/picture/task/getRelationalTask',
    method: 'post',
    params
  })
}

/** 
 * author:caoshuwen
 * date: 2022-03-09
 * description:线上精修明细列表
 * **/
export function getOnlineEditList(data) {
  return request({
    url: 'api/picture/retouch/pageRetouchTaskHistory',
    method: 'post',
    data
  })
}
/** 
 * author:caoshuwen
 * date: 2024-09-24
 * description:提交商品图片
 * **/
export function pictureRetouch(data) {
  return request({
    url: '/api/productShootRecord/generate/pictureRetouch',
    method: 'post',
    data
  })
}