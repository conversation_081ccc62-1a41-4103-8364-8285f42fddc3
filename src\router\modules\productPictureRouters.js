import Iframe from "@/iframe";
const productPictureRouters = {
  path: "/productPicture",
  name: "productPicture",
  // component: Layout,
  component: Iframe,
  redirect: "noRedirect",
  meta: { title: "商品图片", icon: "nested" },
  children: [
    {
      path: "selfApplyProductList",
      name: "selfApplyProductList",
      component: () => import("@/views/productPicture/selfSupport/index"),
      meta: { title: "已申请商品列表" }
    },
    {
      path: "applyProductDispose",
      name: "applyProductDispose",
      component: () => import("@/views/productPicture/selfSupport/applyProductDispose"),
      meta: { title: "已申请商品图片处理"}
    },
    {
      path: "dealPhoto",
      name: "dealPhoto",
      component: () => import("@/views/productPicture/components/dealPhoto"),
      meta: { title: "待精修任务列表处理"}
    },
    {
      path: "productToBeFilm",
      name: "productToBeFilm",
      component: () => import("@/views/productPicture/productToBeFilm"),
      meta: { title: "待拍摄商品" }
    },
    // {
    //   path: "taskToBeFilm",
    //   name: "taskToBeFilm",
    //   component: () => import("@/views/productPicture/taskToBeFilm"),
    //   meta: { title: "待拍摄任务列表" }
    // },
    {
      path: "taskToBeFilmDetail",
      name: "taskToBeFilmDetail",
      component: () => import("@/views/productPicture/taskToBeFilmDetail"),
      meta: { title: "待拍摄任务明细" }
    },
    {
      path: "taskToBeRefined",
      name: "taskToBeRefined",
      component: () => import("@/views/productPicture/taskToBeRefined"),
      meta: { title: "待精修任务列表" }
    },
    {
      path: "taskToBeAudit",
      name: "taskToBeAudit",
      component: () => import("@/views/productPicture/taskToBeAudit"),
      meta: { title: "待审核任务列表" }
    },
    {
      path: "taskToBeAuditDetail",
      name: "taskToBeAuditDetail",
      component: () => import('@/views/productPicture/taskToBeAuditDetail'),
      meta: { title: "待审核任务明细" }
    },
    {
      path: "reportFilmTask",
      name: "reportFilmTask",
      component: () => import('@/views/productPicture/reportFilmTask'),
      meta: { title: "拍摄任务单报表" }
    },
    {
      path: "reportFilmTaskDetail",
      name: "reportFilmTaskDetail",
      component: () => import('@/views/productPicture/reportFilmTaskDetail'),
      meta: { title: "拍摄任务单报表明细" }
    },
    {
      path: "reportRecycleBin",
      name: "reportRecycleBin",
      component: () => import('@/views/productPicture/recyclingRecordList'),
      meta: { title: "图片回收记录" }
    }
  ]
};

export default productPictureRouters;
