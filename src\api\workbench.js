import request from '@/utils/request'
import qs from "qs" // 请求数据 转成form data 形式

// 公告列表
export function getNoticeList(data) {
    return request({
        url: '/api/worksubstitution/find/noticeList',
        method: 'post',
        data,
    })
}
// 新增公告
export function addNotice(data) {
    return request({
        url: '/api/worksubstitution/add/notice',
        method: 'post',
        data,
    })
}
// 编辑公告
export function editNotice(data) {
    return request({
        url: '/api/worksubstitution/modify/notice',
        method: 'post',
        data,
    })
}
// 公告详情
export function noticeDetail(data) {
    return request({
        url: '/api/worksubstitution/find/noticeDetail',
        method: 'post',
        data,
    })
}
// 公告置顶
export function noticeTop(data) {
    return request({
        url: '/api/worksubstitution/modify/noticeTop',
        method: 'post',
        data,
    })
}
// 删除公告
export function deleteNotice(data) {
    return request({
        url: '/api/worksubstitution/delete/notice',
        method: 'post',
        data,
    })
}
// 新增浏览量
export function noticeViews(data) {
    return request({
        url: '/api/worksubstitution/modify/noticeViews',
        method: 'post',
        data,
    })
}

// 查询待办事项列表
export function prepareList(data) {
    return request({
        url: '/api/worksubstitution/find/receivedList',
        data,
        method: 'POST'
    })
}

// 待办事项领取
export function worksubstitutionReceive(data) {
    return request({
        url: `/api/worksubstitution/receive`,
        data,
        method: 'POST'
    })
}

// 待办事项查询待领取任务数量
export function getUserClaimRecordCount(data) {
    return request({
        url: '/api/worksubstitution/find/userClaimRecordList',
        data,
        method: 'POST'
    })
}

// 待办事项批量领取任务
export function batchClaimReview(data) {
    return request({
        url: '/api/worksubstitution/submit/batchClaimReview',
        data,
        method: 'POST'
    })
}

// 待办事项获取审批流程
export function getUserApprovalProcessList() {
    return request({
        url: '/api/worksubstitution/find/userApprovalProcessList',
        method: 'GET'
    })
}

// 待办事项获取机构
export function getUserDptNameList() {
    return request({
        url: '/api/picture/get/dptNameList',
        method: 'GET'
    })
}

export function findProductCorrectionByApplyCode(data) {
    return request({
        url: `/api/correction/findProductCorrectionByApplyCode`,
        data: qs.stringify(data),
        method: 'POST'
    })
}

// 查看excel获取文件流  
export function productExcelCommonPreview(data) {
    return request({
        url: `/api/preview/productExcelCommonPreview`,
        data: qs.stringify(data),
        responseType: 'blob',
        method: 'POST',
    })
}

// 任务看板获取来源
export function getAllSource(params) {
    return request({
        url: '/api/goodsReviewReport/find/getAllSource',
        params,
        method: 'GET',
    })
}

// 任务看板获取任务类型
export function getAllApprovalProcess(params) {
    return request({
        url: '/api/goodsReviewReport/find/getAllApprovalProcess',
        params,
        method: 'GET',
    })
}

// 任务看板获取所有审核结果
export function getAllTaskAuditState(params) {
    return request({
        url: '/api/goodsReviewReport/find/getAllAuditState',
        params,
        method: 'GET',
    })
}

// 图片看板获取所有图片任务来源
export function getAllPictureSource(params) {
    return request({
        url: '/api/productPictureBord/find/getAllPictureSource',
        params,
        method: 'GET',
    })
}

// 图片看板获取所有图片任务类型
export function getAllPictureProcess(params) {
    return request({
        url: '/api/productPictureBord/find/getAllPictureProcess',
        params,
        method: 'GET',
    })
}


// 任务看板获取任务列表
export function getTaskBoardList(data) {
    return request({
        url: '/api/goodsReviewReport/find/pageList',
        data,
        method: 'POST',
    })
}

// 任务看板导出列表
export function exportTaskBoardList(data) {
    return request({
        url: '/api/goodsReviewReport/find/exportInfo',
        data,
        method: 'post',
    })
}

// 任务看板用户列表
export function getTaskBoardUsers(params) {
    return request({
        url: '/api/goodsReviewReport/find/getPostUsers',
        params,
        method: 'GET',
    })
}

// 图片看板获取任务列表
export function getPictureBoardList(data) {
    return request({
        url: '/api/productPictureBord/find/pageList',
        data,
        method: 'POST',
    })
}

// 图片看板导出列表
export function exportPictureBoardList(data) {
    return request({
        url: '/api/productPictureBord/find/exportInfo',
        data,
        method: 'post',
    })
}

// 图片看板获取所有审核结果
export function getAllPictureAuditState(params) {
    return request({
        url: '/api/productPictureBord/find/getAllAuditState',
        params,
        method: 'GET',
    })
}
