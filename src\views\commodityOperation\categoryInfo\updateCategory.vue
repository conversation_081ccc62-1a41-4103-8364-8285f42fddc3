<template>
  <el-dialog
    class="wrapper"
    title="修改商品品类信息"
    width="80%"
    :visible.sync="visible"
    :close-on-click-modal="false"
  >
    <div class="content">
      <el-form class="form" label-width="120px" inline disabled>
        <el-form-item label="商品ID">
          <el-input v-model="form.productId"></el-input>
        </el-form-item>
        <el-form-item label="商品编码">
          <el-input v-model="form.skuCode"></el-input>
        </el-form-item>
        <br />
        <el-form-item label="通用名">
          <el-input v-model="form.generalName"></el-input>
        </el-form-item>
        <el-form-item label="规格/型号">
          <el-input v-model="form.spec"></el-input>
        </el-form-item>
        <el-form-item label="生产厂家">
          <el-input v-model="form.manufacturerName"></el-input>
        </el-form-item>
        <el-form-item label="上市许可持有人">
          <el-input v-model="form.marketAuthor"></el-input>
        </el-form-item>
        <el-form-item label="厂家分类">
          <el-input v-model="form.manufacturerCategoryName"></el-input>
        </el-form-item>
        <el-form-item label="小包装条码">
          <el-input v-model="form.smallPackageCode"></el-input>
        </el-form-item>
        <el-form-item label="批准文号">
          <el-input v-model="form.approvalNo"></el-input>
        </el-form-item>
        <el-form-item label="包装单位">
          <el-input v-model="form.packageUnitName"></el-input>
        </el-form-item>
        <el-form-item label="剂型">
          <el-input v-model="form.dosageFormName"></el-input>
        </el-form-item>
        <el-form-item label="六级分类">
          <el-input v-model="form.firstToSixthClassifyName"></el-input>
        </el-form-item>
        <el-form-item label="品牌/商标">
          <el-input v-model="form.brand"></el-input>
        </el-form-item>
      </el-form>
      <el-divider></el-divider>
      <el-form
        class="form"
        label-width="120px"
        ref="form"
        inline
        :rules="rules"
        :model="form"
      >
        <el-form-item label="品牌分类">
          <el-select v-model="form.brandCategory" style="width: 185px">
            <el-option
              v-for="item in brandCategoryList"
              :key="item.id"
              :label="item.dictName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="慢病品种">
          <el-radio-group
            v-model="form.chronicDiseasesVariety"
            style="width: 185px"
          >
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="一致性评价品种">
          <el-radio-group
            v-model="form.conEvaluateVariety"
            style="width: 185px"
          >
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="医院品种">
          <el-radio-group v-model="form.hospitalVariety" style="width: 185px">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="全国4+7目录">
          <el-radio-group
            v-model="form.fourPlusSevenSelectedListStatus"
            style="width: 185px"
          >
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="4+7公布时间">
          <el-date-picker
            v-model="form.fourPlusSevenPublicationTime"
            type="month"
            placeholder="选择日期"
            :picker-options="pickerOptions"
            value-format="yyyyMM"
            style="width: 185px"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="4+7中选价格" prop="fourPlusSevenPrice">
          <el-input v-model="form.fourPlusSevenPrice"></el-input>
        </el-form-item>
        <el-form-item label="4+7中标供应省区">
          <el-select
            multiple
            v-model="form.fourPlusSevenProvinces"
            filterable
            style="width: 185px"
          >
            <el-option
              v-for="item in provinces"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer" class="footer">
      <el-button type="primary" @click="submit">确定</el-button>
      <el-button @click="visible = false">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import api from "@/api/commodityOperation";
import { filterEmptyField } from "../helper";

const initFormData = () => ({
  productId: "",
  skuCode: "",
  generalName: "",
  spec: "",
  manufacturerName: "",
  marketAuthor: "",
  manufacturerCategoryName: "",
  smallPackageCode: "",
  approvalNo: "",
  packageUnitName: "",
  dosageFormName: "",
  firstToSixthClassifyName: "",
  brand: "",
  brandCategory: "",
  chronicDiseasesVariety: "",
  conEvaluateVariety: "",
  hospitalVariety: "",
  fourPlusSevenSelectedListStatus: "",
  fourPlusSevenPublicationTime: "",
  fourPlusSevenPrice: "",
  fourPlusSevenProvinces: ""
});

export default {
  name: "UpdateCategory",
  props: {
    brandCategoryList: {
      type: Array,
      default() {
        return [];
      }
    },
    provinces: {
      type: Array,
      default() {
        return [];
      }
    }
  },
  data() {
    return {
      visible: false,
      form: initFormData(),
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < new Date("2018").getTime();
        }
      },
      rules: {
        fourPlusSevenPrice: [
          { required: true, message: "请输入", trigger: "blur" },
          {
            pattern: /^\d+(\.)?\d{1,3}$/,
            message: "请输入纯数字，支持小数点后三位",
            trigger: "blur"
          }
        ]
      }
    };
  },
  methods: {
    show(originData) {
      const data = JSON.parse(JSON.stringify(originData));
      const brandCategory = this.brandCategoryList.find(
        val => val.dictName == data.brandCategoryName
      );
      const radioCodeMap = {
        是: 1,
        否: 0,
        无: -1,
        "": ""
      };
      // 把后端的字段name值转为对应的code码
      data.brandCategory = brandCategory ? brandCategory.id : "";
      data.chronicDiseasesVariety =
        radioCodeMap[data.chronicDiseasesVarietyName];
      data.conEvaluateVariety = radioCodeMap[data.conEvaluateVarietyName];
      data.hospitalVariety = radioCodeMap[data.hospitalVarietyName];
      data.fourPlusSevenSelectedListStatus =
        radioCodeMap[data.fourPlusSevenSelectedListStatusName];
      this.form = {
        ...initFormData(),
        ...data
      };
      this.visible = true;
    },
    async submit() {
      const valid = await new Promise(resolve => {
        this.$refs.form.validate(valid => {
          resolve(valid);
        });
      });
      if (!valid) {
        return;
      }
      const form = this.form;
      const data = {
        skuCode: form.skuCode,
        brandCategory: form.brandCategory,
        fourPlusSevenSelectedListStatus: form.fourPlusSevenSelectedListStatus,
        fourPlusSevenPrice: form.fourPlusSevenPrice,
        fourPlusSevenPublicationTime: form.fourPlusSevenPublicationTime,
        fourPlusSevenProvinces: form.fourPlusSevenProvinces,
        chronicDiseasesVariety: form.chronicDiseasesVariety,
        conEvaluateVariety: form.conEvaluateVariety,
        hospitalVariety: form.hospitalVariety
      };
      console.log(data);
      api.updateCategory(filterEmptyField(data)).then(res => {
        this.$message(res.retMsg);
        this.$emit("update");
        this.visible = false;
      });
    }
  }
};
</script>
