<template>
  <div class="task-to-be-film-container" v-loading="loading">
    <div class="loading" v-loading="uploadLoading" v-show="uploadLoading"></div>
    <!-- 查询表单 -->
    <div class="search-form-wrap">
      <el-form :model="model" ref="refSearchForm">
        <el-row type="flex">
          <!-- 来源 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="来源">
              <el-select
                v-model="formData.source"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" value=""></el-option>
                <!-- <el-option label="百草" :value="1"></el-option> -->
                <el-option label="系统上传" :value="2"></el-option>
                <el-option label="荷叶健康" :value="3"></el-option>
                <!-- <el-option label="智慧脸商城" :value="4"></el-option> -->
                <!-- <el-option label="POP" :value="5"></el-option>
                <el-option label="EC" :value="6"></el-option>
                <el-option label="客服" :value="7"></el-option>
                <el-option label="供多多" :value="8"></el-option>
                <el-option label="智鹿" :value="9"></el-option>
                <el-option label="神农" :value="10"></el-option> -->
                <el-option label="爬虫" :value="20"></el-option>
                <el-option label="新品上报" :value="21"></el-option>
                <el-option label="友商库-自动创建" :value="22"></el-option>
                <el-option label="友商库-运营创建" :value="23"></el-option>
                <el-option label="荷叶积分" :value="25"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 申请时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="申请时间" prop="createTime">
              <el-date-picker
                v-model="formData.createTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                clearable
              ></el-date-picker>
            </el-form-item>
          </el-col>

          <!-- 所属机构 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="所属机构" prop="mechanism">
              <el-select
                multiple
                collapse-tags
                v-model="formData.mechanism"
                placeholder="请选择"
                @remove-tag="removeTag1"
                @change="changeSelect1"
                clearable
              >
                <el-option
                  label="全选"
                  value="全选"
                  @click.native="selectAll1"
                ></el-option>

                <el-option
                  v-for="item in mechanismOptions"
                  :key="'key_mechanismOptions_' + item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 是否含图 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="是否含图">
              <el-select
                v-model="formData.containPictureStatus"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" value=""></el-option>
                <el-option label="是" :value="1"></el-option>
                <el-option label="否" :value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 审核状态 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="tabName == 'allTask'">
            <el-form-item label="审核状态">
              <el-select
                v-model="formData.auditStatus"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" value=""></el-option>
                <el-option label="通过" :value="1"></el-option>
                <el-option label="驳回" :value="2"></el-option>
                <el-option label="未审核" :value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 审核时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="审核时间" prop="auditTime">
              <el-date-picker
                v-model="formData.auditTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                clearable
              ></el-date-picker>
            </el-form-item>
          </el-col>

          <!-- 申请原因 -->
          <!-- <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="申请原因">
              <el-select
                v-model="formData.applyReason"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" value=""></el-option>
                <el-option label="图片补全" :value="5"></el-option>
              </el-select>
            </el-form-item>
          </el-col> -->

          <!-- 商品编码 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="商品编码">
              <el-input
                v-model="formData.productCode"
                placeholder="输入商品ID，原商品编码，商品编码搜索"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 商品名称 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="商品名称" prop="productName">
              <el-input
                v-model="formData.productName"
                placeholder="模糊查询"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 生产厂家-->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="生产厂家">
              <el-input
                v-model="formData.manufacturerName"
                placeholder="模糊查询"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 批准文号 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="isUnfold">
            <el-form-item label="批准文号" prop="approvalNo">
              <el-input
                v-model="formData.approvalNo"
                placeholder="模糊查询"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 小包装条码 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="isUnfold">
            <el-form-item label="小包装条码" prop="smallPackageCode">
              <el-input
                v-model="formData.smallPackageCode"
                placeholder="模糊查询"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 申请人 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="isUnfold">
            <el-form-item label="申请人" prop="applyer">
              <el-input
                v-model="formData.applyer"
                placeholder="请输入申请人"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 是否自营 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="是否自营">
              <el-select
                v-model="formData.businessType"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" value=""></el-option>
                <el-option label="自营" :value="1"></el-option>
                <el-option label="非自营" :value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 是否高优 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="是否高优">
              <el-select
                v-model="formData.whetherHighQualityGoods"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" :value='null'></el-option>
                <el-option label="高优" :value="1"></el-option>
                <el-option label="非高优" :value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>


          <!-- 按钮 -->
          <el-col :span="6" :offset="18">
            <el-button
              type="primary"
              size="medium"
              @click="
                () => {
                  isUnfold = !isUnfold;
                }
              "
              >{{ isUnfold ? "收起" : "展开" }}</el-button
            >
            <el-button type="primary" size="medium" @click="btnSearchClick"
              >查询</el-button
            >
            <el-button size="medium" @click="btnResetClick">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- table上方按钮组 -->
    <div class="btn-group-wrap">
      <div class="tab-wrap">
        <el-tabs v-model="tabName" type="card">
          <el-tab-pane label="所有任务" name="allTask"> </el-tab-pane>
          <!-- <el-tab-pane label="拍摄任务" name="shootTask"> </el-tab-pane> -->
          <!-- <el-tab-pane label="待精修任务" name="truingTask"> </el-tab-pane> -->
        </el-tabs>
      </div>
      <div class="btn-wrap">
        <el-button
          v-if="tabName == 'shootTask'"
          type="primary"
          size="medium"
          :disabled="btnDis"
          @click="addShootTask"
          >新增拍摄任务</el-button
        >
        <el-button
          v-if="tabName == 'truingTask'"
          type="primary"
          size="medium"
          :disabled="btnDis"
          @click="addTruingTask"
          >新增精修任务</el-button
        >
        <vxe-column-filter tableRefName="refVxeTable"></vxe-column-filter>
      </div>
    </div>
    <!-- table -->
    <div class="table-wrap">
      <vxe-table
        border
        highlight-hover-row
        resizable
        height="100%"
        auto-resize
        size="small"
        align="center"
        :tooltip-config="{ enterable: false }"
        :checkbox-config="{
          checkMethod: filterCheck,
        }"
        :loading="tableLoading"
        :seq-config="{ startIndex: (pageNum - 1) * pageSize }"
        ref="refVxeTable"
        :data="tableData"
        @checkbox-all="selectAllEvent"
        @checkbox-change="selectChangeEvent"
      >
        <vxe-table-column
          type="checkbox"
          width="60"
          show-header-overflow
          show-overflow
          fixed="left"
        ></vxe-table-column>
        <vxe-table-column
          type="seq"
          title="序号"
          width="60"
          show-header-overflow
          show-overflow
          fixed="left"
        ></vxe-table-column>
        <vxe-table-column
          field="source"
          title="来源"
          min-width="120"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">
            <span>{{ row.source | filterSource }}</span>
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="createTime"
          title="申请时间"
          min-width="150"
          show-header-overflow
          show-overflow
          ><template v-slot="{ row }">
            <span>{{ row.applyDate | parseTimestamp }}</span>
          </template></vxe-table-column
        >

        <!-- <vxe-table-column
          field="applyReason"
          title="申请原因"
          min-width="120"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">
            <span>{{ row.applyReason | filterApplyReason }}</span>
          </template>
        </vxe-table-column> -->
        <vxe-table-column
          field="applyUser"
          title="申请人"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="mechanismName"
          title="所属机构"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="containPictureStatus"
          title="是否含图"
          min-width="120"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">
            {{ row.containPictureStatus | filterContainPictureStatus }}
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="whetherHighQualityGoodsName"
          title="是否高优"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="auditStatus"
          title="审核状态"
          min-width="120"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">
            <span>{{ row.auditStatus | filterAuditStatus }}</span>
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="reviewDate"
          title="审核时间"
          min-width="150"
          show-header-overflow
          show-overflow
          ><template v-slot="{ row }">
            <span>{{ row.reviewDate | parseTimestamp }}</span>
          </template></vxe-table-column
        >
        <vxe-table-column
          field="auditOpinionStr"
          title="驳回理由"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="preOperateStatusName"
          title="是否自营"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="productCode"
          title="商品编码"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="productId"
          title="商品ID"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="productName"
          title="商品名称"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="spec"
          title="规格/型号"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="manufacturerName"
          title="生产厂家"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="approvalNo"
          title="批准文号"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="packageUnitName"
          title="包装单位"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="smallPackageCode"
          title="小包装条码"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>

        <vxe-table-column
          title="操作"
          width="120"
          show-header-overflow
          show-overflow
          fixed="right"
        >
          <template v-slot="{ row }">
            <span>
              <el-link
                :underline="false"
                :disabled="row.auditStatus != 0"
                type="primary"
                @click.stop="disposeImg(row)"
                >{{ row.auditStatus != 0 ? "已处理" : "处理" }}</el-link
              >
            </span>
          </template>
        </vxe-table-column>
      </vxe-table>
    </div>

    <!-- 分页 -->
    <el-row class="custom-pagination-wrap">
      <el-col>
        <el-pagination
          background
          :current-page.sync="pageNum"
          :page-sizes="[20, 50, 100, 200]"
          :page-size.sync="pageSize"
          :total="total"
          layout="prev, pager, next, jumper,total, sizes"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {
  getMechanismList,
  getProductSupplementPictureList,
  productSupplementPictureShootTask,
  productSupplementPictureDetailTask,
  productSupplementPictureGetTask,
} from "@/api/productPicture.js";

const JSZip = require("jszip");

export default {
  name: "",
  components: {},
  filters: {
    filterApplyReason: function (value) {
      switch (value * 1) {
        case 5:
          return "图片补全";
        default:
          return "无";
      }
    },
    filterContainPictureStatus: function (value) {
      switch (value * 1) {
        case 0:
          return "否";
        case 1:
          return "是";
        default:
          return "否";
      }
    },
    filterAuditStatus: function (value) {
      switch (value * 1) {
        case 0:
          return "未审核";
        case 1:
          return "通过";
        case 2:
          return "驳回";
        default:
          return "-";
      }
    },
  },
  props: {},
  data() {
    return {
      page: 1,
      loading: false,
      pageNum: 1,
      pageSize: 20,
      total: 0, //总条数
      formData: {
        mechanism: [], //所属机构
        source: "",
        createTime: "",
        productCode: "", //商品编码
        smallPackageCode: "", //小包装条码
        productName: "", //通用名
        manufacturerName: "", //生产厂家名称
        approvalNo: "",
        containPictureStatus: "", //是否含图
        auditStatus: 0, //审核状态
        applyer: "",
        auditTime: "",
      },
      tableLoading: false,
      tableData: [],
      checkedList: [],
      mechanismOptions: [],
      isUnfold: false,
      btnDis: false,
      tabName: "allTask",
      columns: [],
    };
  },
  computed: {},
  watch: {
    tabName(val) {
      this.page = 1;
      this.searchForm();
      this.checkedList = [];
      this.refreshColumn();
    },
  },
  created() {
    getMechanismList({
      codeType: "dept",
    })
      .then((resp) => {
        if (resp.retCode === 0) {
          resp.data.forEach((item) => {
            if (item.mechanismId) {
              this.mechanismOptions.push({
                value: item.mechanismId,
                label: item.mechanismName,
              });
            }
          });
        }
      })
      .catch((err) => {
        console.log(err);
      });
    this.searchForm();
    this.$nextTick(() => {
      this.columns = this.$refs.refVxeTable.getColumns();
      this.refreshColumn();
    });
  },
  mounted() {},
  methods: {

    changeSelect1(val) {
      if (!val.includes("全选") && val.length === this.mechanismOptions.length) {
        this.formData.mechanism.unshift("全选");
      } else if (
        val.includes("全选") &&
        val.length - 1 < this.mechanismOptions.length
      ) {
        console.log(this.formData.mechanism)
        this.formData.mechanism = this.formData.mechanism.filter((item) => {
          return item !== "全选";
        });
      }
    },
    removeTag1(val) {
      if (val === "全选") {
        this.formData.mechanism = [];
      }
    },
    selectAll1() {
      if (this.formData.mechanism.length < this.mechanismOptions.length) {
        this.formData.mechanism = [];
        this.mechanismOptions.map((item) => {
          this.formData.mechanism.push(item.value);
        });
        this.formData.mechanism.unshift("全选");
      } else {
        this.formData.mechanism = [];
      }
    },

    /**
     * 查询table数据
     */
    async searchForm() {
      try {
        this.tableLoading = true;
        let param = Object.assign({}, this.formData);
        param.page = this.pageNum;
        param.limit = this.pageSize;
        param.createStartTime = param.createTime ? param.createTime[0] : "";
        param.createEndTime = param.createTime ? param.createTime[1] : "";
        param.reviewStartTime = param.auditTime ? param.auditTime[0] : "";
        param.reviewEndTime = param.auditTime ? param.auditTime[1] : "";
        param.businessType = param.businessType ? param.businessType : null;
        delete param.createTime;
        delete param.auditTime
        if (param.mechanism[0] === '全选') {
          param.mechanism.shift()
          param.mechanism = param.mechanism.join()
        } else {
          param.mechanism = param.mechanism.join()
        }
        if (this.tabName == "shootTask") {
          param.auditStatus = 2;
          param.status = 3;
        }
        if (this.tabName == "truingTask") {
          param.auditStatus = 1;
          param.status = 3;
        }
        let res = await getProductSupplementPictureList(param);
        this.tableLoading = false;
        if (res.success) {
          this.tableData = res.data.list;
          this.total = res.data.total;
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      } catch (error) {
        console.error(error);
      }
    },

    // 筛选可复选数据
    filterCheck({ row }) {
      return (
        (row.auditStatus == 2 && row.status == 3) ||
        (row.auditStatus == 1 && row.status == 3)
      );
    },

    // 表格全选
    selectAllEvent({ checked, records }) {
      this.checkedList = records;
    },

    // 表格单选
    selectChangeEvent({ checked, records }) {
      this.checkedList = records;
    },

    /**
     * 查询按钮点击
     */
    btnSearchClick() {
      this.pageNum = 1;
      this.searchForm();
    },

    /**
     * 重置
     */
    btnResetClick() {
      this.formData = {
        mechanism: [], //所属机构
        approvalNo: "", //申请原因
        source: "",
        createTime: "",
        productCode: "", //商品编码
        smallPackageCode: "", //小包装条码
        productName: "", //通用名
        manufacturerName: "", //生产厂家名称
        containPictureStatus: "", //是否含图
        auditStatus: 0, //审核状态
        applyer: "",
        auditTime: "",
      };
      this.pageNum = 1;
      this.searchForm();
    },

    /**
     * pageSize 改变事件
     */
    handleSizeChange(pageSize) {
      this.pageNum = 1;
      this.pageSize = pageSize;
      this.searchForm();
    },

    /**
     * pageNum 改变事件
     */
    handleCurrentChange(currentPage) {
      this.pageNum = currentPage;
      this.searchForm();
    },

    // 新增拍摄任务
    addShootTask() {
      if (this.checkedList.length == 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      let idList = [];
      this.checkedList.forEach((item) => {
        idList.push(item.id);
      });
      this.btnDis = true;
      productSupplementPictureShootTask({
        idList: idList.join(","),
      }).then((res) => {
        this.btnDis = false;
        if (!res.retCode) {
          this.$message.success("操作成功");
          this.searchForm();
        } else {
          this.$message.error(res.retMsg);
        }
      });
    },

    // 新增精修任务
    addTruingTask() {
      if (this.checkedList.length == 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      let idList = [];
      this.checkedList.forEach((item) => {
        idList.push(item.id);
      });
      this.btnDis = true;
      productSupplementPictureDetailTask({
        idList: idList.join(","),
      }).then((res) => {
        this.btnDis = false;
        if (!res.retCode) {
          this.$message.success("操作成功");
          this.searchForm();
        } else {
          this.$message.error(res.retMsg);
        }
      });
    },
    // 领取任务
    getTask(row) {
      productSupplementPictureGetTask({
        id: row.id,
      }).then((res) => {
        if (!res.retCode) {
          this.$message.success("操作成功");
          this.searchForm();
        } else {
          this.$message.error(res.retMsg);
        }
      });
    },
    //审核图片详情
    disposeImg(row) {
      // selfBusinessType: 1自营 2非自营
      try {
        parent.CreateTab(
          `../static/dist/index.html#/productPicture/applyProductDispose?id=${row.id}&page=${row.selfBusinessType === 1 ? 1 : ''}`,
          "已申请商品图片处理"
        );
      } catch {
        this.$router.push({
          path: "/productPicture/applyProductDispose",
          query: {
            id: row.id,
            page: row.selfBusinessType === 1 ? 1 : '',
          },
        });
      }
    },
    refreshColumn() {
      if (this.tabName == "allTask") {
        this.columns.forEach((item) => {
          if (item.type == "checkbox") {
            item.visible = false;
          }
          if (item.title == "操作") {
            item.visible = true;
          }
        });
      } else {
        this.columns.forEach((item) => {
          if (item.title == "操作") {
            item.visible = false;
          }
          if (item.type == "checkbox") {
            item.visible = true;
          }
        });
      }
      this.$refs.refVxeTable.refreshColumn();
    },
  },
};
</script>
<style lang="scss" scoped>
.dialog-wrap /deep/ {
  .el-dialog__body {
    height: 500px;
    overflow-y: scroll;
  }
  .el-form-item {
    display: flex;
    height: 40px;
    padding: 0 50px;
    .el-form-item__content {
      width: 100%;
      position: relative;
      .usePictureStatus-tip {
        color: #f56c6c;
        position: absolute;
        top: 25px;
      }
    }
    .btn-wrap {
      width: 100%;
      display: flex;
      justify-content: center;
    }
  }
  .el-form-item__label {
    width: 200px;
  }
}
.task-to-be-film-container {
  .loading {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
  }
  /**
   * 查询表单
   */
  .search-form-wrap {
    width: 100%;
    padding: 15px;
    border-bottom: 1px dashed #e4e4eb;

    .el-row {
      flex-wrap: wrap;

      .el-col {
        display: flex;
        justify-content: flex-end;

        .el-form-item {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          width: 100%;
          margin-right: 0;
          margin-bottom: 15px;

          /deep/ {
            .el-form-item__label {
              width: 96px;
              line-height: normal;
              padding: 0 12px;
              color: #292933;
              font-weight: normal;
            }

            .el-form-item__content {
              flex: 1;

              .el-range-editor.el-input__inner {
                width: 100%;

                .el-range-separator {
                  width: 14px;
                  padding: 0;
                }
              }
            }
          }

          .el-input {
            width: 100%;
          }

          .el-select {
            width: 100%;
          }
        }
      }
    }
  }
  .tab-wrap {
    padding: 0 15px;
  }
  /**
     * table
     */
  .table-wrap {
    width: 100%;
    min-height: 440px;
    height: calc(100vh - 295px);
    padding: 0 15px;
  }
}
.img-default {
  height: 400px;
  width: 70%;
  margin: 0 auto;
  background: #ccc;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  .icon {
    font-size: 64px;
    color: #fff;
    margin-bottom: 10px;
  }
}
.img-box {
  display: flex;
  justify-content: center;
  height: 400px;
  width: 70%;
  margin: 0 auto;
  .img-xt-list {
    width: 130px;
    padding-left: 50px;
    display: flex;
    flex-direction: column;
    .btn {
      height: 20px;
      text-align: center;
      line-height: 20px;
      background: #ccc;
      color: #fff;
      width: 100%;
      cursor: pointer;
      &.el-icon-arrow-up {
        margin-bottom: 5px;
      }
      &.el-icon-arrow-down {
        margin-top: 5px;
      }
    }
    .img-list {
      flex: 1;
      display: flex;
      flex-direction: column;
      .list {
        height: 25%;
        width: 80px;
        padding: 5px 0;
        .img-wrap {
          height: 100%;
          width: 100%;
          border: 6px solid #ccc;
          cursor: pointer;
          &.active {
            border: 6px solid rgba(245, 108, 108, 0.5);
          }
          img {
            display: block;
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }
  .img-dt {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: hidden;
    .title-wrap {
      height: 50px;
      font-size: 22px;
      font-weight: bold;
      padding-bottom: 10px;
      display: flex;
      align-items: center;
      .tip {
        color: #ccc;
        font-size: 16px;
        text-align: right;
        font-weight: normal;
        padding: 0px 10px 0px 5px;
      }
      .delete {
        color: #f56c6c;
        cursor: pointer;
        font-size: 12px;
        .el-icon-delete {
          font-size: 20px;
        }
      }
    }
    .img-wrap {
      flex: 1;
      height: 350px;
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      position: relative;
      img {
        border: 6px solid #ccc;
        height: auto;
        width: auto;
        max-height: 100%;
        max-width: 100%;
      }
    }
  }
}

/**
     * 按钮组
     */
.btn-group-wrap {
  display: flex;
  align-items: center;
  padding: 20px 0;
  .btn-wrap /deep/ {
    display: flex;
    padding-left: 30px;
    .el-button {
      margin: 0 5px;
    }
  }
  .tab-wrap /deep/ {
    .el-tabs__header {
      margin-bottom: 0px;
    }
  }
}

.el-link.el-link--primary.is-disabled {
  color: #bababa;
}
.upload-img-wrap {
  display: flex;
  img {
    height: 50px;
    width: 60px;
    padding-right: 10px;
  }
}
</style>
