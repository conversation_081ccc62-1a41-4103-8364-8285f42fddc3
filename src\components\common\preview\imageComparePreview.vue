<template>
  <transition name="viewer-fade">
    <div 
      tabindex="-1" 
      ref="imageCompareWrapper" 
      class="image-compare-wrapper"
      @keydown="handleKeyDown"
    >
      <div class="image-compare-mask"></div>
      
      <!-- 关闭按钮 -->
      <span class="image-compare-close" @click="close">
        <i class="el-icon-circle-close"></i>
      </span>
      
      <!-- 主要内容区域 -->
      <div class="image-compare-content">
        
        <!-- 左侧：新品图片 -->
        <div class="image-compare-panel left-panel">
          <div class="panel-header">
            <h3 class="panel-title">新品提报图片</h3>
            <div class="image-counter" v-if="newProductImages.length > 1">
              {{ newProductCurrentIndex + 1 }} / {{ newProductImages.length }}
            </div>
          </div>
          
          <div class="image-container">
            <!-- 图片显示区域 -->
            <div class="image-display-area">
              <img
                v-if="currentNewProductImage"
                :src="currentNewProductImage.url"
                :alt="currentNewProductImage.name || '新品图片'"
                class="preview-image"
                :style="newProductImageStyle"
                @load="handleImageLoad"
                @error="handleImageError"
                @mousedown="handleMouseDown($event, 'newProduct')"
              />
              <div v-else class="no-image">暂无图片</div>
            </div>
            
            <!-- 图片控制按钮 -->
            <div class="image-controls left-controls">
              <div class="control-group">
                <el-tooltip content="缩小" placement="top">
                  <button class="control-btn zoom-out-btn" @click="handleZoom('newProduct', 'out')">
                    <i class="el-icon-zoom-out"></i>
                    <span class="btn-text">缩小</span>
                  </button>
                </el-tooltip>
                <el-tooltip content="放大" placement="top">
                  <button class="control-btn zoom-in-btn" @click="handleZoom('newProduct', 'in')">
                    <i class="el-icon-zoom-in"></i>
                    <span class="btn-text">放大</span>
                  </button>
                </el-tooltip>
                <el-tooltip content="逆时针旋转" placement="top">
                  <button class="control-btn rotate-left-btn" @click="handleRotate('newProduct', 'left')">
                    <i class="el-icon-refresh-left"></i>
                    <span class="btn-text">逆时针</span>
                  </button>
                </el-tooltip>
                <el-tooltip content="顺时针旋转" placement="top">
                  <button class="control-btn rotate-right-btn" @click="handleRotate('newProduct', 'right')">
                    <i class="el-icon-refresh-right"></i>
                    <span class="btn-text">顺时针</span>
                  </button>
                </el-tooltip>
                <el-tooltip content="重置" placement="top">
                  <button class="control-btn reset-btn" @click="resetTransform('newProduct')">
                    <i class="el-icon-refresh"></i>
                    <span class="btn-text">重置</span>
                  </button>
                </el-tooltip>
              </div>
            </div>
            
            <!-- 图片切换按钮 -->
            <template v-if="newProductImages.length > 1">
              <span 
                class="image-nav-btn prev-btn left-prev"
                :class="{ 'disabled': newProductCurrentIndex === 0 }"
                @click="switchNewProductImage('prev')"
              >
                <i class="el-icon-arrow-left"></i>
              </span>
              <span 
                class="image-nav-btn next-btn left-next"
                :class="{ 'disabled': newProductCurrentIndex === newProductImages.length - 1 }"
                @click="switchNewProductImage('next')"
              >
                <i class="el-icon-arrow-right"></i>
              </span>
            </template>
          </div>
          
          <!-- 图片信息 -->
          <div class="image-info" v-if="currentNewProductImage">
            <div class="info-item">
              <span class="info-label">图片类型：</span>
              <span class="info-value">{{ currentNewProductImage.type || '未知' }}</span>
            </div>
            <div class="info-item" v-if="currentNewProductImage.size">
              <span class="info-label">图片尺寸：</span>
              <span class="info-value">{{ currentNewProductImage.size }}</span>
            </div>
          </div>
        </div>
        
        <!-- 右侧：标品图片 -->
        <div class="image-compare-panel right-panel">
          <div class="panel-header">
            <h3 class="panel-title">标品图片</h3>
            <div class="image-counter" v-if="standardProductImages.length > 1">
              {{ standardProductCurrentIndex + 1 }} / {{ standardProductImages.length }}
            </div>
          </div>
          
          <div class="image-container">
            <!-- 图片显示区域 -->
            <div class="image-display-area">
              <img
                v-if="currentStandardProductImage"
                :src="currentStandardProductImage.url"
                :alt="currentStandardProductImage.name || '标品图片'"
                class="preview-image"
                :style="standardProductImageStyle"
                @load="handleImageLoad"
                @error="handleImageError"
                @mousedown="handleMouseDown($event, 'standardProduct')"
              />
              <div v-else class="no-image">暂无图片</div>
            </div>
            
            <!-- 图片控制按钮 -->
            <div class="image-controls right-controls">
              <div class="control-group">
                <el-tooltip content="缩小" placement="top">
                  <button class="control-btn zoom-out-btn" @click="handleZoom('standardProduct', 'out')">
                    <i class="el-icon-zoom-out"></i>
                    <span class="btn-text">缩小</span>
                  </button>
                </el-tooltip>
                <el-tooltip content="放大" placement="top">
                  <button class="control-btn zoom-in-btn" @click="handleZoom('standardProduct', 'in')">
                    <i class="el-icon-zoom-in"></i>
                    <span class="btn-text">放大</span>
                  </button>
                </el-tooltip>
                <el-tooltip content="逆时针旋转" placement="top">
                  <button class="control-btn rotate-left-btn" @click="handleRotate('standardProduct', 'left')">
                    <i class="el-icon-refresh-left"></i>
                    <span class="btn-text">逆时针</span>
                  </button>
                </el-tooltip>
                <el-tooltip content="顺时针旋转" placement="top">
                  <button class="control-btn rotate-right-btn" @click="handleRotate('standardProduct', 'right')">
                    <i class="el-icon-refresh-right"></i>
                    <span class="btn-text">顺时针</span>
                  </button>
                </el-tooltip>
                <el-tooltip content="重置" placement="top">
                  <button class="control-btn reset-btn" @click="resetTransform('standardProduct')">
                    <i class="el-icon-refresh"></i>
                    <span class="btn-text">重置</span>
                  </button>
                </el-tooltip>
              </div>
            </div>
            
            <!-- 图片切换按钮 -->
            <template v-if="standardProductImages.length > 1">
              <span 
                class="image-nav-btn prev-btn right-prev"
                :class="{ 'disabled': standardProductCurrentIndex === 0 }"
                @click="switchStandardProductImage('prev')"
              >
                <i class="el-icon-arrow-left"></i>
              </span>
              <span 
                class="image-nav-btn next-btn right-next"
                :class="{ 'disabled': standardProductCurrentIndex === standardProductImages.length - 1 }"
                @click="switchStandardProductImage('next')"
              >
                <i class="el-icon-arrow-right"></i>
              </span>
            </template>
          </div>
          
          <!-- 图片信息 -->
          <div class="image-info" v-if="currentStandardProductImage">
            <div class="info-item">
              <span class="info-label">图片类型：</span>
              <span class="info-value">{{ currentStandardProductImage.type || '标品主图' }}</span>
            </div>
            <div class="info-item" v-if="currentStandardProductImage.size">
              <span class="info-label">图片尺寸：</span>
              <span class="info-value">{{ currentStandardProductImage.size }}</span>
            </div>
          </div>
        </div>
        
      </div>
      
      <!-- 底部操作栏 -->
      <div class="bottom-toolbar">
        <div class="toolbar-content">
          <span class="toolbar-text">
            ESC：关闭预览 | 方向键：切换图片 | Ctrl+方向键：旋转图片 | Ctrl+R：重置所有变换
          </span>
          <el-button size="small" @click="close">关闭预览</el-button>
        </div>
      </div>
      
    </div>
  </transition>
</template>

<script>
export default {
  name: 'ImageComparePreview',
  props: {
    // 新品图片列表
    newProductImages: {
      type: Array,
      default: () => []
    },
    // 标品图片列表
    standardProductImages: {
      type: Array,
      default: () => []
    },
    // 关闭回调
    onClose: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      // 当前显示的图片索引
      newProductCurrentIndex: 0,
      standardProductCurrentIndex: 0,
      
      // 图片变换状态
      transforms: {
        newProduct: {
          scale: 1,
          offsetX: 0,
          offsetY: 0,
          rotation: 0
        },
        standardProduct: {
          scale: 1,
          offsetX: 0,
          offsetY: 0,
          rotation: 0
        }
      },
      
      // 拖拽状态
      dragging: {
        active: false,
        type: null,
        startX: 0,
        startY: 0,
        startOffsetX: 0,
        startOffsetY: 0
      }
    }
  },
  computed: {
    // 当前新品图片
    currentNewProductImage() {
      return this.newProductImages[this.newProductCurrentIndex] || null;
    },
    
    // 当前标品图片
    currentStandardProductImage() {
      return this.standardProductImages[this.standardProductCurrentIndex] || null;
    },
    
    // 新品图片样式
    newProductImageStyle() {
      const transform = this.transforms.newProduct;
      return {
        transform: `scale(${transform.scale}) rotate(${transform.rotation}deg) translate(${transform.offsetX}px, ${transform.offsetY}px)`,
        transition: 'transform 0.3s ease'
      };
    },

    // 标品图片样式
    standardProductImageStyle() {
      const transform = this.transforms.standardProduct;
      return {
        transform: `scale(${transform.scale}) rotate(${transform.rotation}deg) translate(${transform.offsetX}px, ${transform.offsetY}px)`,
        transition: 'transform 0.3s ease'
      };
    }
  },
  mounted() {
    // console.log('🔧 ImageComparePreview 组件已挂载');
    // console.log('📊 Props数据:', {
    //   newProductImages: this.newProductImages,
    //   standardProductImages: this.standardProductImages
    // });

    // 聚焦到容器，以便接收键盘事件
    this.$nextTick(() => {
      if (this.$refs.imageCompareWrapper) {
        this.$refs.imageCompareWrapper.focus();
      }

      // 检查控制按钮是否正确渲染
      // setTimeout(() => {
      //   const leftControls = document.querySelector('.left-controls');
      //   const rightControls = document.querySelector('.right-controls');

      //   console.log('🎛️ 左侧控制按钮区域:', leftControls);
      //   console.log('🎛️ 右侧控制按钮区域:', rightControls);

      //   if (leftControls) {
      //     console.log('✅ 左侧控制按钮区域已找到');
      //     console.log('🔍 左侧控制按钮数量:', leftControls.querySelectorAll('.control-btn').length);
      //     console.log('🎨 左侧控制按钮样式:', window.getComputedStyle(leftControls));
      //   } else {
      //     console.log('❌ 左侧控制按钮区域未找到');
      //   }

      //   if (rightControls) {
      //     console.log('✅ 右侧控制按钮区域已找到');
      //     console.log('🔍 右侧控制按钮数量:', rightControls.querySelectorAll('.control-btn').length);
      //   } else {
      //     console.log('❌ 右侧控制按钮区域未找到');
      //   }
      // }, 100);
    });

    // 添加全局事件监听
    document.addEventListener('mousemove', this.handleMouseMove);
    document.addEventListener('mouseup', this.handleMouseUp);
  },
  beforeDestroy() {
    // 移除全局事件监听
    document.removeEventListener('mousemove', this.handleMouseMove);
    document.removeEventListener('mouseup', this.handleMouseUp);
  },
  methods: {
    // 关闭预览
    close() {
      this.onClose();
    },
    
    // 键盘事件处理
    handleKeyDown(e) {
      e.preventDefault();
      e.stopPropagation();

      switch (e.keyCode) {
        case 27: // ESC
          this.close();
          break;
        case 37: // 左箭头
          if (e.ctrlKey) {
            // Ctrl + 左箭头：新品图片逆时针旋转
            this.handleRotate('newProduct', 'left');
          } else {
            this.switchNewProductImage('prev');
          }
          break;
        case 39: // 右箭头
          if (e.ctrlKey) {
            // Ctrl + 右箭头：新品图片顺时针旋转
            this.handleRotate('newProduct', 'right');
          } else {
            this.switchNewProductImage('next');
          }
          break;
        case 38: // 上箭头
          if (e.ctrlKey) {
            // Ctrl + 上箭头：标品图片逆时针旋转
            this.handleRotate('standardProduct', 'left');
          } else {
            this.switchStandardProductImage('prev');
          }
          break;
        case 40: // 下箭头
          if (e.ctrlKey) {
            // Ctrl + 下箭头：标品图片顺时针旋转
            this.handleRotate('standardProduct', 'right');
          } else {
            this.switchStandardProductImage('next');
          }
          break;
        case 82: // R键
          if (e.ctrlKey) {
            // Ctrl + R：重置所有变换
            this.resetTransform('newProduct');
            this.resetTransform('standardProduct');
          }
          break;
      }
    },
    
    // 切换新品图片
    switchNewProductImage(direction) {
      if (direction === 'prev' && this.newProductCurrentIndex > 0) {
        this.newProductCurrentIndex--;
        this.resetTransform('newProduct');
      } else if (direction === 'next' && this.newProductCurrentIndex < this.newProductImages.length - 1) {
        this.newProductCurrentIndex++;
        this.resetTransform('newProduct');
      }
    },

    // 切换标品图片
    switchStandardProductImage(direction) {
      if (direction === 'prev' && this.standardProductCurrentIndex > 0) {
        this.standardProductCurrentIndex--;
        this.resetTransform('standardProduct');
      } else if (direction === 'next' && this.standardProductCurrentIndex < this.standardProductImages.length - 1) {
        this.standardProductCurrentIndex++;
        this.resetTransform('standardProduct');
      }
    },
    
    // 缩放处理
    handleZoom(type, direction) {
      // console.log(`🔍 缩放操作: ${type} ${direction}`);
      const transform = this.transforms[type];
      const zoomRate = 0.2;

      if (direction === 'in') {
        transform.scale = Math.min(transform.scale + zoomRate, 3);
      } else if (direction === 'out') {
        transform.scale = Math.max(transform.scale - zoomRate, 0.2);
      }

      // console.log(`📏 ${type} 缩放比例: ${transform.scale}`);
    },

    // 旋转处理
    handleRotate(type, direction) {
      // console.log(`🔄 旋转操作: ${type} ${direction}`);
      const transform = this.transforms[type];

      if (direction === 'left') {
        // 逆时针旋转90度
        transform.rotation -= 90;
      } else if (direction === 'right') {
        // 顺时针旋转90度
        transform.rotation += 90;
      }

      // 确保角度在0-360度范围内
      if (transform.rotation >= 360) {
        transform.rotation -= 360;
      } else if (transform.rotation < 0) {
        transform.rotation += 360;
      }

      // console.log(`🎯 ${type} 旋转到 ${transform.rotation} 度`);
    },
    
    // 重置变换
    resetTransform(type) {
      // console.log(`🔄 重置变换: ${type}`);
      this.transforms[type] = {
        scale: 1,
        offsetX: 0,
        offsetY: 0,
        rotation: 0
      };
      // console.log(`✅ ${type} 变换已重置`);
    },
    
    // 鼠标按下事件
    handleMouseDown(e, type) {
      if (e.button !== 0) return; // 只处理左键
      
      const transform = this.transforms[type];
      this.dragging = {
        active: true,
        type: type,
        startX: e.clientX,
        startY: e.clientY,
        startOffsetX: transform.offsetX,
        startOffsetY: transform.offsetY
      };
      
      e.preventDefault();
    },
    
    // 鼠标移动事件
    handleMouseMove(e) {
      if (!this.dragging.active) return;
      
      const deltaX = e.clientX - this.dragging.startX;
      const deltaY = e.clientY - this.dragging.startY;
      
      const transform = this.transforms[this.dragging.type];
      transform.offsetX = this.dragging.startOffsetX + deltaX;
      transform.offsetY = this.dragging.startOffsetY + deltaY;
    },
    
    // 鼠标释放事件
    handleMouseUp() {
      this.dragging.active = false;
    },
    
    // 图片加载成功
    handleImageLoad() {
      // 可以在这里处理图片加载成功的逻辑
    },
    
    // 图片加载失败
    handleImageError(e) {
      e.target.alt = '图片加载失败';
    }
  }
}
</script>

<style scoped>
/* 基础样式 */
.image-compare-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  outline: none;
}

.image-compare-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
}

.image-compare-close {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10001;
  font-size: 30px;
  color: #fff;
  cursor: pointer;
  transition: color 0.3s;
}

.image-compare-close:hover {
  color: #409eff;
}

.image-compare-content {
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
  padding: 60px 20px 20px;
  box-sizing: border-box;
  max-height: 100vh;
  overflow: hidden;
}

/* 面板样式 */
.image-compare-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  margin: 0 10px;
  overflow: hidden;
  max-height: calc(100vh - 80px);
  min-height: 600px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  flex-shrink: 0;
  min-height: 50px;
}

.panel-title {
  margin: 0;
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.image-counter {
  font-size: 14px;
  color: #606266;
  background: #e4e7ed;
  padding: 4px 8px;
  border-radius: 4px;
}

/* 图片容器 */
.image-container {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

.image-display-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
  background: #f8f9fa;
  min-height: 300px;
  max-height: calc(100vh - 300px);
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  cursor: move;
  user-select: none;
}

.no-image {
  color: #c0c4cc;
  font-size: 16px;
}

/* 控制按钮 */
.image-controls {
  padding: 12px 15px;
  background: #fff;
  border-top: 1px solid #e4e7ed;
  display: flex;
  justify-content: center;
  min-height: 70px;
  max-height: 70px;
  flex-shrink: 0;
  box-sizing: border-box;
}

/* 调试样式 - 临时添加边框以确认区域可见 */
/* .left-controls {
  border: 2px solid #ff4444 !important;
  background: #fff5f5 !important;
}

.right-controls {
  border: 2px solid #44ff44 !important;
  background: #f5fff5 !important;
} */

/* 布局优化 */
.image-compare-panel:first-child {
  margin-left: 0;
}

.image-compare-panel:last-child {
  margin-right: 0;
}

/* 确保内容不会溢出 */
.image-compare-wrapper * {
  box-sizing: border-box;
}

.control-group {
  display: flex;
  gap: 12px;
  align-items: center;
}

.control-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  min-width: 50px;
  min-height: 40px;
  padding: 6px 8px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  background: #ffffff;
  color: #606266;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 14px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.control-btn:hover {
  color: #409eff;
  background: #ecf5ff;
  border-color: #409eff;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
}

.control-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.control-btn i {
  font-size: 16px;
  margin-bottom: 2px;
}

.btn-text {
  font-size: 11px;
  line-height: 1;
  white-space: nowrap;
}

/* 特定按钮样式 */
.zoom-out-btn:hover,
.zoom-in-btn:hover {
  background: #e6f7ff;
  border-color: #1890ff;
  color: #1890ff;
}

.rotate-left-btn:hover,
.rotate-right-btn:hover {
  background: #fff2e8;
  border-color: #fa8c16;
  color: #fa8c16;
}

.reset-btn:hover {
  background: #f6ffed;
  border-color: #52c41a;
  color: #52c41a;
}

/* 导航按钮 */
.image-nav-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  z-index: 10;
}

.image-nav-btn:hover {
  background: rgba(0, 0, 0, 0.8);
}

.image-nav-btn.disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.left-prev {
  left: 15px;
}

.left-next {
  right: 15px;
}

.right-prev {
  left: 15px;
}

.right-next {
  right: 15px;
}

/* 图片信息 */
.image-info {
  padding: 12px 16px;
  background: #fff;
  border-top: 1px solid #e4e7ed;
  flex-shrink: 0;
  max-height: 120px;
  overflow-y: auto;
  box-sizing: border-box;
}

.info-item {
  display: flex;
  margin-bottom: 6px;
  align-items: flex-start;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-weight: 500;
  color: #606266;
  min-width: 70px;
  flex-shrink: 0;
  font-size: 13px;
}

.info-value {
  color: #303133;
  font-size: 13px;
  word-break: break-all;
  line-height: 1.4;
}

/* 底部工具栏 */
.bottom-toolbar {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: rgba(0, 0, 0, 0.8);
  padding: 15px 20px;
  box-sizing: border-box;
}

.toolbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toolbar-text {
  color: #fff;
  font-size: 14px;
}

/* 过渡动画 */
.viewer-fade-enter-active,
.viewer-fade-leave-active {
  transition: opacity 0.3s;
}

.viewer-fade-enter,
.viewer-fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .image-compare-content {
    flex-direction: column;
    padding: 60px 10px 20px;
  }

  .image-compare-panel {
    margin: 5px 0;
    min-height: 400px;
    max-height: calc(50vh - 40px);
  }

  .panel-header {
    padding: 8px 12px;
    min-height: 40px;
  }

  .panel-title {
    font-size: 14px;
  }

  .image-counter {
    font-size: 12px;
  }

  .image-display-area {
    min-height: 200px;
    max-height: calc(50vh - 200px);
  }

  .image-controls {
    padding: 8px 12px;
    min-height: 60px;
    max-height: 60px;
  }

  .control-btn {
    min-width: 40px;
    min-height: 35px;
    padding: 4px 6px;
  }

  .image-info {
    padding: 8px 12px;
    max-height: 80px;
  }

  .info-label {
    min-width: 60px;
    font-size: 12px;
  }

  .info-value {
    font-size: 12px;
  }
}
</style>
