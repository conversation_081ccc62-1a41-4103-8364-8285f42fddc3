import request from '@/utils/request'
import qs from "qs"
// 查询单个审核事项
export function singleReview(data) {
  //接口文档 https://wiki.int.ybm100.com/pages/viewpage.action?pageId=314966841
  return request({
    url: '/api/worksubstitution/find/singleReview',
    data,
    method: 'POST'
  })
}

// 挂起一个审核事项
export function hang(data) {
  //接口文档 https://wiki.int.ybm100.com/pages/viewpage.action?pageId=314967030
  return request({
    url: '/api/worksubstitution/hang',
    data,
    method: 'POST'
  })
}
// 审核
export function review(data) {
  //接口文档 https://wiki.int.ybm100.com/pages/viewpage.action?pageId=314967030
  return request({
    url: '/api/worksubstitution/review',
    data,
    method: 'POST'
  })
}


// 商品合并
// 单据合并信息  
export function mergeProductDetail(data) {
  console.log(data);
  return request({
    url: `/api/worksubstitution/get/mergeProductDetail`,
    data: qs.stringify(data),
    method: 'POST',
  })
}

// 合并商品详情  
export function mergeDetail(data) {
  return request({
    url: `/api/productMerge/get/productMergeInfo`,
    data: qs.stringify(data),
    method: 'POST',
  })
}
