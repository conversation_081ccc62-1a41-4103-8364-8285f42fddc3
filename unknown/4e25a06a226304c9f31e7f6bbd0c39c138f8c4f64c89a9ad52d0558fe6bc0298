<template>
  <div class="container">
    <el-dialog
      title="提示"
      :visible.sync="dialogVisible"
      width="1000px"
      :before-close="handleClose"
    >
      <div class="tabel-wrap">
        <vxe-table
          border
          highlight-hover-row
          resizable
          height="100%"
          auto-resize
          size="small"
          align="center"
          :tooltip-config="{ enterable: false }"
          :data="tableData"
          :loading="tableLoading"
          :seq-config="{ startIndex: (param.page - 1) * param.limit }"
          ref="refVxeTable"
        >
          <vxe-table-column
            type="seq"
            title="序号"
            width="60"
            show-header-overflow
            show-overflow
            fixed="left"
          ></vxe-table-column>
          <vxe-table-column
            field="integrity"
            title="完整度"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
          <vxe-table-column
            field="productCode"
            title="商品编码"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
          <vxe-table-column
            field="skuCode"
            title="sku编码"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
          <vxe-table-column
            field="skuName"
            title="商品名"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
          <vxe-table-column
            field="generalName"
            title="通用名"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
          <vxe-table-column
            field="approvalNo"
            title="批准文号"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
          <vxe-table-column
            field="manufacturerName"
            title="生产厂家"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
          <vxe-table-column
            field="spec"
            title="规格"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
          <vxe-table-column
            field="spuCategoryName"
            title="商品大类"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
          <vxe-table-column
            field="businessScopeMultiStr"
            title="经营范围"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
          <vxe-table-column
            field="packageUnitName"
            title="包装单位"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
          <vxe-table-column
            field="dosageFormName"
            title="剂型"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
          <vxe-table-column
            field="prescriptionCategoryName"
            title="处方分类"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
          <vxe-table-column
            field="validity"
            title="有效期"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          <template v-slot="{ row }">
            <span>{{ row.validity | filterValidity}}</span>
          </template>
          </vxe-table-column>
           <vxe-table-column
            field="smallPackageCode"
            title="小包装条码"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
           <vxe-table-column
            field="shadingAttrName"
            title="存储条件"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
        </vxe-table>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-pagination
          background
          :current-page.sync="param.page"
          :page-sizes="[20, 50, 100, 200]"
          :page-size.sync="param.limit"
          :total="total"
          layout="prev, pager, next, jumper,total, sizes"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { getProductCompleteDetail } from "@/api/product.js";

export default {
  name: "productCompleteDetail",
  data() {
    return {
      dialogVisible: false,
      tableData: [],
      tableLoading: false,
      param: {
        page: 1,
        limit: 20,
      },
      total: 0,
    };
  },
  created() {},
  methods: {
    show(param) {
      this.dialogVisible = true;
      this.param.page = 1;
      this.param = { ...this.param, ...param };
      this.getProductCompleteDetail();
    },
    getProductCompleteDetail() {
      this.tableLoading = true;
      getProductCompleteDetail(this.param).then((res) => {
        if (!res.retCode) {
          this.tableLoading = false;
          this.tableData = res.data.list;
          this.total = res.data.total;
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      });
    },
    /**
     * pageSize 改变事件
     */
    handleSizeChange(pageSize) {
      this.param.page = 1;
      this.param.limit = pageSize;
      this.getProductCompleteDetail();
    },

    /**
     * pageNum 改变事件
     */
    handleCurrentChange(currentPage) {
      this.param.page = currentPage;
      this.getProductCompleteDetail();
    },
  },
};
</script>
<style lang="scss" scoped>
.container /deep/ {
  .el-dialog__body {
    height: 300px;
    padding-bottom: 0;
    overflow-y: scroll;
    .tabel-wrap{
        height: 100%;
    }
  }
  .el-dialog__footer {
    padding: 10px 0;
  }
}
</style>
