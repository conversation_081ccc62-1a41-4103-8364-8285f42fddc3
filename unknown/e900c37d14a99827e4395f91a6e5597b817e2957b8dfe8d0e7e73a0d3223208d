import request from '@/utils/request'
import qs from "qs" // 请求数据 转成form data 形式

// 获取商品补全列表
export function getProductShootRecordList(data) {
  return request({
    url: `/api/productShootRecord/page/pictureComplementList`,
    data,
    method: 'POST',
  })
}

// 获取补全图片批量上传记录
export function getComplementUploadRecordList(data) {
  return request({
    url: `/api/productShootRecord/batch/findBatchUploadPictureComplementUploadRecord`,
    data,
    method: 'POST',
  })
}


// 获取商品去重列表
export function getSamegroupList(data) {
  return request({
    url: `/api/samegroup/find/pageList`,
    data,
    method: 'POST',
  })
}


// 获取商品去重详情
export function getSamegroupDetail(data) {
  return request({
    url: `/api/samegroup/find/auditDetailPage`,
    data: qs.stringify(data),
    method: 'POST',
  })
}


// 去重领取任务
export function receiveSamegroup(data) {
  return request({
    url: `/api/samegroup/opt/receiveTask`,
    data: qs.stringify(data),
    method: 'POST',
  })
}

// 去重任务驳回
export function rejectSamegroup(data) {
  return request({
    url: `/api/samegroup/opt/reject`,
    data: qs.stringify(data),
    method: 'POST',
  })
}

// 去重任务提交
export function submitSamegroup(data) {
  return request({
    url: `/api/samegroup/opt/submitTask`,
    data,
    method: 'POST',
  })
}

// 客户商品导入
// 获取客户商品列表
export function getCustomerProductList(data) {
  return request({
    url: '/api/customerProduct/find/pageList',
    data,
    method: 'POST',
  })
}
// 获取处理状态
export function getAllOperateStatus() {
  return request({
    url: '/api/customerProduct/find/getAllOperateStatus',
    method: 'get',
  })
}
// 领取任务
export function receiveCustomerProductTask(data) {
  return request({
    url: '/api/customerProduct/opt/receiveTask',
    data: qs.stringify(data),
    method: 'post',
  })
}
// 获取处理详情
export function getCustomerProductDetail(data) {
  return request({
    url: '/api/customerProduct/find/toDetailPage',
    data: qs.stringify(data),
    method: 'post',
  })
}
// 处理提交
export function submitDealCustomerProduct(data) {
  return request({
    url: '/api/customerProduct/opt/submitDeal',
    data,
    method: 'post',
  })
}
// 启用停用
export function switcherCustomerProduct(data) {
  return request({
    url: '/api/customerProduct/opt/switcher',
    data: qs.stringify(data),
    method: 'post',
  })
}
// 补充条码提交
export function addBarcodeSubmit(data) {
  return request({
    url: '/api/customerProduct/opt/modifyBarcode',
    data: qs.stringify(data),
    method: 'post',
  })
}
// 客户商品获取商品列表
export function getProductList(data) {
  return request({
    url: '/api/customerProduct/find/getProductList',
    data,
    method: 'post',
  })
}
// 数据导出
export function getCustomerProductExportList(data) {
  return request({
    url: '/api/customerProduct/find/exportList',
    data,
    method: 'post',
  })
}
// 新品上报获取商品信息
export function getCustomerProductReportProduct(data) {
  return request({
    url: '/api/customerProduct/opt/reportProduct',
    data: qs.stringify(data),
    method: 'post',
  })
}

// 商品图片补全批量上传excel
export function picComplementUploadExcel(data) {
  return request({
    url: '/api/productShootRecord/batch/picComplement',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    data,
    method: 'post',
  })
}

// 商品图片补全批量上传zip
export function picComplementUploadZip(data, callback) {
  return request({
    url: '/api/productShootRecord/batch/picComplementZip',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    data,
    method: 'post',
    onUploadProgress (a){
      callback(Math.floor(a.loaded * 100 / a.total))
      console.log('net request', a)
    },
  })
}

/**
 * author:caoshuwen
 * date: 2021-08-11
 * description:批量修改图片上传
 * **/
export function updatePhotoApi(data) {
  return request({
    url: '/api/productShootRecord/batch/pictureUpdateZip',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    data,
    method: 'post',
  })
}

// 商品图片补全批量上传抓图申请
export function picComplementCrawl(data) {
  return request({
    url: '/api/productShootRecord/batch/picComplementCrawl',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    data,
    method: 'post',
  })
}

// 商品图片补全批量上传抓图申请记录
export function getPicComplementCrawlList(data) {
  return request({
    url: '/api/productShootRecord/list/picComplementCrawlList',
    data,
    method: 'post',
  })
}

// 商品图片补全批量上传抓图申请记录
export function sumbitPicComplementCrawlList(data) {
  return request({
    url: '/api/productShootRecord/submit/picComplementCrawl',
    data,
    method: 'post',
  })
}

// 查询友商库图片补全申请记录
export function getFriendLibraryProductShootRecordList(data) {
  return request({
    url: '/api/productShootRecord/friendLibrary/productShootRecordList',
    data,
    method: 'post',
  })
}

/**
 * author:caoshuwen
 * date: 2021-10-20
 * description:导出商品图片补全列表
 * **/
export function exportPictureComplementList(data) {
  return request({
    url: '/api/productShootRecord/exportPictureComplementList',
    method: 'post',
    data
  })
}

/** 
 * author:caoshuwen
 * date: 2022-05-24
 * description:获取临时上传访问秘钥
 * **/
 export function getTempKey() {
  return request({
    url: '/api/file/tmp/credentail',
    method: 'post'
  })
}

/** 
 * description:上传图片补全
 * **/
 export function picComplementCos(data) {
  return request({
    url: '/api/productShootRecord/batch/picComplement/cos',
    method: 'post',
    data
  })
}
