
<template>
  <div class="task-to-be-film-container">
    <!-- 查询表单 -->
    <div class="search-form-wrap">
      <el-form :model="model" ref="refSearchForm">
        <el-row type="flex">
          <!-- 上报时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="申请时间" prop="time">
              <el-date-picker
                v-model="formData.time"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                clearable
              ></el-date-picker>
            </el-form-item>
          </el-col>

          <!-- 申请人 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="申请人">
              <el-input
                v-model="formData.applyUserName"
                placeholder="请输入申请人"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 审批流程 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="审批流程">
              <el-select
                v-model="formData.approvalProcess"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" value></el-option>
                <el-option
                  v-for="item in approvalProcessList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 领取状态 -->
          <!-- <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="领取状态">
              <el-select
                v-model="formData.receiveStatus"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" value></el-option>
                <el-option
                  v-for="item in receiveStatusList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col> -->

          <!-- 详情 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="详情">
              <el-input
                v-model="formData.details"
                placeholder="请输入详情"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 按钮 -->
          <el-col :span="6" :offset="18">
            <el-button size="medium" @click="btnResetClick">重置</el-button>
            <el-button type="primary" size="medium" @click="btnSearchClick"
              >查询</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- table -->
    <div class="table-wrap">
      <vxe-table
        border
        highlight-hover-row
        resizable
        height="100%"
        auto-resize
        size="small"
        align="center"
        :tooltip-config="{ enterable: false }"
        :loading="tableLoading"
        :data="tableData"
        :seq-config="{ startIndex: (pageNum - 1) * pageSize }"
        ref="refVxeTable"
        @sort-change="sortQueryList"
      >
        <vxe-table-column
          type="seq"
          title="序号"
          width="60"
          show-header-overflow
          show-overflow
          fixed="left"
        ></vxe-table-column>
        <vxe-table-column
          field="createTime"
          title="申请时间"
          width="120"
          show-header-overflow
          show-overflow
          sortable
        >
          <template v-slot="{ row }">
            {{ row.createTime | parseTime }}
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="applyUserName"
          title="申请人"
          min-width="120"
          show-header-overflow
          show-overflow
          sortable
        >
        </vxe-table-column>
        <vxe-table-column
          field="affiliation"
          title="所属机构"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="approvalProcess"
          title="审批流程"
          min-width="120"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">
            {{ row.approvalProcess | filterApprovalProcess }}
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="reviewStatus"
          title="审核状态"
          min-width="120"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">
            {{ row.reviewStatus | filterReviewStatus }}
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="details"
          title="详情"
          min-width="120"
          show-header-overflow
          show-overflow
          sortable
        ></vxe-table-column>
        <vxe-table-column
          field="solveNumber"
          title="催办次数"
          min-width="120"
          show-header-overflow
          show-overflow
          sortable
        ></vxe-table-column>
        <vxe-table-column
          field="lastRemindTime"
          title="最后催办时间"
          min-width="120"
          show-header-overflow
          show-overflow
          sortable
          ><template v-slot="{ row }">
            {{ row.lastRemindTime | parseTime }}
          </template></vxe-table-column
        >
        <vxe-table-column
          field="centerReviewTime"
          title="运营中心审核时间"
          min-width="120"
          show-header-overflow
          show-overflow
          sortable
          ><template v-slot="{ row }">
            {{ row.centerReviewTime | parseTime }}
          </template></vxe-table-column
        >
        <vxe-table-column
          field="applyReason"
          title="申请原因"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          title="操作"
          width="220"
          show-header-overflow
          show-overflow
          fixed="right"
        >
          <template v-slot="{ row }">
            <span
              class="btn"
              v-if="
                row.approvalProcess < 3 ||
                row.approvalProcess == 6 ||
                row.approvalProcess == 15
              "
            >
              <el-link
                :underline="false"
                type="primary"
                @click.stop="handleDetail(row)"
                >查看</el-link
              >
            </span>
            <span
              class="btn"
              v-if="
                row.receiveStatus == 1 &&
                row.showHang == 1 &&
                row.approvalProcess >= 3 &&
                row.approvalProcess != 6 &&
                row.approvalProcess != 15 &&
                row.approvalProcess != 14
              "
            >
              <el-link
                :underline="false"
                type="primary"
                @click.stop="handleHang(row)"
                >挂起</el-link
              >
            </span>
            <span class="btn" v-if="row.receiveStatus == 0">
              <el-link
                :underline="false"
                type="primary"
                @click.stop="receive(row)"
                >领取</el-link
              >
            </span>
            <span class="btn" v-else>
              <el-link
                :underline="false"
                type="primary"
                @click.stop="handleAudit(row)"
                >审核</el-link
              >
            </span>

            <span class="btn" v-if="row.changeFileUrl || row.fileUrl">
              <el-link
                :underline="false"
                type="primary"
                @click.stop="downLoadExcel(row)"
                >下载excel</el-link
              >
            </span>
            <span
              class="btn"
              v-if="
                (row.changeFileUrl || row.fileUrl) && row.approvalProcess == 14
              "
            >
              <el-link
                :underline="false"
                type="primary"
                @click.stop="lookExcel(row)"
                >查看excel</el-link
              >
            </span>
          </template>
        </vxe-table-column>
      </vxe-table>
    </div>
    <!-- 分页 -->
    <el-row class="custom-pagination-wrap">
      <el-col>
        <el-pagination
          background
          :current-page.sync="pageNum"
          :page-sizes="[20, 50, 100, 200]"
          :page-size.sync="pageSize"
          :total="total"
          layout="prev, pager, next, jumper,total, sizes"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </el-col>
    </el-row>
    <!-- 审核弹框 -->
    <el-dialog
      title="审核"
      width="500px"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisible"
      :close-on-press-escape="false"
      :show-close="false"
      @close="dialogClose"
    >
      <el-form :model="reviewForm" :rules="rules" ref="reviewForm">
        <el-form-item label="审核状态" prop="value">
          <el-radio-group v-model="reviewForm.value" @change="statuChange">
            <el-radio :label="1">通过</el-radio>
            <el-radio :label="0">不通过</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="原因" prop="reviewOpinion">
          <el-input
            type="textarea"
            placeholder="超过100的长度，不能提交"
            v-model="reviewForm.reviewOpinion"
            autocomplete="off"
            maxlength="100"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="save()">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 查看excel详情 -->
    <el-dialog
      title="查看excel"
      width="1000px"
      :close-on-click-modal="false"
      :visible.sync="excelDialogFormVisible"
      :close-on-press-escape="false"
    >
      <div class="table-wrap">
        <vxe-table
          border
          highlight-hover-row
          resizable
          height="100%"
          auto-resize
          size="small"
          align="center"
          :tooltip-config="{ enterable: false }"
          :data="excelData"
        >
          <vxe-table-column
            v-for="(item, index) in excelDataH"
            :key="index"
            :field="item"
            :title="item"
            width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
        </vxe-table>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import XLSX from "xlsx";
import { receiveStatusList } from "@/utils/config.js";
import {
  prepareList,
  worksubstitutionReceive,
  findProductCorrectionByApplyCode,
  productExcelCommonPreview,
} from "@/api/workbench.js";
import {
  review, // 审核商品
  hang,
} from "@/api/worksubstitution";
export default {
  name: "prepareList",
  components: {},
  filters: {},
  props: ['approvalProcessList'],
  data() {
    return {
      pageNum: 1,
      pageSize: 20,
      total: 0, //总条数
      sortFiled: "updateTime", // 排序查询
      sortRule: "desc", //(升序-ASC, 降序-DESC)
      formData: {
        applyUserName: "",
        approvalProcess: "",
        receiveStatus: "",
        details: "",
        time: "",
      },
      tableLoading: false,
      tableData: [],
      receiveStatusList: receiveStatusList,
      dialogFormVisible: false,
      rules: {
        value: [
          {
            required: true,
            message: "请选择审核状态",
            trigger: "change",
          },
        ],
        reviewOpinion: [
          {
            required: false,
            message: "请输入审核意见",
            trigger: "blur",
          },
        ],
      },
      reviewForm: {
        value: "",
        reviewOpinion: "",
      },
      currentInfo: {},
      excelData: [],
      excelDataH: [],
      excelDialogFormVisible: false,
    };
  },
  computed: {},
  watch: {},
  created() {
    this.searchForm();
  },
  mounted() {},
  methods: {
    /**
     * 查询table数据
     */
    async searchForm() {
      try {
        this.tableLoading = true;
        let param = Object.assign({}, this.formData);
        param.page = this.pageNum;
        param.limit = this.pageSize;
        param.startTime = param.time && param.time[0] ? param.time[0] : "";
        param.endTime = param.time && param.time[1] ? param.time[1] : "";
        param.sortList = this.sortFiled
          ? [
              {
                order: "1",
                columnName: this.sortFiled,
                lift: this.sortRule, //(升序-ASC, 降序-DESC)
              },
            ]
          : [];
        delete param.time;
        let res = await prepareList(param);
        this.tableLoading = false;
        if (!res.retCode) {
          this.tableData = res.data.list;
          this.total = res.data.total;
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      } catch (error) {
        console.error(error);
      }
    },

    /**
     * 查询按钮点击
     */
    btnSearchClick() {
      this.pageNum = 1;
      this.searchForm();
    },

    /**
     * 重置
     */
    btnResetClick() {
      // this.$refs["refSearchForm"].resetFields();
      // this.formData.createTime = "";
      this.formData = {
        applyUserName: "",
        approvalProcess: "",
        receiveStatus: "",
        details: "",
        time: "",
      };
      this.pageNum = 1;
      this.searchForm();
    },

    /**
     * pageSize 改变事件
     */
    handleSizeChange(pageSize) {
      this.pageNum = 1;
      this.pageSize = pageSize;
      this.searchForm();
    },

    /**
     * pageNum 改变事件
     */
    handleCurrentChange(currentPage) {
      this.pageNum = currentPage;
      this.searchForm();
    },

    // 排序查询
    sortQueryList({ column, property, order }) {
      if (order) {
        this.sortFiled = property;
        this.sortRule = order;
      } else {
        this.sortFiled = "updateTime";
        this.sortRule = "desc";
      }
      this.pageNum = 1;
      this.searchForm();
    },

    receive(row) {
      worksubstitutionReceive({
        applyCode: row.applyCode,
        id: row.id,
        receiveStatus: 1,
      }).then((res) => {
        if (!res.retCode) {
          this.searchForm();
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      });
    },
    handleDetail(row) {
      let productCode = "",
        { approvalProcess, productType, extenSpuCode, id, applyCode } = row;
      if (productType == 1) {
        productCode = row.spuCode;
      } else if (productType == 2) {
        productCode = row.skuCode;
      } else {
        productCode = row.sauCode;
      }
      if (approvalProcess == 2 || approvalProcess == 15) {
        try {
          parent.CreateTab(
            "../static/dist/index.html#/product/mergeProductDetail?applyCode=" +
              applyCode +
              "&id=" +
              id +
              "&productType=" +
              productType +
              "&productCode=" +
              productCode +
              "&isReview=true",
            "商品审核明细页"
          );
        } catch {
          this.$router.push({
            path: "/product/mergeProductDetail",
            query: {
              productCode: productCode,
              id: id,
              productType: productType,
              applyCode: applyCode,
              spuCode: extenSpuCode,
              isReview: true,
            },
          });
        }
      } else {
        try {
          parent.CreateTab(
            "../static/dist/index.html#/product/detailProduct?productCode=" +
              productCode +
              "&productType=" +
              productType +
              "&applyCode=" +
              applyCode +
              "&spuCode=" +
              extenSpuCode +
              "&detailType=self",
            "商品查看明细页"
          );
        } catch (error) {
          this.$router.push({
            path: "/product/detailProduct",
            query: {
              applyCode,
              productType,
              productCode,
              spuCode: extenSpuCode,
              detailType: "self",
            },
          });
        }
      }
    },

    downLoadExcel(row) {
      let link = document.createElement("a"),
        href = row.changeFileUrl ? row.changeFileUrl : row.fileUrl;
      link.href = `/api/base/download?filePath=${href}&fileName=${row.fileName}`;
      link.click();
    },

    lookExcel(row) {
      let filePath = row.changeFileUrl ? row.changeFileUrl : row.fileUrl;
      productExcelCommonPreview({ filePath }).then((res) => {
        this.file2Xce(res).then((tabJson) => {
          if (tabJson && tabJson.length > 0) {
            this.xlsxJson = tabJson;
            this.excelData = this.xlsxJson[0].sheet;
            this.excelDataH = Object.keys(this.xlsxJson[0].sheet[0]);
            this.excelDialogFormVisible = true;
          }
        });
      });
    },

    file2Xce(file) {
      return new Promise(function (resolve, reject) {
        const reader = new FileReader();
        reader.onload = function (e) {
          const data = e.target.result;
          this.wb = XLSX.read(data, {
            type: "binary",
          });
          const result = [];
          this.wb.SheetNames.forEach((sheetName) => {
            result.push({
              sheetName: sheetName,
              sheet: XLSX.utils.sheet_to_json(this.wb.Sheets[sheetName]),
            });
          });
          resolve(result);
        };
        reader.readAsBinaryString(file);
        // reader.readAsBinaryString(file) // 传统input方法
      });
    },

    handleAudit(row) {
      let {
        approvalProcess,
        id,
        lastRemindTime,
        extenSpuCode,
        reviewStatus,
        applyCode,
        productType,
        applyUserScope,
      } = row;
      if (
        approvalProcess > 2 &&
        approvalProcess != 6 &&
        approvalProcess != 12 &&
        approvalProcess != 13 &&
        approvalProcess != 15 &&
        approvalProcess != 16
      ) {
        this.currentInfo = {
          id,
          approvalProcess,
          reviewStatus,
          applyCode,
          applyUserScope,
        };
        this.dialogFormVisible = true;
      } else {
        let productCode = "",
          isReview = true;
        if (productType == 1) {
          productCode = row.spuCode;
        } else if (productType == 2) {
          productCode = row.skuCode;
        } else {
          productCode = row.sauCode;
        }
        if (approvalProcess == 13) {
          this.findProductCorrectionByApplyCode({
            applyCode,
            id,
            productType,
            applyCode,
            extenSpuCode,
            approvalProcess,
            productCode,
            spuCode: extenSpuCode,
          });
        }
        if (
          approvalProcess == 0 ||
          approvalProcess == 1 ||
          approvalProcess == -1 ||
          approvalProcess == 6 ||
          approvalProcess == 12 ||
          approvalProcess == 13
        ) {
          try {
            parent.CreateTab(
              "../static/dist/index.html#/product/reviewProduct?productCode=" +
                productCode +
                "&id=" +
                id +
                "&productType=" +
                productType +
                "&applyCode=" +
                applyCode +
                "&spuCode=" +
                extenSpuCode +
                "&detailType=self&approvalProcess=" +
                approvalProcess,
              "商品审核明细页"
            );
          } catch {
            this.$router.push({
              path: "/product/reviewProduct",
              query: {
                productCode: productCode,
                id: id,
                productType: productType,
                applyCode: applyCode,
                spuCode: extenSpuCode,
                detailType: "self",
                approvalProcess: approvalProcess,
              },
            });
          }
        } else if (approvalProcess == 2 || approvalProcess == 15) {
          try {
            parent.CreateTab(
              "../static/dist/index.html#/product/mergeProductDetail?applyCode=" +
                applyCode +
                "&id=" +
                id +
                "&productType=" +
                productType +
                "&productCode=" +
                productCode +
                "&isReview=" +
                isReview +
                "&detailType=edit",
              "商品审核明细页"
            );
          } catch {
            this.$router.push({
              path: "/product/mergeProductDetail",
              query: {
                productCode: productCode,
                id: id,
                productType: productType,
                applyCode: applyCode,
                spuCode: extenSpuCode,
                isReview: isReview,
                detailType: "edit",
              },
            });
          }
        } else if (approvalProcess == 16) {
          try {
            parent.CreateTab(
              `../static/dist/index.html#/product/mergeSpu?applyCode=${applyCode}&detailType=edit`,
              "移动商品"
            );
          } catch (error) {
            this.$router.push({
              path: "/product/mergeSpu",
              query: {
                applyCode,
                detailType: "edit",
              },
            });
          }
        }
      }
    },
    handleHang(row) {
      hang({
        id: row.id,
        reviewStatus: row.reviewStatus,
        hangStatus: 1,
      }).then((res) => {
        if (!res.retCode) {
          this.dialogFormVisible = false;
          this.searchForm();
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      });
    },
    async findProductCorrectionByApplyCode(data) {
      let res = await findProductCorrectionByApplyCode({
        applyCode: data.applyCode,
      });
      res = typeof res == "string" ? JSON.parse(res) : res;
      localStorage.setItem("coorectionData", JSON.stringify(res.data));
      try {
        parent.CreateTab(
          "../static/dist/index.html#/product/reviewProduct?type=coorection&productCode=" +
            data.productCode +
            "&id=" +
            data.id +
            "&productType=" +
            data.productType +
            "&applyCode=" +
            data.applyCode +
            "&spuCode=" +
            data.extenSpuCode +
            "&detailType=self&approvalProcess=" +
            data.approvalProcess,
          "商品审核明细页"
        );
      } catch {
        this.$router.push({
          path: "/product/reviewProduct",
          query: {
            productCode: data.productCode,
            id: data.id,
            productType: data.productType,
            applyCode: data.applyCode,
            spuCode: data.spuCode,
            detailType: "self",
            approvalProcess: data.approvalProcess,
          },
        });
      }
    },
    dialogClose() {
      this.reviewForm = {
        value: "",
        reviewOpinion: "",
      };
      this.rules.reviewOpinion[0].required = false;
      this.currentInfo = {};
    },
    statuChange(e) {
      if (e) {
        this.rules.reviewOpinion[0].required = false;
      } else {
        this.rules.reviewOpinion[0].required = true;
      }
    },
    save() {
      this.$refs.reviewForm.validate(async (valid) => {
        if (valid) {
          let res = await review({
            ...this.currentInfo,
            reviewOpinion: this.reviewForm.reviewOpinion,
            rejectStatus: this.reviewForm.value,
          });
          if (!res.retCode) {
            this.dialogFormVisible = false;
            this.searchForm();
          } else {
            this.$message({
              showClose: true,
              type: "error",
              message: res.retMsg,
            });
          }
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.task-to-be-film-container {
  /**
   * 查询表单
   */
  .search-form-wrap {
    width: 100%;
    padding-bottom: 15px;
    border-bottom: 1px dashed #e4e4eb;

    .el-row {
      flex-wrap: wrap;

      .el-col {
        display: flex;
        justify-content: flex-end;

        .el-form-item {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          width: 100%;
          margin-right: 0;
          margin-bottom: 15px;

          /deep/ {
            .el-form-item__label {
              width: 96px;
              line-height: normal;
              padding: 0 12px;
              color: #292933;
              font-weight: normal;
            }

            .el-form-item__content {
              flex: 1;

              .el-range-editor.el-input__inner {
                width: 100%;

                .el-range-separator {
                  width: 14px;
                  padding: 0;
                }
              }
            }
          }

          .el-input {
            width: 100%;
          }

          .el-select {
            width: 100%;
          }
        }
      }
    }
  }
  /**
     * 按钮组
     */
  .btn-group-wrap {
    font-size: 0;
    padding: 15px;
    .el-col {
      .el-button,
      .vxe-column-filter-container {
        margin-right: 10px;
      }
    }
  }
  /**
     * table
     */
  .table-wrap {
    width: 100%;
    height: 440px;
    padding-top: 15px;
  }
}
.btn {
  padding: 0 10px;
}
.custom-pagination-wrap{
    padding: 20px 0;
}
</style>