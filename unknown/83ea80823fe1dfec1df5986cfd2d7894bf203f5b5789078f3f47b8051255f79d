//商品功能逻辑混入对象
import spu from "@/views/product/spu";
import sku from "@/views/product/sku";
import sau from "@/views/product/sau";
import extendedAttr from "@/views/product/extendedAttr";
import extendedAttr2 from "@/views/product/extendedAttr2";
import extendedAttr3 from "@/views/product/extendedAttr3";
import approvalProcess from "@/views/product/approvalProcess";
import approvalProcessNew from "@/views/product/approvalProcessNew";
import labelAttr from "@/views/product/labelAttr"
import { findIdByOptions } from "@/utils/index.js";
export var productMixinBase = {
  components: {
    approvalProcess,
    approvalProcessNew,
    spu,
    sku,
    sau,
    extendedAttr,
    extendedAttr2,
    extendedAttr3,
    labelAttr
  },
  // 商品公共data
  data() {
    return {
      // 页面加载状态
      productLoading: true,
      // SPU,SKU,SAU,扩展属性数据对象
      spuData: {},
      skuData: [],
      sauData: [],
      extendData: {},
      // 审批申请属性数据
      approvalData: {},
    };
  },
  created() {
    // 检测页面SPU是否渲染完成
    this.$bus.$on("spuLoading", res => {
      // 关闭页面初始渲染加载层状态
      this.productLoading = false;
    });
  },
  computed: {
    urlParam: function () {
      return this.$route.query;
    },
    // 商品操作类型
    operationType: function () {
      return this.$store.getters.operationType;
    }
  },
  // 商品公共处理函数
  methods: {
    /**
     * @description:格式化和组合提交数据
     * @param {object} spu SPU数据
     * @param {array} sku sku数据
     * @param {array} sau  sau数据
     * @param {object} extend 扩展属性数据
     * @return:object submitData 组合好的商品提交数据
     */
    formatSubmitData(spu, sku, sau, extend) {
      if (this.operationType != "spuOperate") {
        if (sku[0].validity == '*') {
          sku[0].validity = 0
        };
        if (sku[0].validity == '-') {
          sku[0].validity = -1
        };
        // sau.forEach(list => {
        //   list.sauAttrList.forEach(item => {
        //     if (item.attrName == '有效期') {
        //       if (item.attrValue == '*') {
        //         item.attrValue = 0
        //       };
        //       if (item.attrValue == '-') {
        //         item.attrValue = -1
        //       };
        //     }
        //   })
        // })
      }

      // 处理高亮显示改动字段处理
      if (this.operationType == "edit" || this.operationType == "auditLevel1" || this.operationType == "auditLevel2" || (this.operationType == "RejectEdit" && this.urlParam.approvalProcess == 1)) {
        spu.productFieldRecord.skuFieldList = sku[0].changeList;
        spu.productFieldRecord.extendFieldList = extend.changeList;
        delete sku[0].changeList;
        delete extend.changeList;
      } else if (this.operationType == "spuOperate") {
        delete spu.productFieldRecord;
      } else {
        delete spu.productFieldRecord;
        if (sku[0]) {
          delete sku[0].changeList;
        }
        delete extend.changeList;
      }
      let submitData = {};
      // TYPEENUM: {
      //   EMPTY: "", //未选择
      //   GIFT: 6, //赠品
      //   NOT_MEDICINE: 5, //非药
      //   MEDICAL_INSTRUMENT: 4, //医疗器械
      //   TRADITIONAL_MEDICINE: 3, //中药
      //   GENERAL_MEDICINE: "GENERAL_MEDICINE" //普通药品
      // },
      if (this.$store.getters.spuCategory.type == "GIFT") {
        //如果为赠品
        submitData = this.formatSubmitDataForGIFT(spu, sku, []);
      } else if (this.operationType == "spuOperate") {
        submitData = spu;
        return submitData;
      } else {
        submitData = spu;
        submitData.skuList = sku;
        for (let skuItem of sku) {
          // 设置测试数据;如果是平台测试部，则通用名，商品名默认添加test
          // if (this.approvalData.applyAttribute.affiliation == "平台测试部") {
          //   if (!submitData.generalName.startsWith("(test)")) {
          //     submitData.generalName = "(test)" + submitData.generalName;
          //   }
          //   if (!skuItem.skuName.startsWith("(test)")) {
          //     skuItem.skuName = "(test)" + skuItem.skuName
          //   }
          // }

          //把SAU关联到对应的SKU
          skuItem.sauList = [];
          // for (let sauItem of sau) {
          //   skuItem.sauList.push(sauItem);
          // }
        }
      }
      // 扩展属性
      submitData.productExtend = extend;
      // console.log(submitData);
      return submitData;
    },
    /**
     * @description:赠品提交数据格式化
     * @param {object} spu SPU数据
     * @param {array} sku sku数据
     * @return:object 提交数据
     */
    formatSubmitDataForGIFT(spu, sku, sau) {
      sku[0].brand = spu.brand; //品牌商标
      sku[0].packageUnit = spu.packageUnit; // 包装单位
      sku[0].spec = spu.spec; // 规格型号
      sku[0].suggestedPrice = spu.suggestedPrice; // 建议零售
      sku[0].validity = spu.validity; // 有效期单位
      sku[0].validityUnit = spu.validityUnit; // 有效期单位
      sku[0].delegationProduct = spu.delegationProduct; // 是否委托生产
      sku[0].entrustedManufacturer = spu.entrustedManufacturer; // 委托生产厂家
      // 包装条码
      sku[0].noSmallPackageCode = spu.noSmallPackageCode;
      sku[0].smallPackageCodeList = spu.smallPackageCodeList;
      sku[0].mediumPackageCodeList = spu.mediumPackageCodeList;
      sku[0].piecePackageCodeList = spu.piecePackageCodeList;
      // 清除无用数据
      delete spu.brand;
      delete spu.packageUnit;
      delete spu.spec;
      delete spu.suggestedPrice;
      delete spu.validityUnit;
      delete spu.smallPackageCodeList;
      delete spu.mediumPackageCodeList;
      delete spu.piecePackageCodeList;
      delete spu.delegationProduct;
      delete spu.entrustedManufacturer;
      delete spu.validity;
      delete spu.noSmallPackageCode;

      // 设置测试数据;如果是平台测试部，则通用名，商品名默认添加test
      // if (this.approvalData.applyAttribute.affiliation == "平台测试部") {
      //   if (!spu.generalName.startsWith("(test)")) {
      //     spu.generalName = "(test)" + spu.generalName;
      //   }
      // }

      // 赠品默认值设置
      // 剂型
      spu.dosageForm = findIdByOptions(
        "其他",
        "dictName",
        this.$store.getters.selectOptions.dosageFormOptions
      );
      // 存储条件
      if (spu.storageCond === "") {
        spu.storageCond = findIdByOptions(
          "常温",
          "dictName",
          this.$store.getters.selectOptions.storageCondOptions
        );
      }
      if (!sku[0].validity && sku[0].validity !== 0 && sku[0].validity !== -1) {
        sku[0].validity = 0; // 有效期
      }
      sku[0].skuName = spu.generalName; // 商品名
      sku[0].sauList = sau;
      spu.skuList = sku;
      return spu;
    }
  },
  beforeDestroy() {
    this.$bus.$off();
  }
}
