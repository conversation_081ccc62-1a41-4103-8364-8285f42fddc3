<template>
  <div class="component-container">
    <approval-process-new :approvalData="approvalData1" :isAdd="true"></approval-process-new>
    <approval-process :approvalData="approvalData" :isAdd="true"></approval-process>
    <spu
      ref="spu"
      @isShowBtn="isShowBtn"
      :spuData="spuData"
      :skuData="skuData"
      @reuseSpu="reuseSpu"
    ></spu>
    <sku ref="sku" :skuData="skuData" :loadForm="false"></sku>
    <label-attr ref="labelAttr" :skuData="skuData" :spuData="spuData" :loadForm="false"
    :showEmpty="spuCategory.type == 'GENERAL_MEDICINE' || spuCategory.type == 'TRADITIONAL_MEDICINE' ? false : true"></label-attr>
    <extended-attr ref="extend" v-if="spuCategory.type == 'GENERAL_MEDICINE'"></extended-attr>
    <extended-attr2 ref="extend" v-if="spuCategory.type == 'TRADITIONAL_MEDICINE'"></extended-attr2>
    <extended-attr3 ref="extend" v-if="spuCategory.type == 'MEDICAL_INSTRUMENT'"></extended-attr3>
    <el-row class="bottom-btns">
      <el-col :span="24" class="bottom-btn-wrap">
        <el-button
          type="primary"
          v-if="operationType == 'update'"
          @click="productReject"
          >驳回</el-button
        >
        <el-button
          type="primary"
          v-if="operationType == 'present' && urlParam.uniqueCode"
          @click="() => (dialogVisibleForPresent = true)"
          >驳回</el-button
        >
        <!-- 实施提报暂时不显示保存草稿 -->
        <el-button v-show="showDraftBtn" @click="submit('draft')"
          >保存草稿</el-button
        >
        <el-button :disabled="banSubmmit" type="primary" @click="submit()">提交商品信息</el-button>
        <!-- <el-button
          type="primary"
          v-show="
            $store.getters.spuCategory.type !== 'GENERAL_MEDICINE' &&
            $store.getters.spuCategory.type !== 'MEDICAL_INSTRUMENT' &&
            !hiddenBtn
          "
          @click="submit(false, 'checkSpu')"
          >spu唯一性校验</el-button
        > -->
      </el-col>
    </el-row>
    <el-dialog title="提示" :visible.sync="dialogVisible" width="30%">
      <el-form ref="form" :model="form" label-width="80px">
        <el-form-item label="驳回原因">
          <el-select
            v-model="rejectState"
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option
              v-for="item in rejectOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="productRejectUpdate(2, rejectState)"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      title="新品上报驳回"
      :visible.sync="dialogVisibleForPresent"
      width="80%"
    >
      <el-form
        ref="formPresent"
        :model="formPresent"
        :rules="rulesPresent"
        label-width="100px"
      >
        <!-- <el-form-item label="驳回原因" prop="statusCode">
          <el-select
            v-model="formPresent.statusCode"
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option label="提报的商品已有标准库信息" :value="2"></el-option>
            <el-option
              label="商品信息不完整，请实施修改后提报"
              :value="3"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="标准库ID"
          v-show="rulesPresent.productId[0].required"
          prop="productId"
        >
          <el-input
            v-model="formPresent.productId"
            placeholder="请输入标准库ID"
          ></el-input>
        </el-form-item> -->
        <p>商品已存在:</p>
          <el-checkbox-group v-model="checkList1" style="width:50%">
            <el-checkbox label="商品已存在，提报的商品已有标准库信息——标准库ID">提报的商品已有标准库信息</el-checkbox>
            <el-input
            :disabled="checkList1.length === 0 ? true : false"
            style="width:200px;margin-left:40px"
            v-model="formPresent.productId"
            placeholder="请输入标准库ID"
          ></el-input>
          </el-checkbox-group>
          <p>商品信息不完整，请实施修改后提报:</p>
          <el-checkbox-group v-model="checkList2">
            <el-checkbox label="商品信息不完整，请实施修改后提报——没有批文">没有批文</el-checkbox>
            <el-checkbox label="商品信息不完整，请实施修改后提报——没有条码">没有条码</el-checkbox>
            <el-checkbox label="商品信息不完整，请实施修改后提报——规格型号不完整">规格型号不完整</el-checkbox>
          </el-checkbox-group>
          <p>商品信息错误:</p>
          <el-checkbox-group v-model="checkList2">
            <el-checkbox label="商品信息错误——品牌与条码网不符">品牌与条码网不符</el-checkbox>
            <el-checkbox label="商品信息错误——通用名与药监网不符">通用名与药监网不符</el-checkbox>
            <el-checkbox label="商品信息错误——生产厂家与药监网不符">生产厂家与药监网不符</el-checkbox>
            <el-checkbox label="商品信息错误——通用名与条码网不符">通用名与条码网不符</el-checkbox>
          </el-checkbox-group>
          <el-input
          style="margin-top:20px"
            v-model="formPresent.customReason"
            placeholder="请输入自定义驳回原因"
          ></el-input>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleForPresent = false">取 消</el-button>
        <el-button type="primary" @click="presentHandle">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// 商品基础共用信息混入对象
import { productMixinBase } from "@/mixin/productMixinBase.js";
import {
  getProductData, //获取商品信息
  getGoodsDataDetail, // 同步待提交数据
  productAdd, // 商品添加
  getApplyInfo, //审批流信息
  productRejectUpdate, //同步待提交商品状态
  presentReject, //新品上报商品驳回
  checkProductId,
  checkSpuSingleApi, //检查SPU唯一性
  productPresentDetail, // 新品上报详情
} from "@/api/product";
import { newGetAddProcessData } from "@/api/workManage"
import { getCustomerProductReportProduct } from "@/api/follow";

export default {
  name: "",
  mixins: [productMixinBase],
  watch: {
    "formPresent.statusCode": function (newValue, oldValue) {
      if (newValue == 2) {
        this.rulesPresent.productId[0].required = true;
      } else {
        this.rulesPresent.productId[0].required = false;
      }
    },
    checkList1(e) {
      if(e.length !== 0) {
        this.checkList2 = []
        this.formPresent.customReason = ""
        this.formPresent.statusCode = 2
      }
    },
    checkList2(e) {
      if(e.length !== 0) {
        this.checkList1 = []
        this.formPresent.productId = ''
        this.formPresent.statusCode = 3
      }
    },
    'formPresent.customReason'(e){
      if(e || e === 0){
        this.checkList1 = []
        this.formPresent.productId = ''
        this.formPresent.statusCode = 3
      }
    }
  },
  data() {
    return {
      banSubmmit: false, //防抖节流标识
      approvalData1: {},
      checkList1: [],
      checkList2: [],
      hiddenBtn: false,
      dialogVisibleForPresent: false,
      dialogVisible: false,
      // 实时提报时，驳回原因选项
      rejectOptions: [
        {
          value: 3,
          label: "商品信息不完整，请实施修改后提报",
        },
        {
          value: 2,
          label: "商品无需匹配标准库",
        },
        {
          value: 4,
          label: "提报的商品已有标准库信息",
        },
      ],
      rejectState: 3,
      formPresent: {
        productId: "",
        statusCode: "",
        customReason: ""
      },
      rulesPresent: {
        productId: [
          { required: false, message: "请输入正确的商品ID", trigger: "change" },
        ],
        statusCode: [
          { required: true, message: "请选择驳回原因", trigger: "change" },
        ],
      },
    };
  },
  computed: {
    showDraftBtn: function () {
      // 同步待提交 || 新品上报不显示草稿保存
      if (
        this.urlParam.source == "implement" ||
        this.urlParam.type == "present"
      ) {
        return false;
      } else {
        return true;
      }
    },
    spuCategory() {
      return this.$store.state.product.spuCategory;
    },
  },
  async created() {
    // 设置商品操作类型为新增
    this.$store.commit("product/SET_OPERATION_TYPE", "add");
    // 新品上报
    await this.presentProduct();
    // 同步待跟进处理
    await this.updatePorduct();
    // 获取审批流信息
    await this.getApplyInfo();
    // 检测页面数据值修改变化
    this.$bus.$on("productChange", (res) => {
      this.editState = res;
    });
  },
  methods: {
    /**
     * @description:同步待提交
     * @param {string} spucode 复用的spucode
     * @return:
     */
    async updatePorduct() {
      if (this.urlParam.type != "followup") {
        // 判断是否为同步待跟进数据
        return false;
      }
      this.productLoading = true;
      // 设置商品操作类型为 同步待提交
      this.$store.commit("product/SET_OPERATION_TYPE", "update");
      let productData = await getGoodsDataDetail({ id: this.urlParam.id });
      this.spuData = Object.freeze(productData.data.spu);
      this.skuData = Object.freeze(productData.data.sku);
      // 同步待提交是默认有数据的，隐藏可以直接保存草稿
      this.editState = true;
    },
    /**
     * @description: 新品上报数据处理
     * @param {type}
     * @return {type}
     */
    async presentProduct() {
      if (this.urlParam.type != "present") {
        // 判断是否为新品上报
        return false;
      }
      this.productLoading = true;
      // 设置商品操作类型为新品上报
      this.$store.commit("product/SET_OPERATION_TYPE", "present");
      let res = {};
      if (this.urlParam.uniqueCode) {
        res = await productPresentDetail({
          uniqueCode: this.urlParam.uniqueCode,
        });
      } else {
        res = await getCustomerProductReportProduct({
          applyCode: this.urlParam.applyCode,
        });
      }

      if (res.success) {
        let productData = res.data;
        this._presentOrigin = productData.productSource;
        this.spuData = {
          ...productData,
          // 税率
          approvalImgList: productData.approvalImgList
            ? productData.approvalImgList
            : [], //批件图片
          inRate: Number(productData.inRate),
          outRate: Number(productData.outRate),
        };
        this.$store.getters.selectOptions.packageUnitOptions.forEach((item) => {
          if (item.dictName == productData.packageUnitName) {
            productData.packageUnitName = item.id;
            return;
          }
        });
        if (typeof productData.packageUnitName == "string") {
          productData.packageUnitName = "";
        }
        this.skuData = [
          {
            skuName: productData.productName, // 商品名
            standardCodes: productData.standardCodes
              ? productData.standardCodes
              : "", //本位码
            spec: productData.spec,
            packageUnit: productData.packageUnitName, // 包装单位-文本类型需要转换
            prescriptionCategory: productData.prescriptionCategory,
            prescriptionCategoryName: productData.prescriptionCategoryName,
            smallPackageCodeList: productData.smallPackageCode && productData.smallPackageCode !== '-'
              ? [productData.smallPackageCode]
              : [],
            brand: productData.brand,
            qualityStandard: productData.qualityStandard,
            validity: productData.validity, // number
            outPackageImgList: productData.outPackageImgList
              ? productData.outPackageImgList
              : [],
            directionImgList: productData.directionImgList
              ? productData.directionImgList
              : [],
            piecePackageCodeList: productData.piecePackageCode
              ? [productData.piecePackageCode]
              : [], //件包装条码
            mediumPackageCodeList: productData.mediumPackageCode
              ? [productData.mediumPackageCode]
              : [], //中包装条码
            delegationProduct: productData.delegationProduct ? productData.delegationProduct : 0, // 是否委托生产
            noSmallPackageCode: productData.noSmallPackageCode == 1 ? 1 : 0, // 无小包装条码
            originPlace: productData.originPlace, // 产地
            entrustedManufacturer: productData.entrustedManufacturerName,//受托生产厂家
            di:productData.di ? productData.id : ''//DI码
          },
        ];
        this.sauData = [
          {
            outPackageImgList: productData.outPackageImgList
              ? productData.outPackageImgList
              : [],
            directionImgList: productData.directionImgList
              ? productData.directionImgList
              : [],
          },
        ];
        if (productData.sku) {
          this.skuData = [...this.skuData, ...productData.sku];
        }
        if (productData.sau) {
          this.sauData = [...this.sauData, ...productData.sau];
        }
        // 同步待提交是默认有数据的，隐藏可以直接保存草稿
        this.editState = true;
      } else {
        this.$message.error(res.retMsg);
      }
    },

    /**
     * @description:spu复用
     * @param {string} spucode 复用的spucode
     * @return:
     */
    async reuseSpu(spuCode) {
      // console.log(spucode);
      this.productLoading = true;
      // 设置商品操作类型为复用
      this.$store.commit("product/SET_OPERATION_TYPE", "reuse");
      // 查询下属所有
      let spuInfo = await getProductData({ spuCode }, "all");
      // console.log(spuInfo);
      this.spuData = Object.freeze(spuInfo.data.spu);
      this.skuData = Object.freeze(spuInfo.data.sku);
      // this.sauData = Object.freeze(spuInfo.data.sau);
    },
    /**
     * @description:商品数据提交
     * @param {string} isDraft 存在，保存草稿，不存在，提交
     * @param {string} checkSpuSingle 是否为检查SPU唯一触发
     * @return:
     */
    async submit(isDraft, checkSpuSingle) {
      if (!this.editState && isDraft) {
        this.$message.error("请至少维护一个字段，再保存草稿");
        return false;
      }
      let spu = await this.$refs.spu.getSpuData(isDraft);
      let spuData = spu.data;
      let skuData = this.$refs.sku.getSkuData(isDraft);
      let extendData = {};
      if (this.spuCategory.type == "GENERAL_MEDICINE" || this.spuCategory.type == "TRADITIONAL_MEDICINE" || this.spuCategory.type == "MEDICAL_INSTRUMENT") {
        extendData = this.$refs.extend.getExtendData(isDraft);
      }
      if (!spu.state || !skuData || !extendData) {
        // 未通过 SPU表单校验
        return;
      }
      if(skuData[0].smallPackageCodeList && skuData[0].smallPackageCodeList.length > 1) {
        this.$message.error('一个商品最多包含1个小包装条码！')
        return
      } else if(skuData[0].mediumPackageCodeList && skuData[0].mediumPackageCodeList.length > 1) {
        this.$message.error('一个商品最多包含1个中包装条码！')
        return
      } else if(skuData[0].piecePackageCodeList && skuData[0].piecePackageCodeList.length > 1) {
        this.$message.error('一个商品最多包含1个件包装条码！')
        return
      }
      this.banSubmmit = true
      // 验证spu唯一性
      if ((spuData.spuCategory == 3 || spuData.spuCategory == 4) && !isDraft) {
        this.productLoading = true;
        let res = await checkSpuSingleApi(spuData);
        this.banSubmmit = false
        this.productLoading = false;
        if (!res.success) {
          this.$message.error(res.retMsg);
          return false;
        }
      }
      if (checkSpuSingle) {
        this.productLoading = true;
        let res = await checkSpuSingleApi(spuData);
        this.banSubmmit = false
        if (res.success) {
          this.$message.success("spu唯一性校验通过");
        } else {
          this.$message.error(res.retMsg);
        }
        this.productLoading = false;
        return false;
      }
      // 设置新增时的商品来源
      this.setProductSpurce(spuData, skuData);
      // formatSubmitData 函数在 productMixinBase.js中定义
      let submitData = this.formatSubmitData(
        _.cloneDeep(spuData),
        _.cloneDeep(skuData),
        [],
        _.cloneDeep(extendData)
      );
      //操作类型,
      // 0:保存草稿,
      // 1:新增spu,
      // 2:属性修改,
      // 3:商品合并,
      // 4:商品停用,
      // 5:三方同步,
      // 6:批量修改,
      // 7:草稿删除,
      // 8:预首营修改)
      if (isDraft) {
        //保存草稿
        submitData.operType = 0;
      } else {
        //新增
        submitData.operType = 1;
      }
      this.productLoading = true;
      // 如果是实施提报，携带SAAS saasUniqueCode
      if (this.urlParam.source && this.urlParam.source == "implement") {
        submitData.saasUniqueCode = this.skuData[0].saasUniqueCode;
      }
      // 如果是新品上报，携带uniqueCode
      if (this.urlParam.type && this.urlParam.type == "present") {
        submitData.uniqueCode = this.urlParam.uniqueCode;
      }
      if (this.urlParam.applyCode) {
        submitData.applyCode = this.urlParam.applyCode;
      }
      let res = await productAdd(submitData);
      this.banSubmmit = false
      // 更新同步待跟进数据状态
      if (this.urlParam.type == "followup") {
        await this.productRejectUpdate(3);
      }
      this.productLoading = false;
      if (res.success) {
        this.$message.success("操作成功");
        // 如果是新品上报则返回至新品上报列表
        if (this.urlParam.type == "present") {
          parent.CreateTab(
            "../static/dist/index.html#/list/newProductSubmit",
            "新品上报",
            true
          );
        } else {
          parent.CreateTab(
            "../static/dist/index.html#/product/productList",
            "商品列表",
            true
          );
        }
        parent.CloseTab("../static/dist/index.html#/product/addProduct"); // 关闭当前页面
      } else {
        this.banSubmmit = false
        this.$message.error(res.retMsg);
      }
    },
    /**
     * @description: 设置新增商品的商品来源：实施提报 || 新品上报
     * @param {object} spu SPU数据
     * @param {array} sku sku数据
     * @param {array} sau  sau数据
     * @return:
     */
    setProductSpurce(spu, sku, sau) {
      // 实施提报处理
      if (this.urlParam.source && this.urlParam.source == "implement") {
        this.setOrigin(spu, sku, sau, "实施提报");
      }
      // 新品上报处理
      if (this.urlParam.type && this.urlParam.type == "present") {
        this.setOrigin(spu, sku, sau, this._presentOrigin);
      }
    },
    /**
     * @description: 设置新增商品的商品来源：实施提报 || 新品上报
     * @param {object} spu SPU数据
     * @param {array} sku sku数据
     * @param {array} sau  sau数据
     * @param {string} origin  来源
     * @return:
     */
    setOrigin(spu, sku, sau, origin) {
      // spu 增加来源标识
      spu.spuSource = origin;
      for (let skuItem of sku) {
        if (!skuItem.skuCode || !skuItem.productId) {
          // 新增的SKU增加来源标识
          skuItem.skuSource = origin;
        }
      }
      for (let sauItem of sau) {
        if (!sauItem.skuCode || !sauItem.productId || !sauItem.sauCode) {
          // 新增的Sau增加来源标识
          sauItem.sauSource = origin;
        }
      }
    },
    async getApplyInfo() {
      try {
        let res1 = await newGetAddProcessData({procKey: "meProductAdd"});
        if (res1.success) {
          this.approvalData1 = res1.data;
        }
      } catch (error) {}
      let param = {
        selectType: 1, // 查询类型, (0:草稿, 1:新增, 2:属性修改,3:审核或驳回修改,4:查询详情)
        applyCode: "", //单据编号
        productCode: "", //商品编码
        productType: 1, //商品类型(1-SPU;2-SKU;3-SAU)
        spuCode: "", //SPU编码
      };
      let res = await getApplyInfo(param);
      // this.productLoading = false;
      if (res.success) {
        this.approvalData = res.data;
        if (this.urlParam.source && this.urlParam.source == "implement") {
          this.approvalData.applyAttribute.productSource = "实施提报";
        }
        // 新品上报
        if (this.urlParam.type && this.urlParam.type == "present") {
          this.approvalData.applyAttribute.productSource = this._presentOrigin;
        }
      } else {
        this.$message.error(res.retMsg);
      }
    },
    // 同步待跟进驳回
    productReject() {
      if (this.urlParam.source == "implement") {
        this.dialogVisible = true;
      } else {
        productRejectUpdate(2);
      }
    },
    /**
     * @description: 新品上报表单处理
     * @param {type}
     * @return {type}
     */
    presentHandle() {
      // console.log(this.checkList2)
      // return
      this.productRejectforPresent();
      // this.$refs.formPresent.validate((valid) => {
      //   if (valid) {
      //     this.productRejectforPresent();
      //   } else {
      //     console.log("error submit!!");
      //   }
      // });
    },
    /**
     * @description: 新品上报驳回
     */
    async productRejectforPresent() {
      try {
        if (this.formPresent.statusCode == 2) {
          let checkProduct = await checkProductId({
            productId: this.formPresent.productId,
          });
          if (!checkProduct.success) {
            this.$message.error(checkProduct.retMsg);
            return;
          }
        }
        // console.log(this.formPresent.statusCode)
        // return
        let statusMsg = ''
        if(this.formPresent.statusCode === 2) {
          statusMsg = this.checkList1.join()
        } else if(this.formPresent.statusCode === 3) {
          if(!this.formPresent.customReason && this.formPresent.customReason !== 0) {
            statusMsg = this.checkList2.join()
          } else {
            let temp = [...this.checkList2]
            temp.push(this.formPresent.customReason)
            statusMsg = temp.join()
          }
        }
        if(!this.formPresent.customReason && this.formPresent.customReason!==0 && this.checkList2.length === 0 && !this.formPresent.productId) {
          this.$message.error('请选择驳回原因')
          return
        }
        let res = await presentReject({
          uniqueCode: this.urlParam.uniqueCode,
          statusCode: this.formPresent.statusCode,
          statusMsg,
          productId: this.formPresent.productId,
        });
        if (res.success) {
          parent.CreateTab(
            "../static/dist/index.html#/list/newProductSubmit",
            "新品上报",
            true
          );
          parent.CloseTab("../static/dist/index.html#/product/addProduct");
        }
      } catch (error) {
        console.error(error);
      }
    },
    /**
     * @description: 更新同步待跟进商品状态
     * @param {type} status 2 关闭， 3 更新
     * @param {type} rejectReason 驳回原因
     * @return:
     */
    // 同步待提交商品状态更新
    async productRejectUpdate(status, rejectReason) {
      this.productLoading = true;
      let res = await productRejectUpdate({
        id: Number(this.urlParam.id),
        statusCode: status,
        reviewOpinion: rejectReason ? rejectReason : "",
      });
      this.productLoading = false;
      if (res && res.retCode == 0 && res.data) {
        parent.CloseTab("../static/dist/index.html#/product/addProduct");
      } else {
        this.$message.error(res.retMsg);
      }
    },
    // isShowBtn(businessScopeList, spuCategory, id) {
    //   if (spuCategory == 4) {
    //      this.hiddenBtn = true;
    //   } else {
    //     this.hiddenBtn = false;
    //   }
    // },
    isShowBtn(businessScopeList, spuCategory, id) {
      if (spuCategory == 4) {
        let flag = false;
        businessScopeList.forEach((item) => {
          item.forEach((list) => {
            if (list == id) {
              flag = true;
              return;
            }
          });
          if (flag) {
            return;
          }
        });
        if (flag) {
          this.hiddenBtn = true;
        } else {
          this.hiddenBtn = false;
        }
      } else {
        this.hiddenBtn = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.component-container {
  padding-top: 0px;
  .loading {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
  }
  /deep/ .el-tabs__header {
    border-bottom: none;
    background-color: #f2f2f2;
  }
  /deep/.el-tabs__content {
    padding: 0 !important;
  }
  .el-tabs--border-card {
    border: none;
    box-shadow: none;
    /deep/ .el-tabs__content {
      padding-bottom: 80px;
      padding-left: 0;
      padding-right: 0;
    }
  }
  .bottom-btns {
    background: #f0f2f5;
    width: 100%;
    position: fixed;
    bottom: 0;
    padding: 15px;
    z-index: 10;
  }
}
.bottom-btn-wrap {
  display: flex;
  justify-content: center;
}
</style>
