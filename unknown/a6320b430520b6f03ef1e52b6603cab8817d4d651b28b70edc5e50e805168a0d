import request from '@/utils/request'
import qs from "qs" // 请求数据 转成form data 形式

// 图片报表获取所有图片任务来源
export function getAllPictureSource(params) {
    return request({
        url: '/api/reportCompletion/find/getAllPictureSource',
        params,
        method: 'GET',
    })
}

// 图片报表获取所有自营类型
export function getAllBusinessType(params) {
    return request({
        url: '/api/reportCompletion/find/getAllBusinessType',
        params,
        method: 'GET',
    })
}

// 图片报表获取任务列表
export function getTaskBoardList(data) {
    return request({
        url: '/api/reportCompletion/find/pageList',
        data,
        method: 'POST',
    })
}

// 图片报表导出列表
export function exportTaskBoardList(data) {
    return request({
        url: '/api/reportCompletion/find/exportInfo',
        data,
        method: 'post',
    })
}

/** 
 * author:caoshuwen
 * date: 2021-09-23
 * description:批量查询与导出-操作记录列表查询
 * **/
export function batchModifyRecordList(data) {
    return request({
      url: '/api/modifyRecord/find/batchModifyRecordList',
      method: 'post',
      data
    })
}

/** 
 * author:caoshuwen
 * date: 2021-09-23
 * description:下载导出文件后导出次数+1
 * **/
export function exportCount(data) {
    return request({
      url: `/api/modifyRecord/update/exportCount?id=${data}`,
      method: 'post',
      data
    })
}