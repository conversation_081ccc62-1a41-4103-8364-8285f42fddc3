import request from '@/utils/request'
import qs from "qs" // 请求数据 转成form data 形式

/**
 * 字典模糊搜索
 */
export function dictSearchTypeAndName (data) {
    return request({
        url: '/api/dict/search/typeAndName',
        method: 'post',
        data
    })
}

/**
 * 字典查询
 */
export function getTotalDictionaryTree (data) {
    return request({
        url: '/api/dict/get/getTotalDictionaryTree',
        method: 'post',
        data
    })
}
/**
 * 字典查询
 */
export function dictListSearch (data) {
    return request({
        url: '/api/dict/findCache/dictList',
        method: 'post',
        data
    })
}

/**
 * 字典查询
 */
export function findDictList (data) {
    return request({
        url: '/api/dict/find/dictList',
        method: 'post',
        data
    })
}

/**
 * 字典导出
 */
export function dictExport (data) {
    return request({
        url: '/api/dict/export',
        method: 'post',
        data,
    })
}

/**
 * 分类选项
 */
export function categoryList (data) {
    return request({
        url: '/api/category/find/categoryList',
        method: 'post',
        data
    })
}

/**
 * 六级分类选项
 */
export function searchSixCategory (data) {
    return request({
        url: '/api/category/search/searchSixCategory',
        method: 'post',
        data: qs.stringify(data)
    })
}


/**
 * 字典修改记录
 */
export function dictionaryRecordList (data) {
    return request({
        url: '/api/dict/find/dictionaryRecordList',
        method: 'post',
        data
    })
}



/**
 * 保存字典
 */
export function saveDict (data) {
    return request({
        url: '/api/dict/save/dict',
        method: 'post',
        data
    })
}


/***中药字典拓展****/

/**
 * 商品大类
 * **/
export function apiGetCategoryDict (data) {
    return request({
        url: '/api/category/ext/getCategoryDict',
        method: 'post',
        data
    })
}


/**
 * 字典值查询 type 1:扩展属性 2:扩展值',
 * **/
export function apiSelectDictList (data) {
    return request({
        url: '/api/category/ext/selectDictList',
        method: 'post',
        data
    })
}

/**
 * 首页查询
 * **/
export function apiQueryList (data) {
    return request({
        url: '/api/category/ext/detail/queryList',
        method: 'post',
        data
    })
}

/**
 * 修改日志查询
 * **/
export function apiQueryLogList (data) {
    return request({
        url: '/api/category/ext/detail/queryLogList',
        method: 'post',
        data
    })
}

/**
 * 扩展值配置通用名查询
 * **/
export function apiGetSpuGeneralNameList (data) {
    return request({
        url: '/api/spu/find/spuGeneralNameList',
        method: 'post',
        data
    })
}

/**
 * 扩展字段配置页查询
 * **/
export function apiGetQueryExtList (data) {
    return request({
        url: '/api/category/ext/queryExtList',
        method: 'post',
        data
    })
}

/**
 * 扩展字段修改状态
 * **/
export function apiExtUpdateStatus (data) {
    return request({
        url: '/api/category/ext/updateStatus',
        method: 'post',
        data
    })
}

/**
 * 新增扩展属性
 * **/
export function apiExtSaveItem (data) {
    return request({
        url: '/api/category/ext/save',
        method: 'post',
        data
    })
}

/**
 * 扩展值查询
 * **/
export function apiExtQueryDetailList (data) {
    return request({
        url: '/api/category/ext/detail/queryDetailList',
        method: 'post',
        data
    })
}

/**
 * 扩展值修改状态
 * **/
export function apiExtDetailUpdateStatus (data) {
    return request({
        url: '/api/category/ext/detail/updateStatus',
        method: 'post',
        data
    })
}

/**
 * 
 * 新增扩展值
*/
export function apiExtDetailSaveItem (data) {
    return request({
        url: '/api/category/ext/detail/save',
        method: 'post',
        data
    })
}


/**
 * 
 * 新增扩展值
*/
export function apiExtDetailItemUpdate (data) {
    return request({
        url: '/api/category/ext/detail/update',
        method: 'post',
        data
    })
}


