<template>
  <div class="container">
    <draggable class="drag-wrap" v-model="draggableData">
      <!-- <el-row style="margin-bottom: 20px" v-if="morePicList.length">
        <el-card style="height:200px;overflow-y: auto;overflow-x: hidden;">
          <div class="title">备选图片</div>
          <draggable class="img-wrap" :list="morePics" :group="{ name: 'row' }">
            <el-col :span="4" v-for="(item, index) in morePics" :key="index">
              <el-card style="height:150px">
                <img
                style="width:150px;height:150px;"
                  @click="previewImg(index, -1)"
                  :src="item.pictureUrl"
                  alt=""
                />
              </el-card>
            </el-col>
          </draggable>
        </el-card>
      </el-row> -->
      <!-- 友商图片拖拽框 -->
      <el-row style="margin-bottom: 20px" v-if="friendPhotoList.length">
        <el-card style="height:220px;overflow-y: auto;overflow-x: hidden;">
          <el-tabs v-model="tabName" type="card">
            <el-tab-pane v-for="(item, index) in friendPhotoList" :key="index" :label="item.sourceSiteName+'('+item.friendLibraryPictureDtos.length+')'" :name="index">
              <draggable class="img-wrap" :list="item.friendLibraryPictureDtos" :group="{ name: 'row' }">
                <el-col :span="4" v-for="(item1, index1) in item.friendLibraryPictureDtos" :key="index1">
                  <el-card style="height:150px">
                    <img
                    style="width:150px;height:150px;"
                      @click="previewImg(index1, -2, index)"
                      :src="item1.pictureUrl"
                      alt=""
                    />
                  </el-card>
                </el-col>
              </draggable>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-row>
      <el-row style="margin-bottom: 20px" :gutter="10"
        ><el-col
          :span="8"
          v-for="(row, parIndex) in draggableData"
          :key="parIndex"
        >
          <el-card>
            <div class="title">{{ row.title }}</div>
            <draggable
              class="img-wrap"
              :list="row.imgList"
              :group="{ name: 'row' }"
            >
              <el-col
                :span="24"
                v-for="(item, index) in row.imgList"
                :key="index"
              >
                <el-card :class="item.deleteStatus?'is-delete-bg':''">
                  <span
                    v-if="item.deleteStatus == 0"
                    @click="deleteImg(item)"
                    class="delete-btn"
                    >删除</span
                  >
                  <span
                    v-if="item.deleteStatus == 1"
                    class="delete-btn cancel"
                    @click="deleteImg(item)"
                    >取消删除</span
                  >
                  <img
                    v-if ="item.dealOperation && item.dealOperation.dealSrc"
                    @click="previewImg(index, parIndex)"
                    :src="item.dealOperation.dealSrc"
                    alt=""
                  />
                  <img
                    v-else
                    @click="previewImg(index, parIndex)"
                    :src="item.pictureUrl"
                    alt=""
                  />
                  <div class="img-box">
                    <span>{{ item.pictureName }}</span>
                    <p v-if="finishingAudit">{{ ['','原精修图版本','上一级人员添加','友商库添加'][item.replacePictureSource] }}</p>
                    <p v-if="finishingAudit && item.isToFinishing" :class="item.deleteStatus?'is-delete':'no-delete'">转精修</p>
                    <br>
                    <span v-if="item.dealOperation && item.dealOperation.saveStatus" :class="item.deleteStatus == 0 ? 'bg1' : 'bg2'">{{ item.dealOperation.saveStatus ? ['','线下修图','已保存','已保存（不裁剪）'][item.dealOperation.saveStatus] : '' }}</span>
                  </div>
                </el-card>
              </el-col>
            </draggable>
          </el-card>
        </el-col>
      </el-row>
      <!-- 一审 上传图片 -->
      <el-row style="margin-bottom: 20px" v-if="finishingDeal">
        <el-form>
          <el-form-item label="上传图片">
            <el-upload
              class="upload-wrapper"
              ref="refUpload"
              :action="no"
              :data="disposeForm"
              :multiple="true"
              accept=".jpg,.jpeg,.png,.gif,.bmp,.JPG,.JPEG,.PBG,.GIF,.BMP"
              :show-file-list="false"
              :auto-upload="false"
              :on-change="uploadImage"
            >
            <el-button
              style="width:240px"
              type="primary"
              size="medium"
              >上传图片
              <el-popover
                  placement="top-start"
                  title="提示"
                  width="300"
                  trigger="hover"
                >
                  <span class="popover-content">
<!--                    1、图片放在文件夹中，将文件夹压缩成ZIP格式后上传。<br />-->
                    图片支持jpg、png、jpeg、bmp格式，不支持pdf或其他非图片的文件格式，可同时上传多张。<br />
                    <!-- 3、图片名称格式：商品编码+“-”+数字。 -->
                  </span>
                  <i
                    class="el-icon-question el-icon--right icon-question"
                    slot="reference"
                  ></i>
                </el-popover>
              </el-button>
            </el-upload>
          </el-form-item>
        </el-form>
      </el-row>
      <el-row style="margin-bottom: 20px" v-if="productPictureBase64List.length">
        <el-card style="height:200px;overflow-y: auto;overflow-x: hidden;">
          <draggable class="img-wrap" :list="productPictureBase64List" :group="{ name: 'row' }">
            <el-col :span="4" v-for="(item, index) in productPictureBase64List" :key="index">
              <el-card class="upload-card" style="height:150px">
                <img
                style="width:140px;height:140px;"
                  :src="item.pictureUrl"
                  alt=""
                />
                <!-- <span>{{ item.pictureName }}</span> -->
              </el-card>
            </el-col>
          </draggable>
        </el-card>
        <p style="color:red">上传图片后，请将图片拖动到对应位置，否则上传的图片无法保存！</p>
      </el-row>
    </draggable>
    <img-edit v-if="needDeal===true" ref="previewImg" :productInfo="productInfo" @updateImgList="updateImgList"></img-edit>
    <preview-img v-else ref="previewImg" :productInfo="productInfo" :finishingAudit="finishingAudit"></preview-img>
  </div>
</template>
<script>
import draggable from "vuedraggable";
import previewImg from "@/components/uploadImg/previewImg";
import imgEdit from "@/components/imgEdit"
import { getTempKey } from "@/api/follow.js"
const JSZip = require("jszip");
var COS = require("cos-js-sdk-v5")
export default {
  components: {
    draggable,
    previewImg,
    imgEdit
  },
  props: {
    imgList:{
      type: Array,
      default: ()=> {
        return []
      }
    },
    productInfo:{
      type: Array,
      default: ()=> {
        return []
      }
    },
    // 更多图片列表
    morePicList:{
      type: Array,
      default: ()=> {
        return []
      }
    },
    needDeal:{
      type: Boolean,
      default: false
    },
    // 友商库图片列表
    friendPhotoList:{
      type: Array,
      default: ()=> {
        return []
      }
    },
    // 是否已申请商品列表
    applyProductDispose:{
      type: Boolean,
      default: false
    },
    // 是否一审
    finishingDeal:{
      type: Boolean,
      default: false
    },
    // 是否二审
    finishingAudit:{
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tempKey: {},
      cos: null,
      productPictureBase64List:[],//上传图片列表
      disposeForm: {
        usePictureStatus: "",
        pictureQualityStatus: 1,
        fileName: "",
        type: [],
        productRecognizeMethod: 1,
      },
      tabName:0,
      draggableData: [],
      preImgList: [],
      preIndex: 0,
      morePics: [],
    };
  },
  watch: {
    imgList(val) {
      this.filterImgList(val);
    },
    morePicList(val) {
      this.morePics = JSON.parse(JSON.stringify(val));
    },
    morePics(val) {
      this.$emit('changeNum',val.length)
    }
  },
  created() {
    this.getTempKeyApi()
  },
  methods: {
    uploadImage(file){
      const that = this;
      console.log('uploadfile changed', file)
      // this.productPictureBase64List = [];
      this.disposeForm.fileName = "";
      if(file.name.endsWith('.zip')){
        this.importPhoto(file)
      }else if(/\.(png|jpg|jpeg|bmp)$/.test(file.name)){
        // let base = zip.file(zip.files[key].name).async("base64"); // 将图片转化为base64格式
        let reader = new FileReader()
        reader.readAsDataURL(file.raw)
        reader.onload = ()=>{
          let img = new Image();
          img.src = reader.result.toString();
          img.onload = () => {
            let fileblobList = [];
            fileblobList.push({
              Bucket: this.tempKey.bucket,
              Region: this.tempKey.region,
              Key: `BMP/product/${file.name}`,
              Body: file.raw,
            })
            that.cos.uploadFiles(
                {
                  files: fileblobList,
                  SliceSize: 1024 * 1024 * 5 /* 触发分块上传的阈值，超过5MB使用分块上传，非必须 */,
                  onFileFinish: function (err, data, options) {
                    console.log(options.Key + " 上传" + (err ? "失败" : "完成"))
                  },
                },
                function (err, data) {
                  console.log("uploadFiles:", err || data)
                  if (err) {
                    this.$alert(`上传文件失败，错误 ${err}`, "错误", {
                      confirmButtonText: "确定",
                    })
                      .then(() => {})
                      .catch(() => {})
                  } else if (data && data.files) {
                    data.files.forEach((file) => {
                      that.productPictureBase64List.push({
                        deleteStatus:0,
                        isToFinishing:0,
                        keywords:'',
                        pictureId:'',
                        // pictureOrdinal:1,
                        productSource:'',
                        replacePictureSource:2,
                        pictureName: file.options.Key.split('/')[2],
                        pictureUrl: window.tempKey.host +'/'+ file.options.Key,
                        pictureWidth:img.width,
                        pictureHeight:img.height
                      })
                    })
                  }
                }
              )

            // this.productPictureBase64List.push({
            //   pictureName: file.name,
            //   pictureUrl:  img.src,//`data:image/png;base64,${res}`,
            //   pictureOrdinal:
            //     file.name
            //       .split("-")[1]
            //       .split(".")[0] * 1,
            //   deleteStatus: 0,
            //   pictureWidth: img.width,
            //   pictureHeight: img.height,
            // });
          };
        }
        reader.onerror = ()=>{
          //忽略
        }
      }else {
        this.$message.error("上传图片只能是jpg，png，jpeg，bmp格式！");
      }
    },

    // zip上传 并显示图片
    importPhoto(file) {
      let index = 0
      // this.disposeForm.fileName = file.name;
      // this.productPictureBase64List = [];
      const jszip = new JSZip();
      jszip.loadAsync(file.raw).then((zip) => {
        // 读取zip
        for (let key in zip.files) {
          // 判断是否是目录
          if (!zip.files[key].dir) {
            if (/\.(png|jpg|jpeg|bmp)$/.test(zip.files[key].name)) {
              // 判断是否是图片格式
              let base = zip.file(zip.files[key].name).async("base64"); // 将图片转化为base64格式
              base.then((res) => {
                let img = new Image();
                img.src = `data:image/png;base64,${res}`;
                img.onload = () => {
                  index++
                  this.productPictureBase64List.push({
                    deleteStatus:0,
                    isToFinishing:0,
                    keywords:'',
                    pictureId:'',
                    // pictureOrdinal:1,
                    productSource:'',
                    replacePictureSource:2,
                    pictureName: zip.files[key].name.split("/")[1],
                    pictureUrl: `data:image/png;base64,${res}`,
                    pictureWidth:img.width,
                    pictureHeight:img.height
                  })
                  if(index === Object.keys(zip.files).length - 1) {
                    console.log(this.productPictureBase64List)
                    if(this.productPictureBase64List.length){
                      // 显示图片的操作
                    } else {
                      this.$message.error("文件上传失败!  图片宽度需大于800像素")
                    }
                  }
                };
              });
            } else {
              this.$message.error("上传图片只能是jpg，png，jpeg，bmp格式！");
              // break
            }
          }
        }
      });
    },
    updateImgList(e) {
      this.$emit('updateImgList', e)
      this.filterImgList(e)
    },
    filterImgList(arr) {
      console.log(arr)
      this.draggableData = [
        { imgList: [], title: "主图" },
        { imgList: [], title: "外包装" },
        { imgList: [], title: "说明书" },
      ];
      arr.forEach((item) => {
        if (item.pictureOrdinal == 1) {
          this.draggableData[0].imgList.push(item);
        } else if (item.pictureOrdinal >= 2 && item.pictureOrdinal <= 5) {
          this.draggableData[1].imgList.push(item);
        } else {
          this.draggableData[2].imgList.push(item);
        }
      });
    },
    // 删除原图
    deleteImg(item) {
      if (item.deleteStatus) {
        item.deleteStatus = 0;
      } else {
        item.deleteStatus = 1;
      }
      this.$forceUpdate()
    },
    getSubmitData() {
      debugger
      let flag = true;
      let imgList = [];
      let unsaveList = []
      this.draggableData.forEach((list, parentIndex) => {
        let normalArr = list.imgList.filter((item) => {
          return item.deleteStatus != 1;
        });
        // if (parentIndex == 0 && normalArr.length != 1) {
        //   this.$message.error("主图数量必须为1张");
        //   flag = false;
        //   return;
        // }
        // 已申请商品列表主图校验
        if (parentIndex == 0 && normalArr.length != 1 && this.applyProductDispose) {
          this.$message.error("只能上传1张主图！");
          flag = false;
          return;
        }
        // 新审批流一二审主图校验
        if (parentIndex == 0 && normalArr.length != 1 && (this.finishingDeal || this.finishingAudit)) {
          this.$message.error("主图数量必须为1张");
          flag = false;
          return;
        }
        if (parentIndex == 1 && normalArr.length > 4) {
          this.$message.error("外包装数量必须小于或等于4张");
          flag = false;
          return;
        }
        if (parentIndex == 2 && normalArr.length > 5) {
          this.$message.error("说明书数量必须小于或等于5张");
          flag = false;
          return;
        }
        if (flag) {
          let index = 0;
          list.imgList.forEach((item) => {
            if (parentIndex == 0) {
              item.pictureOrdinal = 1;
            } else if (parentIndex == 1) {
              if (item.deleteStatus == 1) {
                item.pictureOrdinal = 2;
              } else {
                item.pictureOrdinal = index + 2;
                if (index != 3) {
                  index++;
                }
              }
            } else if (parentIndex == 2) {
              if (item.deleteStatus == 1) {
                item.pictureOrdinal = 6;
              } else {
                item.pictureOrdinal = index + 6;
                if (index != 4) {
                  index++;
                }
              }
            }

            imgList.push(item);
          });
        }
      });
      // this.morePics.forEach((info) => {
      //   imgList.push({
      //     ...info,
      //     deleteStatus: 1,
      //   });
      // });
      if(!this.finishingDeal) {
        let temp = [].concat.apply([],this.friendPhotoList)
        temp.forEach((info) => {
          info.friendLibraryPictureDtos.map(item => {
              imgList.push({
              ...item,
              deleteStatus: 1,
            });
          })
        });
      }
      if (this.needDeal) {
        imgList.map((item, i) => {
          if (!item.deleteStatus && !item.offlineRetouchStatus && !item.dealOperation.saveStatus) {
            unsaveList.push(i + 1)
          }
        })
      }
      return {
        isFlag: flag,
        imgList,
        unsaveList
      };
    },
    previewImg(index, parIndex, fatherIndex) {
      let flag = true;
      this.preIndex = 0;
      this.preImgList = [];
      if(parIndex == -2) { //友商图
        this.preImgList = this.friendPhotoList[fatherIndex].friendLibraryPictureDtos;
        this.preIndex = index;
      } else if (parIndex == -1) {
        this.preImgList = this.morePics;
        this.preIndex = index;
      } else {
        this.draggableData.forEach((list, dParIndex) => {
          list.imgList.forEach((item, dIndex) => {
            if (dParIndex == parIndex && dIndex == index) {
              flag = false;
            }
            if (flag) {
              this.preIndex++;
            }
            this.preImgList.push(item);
          });
        });
      }
      this.$refs.previewImg.openDlg(
        this.preImgList,
        this.preIndex,
        this.productInfo.smallPackageCode
      );
    },

    // 初始化腾讯云插件
    async getTempKeyApi() {
      try {
        const { data } = await getTempKey()
        this.tempKey = data
        window.tempKey = data

        this.cos = new COS({
          // getAuthorization 必选参数
          getAuthorization: function (options, callback) {
            callback({
              TmpSecretId: window.tempKey.tmpSecretId,
              TmpSecretKey: window.tempKey.tmpSecretKey,
              SecurityToken: window.tempKey.sessionToken,
              // 建议返回服务器时间作为签名的开始时间，避免用户浏览器本地时间偏差过大导致签名错误
              StartTime: window.tempKey.startTime, // 时间戳，单位秒，如：1580000000
              ExpiredTime: window.tempKey.expiredTime, // 时间戳，单位秒，如：1580000000
            })
          },
        })
      } catch (error) {
        console.log(error)
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding-bottom: 10px !important;
}
.drag-wrap /deep/ {
  background: #fff;
  .el-card__body {
    padding: 10px;
  }
  .title {
    text-align: center;
    font-size: 20px;
    font-weight: bold;
    padding-bottom: 10px;
  }
}
.img-wrap /deep/ {
  min-height: 50px;
  .el-card__body {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    .delete-btn {
      position: absolute;
      top: 0;
      right: 0;
      font-size: 12px;
      color: #fff;
      padding: 5px;
      background: #f56c6c;
      border-top-right-radius: 5px;
      cursor: pointer;
      &.cancel {
        background: #909399;
      }
    }
    div {
      flex:1;
        span {
        flex: 1;
        text-align: center;
        position: relative;
        padding-left: 5px;
      }
      .bg1 {
        color:#ffffff;
        background: #3B95A8;
      }
      .bg2 {
        color:#ffffff;
        background: #909399;
      }
    }
  }
  img {
    height: 80px;
    width: 80px;
    border: 5px solid #fff;
  }
}
/deep/ .el-tabs__content{
  padding: 0!important;
}
.img-box {
  p{
    margin:0;
    padding-left: 5px;
  }
}
.is-delete-bg {
  background: #f2f2f2;
}
.is-delete {
  color:#909399;
}
.no-delete {
  color:#3B95A8;
}
/deep/ .upload-card .el-card__body{
  padding: 5px!important;
}
</style>
