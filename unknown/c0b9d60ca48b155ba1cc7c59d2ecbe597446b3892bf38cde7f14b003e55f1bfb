<template>
  <el-table
    :data="tableData"
    height="250"
    border
    style="width: 100%"
    :highlight-current-row="true"
    current-row-key
    @row-click="rowClick"
    @select="select"
  >
    <el-table-column type="inde" width="100"></el-table-column>

    <template v-for="(item,index) in tableCol">
      <el-table-column
        v-if="!item.expand"
        :key="index"
        :index="index"
        :prop="item.prop"
        :label="item.text"
        :min-width="item.width"
        align="center"
      ></el-table-column>

      <slot v-else :name="item.prop" :item="item"></slot>
    </template>
  </el-table>
</template>

<script>
export default {
  name: "ProTable",
  components: {},
  filters: {},
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    tableCol: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      currIndex: null
    };
  },
  computed: {},
  watch: {},
  created() {
    console.log(this.tableData);
  },
  mounted() {},
  methods: {
    rowClick(row, column, event) {
      // console.log(column.rowSpan);
      console.log(row, column, event);
      console.log(column.id);

      // this.$emit("row-click", row, column, event);
    },
    select(selection, row) {
      // console.log(selection, row)
    }
  }
};
</script>

<style lang="scss" scoped>
</style>