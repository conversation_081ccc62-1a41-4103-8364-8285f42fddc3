
<template>
  <div class="task-to-be-film-container" v-loading="loading">
    <!-- 查询表单 -->
    <div class="search-form-wrap">
      <el-form :model="model" ref="refSearchForm">
        <el-row type="flex">
          <!-- 商品大类 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="商品大类">
              <el-select
                v-model="formData.spuCategory"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" value></el-option>
                <el-option
                  v-for="item in spuCategoryList"
                  :key="item.id"
                  :label="item.dictName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 筛选条件 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="筛选条件">
              <el-select v-model="formData.groupType" placeholder="请选择" clearable>
                <el-option label="全部" value></el-option>
                <el-option
                  v-for="item in groupTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 商品组状态 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="商品组状态">
              <el-select v-model="formData.operateStatus" placeholder="请选择" clearable>
                <el-option label="全部" value></el-option>
                <el-option label="已领取" value="1"></el-option>
                <el-option label="待领取" value="2"></el-option>
                <el-option label="已驳回" value="3"></el-option>
                <el-option label="已提交审核" value="4"></el-option>
                <el-option label="审核驳回" value="5"></el-option>
                <el-option label="审核通过" value="6"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 商品id -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="商品id">
              <el-input
                v-model="formData.productNo"
                placeholder=""
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- 通用名 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="通用名">
              <el-input
                v-model="formData.generalName"
                placeholder=""
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- 生产厂家 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="生产厂家">
              <el-input
                v-model="formData.manufacturerName"
                placeholder=""
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- 小包装条码 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="小包装条码">
              <el-input
                v-model="formData.smallPackageCode"
                placeholder=""
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- 批准文号 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="批准文号">
              <el-input
                v-model="formData.approvalNo"
                placeholder=""
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- 按钮 -->
          <el-col :span="6" :offset="18">
            <el-button type="primary" size="medium" @click="btnSearchClick"
              >查询</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- table表单 -->
    <!-- table -->
    <div class="table-wrap">
      <vxe-table
        border
        highlight-hover-row
        resizable
        height="100%"
        auto-resize
        size="small"
        align="center"
        :tooltip-config="{ enterable: false }"
        :loading="tableLoading"
        :data="tableData"
        :seq-config="{ startIndex: (pageNum - 1) * pageSize }"
        ref="refVxeTable"
      >
        <vxe-table-column
          type="seq"
          title="序号"
          width="60"
          show-header-overflow
          show-overflow
          fixed="left"
        ></vxe-table-column>
        <vxe-table-column
          field="orderCode"
          title="商品组编码"
          width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="num"
          title="包含商品数量"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="num"
          title="商品大类"
          min-width="120"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">
            <span>{{ row.spuCategory | filterSpuCategory }}</span>
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="approvalNo"
          title="批准文号"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="manufacturerName"
          title="生产厂家"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="spec"
          title="规格型号"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="generalName"
          title="商品名称"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="smallPackageCode"
          title="小包装条码"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="operateStatus"
          title="商品组状态"
          min-width="120"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">
            <span>{{ row.operateStatus | filterOperateStatus }}</span>
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="receiverName"
          title="操作人"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="updateTime"
          title="操作时间"
          min-width="120"
          show-header-overflow
          show-overflow
          ></vxe-table-column
        >
        <vxe-table-column
          title="操作"
          width="120"
          show-header-overflow
          show-overflow
          fixed="right"
        >
          <template v-slot="{ row }">
            <span>
              <el-link
                :underline="false"
                type="primary"
                @click.stop="goDetail(row.orderCode)"
                >查看</el-link
              >
            </span>
            <span v-if="row.operateStatus == 1">
              <el-link
                :underline="false"
                type="primary"
                v-if="row.receiveSelf"
                @click.stop="deal(row.orderCode)"
                >处理</el-link
              >
              <el-link v-else :underline="false" type="primary" :disabled="true"
                >已领取</el-link
              >
            </span>
            <span v-if="row.operateStatus == 2">
              <el-link
                :underline="false"
                type="primary"
                @click.stop="receiveTask(row.orderCode)"
                >领取</el-link
              >
            </span>
          </template>
        </vxe-table-column>
      </vxe-table>
    </div>

    <!-- 分页 -->
    <el-row class="custom-pagination-wrap">
      <el-col>
        <el-pagination
          background
          :current-page.sync="pageNum"
          :page-sizes="[20, 50, 100, 200]"
          :page-size.sync="pageSize"
          :total="total"
          layout="prev, pager, next, jumper,total, sizes"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getSamegroupList, receiveSamegroup } from "@/api/follow";
import { findDictList } from "@/api/dict.js";

export default {
  name: "",
  components: {},
  filters: {},
  props: {},
  data() {
    return {
      loading: false,
      pageNum: 1,
      pageSize: 20,
      total: 0, //总条数
      formData: {
        spuCategory: "",
        groupType: 1,
        operateStatus: "",
        productNo: "",
        generalName: "",
        manufacturerName: "",
        smallPackageCode: "",
        approvalNo: "",
      },
      groupTypeList: [
        {
          value: 1,
          label: "小包装条码",
        },
        {
          value: 2,
          label: "四要素(五要素去掉小包装条码)",
        },
        {
          value: 3,
          label: "规格_批准文号_生产厂家",
        },
        {
          value: 4,
          label: "规格_批准文号_通用名",
        },
      ],
      spuCategoryList: [],
      tableLoading: false,
      tableData: [],
    };
  },
  filters: {
    filterOperateStatus(val) {
      switch (val * 1) {
        case 2:
          return "待领取";
        case 1:
          return "已领取";
        case 3:
          return "已驳回";
        case 4:
          return "已提交审核";
        case 5:
          return "审核驳回";
        case 6:
          return "审核通过";
        default:
          return "待领取";
      }
    },
  },
  computed: {},
  watch: {},
  created() {
    this._formDefault = _.cloneDeep(this.formData);
    this.searchForm();
    this.getCategoryList();
  },
  mounted() {},
  methods: {
    /**
     * 查询table数据
     */
    async searchForm() {
      try {
        this.tableLoading = true;
        let param = Object.assign({}, this.formData);
        param.page = this.pageNum;
        param.limit = this.pageSize;
        let res = await getSamegroupList(param);
        this.tableLoading = false;
        if (res.success) {
          this.tableData = res.data.list;
          this.total = res.data.total;
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      } catch (error) {
        console.error(error);
      }
    },
    // 获取商品大类
    getCategoryList() {
      findDictList({
        type: 7,
      }).then((res) => {
        this.spuCategoryList = res.data.list;
      });
    },
    /**
     * 查询按钮点击
     */
    btnSearchClick() {
      this.pageNum = 1;
      this.searchForm();
    },

    /**
     * pageSize 改变事件
     */
    handleSizeChange(pageSize) {
      this.pageNum = 1;
      this.pageSize = pageSize;
      this.searchForm();
    },

    /**
     * pageNum 改变事件
     */
    handleCurrentChange(currentPage) {
      this.pageNum = currentPage;
      this.searchForm();
    },

    receiveTask(orderCode) {
      receiveSamegroup({ orderCode }).then((res) => {
        if (!res.retCode) {
          this.$message.success("操作成功");
          this.searchForm();
        } else {
          this.$message.error(res.retMsg);
        }
      });
    },

    goDetail(orderCode) {
      try {
        parent.CreateTab(
          `../static/dist/index.html#/follow/repetitionDetail?orderCode=${orderCode}&type=detail`,
          "商品去重详情"
        );
      } catch {
        this.$router.push({
          path: "/follow/repetitionDetail",
          query: {
            orderCode,
            type: "detail",
          },
        });
      }
    },

    deal(orderCode) {
      try {
        parent.CreateTab(
          `../static/dist/index.html#/follow/repetitionDetail?orderCode=${orderCode}&type=deal`,
          "商品去重处理"
        );
      } catch {
        this.$router.push({
          path: "/follow/repetitionDetail",
          query: {
            orderCode,
            type: "deal",
          },
        });
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.task-to-be-film-container {
  /**
   * 查询表单
   */
  .search-form-wrap {
    width: 100%;
    padding: 15px;
    border-bottom: 1px dashed #e4e4eb;

    .el-row {
      flex-wrap: wrap;

      .el-col {
        display: flex;
        justify-content: flex-end;

        .el-form-item {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          width: 100%;
          margin-right: 0;
          margin-bottom: 15px;

          /deep/ {
            .el-form-item__label {
              width: 96px;
              line-height: normal;
              padding: 0 12px;
              color: #292933;
              font-weight: normal;
            }

            .el-form-item__content {
              flex: 1;

              .el-range-editor.el-input__inner {
                width: 100%;

                .el-range-separator {
                  width: 14px;
                  padding: 0;
                }
              }
            }
          }

          .el-input {
            width: 100%;
          }

          .el-select {
            width: 100%;
          }
        }
      }
    }
  }

  /**
     * table
     */
  .table-wrap {
    width: 100%;
    min-height: 440px;
    height: calc(100vh - 295px);
    padding: 0 15px;
    margin-top: 15px;
    /deep/ .el-link {
      padding: 5px;
      &.is-disabled {
        color: #c0c4cc;
      }
    }
  }
}
</style>
