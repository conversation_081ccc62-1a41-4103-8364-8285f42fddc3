import Layout from '@/layout'

const systemManagementRouters = {
    path: '/systemManagement',
    name: 'systemManagement',
    component: Layout,
    redirect: 'noRedirect',
    meta: { title: '系统管理', icon: 'nested' ,roles: ['admin']},
    children: [
        {
            path: 'personnel',
            name: 'personnel',
            component: () => import('@/views/systemManagement/personnel'),
            meta: { title: '员工管理' , roles: ['admin']}
        },
        {
            path: 'role',
            name: 'role',
            component: () => import('@/views/systemManagement/role'),
            meta: { title: '角色管理' ,roles: ['admin']}
        },
        {
            path: 'editPwd',
            name: 'editPwd',
            component: () => import('@/views/systemManagement/editPwd'),
            meta: { title: '修改密码', roles: ['admin']}
        }
    ]
}

export default systemManagementRouters