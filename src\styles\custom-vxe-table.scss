@import 'vxe-table/styles/variable.scss';

// 局部修改变量
$vxe-primary-color: #62aab9;

@import 'vxe-table/styles/default.scss';

/**
 * checkout待定状态时样式
 */
.vxe-table .vxe-cell--checkbox.is--indeterminate::after{
  height: 0.25em;
}

/**
 * checkbox选中行的样式
 */
.vxe-table .vxe-body--row.row-checked {
  background-color: #fbf8ec;
}

.vxe-table .vxe-body--row.row--current {
  background-color: #3B95A8;
  color: #fff;
}
.vxe-table .vxe-body--row.row--hover.row--current {
  background-color: #29617a;
  color: #fff;
}