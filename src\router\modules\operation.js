import Iframe from '@/iframe'

export default {
  path: '/commodityOperation',
  name: 'commodityOperation',
  component: Iframe,
  redirect: 'noRedirect',
  meta: { title: '', icon: 'nested' },
  children: [
    {
      path: 'medicareInfo',
      name: 'medicareInfo',
      component: () => import('@/views/commodityOperation/medicareInfo.vue'),
      meta: { title: '商品医保信息' }
    },
    {
      path: 'batchMedicare',
      name: 'batchMedicare',
      component: () => import('@/views/commodityOperation/batchMedicare.vue'),
      meta: { title: '批量更新医保' }
    },
    {
      path: 'categoryInfo',
      name: 'categoryInfo',
      component: () => import('@/views/commodityOperation/categoryInfo/index.vue'),
      meta: { title: '商品品类信息' }
    },
    {
      path: 'batchCategory',
      name: 'batchCategory',
      component: () => import('@/views/commodityOperation/batchCategory.vue'),
      meta: { title: '批量更新品类' }
    },
  ]
}
