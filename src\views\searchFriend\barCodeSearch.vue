<template>
  <div class="page-container">
    <!-- 查询表单 -->
    <div class="search-form-wrap">
      <el-form ref="refSearchForm">
        <el-row type="flex">
          <!-- 图片数量下限 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="条码">
              <el-input v-model="form.barCode" clearable></el-input>
            </el-form-item>
          </el-col>
          <!-- 按钮 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" :offset="1">
            <el-form-item label="">
              <el-button type="primary" size="medium" @click="btnSearchClick">查询</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="table-wrap">
      <el-row>
        <el-card>
          <p v-if="!detail.imageLink.length">未查询到图片</p>
          <div v-for="(item, index) in detail.imageLink" :key="index" class="img-box">
            <el-image :src="item" :preview-src-list="[item]" />
            <el-button @click="doCopy(item)" size="mini" class="copy-btn">复制url</el-button>
          </div>
        </el-card>
      </el-row>
      <el-row>
        <div class="list">
          <span class="label">商品条码：</span>
          <span class="content">{{ detail.gtin }}</span>
          <el-button @click="doCopy(detail.gtin)" v-if="detail.gtin" type="text" style="margin-left:10px">复制</el-button>
        </div>
        <div class="list">
          <span class="label">产品名称：</span>
          <span class="content">{{ detail.productName }}</span>
          <el-button @click="doCopy(detail.productName)" v-if="detail.productName" type="text" style="margin-left:10px">复制</el-button>
        </div>
        <div class="list">
          <span class="label">品牌名称：</span>
          <span class="content">{{ detail.brandName }}</span>
          <el-button @click="doCopy(detail.brandName)" v-if="detail.brandName" type="text" style="margin-left:10px">复制</el-button>
        </div>
        <div class="list">
          <span class="label">产品规格：</span>
          <span class="content">{{ detail.spec }}</span>
          <el-button @click="doCopy(detail.spec)" v-if="detail.spec" type="text" style="margin-left:10px">复制</el-button>
        </div>
        <div class="list">
          <span class="label">产品分类：</span>
          <span class="content">{{ detail.category }}</span>
          <el-button @click="doCopy(detail.category)" v-if="detail.category" type="text" style="margin-left:10px">复制</el-button>
        </div>
        <div class="list">
          <span class="label">上市日期：</span>
          <span class="content">{{ detail.listingDate }}</span>
          <el-button @click="doCopy(detail.listingDate)" v-if="detail.listingDate" type="text" style="margin-left:10px">复制</el-button>
        </div>
        <div class="list">
          <span class="label">企业名称：</span>
          <span class="content">{{ detail.partyContactName }}</span>
          <el-button @click="doCopy(detail.partyContactName)" v-if="detail.partyContactName" type="text" style="margin-left:10px"
            >复制</el-button
          >
        </div>
        <div class="list">
          <span class="label">企业地址：</span>
          <span class="content">{{ detail.partyContactAddress }}</span>
          <el-button @click="doCopy(detail.partyContactAddress)" v-if="detail.partyContactAddress" type="text" style="margin-left:10px"
            >复制</el-button
          >
        </div>
        <div class="list">
          <span class="label">信用代码：</span>
          <span class="content">{{ detail.firmCode }}</span>
          <el-button @click="doCopy(detail.firmCode)" v-if="detail.firmCode" type="text" style="margin-left:10px">复制</el-button>
        </div>
        <div class="list">
          <span class="label">条码状态：</span>
          <span class="content">{{ detail.stringMsg }}</span>
        </div>
      </el-row>
    </div>
  </div>
</template>

<script>
import { barcodeApi } from "@/api/searchFriend"
export default {
  name: "",
  components: {},
  filters: {},
  props: {},
  data() {
    return {
      form: {
        barCode: ""
      },
      detail: {
        imageLink: []
      }
    }
  },
  computed: {},
  watch: {},
  created() {},
  methods: {
    async btnSearchClick() {
      if (!this.form.barCode) {
        this.$message.error("请输入条码")
        return
      }
      try {
        const res = await barcodeApi(this.form.barCode)
        if (res.retCode === 0) {
          if(res.data){
            this.detail = res.data
          }
          if (!res.data.imageLink) {
            this.detail.imageLink = []
          }
        } else {
          this.$message.error(res.retMsg)
        }
      } catch (error) {
        console.log(error)
      }
    },
    doCopy(e) {
      this.$copyText(e).then(() => {
        this.$message.success("复制成功")
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  /**
   * 查询表单
   */
  .search-form-wrap {
    width: 100%;
    padding: 15px;
    border-bottom: 1px dashed #e4e4eb;

    .el-row {
      flex-wrap: wrap;

      .el-col {
        display: flex;
        justify-content: flex-end;

        .el-form-item {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          width: 100%;
          margin-right: 0;
          margin-bottom: 15px;
          /deep/ {
            .el-form-item__label {
              width: 116px;
              line-height: normal;
              padding: 0 12px;
              color: #292933;
              font-weight: normal;
            }

            .el-form-item__content {
              flex: 1;

              .el-range-editor.el-input__inner {
                width: 100%;

                .el-range-separator {
                  width: 14px;
                  padding: 0;
                }
              }
            }
          }

          .el-input {
            width: 100%;
          }
        }
      }
    }
  }
}
.table-wrap {
  margin-bottom: 20px;
  padding: 20px;
  el-row {
    padding: 20px;
  }
  .el-card {
    height: 280px;
    overflow-y: auto;
    overflow-x: hidden;
    .img-box{
      position:relative;
      display:inline;
      .el-image {
      margin: 5px;
      width: 150px;
      height: 150px;
      border: 1px solid #666;
    }
      .copy-btn{
        position:absolute;
        bottom:10px;
        right:8px;
      }
    }
  }
}
.list {
  height: 36px;
  line-height: 36px;
  .label {
    font-weight: bold;
    color: #333;
  }
  .content {
    text-indent: 10px;
  }
}
</style>
