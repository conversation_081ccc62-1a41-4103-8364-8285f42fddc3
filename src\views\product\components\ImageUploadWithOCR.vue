<template>
  <div class="image-upload-ocr-container">
    <div class="upload-ocr-layout">
      <!-- 左侧：图片上传区域 -->
      <div class="left-section">
        <!-- 图片上传区域 -->
        <div class="upload-section wrap">
          <el-upload
            :action="uploadUrl"
            list-type="picture-card"
            multiple
            :disabled="disabled"
            :limit="limit"
            :on-exceed="handleExceed"
            :before-upload="beforeUpload"
            :on-success="handleSuccess"
            :on-error="handleError"
            :file-list="list"
          >
            <i slot="default" class="el-icon-plus"></i>
            <div slot="file" slot-scope="{ file }" class="image-item-container">
              <div class="image-content">
                <img
                  class="el-upload-list__item-thumbnail"
                  v-if="file.status == 'success'"
                  :src="file.url || file.mediaUrl"
                  alt
                  width="100%"
                  height="100%"
                />
                <el-progress
                  v-if="file.status === 'uploading' || file.status === 'ready'"
                  :type="'circle'"
                  :stroke-width="6"
                  :percentage="parsePercentage(file.percentage)"
                >
                </el-progress>
                <span
                  class="el-upload-list__item-actions"
                  v-if="file.status !== 'uploading'"
                >
                  <span @click="handleLeft(file)" v-if="!disabled" title="左移">
                    <i class="el-icon-back"></i>
                  </span>

                  <span @click="handlePreview(file)" v-if="preview" title="预览">
                    <i class="el-icon-zoom-in"></i>
                  </span>

                  <span @click="handleRemove(file)" v-if="!disabled" title="删除">
                    <i class="el-icon-delete"></i>
                  </span>

                  <span @click="handleRight(file)" v-if="!disabled" title="右移">
                    <i class="el-icon-right"></i>
                  </span>
                </span>
              </div>

              <!-- OCR按钮紧贴图片底部 -->
              <div class="ocr-button-container" v-if="enableOCR">
                <!-- 加载中状态 -->
                <span
                  v-if="getOcrResult(file).loading"
                  class="ocr-extract-btn loading"
                >
                  提取中
                </span>
                <!-- 可点击状态：图片已上传成功或已有图片URL -->
                <span
                  v-else-if="isImageReady(file)"
                  class="ocr-extract-btn clickable"
                  @click="extractTextFromImage(file)"
                >
                  提取图片文字
                </span>
                <!-- 不可用状态 -->
                <span
                  v-else
                  class="ocr-extract-btn disabled"
                >
                  等待上传完成
                </span>
              </div>
            </div>
          </el-upload>
        </div>
      </div>

      <!-- 右侧：OCR结果展示区域 -->
      <div class="right-section" v-if="enableOCR && hasOcrResults">
        <div class="ocr-results-section">
          <div class="ocr-results-header">
            <div class="ocr-results-title">提取结果：</div>
            <div class="ocr-results-controls">
              <span
                class="expand-toggle-btn"
                @click="toggleExpanded"
                :title="isExpanded ? '收起' : '展开'"
              >
                {{ isExpanded ? '收起' : '展开' }}
                <i :class="isExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
              </span>
            </div>
          </div>
          <div
            class="ocr-results-content"
            :class="{ 'expanded': isExpanded }"
          >
            <div
              v-for="(file, fileIndex) in list"
              :key="'result-' + (file.uid || fileIndex)"
              v-if="getOcrResult(file).extracted && getOcrResult(file).fields.length > 0"
              class="ocr-result-group"
            >
              <div class="ocr-result-group-header">
                <div class="ocr-result-group-title">图片{{ fileIndex + 1 }}识别结果：</div>
                <div class="ocr-result-group-actions">
                  <span
                    class="delete-result-btn"
                    @click="deleteOcrResult(file)"
                    title="删除识别结果"
                  >
                    <i class="el-icon-delete"></i>
                  </span>
                </div>
              </div>
              <div class="ocr-result-fields">
                <div
                  v-for="field in getOcrResult(file).fields"
                  :key="field.id"
                  class="ocr-result-field"
                >
                  <div class="field-content">
                    <span class="field-label">{{ field.label }}：</span>
                    <span class="field-value">{{ field.value }}</span>
                  </div>
                  <div class="field-copy">
                    <span
                      class="copy-btn-below"
                      @click="copyInfo(field.value)"
                      title="复制"
                    >
                      复制
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图片预览组件 -->
    <imagePreviewWithOCR
      :on-close="closeImageViewer"
      v-if="imgPreview"
      :url-list="imgPreviewList"
      :initialIndex="initialIndex"
      :enable-o-c-r="enableOCR"
      :ocr-results="ocrResults"
      :extract-text-from-image="extractTextFromImageInPreview"
      :copy-info="copyInfo"
    ></imagePreviewWithOCR>
  </div>
</template>

<script>
import imageConversion from "image-conversion";
import imagePreviewWithOCR from "@/components/common/preview/imagePreviewWithOCR";
import request from "@/utils/request";
import { getOcrProxyPath, getEnvironmentInfo } from "@/config/ocr";

// 扩展数组原型方法
Array.prototype.remove = function (val) {
  var index = this.indexOf(val);
  if (index > -1) {
    this.splice(index, 1);
  }
};

export default {
  name: "ImageUploadWithOCR",
  components: {
    imagePreviewWithOCR,
  },
  props: {
    fileList: {
      type: Array,
      default: () => [],
    },
    uploadUrl: {
      type: String,
      default: "",
    },
    limit: {
      type: Number,
      default: 10,
    },
    minWidth: {
      type: Number,
      default: 0,
    },
    maxSize: {
      type: Number,
      default: 10,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    preview: {
      type: Boolean,
      default: false,
    },
    // 新增：是否启用OCR功能
    enableOCR: {
      type: Boolean,
      default: true,
    },
    // 新增：OCR API地址（预留）
    ocrApiUrl: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      initialIndex: 0,
      list: [],
      fileNameList: [],
      imgPreview: false,
      imgPreviewList: [],
      // OCR相关数据
      ocrResults: {}, // 使用普通对象存储每个文件的OCR结果，确保响应式
      isExpanded: false, // 控制OCR结果区域的展开/收起状态
    };
  },
  computed: {
    // 是否有OCR结果
    hasOcrResults() {
      return Object.values(this.ocrResults).some(result =>
        result.extracted && result.fields.length > 0
      );
    },
  },
  watch: {
    list: {
      handler: function (newValue, oldValue) {
        // 检测list的变动。避免取消后 fileNameList 和 list 不一致导致提示重复问题
        this.fileNameList = [];
        for (let item of newValue) {
          if (item.name) {
            this.fileNameList.push(item.name);
          }
        }
        // 清理已删除文件的OCR结果
        this.cleanupOcrResults(newValue);
      },
      immediate: true,
    },
    fileList: {
      handler: function (newValue, oldValue) {
        this.list = newValue;
      },
      immediate: true,
    },
  },
  methods: {
    // 获取文件的OCR结果
    getOcrResult(file) {
      const key = file.uid || file.mediaUrl || file.url;
      if (!this.ocrResults[key]) {
        this.$set(this.ocrResults, key, {
          loading: false,
          extracted: false,
          fields: []
        });
      }
      return this.ocrResults[key];
    },

    // 判断图片是否准备好进行OCR提取
    isImageReady(file) {
      // 如果有明确的成功状态
      if (file.status === 'success') {
        return true;
      }

      // 如果图片有URL或mediaUrl，说明图片已经存在（可能是初始化数据）
      if (file.url || file.mediaUrl) {
        return true;
      }

      // 如果没有status但有response数据，说明上传成功
      if (file.response && file.response.data && file.response.data.mediaUrl) {
        return true;
      }

      // 其他情况认为图片还未准备好
      return false;
    },
    
    // 清理已删除文件的OCR结果
    cleanupOcrResults(currentList) {
      const currentKeys = new Set(currentList.map(file =>
        file.uid || file.mediaUrl || file.url
      ));

      Object.keys(this.ocrResults).forEach(key => {
        if (!currentKeys.has(key)) {
          this.$delete(this.ocrResults, key);
        }
      });
    },

    // OCR文字提取功能
    async extractTextFromImage(file) {
      const ocrResult = this.getOcrResult(file);
      if (ocrResult.extracted) {
        this.$message.warning('已成功提取图片文字，勿重复操作，提取的文字信息删除后支持再次提取');
        return;
      }

      // 设置加载状态
      this.$set(ocrResult, 'loading', true);

      try {
        // 获取图片URL
        const imgUrl = this.getImageUrl(file);
        if (!imgUrl) {
          throw new Error('图片URL无效');
        }

        // 获取环境信息和OCR配置
        const envInfo = getEnvironmentInfo();
        const ocrPath = getOcrProxyPath();

        // console.log('开始OCR识别，图片URL:', imgUrl);
        // console.log('环境信息:', envInfo);

        // 调用OCR API - 使用代理路径避免CORS问题
        const response = await request({
          url: ocrPath,
          method: 'POST',
          data: {
            imgUrl: imgUrl,
            type: 'GENERAL_STRUCTURE'
          }
        });

        // console.log('OCR API响应:', response);

        // 检查响应状态 - 项目request工具可能直接返回data
        const responseData = response.data || response;
        if (responseData.code !== 0) {
          throw new Error(responseData.msg || 'OCR识别失败');
        }

        // 转换OCR结果格式
        const ocrFields = this.convertOcrResultToFields(responseData.result.Data);

        // 更新结果
        this.$set(ocrResult, 'loading', false);
        this.$set(ocrResult, 'extracted', true);
        this.$set(ocrResult, 'fields', ocrFields);

        this.$message.success('文字提取完成');

      } catch (error) {
        this.$set(ocrResult, 'loading', false);

        // 详细的错误处理
        let errorMessage = '文字提取失败，请重试';

        if (error.response) {
          // 服务器响应了错误状态码
          console.error('OCR API错误响应:', error.response);
          const status = error.response.status;
          const data = error.response.data;

          if (status === 400) {
            errorMessage = '请求参数错误，请检查图片URL是否有效';
          } else if (status === 401) {
            errorMessage = '认证失败，请检查API权限';
          } else if (status === 403) {
            errorMessage = '访问被拒绝，请检查API权限';
          } else if (status === 404) {
            errorMessage = 'OCR服务不可用';
          } else if (status === 500) {
            errorMessage = 'OCR服务内部错误';
          } else {
            errorMessage = (data && data.message) || (data && data.msg) || `服务器错误 (${status})`;
          }
        } else if (error.request) {
          // 请求已发出但没有收到响应
          console.error('OCR请求超时或网络错误:', error.request);
          errorMessage = '网络连接失败，请检查网络连接';
        } else {
          // 其他错误
          console.error('OCR请求配置错误:', error.message);
          errorMessage = error.message || '请求配置错误';
        }

        this.$message.error(errorMessage);
        console.error('OCR提取失败:', error);
      }
    },

    // 获取图片URL
    getImageUrl(file) {
      let imgUrl = null;

      // 优先使用mediaUrl，然后是url，最后是response中的mediaUrl
      if (file.mediaUrl) {
        imgUrl = file.mediaUrl;
      } else if (file.url) {
        imgUrl = file.url;
      } else if (file.response && file.response.data && file.response.data.mediaUrl) {
        imgUrl = file.response.data.mediaUrl;
      }

      // 确保URL是完整的HTTP/HTTPS地址
      if (imgUrl && !imgUrl.startsWith('http')) {
        // 如果是相对路径，需要根据项目配置补全域名
        // 这里可能需要根据实际项目的图片服务器地址进行调整
        console.warn('图片URL不是完整地址:', imgUrl);
      }

      // console.log('获取到的图片URL:', imgUrl);
      return imgUrl;
    },

    // 将OCR API返回的数据转换为组件期望的格式
    convertOcrResultToFields(ocrData) {
      if (!ocrData || typeof ocrData !== 'object') {
        return [];
      }

      const fields = [];
      let fieldIndex = 1;

      Object.keys(ocrData).forEach(key => {
        const value = ocrData[key];
        if (value && value.toString().trim()) {
          fields.push({
            id: `ocr_field_${fieldIndex}`,
            label: key,
            value: value.toString().trim()
          });
          fieldIndex++;
        }
      });

      return fields;
    },



    // 复制功能
    copyInfo(info) {
      if (info) {
        if (navigator.clipboard && navigator.clipboard.writeText) {
          navigator.clipboard.writeText(info).then(() => {
            this.$message.success('复制成功');
          });
        } else {
          const textArea = document.createElement('textarea');
          textArea.value = info;
          document.body.appendChild(textArea);
          textArea.select();
          document.execCommand('copy');
          document.body.removeChild(textArea);
          this.$message.success('复制成功');
        }
      }
    },

    // 切换OCR结果区域的展开/收起状态
    toggleExpanded() {
      this.isExpanded = !this.isExpanded;
    },

    // 删除指定文件的OCR识别结果
    deleteOcrResult(file) {
      const key = file.uid || file.mediaUrl || file.url;
      if (this.ocrResults[key]) {
        this.$confirm('确定要删除这张图片的识别结果吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 删除OCR结果
          this.$delete(this.ocrResults, key);
          this.$message.success('识别结果已删除');
        }).catch(() => {
          // 用户取消删除
        });
      }
    },

    // 在预览组件中提取OCR文字的方法
    async extractTextFromImageInPreview(file) {
      // 检查是否已经提取过
      const ocrResult = this.getOcrResult(file);
      if (ocrResult.extracted) {
        this.$message.info('已成功提取图片文字，勿重复操作');
        return;
      }

      // 调用主要的OCR提取方法
      await this.extractTextFromImage(file);
    },

    // 以下是继承自原uploadImg组件的方法
    parsePercentage(val) {
      if (val == 100) {
        // 模拟一个数，避免完成时动画展示不自然
        let min = Math.ceil(0),
          max = Math.floor(1);
        return 97 + Math.floor(Math.random() * (max - min + 1)) + min;
      } else {
        return parseInt(val, 10);
      }
    },

    getImgIndex(url) {
      let index = null;
      this.list.forEach((item, i) => {
        if (item.mediaUrl === url) {
          index = i;
          return;
        }
      });
      return index;
    },

    getImgIndex2(url) {
      let index = null;
      this.list.forEach((item, i) => {
        if (item.uid === url) {
          index = i;
          return;
        }
      });
      return index;
    },

    handlePreview(file) {
      // 找到当前点击图片在列表中的索引
      const clickedIndex = this.list.findIndex(item =>
        (item.uid && item.uid === file.uid) ||
        (item.mediaUrl && item.mediaUrl === file.mediaUrl) ||
        (item.url && item.url === file.url)
      );

      this.initialIndex = clickedIndex >= 0 ? clickedIndex : 0;

      // 将所有图片转换为预览组件需要的格式
      this.imgPreviewList = this.list.map(item => {
        const responseMediaUrl = item.response && item.response.data && item.response.data.mediaUrl;
        return {
          uid: item.uid,
          name: item.name || item.mediaName || `image-${item.uid}`,
          mediaName: item.name || item.mediaName || `image-${item.uid}`,
          mediaUrl: item.url || item.mediaUrl || responseMediaUrl,
          url: item.url || item.mediaUrl || responseMediaUrl,
          status: item.status
        };
      });

      // 检查是否有有效的图片URL
      if (this.imgPreviewList.length === 0 || !this.imgPreviewList[this.initialIndex].mediaUrl) {
        this.$message.error('图片地址无效，无法预览');
        return;
      }

      this.imgPreview = true;
    },

    closeImageViewer() {
      this.imgPreview = false;
    },

    handleRemove(file) {
      if (this.disabled) {
        this.$message.error("当前状态不可操作");
        return;
      }
      let index = this.getImgIndex(file.mediaUrl);
      if (index !== null) {
        this.list.splice(index, 1);
        this.$emit("change", this.list);
        this.fileNameList.remove(file.name);
      }
    },

    handleLeft(file) {
      if (this.disabled) {
        this.$message.error("当前状态不可操作");
        return;
      }
      let index = this.getImgIndex(file.mediaUrl);
      if (index != 0) {
        this.list.splice(
          index,
          1,
          ...this.list.splice(index - 1, 1, this.list[index])
        );
        this.$emit("change", this.list);
      }
    },

    handleRight(file) {
      if (this.disabled) {
        this.$message.error("当前状态不可操作");
        return;
      }
      let index = this.getImgIndex(file.mediaUrl);
      if (index != this.list.length - 1) {
        this.list.splice(
          index,
          1,
          ...this.list.splice(index + 1, 1, this.list[index])
        );
        this.$emit("change", this.list);
      }
    },

    handleExceed() {
      this.$message.warning(`最多上传${this.limit}张`);
    },

    async beforeUpload(file) {
      if (this.fileNameList.includes(file.name)) {
        this.$message.error("该文件已经被选择了！");
        return Promise.reject();
      }
      const isImage =
        file.type === "image/jpeg" ||
        file.type === "image/jpg" ||
        file.type === "image/png";
      const isMaxSize = file.size / 1024 / 1024 < this.maxSize;
      if (!isImage) {
        this.$message.error("上传图片只能是 JPG,PNG,JPEG 格式！");
        return Promise.reject();
      }
      if (!isMaxSize) {
        this.$message.error(`上传头像图片大小不能超过${this.maxSize}MB!`);
        return Promise.reject();
      }
      if (this.minWidth) {
        const isCheckWidth = await this.checkWidth(file);
        if (!isCheckWidth) {
          this.$message.error(`请上传宽度大于${this.minWidth}px(含)的图片`);
          return Promise.reject();
        }
      }
      // 对大于1M的图片进行压缩
      if (file.size / 1024 / 1024 > 1 && isImage && isMaxSize) {
        return imageConversion.compress(file, 0.6);
      } else {
        return isImage && isMaxSize;
      }
    },

    checkWidth(file) {
      const isSize = new Promise((resolve, reject) => {
        let _URL = window.URL || window.webkitURL;
        let img = new Image();
        img.onload = () => {
          let valid = true;
          if (img.width < this.minWidth) valid = false;
          valid ? resolve() : reject();
        };
        img.src = _URL.createObjectURL(file);
      }).then(
        () => {
          return true;
        },
        () => {
          return false;
        }
      );
      return isSize;
    },

    handleSuccess(response, file, fileList) {
      this.fileNameList.push(file.name);
      // 将fileList赋值给list避免 只选择，不保存时切换位置报错
      this.list = fileList;
      this.$emit("change", fileList);
    },

    handleError(err, file, fileList) {
      console.log(err, file, fileList);
      this.$message.error("上传失败！");
    },
  },
};
</script>

<style lang="scss" scoped>
.image-upload-ocr-container {
  width: 100%;

  // 左右布局容器
  .upload-ocr-layout {
    display: flex;
    gap: 20px;
    align-items: flex-start;

    // 左侧区域（图片上传和OCR按钮）
    .left-section {
      flex-shrink: 0;
    }

    // 右侧区域（OCR结果）
    .right-section {
      flex: 1;
      min-width: 300px;
      max-width: 500px;
    }
  }

  // 上传区域样式 - 使用与原始组件相同的样式结构
  .wrap {
    /deep/ .el-upload--picture-card {
      width: 100px;
      height: 100px;
    }
    /deep/ .el-upload {
      width: 100px;
      height: 100px;
      line-height: 100px;
    }
    /deep/ .el-upload-list--picture-card .el-upload-list__item {
      width: 100px;
      height: auto; // 改为auto以容纳OCR按钮
      line-height: 100px;
      position: relative;
    }
    /deep/ .el-upload-list--picture-card .el-upload-list__item-thumbnail {
      width: 100px;
      height: 100px;
      line-height: 100px;
    }
    /deep/ .avatar {
      width: 100px;
      height: 100px;
    }

    // 图片项容器
    .image-item-container {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    // 图片内容区域
    .image-content {
      width: 100%;
      height: 100px;
      position: relative;
    }

    // OCR按钮容器
    .ocr-button-container {
      width: 100%;
      margin-top: 2px; // 紧贴图片，只留很小间距
      display: flex;
      justify-content: center;
    }

    // 确保操作按钮可见
    /deep/ .el-upload-list__item-actions {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      cursor: default;
      text-align: center;
      color: #fff;
      opacity: 0;
      font-size: 20px;
      background-color: rgba(0, 0, 0, 0.5);
      transition: opacity 0.3s;
      display: flex !important;
      align-items: center;
      justify-content: center;
      z-index: 10;

      span {
        display: inline-block;
        cursor: pointer;
        margin: 0 3px;
        padding: 3px;
        border-radius: 3px;
        transition: all 0.3s;

        &:hover {
          background-color: rgba(255, 255, 255, 0.2);
        }

        i {
          font-size: 14px;
        }
      }
    }

    // 鼠标悬停时显示操作按钮
    /deep/ .el-upload-list--picture-card .el-upload-list__item:hover .el-upload-list__item-actions {
      opacity: 1 !important;
    }
  }

  // OCR按钮样式 - 现在直接在图片容器内
  .ocr-extract-btn {
    width: 100%;
    font-size: 12px;
    padding: 4px 8px; // 减小padding使按钮更紧凑
    border-radius: 4px;
    text-align: center;
    line-height: 1.2;
    border: 1px solid;
    transition: all 0.3s;
    user-select: none;

    // 可点击状态（模拟 el-button primary）
    &.clickable {
      background-color: #409eff;
      border-color: #409eff;
      color: #ffffff;
      cursor: pointer;

      &:hover {
        background-color: #66b1ff;
        border-color: #66b1ff;
      }

      &:active {
        background-color: #3a8ee6;
        border-color: #3a8ee6;
      }
    }

    // 加载中状态（模拟 el-button loading）
    &.loading {
      background-color: #c0c4cc;
      border-color: #c0c4cc;
      color: #ffffff;
      cursor: not-allowed;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        left: 6px; // 调整位置适应更小的按钮
        top: 50%;
        transform: translateY(-50%);
        width: 10px;
        height: 10px;
        border: 2px solid transparent;
        border-top: 2px solid #ffffff;
        border-radius: 50%;
        animation: loading-rotate 1s linear infinite;
      }

      // 为加载图标留出空间
      padding-left: 20px;
    }

    // 禁用状态
    &.disabled {
      background-color: #f5f7fa;
      border-color: #e4e7ed;
      color: #c0c4cc;
      cursor: not-allowed;
    }
  }

  // 加载动画
  @keyframes loading-rotate {
    0% {
      transform: translateY(-50%) rotate(0deg);
    }
    100% {
      transform: translateY(-50%) rotate(360deg);
    }
  }

  // OCR结果展示区域样式
  .ocr-results-section {
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    height: fit-content;

    .ocr-results-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;

      .ocr-results-title {
        font-size: 14px;
        font-weight: bold;
        color: #333;
        margin: 0;
      }

      .ocr-results-controls {
        .expand-toggle-btn {
          font-size: 12px;
          color: #4A95A9;
          cursor: pointer;
          padding: 4px 8px;
          border-radius: 3px;
          transition: all 0.3s;
          user-select: none;
          display: flex;
          align-items: center;
          gap: 4px;

          &:hover {
            background-color: #4A95A9;
            color: white;
          }

          i {
            font-size: 12px;
            transition: transform 0.3s;
          }
        }
      }
    }

    .ocr-results-content {
      max-height: 300px; // 默认最大高度
      overflow-y: auto;
      transition: max-height 0.3s ease;

      // 展开状态
      &.expanded {
        max-height: none;
      }

      // 自定义滚动条样式
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
          background: #a8a8a8;
        }
      }

      .ocr-result-group {
        margin-bottom: 20px;

        &:last-child {
          margin-bottom: 0;
        }

        .ocr-result-group-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;
          padding-bottom: 5px;
          border-bottom: 1px solid #e9ecef;

          .ocr-result-group-title {
            font-size: 13px;
            font-weight: 600;
            color: #4A95A9;
            margin: 0;
          }

          .ocr-result-group-actions {
            .delete-result-btn {
              color: #f56c6c;
              cursor: pointer;
              padding: 4px;
              border-radius: 3px;
              transition: all 0.3s;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 24px;
              height: 24px;

              &:hover {
                background-color: #f56c6c;
                color: white;
              }

              i {
                font-size: 12px;
              }
            }
          }
        }

        .ocr-result-fields {
          .ocr-result-field {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;

            &:last-child {
              border-bottom: none;
            }

            .field-content {
              flex: 1;
              display: flex;
              align-items: center;

              .field-label {
                font-size: 12px;
                color: #666;
                min-width: 80px;
                margin-right: 10px;
              }

              .field-value {
                font-size: 12px;
                color: #333;
                word-break: break-all;
                line-height: 1.4;
              }
            }

            .field-copy {
              margin-left: 10px;

              .copy-btn-below {
                color: #4A95A9;
                cursor: pointer;
                font-size: 12px;
                padding: 2px 6px;
                border-radius: 3px;
                transition: all 0.3s;

                &:hover {
                  background-color: #4A95A9;
                  color: white;
                }

                &:focus {
                  outline: none;
                }
              }
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .image-upload-ocr-container {
    .upload-ocr-layout {
      flex-direction: column;

      .right-section {
        max-width: none;
        margin-top: 20px;
      }
    }
  }
}

@media (max-width: 768px) {
  .image-upload-ocr-container {
    .upload-ocr-layout {
      gap: 15px;
    }

    .ocr-results-section {
      .ocr-results-content {
        .ocr-result-group {
          .ocr-result-fields {
            .ocr-result-field {
              flex-direction: column;
              align-items: flex-start;

              .field-content {
                width: 100%;
                margin-bottom: 5px;

                .field-label {
                  min-width: auto;
                  margin-right: 5px;
                }
              }

              .field-copy {
                margin-left: 0;
                align-self: flex-end;
              }
            }
          }
        }
      }
    }
  }
}
</style>
