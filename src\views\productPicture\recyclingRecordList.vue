<template>
  <div class="v-container">
    <el-form :model="form" ref="recycleForm" label-width="110px">
      <el-row class="search-form">
        <el-col :xs="24" :sm="16" :md="8" :xl="8">
          <el-form-item label="发起时间" prop="createTime">
            <el-date-picker
              v-model="form.createTime"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :xs="12" :sm="8" :md="8" :xl="4">
          <el-form-item label="发起人" prop="createUser">
            <el-input v-model="form.createUser"></el-input>
          </el-form-item>
        </el-col>

        <el-col :xs="12" :sm="8" :md="8" :xl="4">
          <el-form-item label="商品编码" prop="productCode">
            <el-input v-model="form.productCode" placeholder="商品编码/原商品编码/商品id"></el-input>
          </el-form-item>
        </el-col>

        <el-col :xs="12" :sm="8" :md="8" :xl="4">
          <el-form-item label="商品名称" prop="productName">
            <el-input v-model="form.productName"></el-input>
          </el-form-item>
        </el-col>

        <el-col :xs="12" :sm="8" :md="8" :xl="4">
          <el-form-item label="拍摄任务单号" prop="applyCode">
            <el-input v-model="form.applyCode"></el-input>
          </el-form-item>
        </el-col>

        <el-col :xs="12" :sm="8" :md="8" :xl="4">
          <el-form-item label="图片版本号" prop="pictureVersion">
            <el-input v-model="form.pictureVersion"></el-input>
          </el-form-item>
        </el-col>

        <el-col :xs="12" :sm="8" :md="8" :xl="4">
          <el-form-item label="业务类型" prop="pictureStatus">
            <el-select v-model="form.pictureStatus" placeholder="请选择">
              <el-option value="" label="全部"></el-option>

              <el-option value="1" label="精修图驳回"></el-option>
              <el-option value="3" label="精修图删除"></el-option>

              <el-option value="2" label="设计驳回"></el-option>
              <el-option value="4" label="设计删除"></el-option>
              <el-option value="5" label="运营驳回"></el-option>
              <el-option value="6" label="运营删除"></el-option>
              <el-option value="7" label="图片合并"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :xs="12" :sm="8" :md="8" :xl="4">
          <el-form-item label="生产厂家" prop="manufacturer">
            <el-select
              v-model="form.manufacturer"
              placeholder="模糊查询"
              filterable
              clearable
              :filter-method="remoteMethod"
              @clear="clearMethod"
            >
              <el-option
                v-for="item in manufacturerOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="12" :sm="24" :md="8" :xl="12">
          <div style="text-align:right">
            <el-button size="medium" type="primary" @click="queryList">查询</el-button>
            <el-button size="medium" @click="resetList">重置</el-button>
          </div>
        </el-col>
      </el-row>
    </el-form>

    <div class="tool-btn">
      <el-button size="medium" @click="exportExcel">导出excel</el-button>
    </div>

    <div class="table-wrap">
      <vxe-table
        border
        highlight-hover-row
        resizable
        height="100%"
        auto-resize
        size="small"
        align="center"
        :seq-config="{startIndex: (pageNum-1) * pageSize}"
        :tooltip-config="{enterable: false}"
        :loading="tableLoading"
        :data="tableData"
        @sort-change="sortQueryList"
        ref="refVxeTable"
      >
        <vxe-table-column
          type="index"
          title="序号"
          width="60"
          show-header-overflow
          show-overflow
          fixed="left"
        ></vxe-table-column>

        <vxe-table-column
          field="createTime"
          title="发起时间"
          min-width="150"
          show-header-overflow
          show-overflow
          sortable="custom"
        >
          <template v-slot="{ row }">{{ row.createTime | dateTime }}</template>
        </vxe-table-column>

        <vxe-table-column
          field="createUser"
          title="发起人"
          min-width="100"
          show-header-overflow
          show-overflow
          sortable="custom"
        ></vxe-table-column>

        <vxe-table-column
          field="pictureStatus"
          title="业务类型"
          min-width="100"
          show-header-overflow
          show-overflow
          sortable="custom"
        >
          <template v-slot="{ row }">{{ ["","精修图驳回","设计驳回", '精修图删除', '设计删除',"运营驳回","运营删除","图片合并"][row.pictureStatus] }}</template>
        </vxe-table-column>

        <vxe-table-column
          field="productCode"
          title="商品编码"
          min-width="120"
          show-header-overflow
          show-overflow
          sortable="custom"
        ></vxe-table-column>

        <vxe-table-column
          field="productId"
          title="商品id"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>

        <vxe-table-column
          field="productName"
          title="商品名称"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>

        <vxe-table-column
          field="spec"
          title="规格/型号"
          min-width="100"
          show-header-overflow
          show-overflow
        ></vxe-table-column>

        <vxe-table-column
          field="manufacturer"
          title="生产厂家"
          min-width="150"
          show-header-overflow
          show-overflow
        ></vxe-table-column>

        <vxe-table-column
          field="applyCode"
          title="拍摄任务单号"
          min-width="130"
          show-header-overflow
          show-overflow
          sortable="custom"
        ></vxe-table-column>

        <vxe-table-column
          field="rejectPictureNum"
          title="图片数量"
          min-width="130"
          show-header-overflow
          show-overflow
          sortable="custom"
        ></vxe-table-column>

        <vxe-table-column
          field="pictureVersion"
          title="图片版本号"
          min-width="130"
          show-header-overflow
          show-overflow
        ></vxe-table-column>

        <vxe-table-column title="操作" min-width="100" fixed="right" show-overflow>
          <template v-slot="{ row }">
            <el-button type="text" @click="downloadPictures(row)">下载图片</el-button>
          </template>
        </vxe-table-column>
      </vxe-table>

      <!-- 分页 -->
      <el-row class="custom-pagination-wrap">
        <el-col>
          <el-pagination
            background
            :current-page.sync="pageNum"
            :page-size.sync="pageSize"
            :page-sizes="[20, 50, 100, 200]"
            :total="total"
            layout="prev, pager, next, jumper,total, sizes"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { dictSearchTypeAndName } from "@/api/dict.js";
import {
  picRecyclingRecordList,
  picRecyclingRecordExport,
  fileDownLoad,
} from "../../api/productPicture.js";

import { parseTime, utils_export_excel } from "@/utils/index.js";

export default {
  name: "",
  components: {},
  filters: {
    dateTime(value) {
      return parseTime(value);
    },
  },
  props: {},
  data() {
    return {
      manufacturerOptions: [], //生产厂家
      form: {
        createTime: [],
        applyCode: "", // 拍摄任务单号
        createUser: "", // 发起人
        productCode: "", //	商品编码
        productName: "", // 商品名称
        pictureVersion: "", // 图片版本
        pictureStatus: "", // 业务类型 (1:精修图驳回2:原图驳回)
        manufacturer: "", // 生产厂家
      },

      pageNum: 1,
      pageSize: 20,
      total: 0, //总条数

      sortFiled: "", // 排序查询
      sortRule: "", //(升序-ASC, 降序-DESC)

      tableLoading: false,
      tableData: [],
    };
  },
  computed: {},
  watch: {
    "form.createTime": (newV, oldV) => {
      console.log(newV, oldV);
    },
  },
  created() {
    /**
     * 查询生产厂家
     */
    this.remoteMethod("药");
    // 查询列表
    this.queryList();
  },
  mounted() {},
  methods: {
    getQueryParams() {
      let {
        createTime,
        applyCode, // 拍摄任务单号
        createUser, // 发起人
        createStartTime, // 发起开始时间
        createEndTime, // 发起结束时间
        productCode, //	商品编码
        productName, // 商品名称
        pictureVersion, // 图片版本
        pictureStatus, // 业务类型 (1:精修图驳回2:原图驳回)
        manufacturer, // 生产厂家
      } = this.form;
      return {
        applyCode,
        createUser,
        createStartTime: createTime[0] || "",
        createEndTime: createTime[1] || "",
        productCode,
        productName,
        pictureVersion,
        pictureStatus,
        manufacturer,
        page: this.pageNum,
        limit: this.pageSize,
        sortList: this.sortFiled
          ? [
              {
                order: "1",
                columnName: this.sortFiled,
                lift: this.sortRule, //(升序-ASC, 降序-DESC)
              },
            ]
          : [],
      };
    },

    // 排序查询
    sortQueryList({ column, prop, order }) {
      if (order == null) {
        this.sortFiled = "";
      } else {
        this.pageNum = 1;
        this.sortFiled = prop;
        this.sortRule = order;
      }
      this.queryList();
    },

    // 查询
    async queryList() {
      // console.log("查询列表");
      this.tableLoading = true;
      let resp = await picRecyclingRecordList(this.getQueryParams());
      this.total = resp.data.total;
      this.tableLoading = false;
      this.tableData = resp.data.list;
    },

    //  重置
    resetList() {
      this.$refs["recycleForm"].resetFields();
      this.queryList();
    },

    // 导出excel
    exportExcel() {
      console.log("导出excel");
      picRecyclingRecordExport(this.getQueryParams())
        .then((resp) => {
          if (resp.data.retCode) {
            //失败
            this.$message({
              showClose: true,
              type: "error",
              message: resp.data.retMsg,
            });
          } else {
            let fileName = resp.responseHeaders["content-disposition"].split(
              ";"
            )[1]; //取responseHeaders中的文件名
            fileName = decodeURIComponent(fileName.split("=")[1]);
            utils_export_excel(resp.data, fileName);
          }
        })
        .catch((err) => {
          this.$message({
            showClose: true,
            type: "error",
            message: "导出发生错误，请重试",
          });
        });
    },

    // 下载图片
    downloadPictures(rowData) {
      console.log("下载图片");
      // 下载图片
      fileDownLoad(rowData.annexUrl, `${rowData.productCode}-驳回图片.zip`)
        .then((resp) => {
          if (resp.data && resp.data.retCode) {
            // 失败
            this.$message({
              showClose: true,
              type: "error",
              message: resp.data.retMsg,
            });
          } else {
            let blob = new Blob([resp]);
            let blobUrl = window.URL.createObjectURL(blob);
            let a = document.createElement("a");
            a.style.display = "none";
            a.download = `${rowData.productCode}-驳回图片.zip`;
            a.href = blobUrl;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
          }
        })
        .catch((err) => {
          this.$message({
            showClose: true,
            type: "error",
            message: "下载失败，请重试",
          });
        });
    },

    /**
     * pageSize 切换每页条数
     */
    handleSizeChange(pageSize) {
      this.pageNum = 1;
      this.pageSize = pageSize;
      this.queryList();
    },

    /**
     * pageNum 切换页码 上一页 下一页 前往第几页
     */
    handleCurrentChange(currentPage) {
      this.pageNum = currentPage;
      this.queryList();
    },

    /**
     * 生产厂家模糊搜索
     */
    remoteMethod(query) {
      let dictName = query || "药";
      dictSearchTypeAndName({
        dictName: dictName,
        type: "12",
      })
        .then((resp) => {
          if (resp.list && resp.list.length) {
            let list = [];
            resp.list.forEach((item, index) => {
              list.push({
                label: item.dictName,
                value: item.id,
              });
            });
            this.manufacturerOptions = list;
          }
        })
        .catch((err) => {
          this.$message({
            showClose: true,
            type: "error",
            message: "请求发生错误，请重试",
          });
        });
    },

    /**
     * 生产厂家清空
     */
    clearMethod() {
      // this.manufacturerOptions = [];
    },
  },
};
</script>

<style lang="scss" scoped>
.v-container {
  .search-form {
    width: 100%;
    padding: 15px;
    border-bottom: 1px dashed #e4e4eb;
    .el-date-editor,
    .el-select {
      width: 100%;
    }
  }
  .tool-btn {
    width: 100%;
    padding: 15px;
  }
  .table-wrap {
    padding: 0 15px;
    min-height: 300px;
    width: 100%;
    height: calc(100vh - 331px);
  }
}
</style>
