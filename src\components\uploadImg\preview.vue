<template>
  <div>
    <!-- 插槽内容展示方式 -->
    <div v-if="type == 'slot'" class="img-slot" @click="openDlg()">
      <slot></slot>
    </div>

    <!-- 默认图片展示方式 -->
    <template v-else>
      <div v-if="mainPicture" class="img-box" @click="openDlg()">
        <img :src="mainPicture" alt class="img-s" />
        <span class="img-actions">
          <i class="el-icon-zoom-in"></i>
        </span>
      </div>
      <div v-else style="height: 83px"></div>
    </template>

    <!-- 图片预览对话框 -->
    <el-dialog :before-close="closeDlg" :visible.sync="dlgVisible" fullscreen class="text-lf" :destroy-on-close="true">
      <div slot="title" class="dlg-title">
        <span>{{ title }}</span>
        <div>
          <el-button :disabled="currImgObj.addBack" @click="addBackground(currImgUrl)" size="mini">{{
            !currImgObj.addBack ? "一键加白底" : "已填充白底"
          }}</el-button>
          <span class="cc" v-if="!!currImgObj && currImgObj.hasOwnProperty('pictureWidth')"
            >宽高：{{ currImgObj.pictureWidth + "px * " + currImgObj.pictureHeight + "px" }}</span
          >
        </div>
      </div>
      <div class="preview-box">
        <div class="productInfo" v-if="!!row">
          <div class="title">商品资料：</div>
          <div class="list">
            <span class="label">通用名：</span>
            <span class="content">{{ row.generalName || "无" }}</span>
          </div>
          <div class="list">
            <span class="label">商品名：</span>
            <span class="content">{{ row.skuName || "无" }}</span>
          </div>
          <!-- <div class="list">
            <span class="label">商品大类：</span>
            <span class="content">{{ row.spuCategoryName }}</span>
          </div> -->
          <div class="list">
            <span class="label">规格型号：</span>
            <span class="content">{{ row.spec || "无" }}</span>
          </div>
          <div class="list">
            <span class="label">批准文号：</span>
            <span class="content">{{ row.approvalNo || "无" }}</span>
          </div>
          <div class="list">
            <span class="label">生产厂家：</span>
            <span class="content">{{ row.manufacturerName || "无" }}</span>
          </div>
          <div class="list">
            <span class="label">小包装条码：</span>
            <span class="content">{{ row.smallPackageCode || "无" }}</span>
          </div>
          <div class="list">
            <span class="label">品牌：</span>
            <span class="content">{{ row.brand || "无" }}</span>
          </div>
          <div class="list">
            <span class="label">处方分类：</span>
            <span class="content">{{ row.prescriptionCategoryName || "无" }}</span>
          </div>
          <!-- <div class="list">
            <span class="label">是否委托生产：</span>
            <span class="content">{{ row.delegationProduct != 0 ? '是' : '否'}}</span>
          </div> -->
          <div class="list">
            <span class="label">受托生产厂家：</span>
            <span class="content">{{ row.entrustedManufacturerName || "无" }}</span>
          </div>
          <div class="list">
            <span class="label">管理属性：</span>
            <span class="content" v-if="!row || !row.sauAttrMap">无</span>
            <p class="content" v-if="row && row.sauAttrMap && row.sauAttrMap.key1">{{ row.sauAttrMap.key1 }}:{{ row.sauAttrMap.value1 }}</p>
            <p class="content" v-if="row && row.sauAttrMap && row.sauAttrMap.key2">{{ row.sauAttrMap.key2 }}:{{ row.sauAttrMap.value2 }}</p>
            <p class="content" v-if="row && row.sauAttrMap && row.sauAttrMap.key3">{{ row.sauAttrMap.key3 }}:{{ row.sauAttrMap.value3 }}</p>
            <p class="content" v-if="row && row.sauAttrMap && row.sauAttrMap.key4">{{ row.sauAttrMap.key4 }}:{{ row.sauAttrMap.value4 }}</p>
          </div>
          <div class="list">
            <span class="label">可用精修图版本数量：</span>
            <span class="content">{{ row.detailPictureVersionCount || "无" }}</span>
          </div>
          <div class="list">
            <span class="label">自营状态：</span>
            <span class="content">{{ row.preOperateStatusName || "无" }}</span>
          </div>
          <div class="list" v-if="fileList && fileList[currImgIndex]">
            <span class="label">爬取搜索关键词：</span>
            <span class="content">{{ fileList[currImgIndex].keywords || "无" }}</span>
          </div>
          <div class="list" v-if="fileList && fileList[currImgIndex]">
            <span class="label">来源网站：</span>
            <span class="content">{{ fileList && fileList[currImgIndex] ? fileList[currImgIndex].sourceSiteName : "无" }}</span>
          </div>
          <div class="list" v-if="fileList && fileList[currImgIndex]">
            <span class="label">图片地址：</span>
            <span class="content">{{ fileList[currImgIndex].productSource || "无" }}</span>
          </div>
          <div class="list">
            <span class="label">产地：</span>
            <span class="content">{{ row.originPlace || "无" }}</span>
          </div>
        </div>
        <span class="circle circle-lf" @click="handlePicturePreview(currImgIndex - 1)">
          <i class="el-icon-arrow-left"></i>
        </span>
        <!--  // 审核状态(0:待审核, 1:通过, 2:驳回, 3：删除，4:待预审核, 5:预审核通过, 6:预审核驳回) -->
        <div class="img-status" v-if="showStatus">
          <img v-show="currImgObj.auditStatus == '2' || currImgObj.auditStatus == '6'" src="../../assets/images/reject.png" />
          <img v-show="currImgObj.auditStatus == '3'" src="../../assets/images/delete.png" />
          <img v-show="currImgObj.auditStatus == '1' || currImgObj.auditStatus == '5'" src="../../assets/images/pass.png" />
        </div>
        <!-- 当前图片 -->
        <div class="img-l">
          <vue-cropper ref="cropper" :img="base64 ? base64 : currImgUrl" :mosaic="false"></vue-cropper>
        </div>

        <span class="circle circle-rt" @click="handlePicturePreview(currImgIndex + 1)">
          <i class="el-icon-arrow-right"></i>
        </span>
      </div>

      <div class="text-center">{{ currImgIndex + 1 }} / {{ total }}</div>

      <el-row class="fot-btn">
        <el-col :span="6">
          <el-button-group>
            <el-button size="medium" @click="downloadImg">下载</el-button>
            <el-button size="medium" @click="downloadOriginalImg">下载原图</el-button>
            <el-button size="medium" icon="el-icon-refresh-right" title="旋转" @click="toRevolve"></el-button>
          </el-button-group>
        </el-col>

        <el-col :span="14" class="text-rt">
          <el-upload
            v-if="operation == 0 && showSupBtn"
            style="display: inline-block; margin-right: 10px"
            ref="upload"
            multiple
            :action="action"
            :show-file-list="false"
            :data="{ applyCode: applyCode, productCode: productCode }"
            :before-upload="beforeAvatarUpload"
            :on-success="handleSuccess"
            :on-error="handleError"
          >
            <el-button size="medium" slot="trigger" type="primary">补充图片</el-button>
          </el-upload>

          <el-button v-if="operation == 0" size="medium" @click="bindThisOrgan()">绑定到本机构</el-button>
          <el-button v-if="operation == 1" size="medium" @click="removePicture()">删除</el-button>
        </el-col>
      </el-row>
    </el-dialog>
    <el-dialog :visible.sync="dlgVisible2" append-to-body title="请确认" :close-on-click-modal="false">
      <p style="font-size:16px;text-align:center;margin:20px;">请确认是否保存当前的白底图片？</p>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" type="info" @click="dlgVisible2 = false">取 消</el-button>
        <el-button size="mini" type="danger" @click="cancelSave">不保存</el-button>
        <el-button size="mini" type="primary" @click="confirmSave">保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  supplementPicture, // 补充图片 - 原图
  bindOrgan, // 绑定到本机构
  deletePicture // 删除图片 - 原图
} from "../../api/productPicture"
import { addBackground, saveAddBackground } from "@/api/common"
import vueCropper from "@/components/vue-cropper/src/vue-cropper"

export default {
  props: {
    // 列表 - 当前行数据
    rowData: {
      type: Object,
      default: () => {}
    },
    // 是否显示图片状态
    showStatus: {
      type: Boolean,
      default: false
    },
    // 图片list - 名称
    field: {
      type: String,
      default: ""
    },
    // 操作类型 （-1新建任务单,0上传原图,1下载原图,2上传精修图,3领取,4提交审核）
    operation: {
      type: Number,
      default: 9
    },
    // 查询参数
    params: {
      type: Object,
      default: () => {}
    },
    // 展示类型 - 文案/图片
    type: {
      type: String,
      default: ""
    },
    imgTitle: {
      type: String,
      default: ""
    },
    // 判断是否为图片版本管理状态
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  components: {
    vueCropper
  },
  computed: {
    //当前行数据
    row() {
      return this.rowData.row
    },
    // 当前是列表第几行
    seq() {
      return this.rowData.seq
    },
    // 图片列表
    fileList() {
      return this.row[this.field] ? this.row[this.field] : []
    },
    // 图片总数量
    total() {
      return this.fileList ? this.fileList.length : []
    },
    // 列表默认展示主图-地址
    mainPicture() {
      return this.total ? this.fileList[0].pictureUrl : ""
    },
    //单据编号
    applyCode() {
      return this.row.applyCode
    },
    // 商品编号
    productCode() {
      return this.row.productCode
    },
    // 商品名称
    productName() {
      return this.row.productName
    }
  },
  created() {},
  mounted() {},
  data() {
    return {
      close: null,
      continueIndex: null,
      originUrl: "",
      dlgVisible2: false,
      imgList: [],
      scale: 1,
      rotate: 0,
      // 当前图片-对象信息
      currImgObj: {},
      // 当前图片-路径
      currImgUrl: "",
      base64: "",
      // 当前图片-索引
      currImgIndex: -1,
      // 对话框-状态
      dlgVisible: false,
      // 对话框-标题
      title: "",
      // 补充图片提交地址
      action: process.env.VUE_APP_BASE_API + "/api/picture/add/uploadOriginalPicture",
      showSupBtn: false //  补充图片按钮的展示状态
    }
  },
  methods: {
    // 打开弹框
    openDlg() {
      if (this.fileList.length !== 0) {
        this.dlgVisible = true
        this.showSupBtn = this.row.pictureDetailVersion ? true : false
        this.handlePicturePreview(0) // 默认第一张
      }
    },
    closeDlg() {
      if (this.currImgIndex !== -1 && this.fileList[this.currImgIndex].addBack && !this.fileList[this.currImgIndex].isAddBack) {
        this.dlgVisible2 = true
        this.close = null
      } else {
        this.dlgVisible = false
      }
    },
    // 预览图片
    handlePicturePreview(index) {
      if (this.currImgIndex !== -1 && this.fileList[this.currImgIndex].addBack && !this.fileList[this.currImgIndex].isAddBack) {
        this.continueIndex = index
        this.dlgVisible2 = true
        this.close = 1
      } else {
        if (this.total > 0) {
          index < 0 && (index = this.total - 1)
          index >= this.total && (index = 0)
          this.changeTitle(this.fileList[index])
          this.currImgObj = this.fileList[index]
          this.currImgUrl = this.fileList[index].pictureUrl
          this.base64 = this.fileList[index].base64
          this.currImgIndex = index

          this.scale != 1 && this.styleRecovery()
        }
        if (this.isEdit) {
          this.$nextTick(() => {
            this.imgList = JSON.parse(JSON.stringify(this.row))
          })
        }
      }
    },

    // 图片样式复原
    styleRecovery() {
      this.scale = 1
      this.rotate = 0
      let el = this.$refs.img.style
      // el.setProperty("transform", `scale(1)`);
    },

    // 修改标题
    changeTitle(obj) {
      if (this.productCode && this.productName) {
        this.title = this.productCode + "_" + this.productName + "_" + obj.pictureVersion + "-" + obj.pictureOrdinal
      } else {
        this.title = this.imgTitle || obj.pictureName
      }
    },

    getBase64(src) {
      var _this = this
      return new Promise(function (resolve, reject) {
        let image = new Image()
        // 处理缓存
        if (src.indexOf('?')<0) {
          image.src = src + "?v=" + Math.random()
        } else {
           image.src = src
        }

        // 支持跨域图片
        image.crossOrigin = "*"
        image.onload = function () {
          let base64 = _this.transBase64FromImage(image)
          resolve(base64)
        }
      })
    },
    // 将网络图片转换成base64格式
    transBase64FromImage(image) {
      let canvas = document.createElement("canvas")
      canvas.width = image.width
      canvas.height = image.height
      let ctx = canvas.getContext("2d")
      ctx.drawImage(image, 0, 0, image.width, image.height)
      // 可选其他值 image/jpeg
      return canvas.toDataURL("image/png")
    },
    // 下载原图
    downloadOriginalImg() {
      this.getBase64(this.currImgUrl).then(data=>{
        let a = document.createElement('a')
        a.download = this.title
        a.href = data
        a.click()
      })
    },
    // 下载图片
    downloadImg() {
      var aLink = document.createElement("a")
      aLink.download = this.title
      this.$refs.cropper.getCropBlob(data => {
        this.downImg = window.URL.createObjectURL(data)
        aLink.href = window.URL.createObjectURL(data)
        aLink.click()
      })
    },
    // 旋转图片
    toRevolve() {
      this.$refs.cropper.rotateRight()
    },
    toPercent(point) {
      var str = Number(point * 100).toFixed(1)
      str += "%"
      return str
    },

    // 上传图片前 - 校验
    beforeAvatarUpload(file) {
      // 补充图片前应该判断 是否有绑定版本
      if (!this.currImgObj.pictureVersion) {
        this.$message.error("请先绑定图片版本！")
        return false
      }
      // 图片格式 大小 名称格式 是否重复
      const isJPG = file.type === "image/jpeg" || file.type === "image/jpg" || file.type === "image/bmp" || file.type === "image/png"

      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG) {
        this.$message.error("上传图片只能是jpg，png，jpeg，bmp格式！")
        return false
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 2MB!")
        return false
      }

      let i = file.name.indexOf("-")
      let codeStr = file.name.substring(0, i) // 商品编码
      let j = file.name.lastIndexOf(".")
      let str = file.name.substring(i + 1, j)

      if (i === -1) {
        this.$message.error("该图片名称格式不正确！")
        return false
      } else if (this.currImgObj.productCode != codeStr) {
        this.$message.error("该图片名称商品编号不正确！")
        return false
      } else if (this.currImgObj.pictureOrdinal == str) {
        this.$message.error("图片名称重复，请修改后保存")
        return false
      }
      return true
    },

    // 补充图片-成功
    handleSuccess(res, file, fileList) {
      // console.log(res, file, fileList);
      if (res.retCode == 0) {
        this.$message.success("补充成功！")
        // 关闭对话框
        this.dlgVisible = false
        // 隐藏补充图片按钮
        this.showSupBtn = false
        // 刷新列表
        this.$emit("update-list", this.params)
      } else {
        this.$message.error(res.retMsg)
      }
    },

    // 补充图片-失败
    handleError(err, file, fileList) {
      // console.log(err, file, fileList);
      this.$message.error(`上传失败！${err}`)
    },

    // 绑定到本机构
    bindThisOrgan() {
      // 判断图片版本是否有值
      if (this.row.pictureDetailVersion) {
        if (this.row.pictureDetailVersion != this.currImgObj.pictureVersion) {
          this.$confirm(`商品已绑定版本【${this.row.pictureDetailVersion}】，是否确定修改为【${this.currImgObj.pictureVersion}】？`, "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消"
          })
            .then(() => {
              //与后台交互 提交绑定信息
              this.submitBind(this.currImgObj)
            })
            .catch(() => {})
        } else {
          this.$message.error(`已绑定版本【${this.row.pictureDetailVersion}】，无需重复绑定`)
        }
      } else {
        this.submitBind(this.currImgObj)
      }
    },

    // 删除图片
    removePicture() {
      this.$confirm(`该操作不可恢复，是否确定删除？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消"
      })
        .then(() => {
          //删除
          this.submitDetele(this.currImgObj)
        })
        .catch(() => {})
    },

    // 提交删除
    submitDetele(imgData) {
      let {
        productCode, // 商品编码
        id // 图片id
      } = imgData
      deletePicture(this.applyCode, productCode, id).then(res => {
        if (res.retCode == 0) {
          this.$message.success("删除成功！")
          // 关闭对话框
          this.dlgVisible = false
          // 隐藏补充图片按钮
          this.showSupBtn = false
          // 刷新列表
          this.$emit("update-list", this.params)
        } else {
          this.$message.error(res.retMsg)
        }
      })
    },

    // 提交绑定
    submitBind(imgData) {
      // console.log(data, "提交绑定信息-----");
      let {
        productCode, // 商品编码
        pictureVersion // 要绑定的精修图版本
      } = imgData
      // 绑定成功后要修改列表中的图片绑定版本信息 选择确定后替换绑定图片版本
      bindOrgan(this.applyCode, productCode, pictureVersion).then(res => {
        if (res.retCode == 0) {
          // this.dlgVisible = false;
          this.$message.success("绑定成功！")
          // 展示补充图片按钮
          this.showSupBtn = true
          // 刷新列表
          this.$emit("update-list", this.params)
        } else {
          this.$message.error(res.retMsg)
        }
      })
    },
    // 图片加白底
    async addBackground(e) {
      console.log(e)
      this.originUrl = e
      try {
        const res = await addBackground({ pictureUrl: e })
        if (res.retCode === 0) {
          console.log(res)
          this.fileList[this.currImgIndex].pictureUrl = res.data.pictureUrl
          this.fileList[this.currImgIndex].base64 = res.data.base64
          this.fileList[this.currImgIndex].addBack = true
          this.currImgUrl = res.data.pictureUrl
          this.base64 = res.data.base64
        } else {
          this.$message.error(res.retMsg)
        }
      } catch (error) {
        this.$message.error(res.retMsg)
      }
    },
    // 取消加白底
    cancelSave() {
      this.fileList[this.currImgIndex].pictureUrl = this.originUrl
      this.fileList[this.currImgIndex].addBack = false
      this.dlgVisible2 = false
      if (this.close) {
        this.handlePicturePreview(this.continueIndex)
      } else {
        this.closeDlg()
      }
    },
    // 保存加白底
    async confirmSave() {
      this.originUrl = ""
      this.fileList[this.currImgIndex].isAddBack = true
      this.dlgVisible2 = false
      try {
        if (this.fileList[this.currImgIndex].id) {
          const res = await saveAddBackground({
            pictureId: this.fileList[this.currImgIndex].id,
            pictureUrl: this.fileList[this.currImgIndex].pictureUrl
          })
          if (res.retCode !== 0) {
            this.$message.error(res.retMsg)
          } else {
            if (this.close) {
              this.handlePicturePreview(this.continueIndex)
            } else {
              this.closeDlg()
            }
          }
        } else {
          if (this.close) {
            this.handlePicturePreview(this.continueIndex)
          } else {
            this.closeDlg()
          }
        }
      } catch (error) {
        this.$message.error(res.retMsg)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.el-upload-list {
  display: none;
}
.el-dialog__wrapper /deep/ .el-dialog .el-dialog__body {
  padding: 0 0 20px 0;
}
.el-dialog__wrapper /deep/ .el-dialog {
  // max-height: 100%;
}
.text-center {
  text-align: center;
  margin-top: 10px;
}
.text-lf {
  text-align: left;
}
.text-rt {
  text-align: right;
}
.img-slot:hover {
  cursor: pointer;
}
.fot-btn {
  margin: 0 20px;
}
.img-box {
  position: relative;
  display: inline-block;
  border-radius: 5px;
  width: 80px;
  height: 80px;
}
.img-s {
  border-radius: 5px;
  width: 80px;
  height: 80px;
}
.img-status {
  position: absolute;
  width: 100px;
  height: 100px;
  top: 10px;
  right: 10px;
  z-index: 200;
  img {
    width: 100%;
  }
}
.img-l {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  img {
    // position: absolute;
    // top: 50%;
    // left: 50%;
    // transform: translate(-50%, -50%);
    position: relative;
    left: 0;
    right: 0;
  }
}

.img-actions {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  border-radius: 5px;
  cursor: pointer;
  text-align: center;
  opacity: 0;
  font-size: 24px;
  color: #fff;
  background-color: rgb(0, 0, 0, 0.5);
  line-height: 80px;
  transition: opacity 0.3s;
}
.img-actions:hover {
  opacity: 0.5;
}
.preview-box {
  position: relative;
  width: 100%;
  height: 90%;
  display: flex;
}
.circle {
  position: absolute;
  z-index: 2000;
  top: calc(50% - 15px);
  width: 30px;
  height: 30px;
  border-radius: 50%;
  opacity: 0.5;
  background-color: #ccc;
  text-align: center;
  font-size: 22px;
  line-height: 30px;
  transition: opacity 0.3s;
}
.circle-lf {
  left: 440px;
}
.circle-rt {
  right: 20px;
}
.circle:hover {
  opacity: 1;
}
.dlg-title {
  display: flex;
  justify-content: space-between;
  .cc {
    padding: 0 50px;
  }
}
.productInfo {
  min-width: 400px;
  max-width: 500px;
  padding: 20px;
  line-height: 32px;
  .title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
  }
  .list {
    .label {
      font-weight: bold;
      color: #333;
    }
    .content {
      text-indent: 10px;
    }
  }
}
</style>
