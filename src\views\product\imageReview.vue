<template>
  <div class="image-review-container">
    <!-- 图片审核标题区域 -->
    <div class="image-review-header">
      <div class="review-title-section">
        <span class="review-title">图片审核</span>
        <el-tooltip
          content="此处为新品上报的图片审核流程，此处图片审核完，不会流转至原独立的图片待审核列表。请务必按照正常的图片审核要求进行审核。"
          placement="top"
        >
          <i class="el-icon-question review-help-icon"></i>
        </el-tooltip>
        <span class="collapse-toggle" @click="toggleCollapse">
          <i :class="isCollapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
        </span>
      </div>
    </div>

    <!-- 图片区域 - 可折叠 -->
    <div v-show="!isCollapsed" class="image-sections">
      <!-- 主图区域 -->
      <div class="image-section main-image-section">
        <div class="section-title">
          主图
          <span class="section-subtitle">(最多1张)</span>
        </div>
        <div class="section-content">
          <draggable
            class="draggable-area main-draggable"
            :list="mainImageList"
            :group="{ name: 'imageGroup', pull: true, put: true }"
            @change="handleMainImageChange"
            :options="{ animation: 200, ghostClass: 'sortable-ghost' }"
          >
            <div
              v-for="(item, index) in mainImageList"
              :key="item.uid || item.mediaUrl || index"
              class="image-item-wrapper draggable-item"
            >
              <div class="image-container">
                <img
                  :src="item.url || item.mediaUrl"
                  :alt="item.name || '主图'"
                  class="image-preview"
                  @click="previewImage(item)"
                />
                <div class="image-actions">
                  <i class="el-icon-view" @click="previewImage(item)" title="预览"></i>
                  <i class="el-icon-delete" @click="removeImage('main', index)" title="删除"></i>
                </div>
                <!-- OCR按钮 -->
                <div class="ocr-button-container" v-if="enableOCR">
                  <span
                    v-if="getOcrLoading(item)"
                    class="ocr-extract-btn loading"
                  >
                    提取中
                  </span>
                  <span
                    v-else-if="isImageReady(item)"
                    class="ocr-extract-btn clickable"
                    @click="extractOCR(item)"
                  >
                    提取图片文字
                  </span>
                  <span
                    v-else
                    class="ocr-extract-btn disabled"
                  >
                    等待上传完成
                  </span>
                </div>
              </div>
            </div>
          </draggable>
          <!-- 空状态时的上传区域 -->
          <div v-if="mainImageList.length === 0" class="empty-upload-area">
            <ImageUploadWithOCR
              :fileList="[]"
              :limit="1"
              :uploadUrl="uploadUrl"
              :enableOCR="enableOCR"
              :disabled="disabled"
              @change="handleMainImageUpload"
            />
          </div>
        </div>
      </div>

      <!-- 外包装图片区域 -->
      <div class="image-section package-image-section">
        <div class="section-title">
          外包装图片
          <span class="section-subtitle">(最多60张)</span>
        </div>
        <div class="section-content">
          <draggable
            class="draggable-area package-draggable"
            :list="packageImageList"
            :group="{ name: 'imageGroup', pull: true, put: true }"
            @change="handlePackageImageChange"
            :options="{ animation: 200, ghostClass: 'sortable-ghost' }"
          >
            <div
              v-for="(item, index) in packageImageList"
              :key="item.uid || item.mediaUrl || index"
              class="image-item-wrapper draggable-item"
            >
              <div class="image-container">
                <img
                  :src="item.url || item.mediaUrl"
                  :alt="item.name || '外包装图片'"
                  class="image-preview"
                  @click="previewImage(item)"
                />
                <div class="image-actions">
                  <i class="el-icon-view" @click="previewImage(item)" title="预览"></i>
                  <i class="el-icon-delete" @click="removeImage('package', index)" title="删除"></i>
                </div>
                <!-- OCR按钮 -->
                <div class="ocr-button-container" v-if="enableOCR">
                  <span
                    v-if="getOcrLoading(item)"
                    class="ocr-extract-btn loading"
                  >
                    提取中
                  </span>
                  <span
                    v-else-if="isImageReady(item)"
                    class="ocr-extract-btn clickable"
                    @click="extractOCR(item)"
                  >
                    提取图片文字
                  </span>
                  <span
                    v-else
                    class="ocr-extract-btn disabled"
                  >
                    等待上传完成
                  </span>
                </div>
              </div>
            </div>
          </draggable>
          <!-- 添加新图片的上传区域 -->
          <div v-if="packageImageList.length < 60" class="add-upload-area">
            <ImageUploadWithOCR
              :fileList="[]"
              :limit="60 - packageImageList.length"
              :uploadUrl="uploadUrl"
              :enableOCR="enableOCR"
              :disabled="disabled"
              @change="handlePackageImageUpload"
            />
          </div>
        </div>
      </div>

      <!-- 说明书图片区域 -->
      <div class="image-section instruction-image-section">
        <div class="section-title">
          说明书图片
          <span class="section-subtitle">(最多60张)</span>
        </div>
        <div class="section-content">
          <draggable
            class="draggable-area instruction-draggable"
            :list="instructionImageList"
            :group="{ name: 'imageGroup', pull: true, put: true }"
            @change="handleInstructionImageChange"
            :options="{ animation: 200, ghostClass: 'sortable-ghost' }"
          >
            <div
              v-for="(item, index) in instructionImageList"
              :key="item.uid || item.mediaUrl || index"
              class="image-item-wrapper draggable-item"
            >
              <div class="image-container">
                <img
                  :src="item.url || item.mediaUrl"
                  :alt="item.name || '说明书图片'"
                  class="image-preview"
                  @click="previewImage(item)"
                />
                <div class="image-actions">
                  <i class="el-icon-view" @click="previewImage(item)" title="预览"></i>
                  <i class="el-icon-delete" @click="removeImage('instruction', index)" title="删除"></i>
                </div>
                <!-- OCR按钮 -->
                <div class="ocr-button-container" v-if="enableOCR">
                  <span
                    v-if="getOcrLoading(item)"
                    class="ocr-extract-btn loading"
                  >
                    提取中
                  </span>
                  <span
                    v-else-if="isImageReady(item)"
                    class="ocr-extract-btn clickable"
                    @click="extractOCR(item)"
                  >
                    提取图片文字
                  </span>
                  <span
                    v-else
                    class="ocr-extract-btn disabled"
                  >
                    等待上传完成
                  </span>
                </div>
              </div>
            </div>
          </draggable>
          <!-- 添加新图片的上传区域 -->
          <div v-if="instructionImageList.length < 60" class="add-upload-area">
            <ImageUploadWithOCR
              :fileList="[]"
              :limit="60 - instructionImageList.length"
              :uploadUrl="uploadUrl"
              :enableOCR="enableOCR"
              :disabled="disabled"
              @change="handleInstructionImageUpload"
            />
          </div>
        </div>
      </div>

      <!-- 渠道图片使用控制 -->
      <div class="channel-image-controls">
        <div class="control-group">
          <span class="control-label">* 是否使用渠道上传图片</span>
          <el-radio-group v-model="channelImageUsage" :disabled="disabled">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </div>
        <div class="control-group">
          <span class="control-label">* 图片质量</span>
          <el-radio-group v-model="imageQuality" :disabled="disabled">
            <el-radio label="需设计精修">需设计精修</el-radio>
            <el-radio label="可直接使用">可直接使用</el-radio>
          </el-radio-group>
        </div>
      </div>
    </div>

    <!-- 图片预览模态框 -->
    <el-dialog
      title="图片预览"
      :visible.sync="previewVisible"
      width="80%"
      center
    >
      <div class="image-preview-container">
        <img :src="previewImageUrl" alt="预览图片" class="preview-image" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ImageUploadWithOCR from './components/ImageUploadWithOCR.vue'
import draggable from 'vuedraggable'

export default {
  name: 'ImageReview',
  components: {
    ImageUploadWithOCR,
    draggable
  },
  props: {
    // 初始图片数据
    initialData: {
      type: Object,
      default: () => ({
        mainImageList: [],
        outPackageImgList: [],
        directionImgList: []
      })
    },
    // 上传地址
    uploadUrl: {
      type: String,
      default: ''
    },
    // 是否启用OCR
    enableOCR: {
      type: Boolean,
      default: true
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 折叠状态
      isCollapsed: false,

      // 图片列表
      mainImageList: [],
      packageImageList: [],
      instructionImageList: [],

      // 渠道图片使用控制
      channelImageUsage: true, // 默认选择"是"
      imageQuality: '需设计精修', // 默认选择"需设计精修"

      // OCR 相关状态
      ocrLoadingMap: {}, // 记录每个图片的OCR加载状态

      // 图片预览
      previewVisible: false,
      previewImageUrl: ''
    }
  },
  watch: {
    // 监听初始数据变化
    initialData: {
      handler(newData) {
        this.initializeImageData(newData)
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    // 组件挂载后初始化数据
    this.initializeImageData(this.initialData)
  },
  methods: {
    // 初始化图片数据
    initializeImageData(data) {
      if (!data) return

      // 初始化外包装图片
      this.packageImageList = Array.isArray(data.outPackageImgList)
        ? [...data.outPackageImgList]
        : []

      // 初始化说明书图片
      this.instructionImageList = Array.isArray(data.directionImgList)
        ? [...data.directionImgList]
        : []

      // 初始化主图 - 自动显示外包装图片的第一张
      if (Array.isArray(data.mainImageList) && data.mainImageList.length > 0) {
        this.mainImageList = [...data.mainImageList]
      } else if (this.packageImageList.length > 0) {
        // 如果没有主图但有外包装图片，自动设置第一张外包装图片为主图
        this.mainImageList = [{ ...this.packageImageList[0] }]
      } else {
        this.mainImageList = []
      }
    },

    // 切换折叠状态
    toggleCollapse() {
      this.isCollapsed = !this.isCollapsed
    },

    // 处理主图变化
    handleMainImageChange(evt) {
      this.handleDragChange(evt, 'main')
    },

    // 处理外包装图片变化
    handlePackageImageChange(evt) {
      this.handleDragChange(evt, 'package')
    },

    // 处理说明书图片变化
    handleInstructionImageChange(evt) {
      this.handleDragChange(evt, 'instruction')
    },

    // 处理拖拽变化的通用方法
    handleDragChange(evt, targetType) {
      // 如果是添加操作（从其他区域拖拽过来）
      if (evt.added) {
        const addedItem = evt.added.element
        const newIndex = evt.added.newIndex

        // 根据目标类型处理
        if (targetType === 'main') {
          // 主图区域只能有一张图片
          if (this.mainImageList.length > 1) {
            this.mainImageList = [addedItem]
          }
        }

        // 触发上传逻辑（如果需要）
        this.handleCrossAreaDrag(addedItem, targetType, newIndex)
      }

      // 如果是移除操作（拖拽到其他区域）
      if (evt.removed) {
        // 可以在这里处理移除逻辑
        this.emitDataChange()
      }

      // 如果是同区域内移动
      if (evt.moved) {
        this.emitDataChange()
      }
    },

    // 处理跨区域拖拽
    handleCrossAreaDrag(item, targetType, index) {
      // 这里可以添加跨区域拖拽的特殊处理逻辑
      // 比如触发上传API、更新数据库等
      console.log(`图片拖拽到${targetType}区域，位置：${index}`, item)

      // 触发数据变化事件
      this.emitDataChange()
    },

    // 预览图片
    previewImage(item) {
      this.previewImageUrl = item.url || item.mediaUrl
      this.previewVisible = true
    },

    // 移除图片
    removeImage(type, index) {
      this.$confirm('确定要删除这张图片吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.removeImageAtIndex(type, index)
      }).catch(() => {
        // 用户取消删除
      })
    },

    // 获取OCR加载状态
    getOcrLoading(item) {
      const key = this.getImageKey(item)
      return this.ocrLoadingMap[key] || false
    },

    // 判断图片是否准备好进行OCR
    isImageReady(item) {
      return !!(item.url || item.mediaUrl)
    },

    // 提取OCR
    async extractOCR(item) {
      const key = this.getImageKey(item)

      if (this.ocrLoadingMap[key]) {
        this.$message.warning('正在提取中，请稍候...')
        return
      }

      this.$set(this.ocrLoadingMap, key, true)

      try {
        // 这里调用OCR API，可以复用 ImageUploadWithOCR 组件的逻辑
        // 暂时模拟OCR调用
        await new Promise(resolve => setTimeout(resolve, 2000))

        this.$message.success('文字提取完成')
        // 这里可以处理OCR结果

      } catch (error) {
        this.$message.error('文字提取失败，请重试')
        console.error('OCR提取失败:', error)
      } finally {
        this.$set(this.ocrLoadingMap, key, false)
      }
    },

    // 获取图片的唯一标识
    getImageKey(item) {
      return item.uid || item.mediaUrl || item.url || Math.random().toString(36)
    },

    // 处理单个图片变化
    handleSingleImageChange(list, type, index) {
      if (list.length === 0) {
        // 图片被删除
        this.removeImageAtIndex(type, index)
      } else {
        // 图片被更新
        this.updateImageAtIndex(type, index, list[0])
      }
    },

    // 处理主图上传
    handleMainImageUpload(list) {
      if (list.length > 0) {
        this.mainImageList = [list[0]] // 主图只能有一张
        this.emitDataChange()
      }
    },

    // 处理外包装图片上传
    handlePackageImageUpload(list) {
      this.packageImageList = [...this.packageImageList, ...list]
      this.emitDataChange()
    },

    // 处理说明书图片上传
    handleInstructionImageUpload(list) {
      this.instructionImageList = [...this.instructionImageList, ...list]
      this.emitDataChange()
    },

    // 移除指定位置的图片
    removeImageAtIndex(type, index) {
      switch (type) {
        case 'main':
          this.mainImageList.splice(index, 1)
          break
        case 'package':
          this.packageImageList.splice(index, 1)
          break
        case 'instruction':
          this.instructionImageList.splice(index, 1)
          break
      }
      this.emitDataChange()
    },

    // 更新指定位置的图片
    updateImageAtIndex(type, index, newImage) {
      switch (type) {
        case 'main':
          this.$set(this.mainImageList, index, newImage)
          break
        case 'package':
          this.$set(this.packageImageList, index, newImage)
          break
        case 'instruction':
          this.$set(this.instructionImageList, index, newImage)
          break
      }
      this.emitDataChange()
    },

    // 发出数据变化事件
    emitDataChange() {
      const data = {
        mainImageList: [...this.mainImageList],
        outPackageImgList: [...this.packageImageList],
        directionImgList: [...this.instructionImageList],
        channelImageUsage: this.channelImageUsage,
        imageQuality: this.imageQuality
      }
      this.$emit('change', data)
    },

    // 获取当前数据（供父组件调用）
    getData() {
      return {
        mainImageList: [...this.mainImageList],
        outPackageImgList: [...this.packageImageList],
        directionImgList: [...this.instructionImageList],
        channelImageUsage: this.channelImageUsage,
        imageQuality: this.imageQuality
      }
    },

    // 验证数据
    validate() {
      // 可以添加验证逻辑
      return true
    }
  }
}
</script>

<style lang="scss" scoped>
.image-review-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #e4e7ed;

  .image-review-header {
    margin-bottom: 20px;
    border-bottom: 1px solid #e4e7ed;
    padding-bottom: 15px;

    .review-title-section {
      display: flex;
      align-items: center;
      gap: 8px;

      .review-title {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
      }

      .review-help-icon {
        color: #909399;
        cursor: pointer;
        font-size: 14px;

        &:hover {
          color: #409eff;
        }
      }

      .collapse-toggle {
        margin-left: auto;
        cursor: pointer;
        color: #606266;
        font-size: 14px;
        padding: 4px;
        border-radius: 2px;
        transition: all 0.3s;

        &:hover {
          background-color: #f5f7fa;
          color: #409eff;
        }

        i {
          transition: transform 0.3s;
        }
      }
    }
  }

  .image-sections {
    .image-section {
      margin-bottom: 30px;

      .section-title {
        font-size: 14px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 5px;

        .section-subtitle {
          font-size: 12px;
          color: #909399;
          font-weight: normal;
        }
      }

      .section-content {
        .draggable-area {
          display: flex;
          flex-wrap: wrap;
          gap: 10px;
          min-height: 120px;
          padding: 10px;
          border: 2px dashed #d9d9d9;
          border-radius: 4px;
          transition: border-color 0.3s;

          &:hover {
            border-color: #409eff;
          }

          .image-item-wrapper {
            position: relative;

            &.draggable-item {
              cursor: move;

              .image-container {
                position: relative;
                width: 100px;
                height: auto;
                border-radius: 4px;
                overflow: hidden;
                border: 1px solid #e4e7ed;
                transition: all 0.3s;

                &:hover {
                  border-color: #409eff;
                  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);

                  .image-actions {
                    opacity: 1;
                  }
                }

                .image-preview {
                  width: 100%;
                  height: 100px;
                  object-fit: cover;
                  display: block;
                  cursor: pointer;
                }

                .image-actions {
                  position: absolute;
                  top: 5px;
                  right: 5px;
                  display: flex;
                  gap: 5px;
                  opacity: 0;
                  transition: opacity 0.3s;

                  i {
                    width: 20px;
                    height: 20px;
                    line-height: 20px;
                    text-align: center;
                    background-color: rgba(0, 0, 0, 0.6);
                    color: white;
                    border-radius: 2px;
                    cursor: pointer;
                    font-size: 12px;

                    &:hover {
                      background-color: rgba(0, 0, 0, 0.8);
                    }
                  }
                }

                .ocr-button-container {
                  width: 100%;
                  padding: 4px;

                  .ocr-extract-btn {
                    width: 100%;
                    font-size: 12px;
                    padding: 4px 8px;
                    border-radius: 4px;
                    text-align: center;
                    line-height: 1.2;
                    border: 1px solid;
                    transition: all 0.3s;
                    user-select: none;
                    display: block;

                    &.clickable {
                      background-color: #409eff;
                      border-color: #409eff;
                      color: #ffffff;
                      cursor: pointer;

                      &:hover {
                        background-color: #66b1ff;
                        border-color: #66b1ff;
                      }
                    }

                    &.loading {
                      background-color: #909399;
                      border-color: #909399;
                      color: #ffffff;
                      cursor: not-allowed;
                    }

                    &.disabled {
                      background-color: #f5f7fa;
                      border-color: #e4e7ed;
                      color: #c0c4cc;
                      cursor: not-allowed;
                    }
                  }
                }
              }
            }
          }
        }

        .empty-upload-area,
        .add-upload-area {
          margin-top: 10px;
        }
      }
    }

    // 主图区域特殊样式
    .main-image-section {
      .draggable-area {
        justify-content: flex-start;
        max-width: 120px;
      }
    }

    // 外包装图片和说明书图片区域样式
    .package-image-section,
    .instruction-image-section {
      .draggable-area {
        justify-content: flex-start;
      }
    }
  }

  .channel-image-controls {
    margin-top: 30px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;

    .control-group {
      display: flex;
      align-items: center;
      margin-bottom: 15px;

      &:last-child {
        margin-bottom: 0;
      }

      .control-label {
        min-width: 160px;
        font-size: 14px;
        color: #303133;
        margin-right: 20px;

        &::before {
          content: "*";
          color: #f56c6c;
          margin-right: 4px;
        }
      }

      /deep/ .el-radio-group {
        .el-radio {
          margin-right: 20px;

          .el-radio__label {
            font-size: 14px;
          }
        }
      }
    }
  }
}

// 图片预览样式
.image-preview-container {
  text-align: center;

  .preview-image {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
  }
}

// 拖拽相关样式
.sortable-ghost {
  opacity: 0.5;
}

.sortable-chosen {
  opacity: 0.8;
}

.sortable-drag {
  opacity: 0.9;
}

// 响应式设计
@media (max-width: 768px) {
  .image-review-container {
    padding: 15px;

    .image-sections {
      .image-section {
        .section-content {
          .draggable-area {
            gap: 8px;
            padding: 8px;
          }
        }
      }
    }

    .channel-image-controls {
      padding: 15px;

      .control-group {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;

        .control-label {
          min-width: auto;
          margin-right: 0;
        }
      }
    }
  }
}
</style>