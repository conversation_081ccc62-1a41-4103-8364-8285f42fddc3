<template>
  <div class="component-container" v-if="!isGIFT">
    <div class="sau-title">
      <div>
        管理属性
        <el-popover
          placement="top"
          width="200"
          trigger="hover"
          :content="
            $store.getters.info[0] ? $store.getters.info[0].showContent : ''
          "
        >
          <i class="info-suffix-icon el-icon-question" slot="reference"></i>
        </el-popover>
      </div>
      <div
        class="grounp-btn"
        v-if="
          this.operationType == 'add' ||
          this.operationType == 'edit' ||
          this.operationType == 'reuse' ||
          operationType == 'draft' ||
          operationType == 'update'
        "
      >
        <el-button :disabled="disableStatus" type="primary" size="mini" @click="addSau">添加</el-button>
      </div>
    </div>
    <!-- 单个sau编辑 -->
    <sauform
      :formDisable="true"
      ref="sauForm"
      :sauForm="currentSauForm"
      :sauItems="sauItems"
      :cctjID="cctjID"
      :manufacturerOptions="manufacturerOptions"
      v-if="
        sauTableData.length < 2 &&
        $route.query.page != 'spu' &&
        $route.query.page != 'sku'
      "
    ></sauform>
    <div class="sau-table" v-else>
      <vxe-table
        style="z-index:0"
        border
        resizable
        max-height="500"
        align="center"
        highlight-current-row
        :radio-config="{ labelFiled: '', trigger: 'row' }"
        :tooltip-config="{ enterable: false }"
        :data="sauTableData"
        ref="sauTable"
      >
        <vxe-table-column
          type="index"
          title="序号"
          width="60"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="skuCode"
          title="sku编码"
          min-width="100"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="productId"
          title="商品id"
          min-width="100"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="sauCode"
          title="商品编码"
          min-width="100"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="sauAttrList"
          title="属性&属性值"
          min-width="200"
          show-header-overflow
          :show-overflow-tooltip="true"
        >
          <template v-slot="{ row }">
            <div style="padding: 10px 0">
              <p
                style="margin: 0"
                v-for="(item, index) in row.sauAttrList"
                :key="index"
              >
                {{ item.attrName }}：
                <!-- 包装单位 -->
                <template v-if="item.attrId == 23"
                  >{{ item.attrValue }} 月</template
                >
                <!-- 生产厂家 -->
                <template v-else-if="item.attrId == 21">{{
                  item.attrValueName
                }}</template>
                <!-- 处方分类 -->
                <template v-else-if="item.attrId == 22">{{
                  item.attrValue
                    | findName(
                      $store.getters.selectOptions.prescriptionCategoryOptions
                    )
                }}</template>
                <!-- 储存条件 -->
                <template v-else-if="item.attrId === cctjID">{{
                  item.attrValue
                    | findName($store.getters.selectOptions.storageCondOptions)
                }}</template>
                <!-- 包装单位 -->
                <template v-else-if="item.attrId == 25">{{
                  item.attrValue
                    | findName($store.getters.selectOptions.packageUnitOptions)
                }}</template>
                <template v-else>{{ item.attrValue }}</template>
              </p>
            </div>
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="offlineBusinessType"
          title="是否线下业务"
          min-width="120"
          show-header-overflow
          show-overflow
          ><template v-slot="{ row }">
            {{ row.offlineBusinessType | filterOfflineBusinessType }}
          </template></vxe-table-column
        >
        <vxe-table-column
          field="preOperateStatusName"
          title="自营状态"
          min-width="100"
          show-header-overflow
          show-overflow
        ></vxe-table-column>

        <vxe-table-column
          field="disableStatus"
          title="停用状态"
          min-width="100"
        >
          <!-- 0:'停用'1:'启用' -->
          <template v-slot="{ row }">{{
            ["停用", "启用"][row.disableStatus]
          }}</template>
        </vxe-table-column>

        <vxe-table-column
          field="disableReason"
          title="停用原因"
          min-width="100"
          show-header-overflow
          show-overflow
        ></vxe-table-column>

        <vxe-table-column
          field="outPackageImgList"
          title="外包装图片"
          min-width="100"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">
            <el-link
              :underline="false"
              type="primary"
              @click.stop="previewImg(row.outPackageImgList)"
              >预览（{{ row.outPackageImgList.length }}）</el-link
            >
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="directionImgList"
          title="说明书图片"
          min-width="100"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">
            <el-link
              :underline="false"
              type="primary"
              @click.stop="previewImg(row.directionImgList)"
              >预览（{{ row.directionImgList.length }}）</el-link
            >
          </template>
        </vxe-table-column>
        <vxe-table-column
          title="操作"
          width="120"
          show-header-overflow
          show-overflow
          fixed="right"
          v-if="operationType != 'detail'"
        >
          <template v-slot="{ row, $rowIndex }">
            <span class="btn" v-if="hasPermission('商品列表-修改商品')">
              <el-link
                v-if="!formDisable && !disableStatus && (row.skuPrimaryType === 0 || (!isAdd && !row.hasOwnProperty('skuPrimaryType')) || (isAdd && $rowIndex !== 0))"
                :underline="false"
                type="primary"
                @click.stop="handleEdit(row, $rowIndex)"
                >修改</el-link
              >
            </span>
            <span class="btn">
              <el-link
                :underline="false"
                type="danger"
                @click.stop="handleDelete($rowIndex)"
                v-if="hasPermission('商品列表-修改商品') && !row.skuCode && (row.skuPrimaryType === 0 || (!isAdd && !row.hasOwnProperty('skuPrimaryType')) || (isAdd && $rowIndex !== 0))"
                >删除</el-link
              >
            </span>
          </template>
        </vxe-table-column>
      </vxe-table>
    </div>
    <!-- 多个sau弹框编辑 -->
    <div class="dialog-wrapper">
      <el-dialog
        :title="operType + '-属性'"
        :visible.sync="sauDlgVisible"
        width="1000px"
        ref="Dialog"
        destroy-on-close
        @close="closeSauDlg"
      >
        <sauform
          ref="sauFormDialog"
          :sauForm="dialogSauForm"
          :sauItems="sauItems"
          :cctjID="cctjID"
          :manufacturerOptions="manufacturerOptions"
        ></sauform>
        <div slot="footer" class="dialog-footer">
          <el-button size="medium" @click="sauDlgVisible = false"
            >取 消</el-button
          >
          <el-button size="medium" type="primary" @click="saveSauData"
            >保 存</el-button
          >
        </div>
      </el-dialog>
    </div>
    <imagePreview
      :on-close="closeApprovalViewer"
      v-if="previewDlgVisible"
      :url-list="previewImgList"
    ></imagePreview>
  </div>
</template>

 <script>
import { getDictList, getSauAttr } from "../../api/product";
import uploadImg from "../../components/uploadImg/index";
import sauform from "./components/sauform";
import imagePreview from "@/components/common/preview/imagePreview";
import { dictSearchTypeAndName } from "@/api/dict";
import { findNameByOptions, hasPermission } from "@/utils/index.js";
import { reduceIterate } from "xe-utils/methods";
import { start } from "nprogress";
import { Edit } from "vxe-table";

export default {
  name: "",
  components: { uploadImg, imagePreview, sauform },
  filters: {
    findName(val, arg2) {
      return findNameByOptions(val, "id", arg2);
    },
  },
  props: {
    formDisable:{
      type: Boolean,
      default: false
    },
    sauData: {
      type: Array,
      default: () => [],
    },
    disableStatus:{
      type: Boolean,
      default: false
    },
    isAdd:{
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      isEdit: false,
      rowIndex: -1,
      cctjID: 0,
      isGIFT: false,
      sauItems: [], // 所有属性值
      sauDlgVisible: false,
      operType: "新增",
      sauTableData: [],
      previewDlgVisible: false, // 预览图片
      previewImgList: [], // 预览图片
      manufacturerOptions:
        this.$store.getters.selectOptions.distManufacturerOptions || [], //生产厂家字典
      dialogSauForm: {
        attr: [], // 选中属性id 集合
        selected: [], // 存储选中处理后的数据
        outPackageImgList: [],
        directionImgList: [],
      },
      currentSauForm: {
        attr: [], // 选中属性id 集合
        selected: [], // 存储选中处理后的数据
        outPackageImgList: [],
        directionImgList: [],
        offlineBusinessType: 0,
      },
    };
  },
  computed: {
    // 商品分类
    spuCategory() {
      return this.$store.getters.spuCategory;
    },
    // 商品操作类型
    operationType: function () {
      // 驳回修改SKU 以 修改处理
      if (this.$store.getters.operationType == "RejectEdit") {
        return "edit";
      } else {
        return this.$store.getters.operationType;
      }
    },
  },
  watch: {
    sauData: {
      handler: function (val) {
        let arr = JSON.parse(JSON.stringify(val)),
          skuCode;
        if (this.$route.query.page == "spu") {
          this.sauTableData = val;
        } else if (this.operationType == "present") {
          this.sauTableData = val;
          this.currentSauForm = {
            attr: [], // 选中属性id 集合
            selected: [], // 存储选中处理后的数据
            outPackageImgList: [],
            directionImgList: [],
            ...val[0],
          };
        } else {
          arr.forEach((item, index) => {
            if (item.sauCode == this.$route.query.productCode) {
              skuCode = item.skuCode;
              return;
            }
          });
          this.sauTableData = arr.filter((item) => item.skuCode == skuCode);
          this.$nextTick(() => {
            this.sauTableData.forEach((item, index) => {
              item.sauAttrList.forEach((item) => {
                if (item.attrName == "有效期") {
                  if (item.attrValue == 0) {
                    item.attrValue = "*";
                  }
                  if (item.attrName == 1) {
                    item.attrValue = "-";
                  }
                }
              });
              if (item.sauCode == this.$route.query.productCode) {
                this.formatSau(item);
                if (this.sauTableData.length < 2) return;
                this.$refs.sauTable.setCurrentRow(this.sauTableData[index]);
                return;
              }
            });
          });
        }
      },
      deep: true,
    },
    // 修改商品分类
    spuCategory(newValue) {
      this.isGIFT = newValue.name == "赠品" ? true : false;
    },
  },
  created() {
    getDictList(1).then((data) => {
      data.map((item) => {
        if (item.dictName == "存储条件" && !this.cctjID) {
          this.cctjID = item.id;
        }
      });
      this.sauItems = data.map((item) => {
        return {
          attrId: item.id,
          attrName: item.dictName,
          checked: true,
        };
      });
    });
  },
  mounted() {},
  methods: {
    // 暴露给父组件 所有数据 可通过ref
    getSauData(isDraft) {
      if (this.isGIFT) {
        return [];
      }
      if (!isDraft) {
        if (
          this.sauTableData.length < 2 &&
          this.operationType != "spuOperate"
        ) {
          this.sauTableData = [];

          let { flag: flag1, arr: imgArr1 } = this.handleImgData(
            "outPackageImgList",
            this.$refs.sauForm.sauForm
          );
          if (!flag1) return false;
          this.$refs.sauForm.sauForm.outPackageImgList = imgArr1; // 外包装图片

          let { flag: flag2, arr: imgArr2 } = this.handleImgData(
            "directionImgList",
            this.$refs.sauForm.sauForm
          );
          if (!flag2) return false;
          this.$refs.sauForm.sauForm.directionImgList = imgArr2; // 说明书图片
          this.sauTableData.push({
            ...this.$refs.sauForm.sauForm,
            sauAttrList: this.$refs.sauForm.sauForm.selected,
          });
        }
      }
      return this.sauTableData;
    },
    saveSauData() {
      let isValid = true;
      this.$refs.sauFormDialog.$refs.sauForm.validate((valid) => {
        if (!valid) isValid = false;
      });
      if (!isValid) return false;
      this.$refs.sauFormDialog.sauForm.sauAttrList = this.$refs.sauFormDialog.sauForm.selected;
      let _sauTableData = JSON.parse(JSON.stringify(this.sauTableData));
      if (this.isEdit) {
        _sauTableData.splice(this.rowIndex, 1);
      }
      let repeat_result = this.objComparator(
        this.$refs.sauFormDialog.sauForm,
        _sauTableData
      );
      if (repeat_result == "重复") {
        this.$message.error("属性不能重复");
        return;
      }
      let { flag: flag1, arr: imgArr1 } = this.handleImgData(
        "outPackageImgList",
        this.$refs.sauFormDialog.sauForm
      );
      if (!flag1) return;
      this.$refs.sauFormDialog.sauForm.outPackageImgList = imgArr1; // 外包装图片

      let { flag: flag2, arr: imgArr2 } = this.handleImgData(
        "directionImgList",
        this.$refs.sauFormDialog.sauForm
      );
      if (!flag2) return;
      this.$refs.sauFormDialog.sauForm.directionImgList = imgArr2; // 说明书图片
      if (this.isEdit) {
        this.sauTableData.splice(
          this.rowIndex,
          1,
          this.$refs.sauFormDialog.sauForm
        );
      } else {
        this.sauTableData.push(this.$refs.sauFormDialog.sauForm);
      }
      this.sauDlgVisible = false;
    },
    // 处理图片接口结构
    handleImgData(filed, obj) {
      let flag = true;
      let arr = [];
      for (let item of obj[filed]) {
        // 防止图片未上传完成
        if (item.status == "uploading") {
          this.$message.error("图片上传中，请稍后再试...");
          flag = false;
          break;
        }
        if (item.status == "success") {
          if (item.response) {
            let { mediaName, mediaUrl, meidiaType } = item.response.data;
            arr.push({ mediaName, mediaUrl, meidiaType: 0 });
          } else {
            arr.push({
              mediaName: item.mediaName ? item.mediaName : item.name,
              mediaUrl: item.mediaUrl ? item.mediaUrl : item.url,
              meidiaType: 0, // 媒体类型(0:图片, 1:视频)
            });
          }
          flag = true;
        }
      }
      return { flag, arr };
    },
    addSau() {
      this.operType = "新增";
      this.isEdit = false;
      let isVaild = true;
      if (this.sauTableData.length < 2) {
        this.$refs.sauForm.$refs.sauForm.validate((valid) => {
          if (valid) {
            this.sauTableData = [];

            let { flag: flag1, arr: imgArr1 } = this.handleImgData(
              "outPackageImgList",
              this.$refs.sauForm.sauForm
            );
            if (!flag1) {
              isVaild = false;
              return;
            }
            this.$refs.sauForm.sauForm.outPackageImgList = imgArr1; // 外包装图片

            let { flag: flag2, arr: imgArr2 } = this.handleImgData(
              "directionImgList",
              this.$refs.sauForm.sauForm
            );
            this.$refs.sauForm.sauForm.directionImgList = imgArr2;
            if (!flag2) {
              isVaild = false;
              return;
            }

            this.sauTableData.push({
              ...this.$refs.sauForm.sauForm,
              sauAttrList: this.$refs.sauForm.sauForm.selected,
            });
          } else {
            isVaild = false;
            this.$message.warning("请完善sau信息，在添加多个");
          }
        });
      }
      if (!isVaild) return;
      this.dialogSauForm = {
        attr: [], // 选中属性id 集合
        selected: [], // 存储选中处理后的数据
        outPackageImgList: [],
        directionImgList: [],
        offlineBusinessType: 0,
      };
      this.sauDlgVisible = true;
    },
    // 预览图片
    previewImg(list) {
      if (list.length) {
        this.previewImgList = list;
        this.previewDlgVisible = true;
      }
    },

    // 关闭批件图片预览
    closeApprovalViewer() {
      this.previewImgList = [];
      this.previewDlgVisible = false;
    },
    closeSauDlg() {},
    formatSau(row, isEdit) {
      let obj = JSON.parse(JSON.stringify(row));
      obj.offlineBusinessType = obj.offlineBusinessType ? 1 : 0;
      this.dialogSauForm = {
        ...obj,
        attr: [], // 选中属性id 集合
        selected: [], // 存储选中处理后的数据
        outPackageImgList: [],
        directionImgList: [],
      };
      this.currentSauForm = {
        ...obj,
        attr: [], // 选中属性id 集合
        selected: [], // 存储选中处理后的数据
        outPackageImgList: [],
        directionImgList: [],
      };
      let formData = {};
      if (isEdit) {
        formData = this.dialogSauForm;
      } else {
        formData = this.currentSauForm;
      }
      obj.sauAttrList.forEach((item) => {
        // 委托生产厂家需要加载对应的选项值
        if (item.attrId == 21) {
          this.searchEntrustedManufacturer(item.attrValueName);
        }
        formData.attr.push(item.attrId);
      });

      formData.selected = obj.sauAttrList.map((item) => {
        if ( (item.attrName == "有效期" && (item.attrValue == "-" || item.attrValue == "*")) || item.attrName == "合并商品" || item.attrName == '件包装') {
          item.attrValue = item.attrValue;
        } else { 
          if (parseInt(item.attrValue)) {
            item.attrValue = parseInt(item.attrValue);
          }
        }
        // 委托生产厂家需要加载对应的选项值
        if (item.attrId == 21 && !item.attrValueName) {
          item.attrValueName = findNameByOptions(
            item.attrValue,
            "id",
            this.manufacturerOptions
          );
        }
        return item;
      });

      obj.outPackageImgList.forEach((item) => {
        let { mediaName, mediaUrl } = item;
        formData.outPackageImgList.push({
          name: mediaName,
          url: mediaUrl,
        });
      });

      obj.directionImgList.forEach((item) => {
        let { mediaName, mediaUrl } = item;
        formData.directionImgList.push({
          name: mediaName,
          url: mediaUrl,
        });
      });
    },
    async searchEntrustedManufacturer(inputValue) {
      let distManufacturer = await dictSearchTypeAndName({
        dictName: inputValue ? inputValue : "药",
        type: 12,
      });
      this.manufacturerOptions = distManufacturer.list;
    },
    hasPermission(str) {
      return hasPermission(str);
    },
    handleEdit(row, index) {
      this.operType = "编辑";
      this.isEdit = true;
      this.rowIndex = index;
      this.formatSau(row, true);
      this.sauDlgVisible = true;
    },
    handleDelete(index) {
      this.sauTableData.splice(index, 1);
      if (this.sauTableData.length == 1) {
        this.formatSau(this.sauTableData[0]);
      }
    },
    // 对象-数组包含关系
    objComparator(obj, original) {
      let currObj = _.cloneDeep(obj);
      let originalArr = _.cloneDeep(original);
      let resultArr = [];
      originalArr.forEach((item) => {
        let res;
        if (currObj.sauAttrList.length == item.sauAttrList.length) {
          let arr1 = currObj.sauAttrList.map((item) => {
            return JSON.stringify(item);
          });
          let arr2 = item.sauAttrList.map((item) => {
            return JSON.stringify(item);
          });

          // 过滤 找出不同
          let a = new Set([...arr1, ...arr2]);
          let b = Array.from(a);
          let c = [
            ...b.filter((_) => !arr1.includes(_)),
            ...b.filter((_) => !arr2.includes(_)),
          ];
          res = c.length ? false : true;
        } else {
          res = _.isEqual(currObj.sauAttrList, item.sauAttrList);
        }

        resultArr.push(res);
      });
      return resultArr.includes(true) ? "重复" : "不重复";
    },
  },
};
</script>

 <style lang="scss" scoped>
.component-container {
  .sau-title {
    font-weight: 600;
    margin-bottom: 22px;
    border-bottom: 1px solid #e4e4eb;
    line-height: 60px;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
  }

  .info-suffix-icon {
    margin: 12px 0 0 5px;
    color: #77787e;
    cursor: pointer;
  }
  .border-bottom-dashed {
  }

  .marbot20 {
    margin-bottom: 20px;
  }

  .dialog-wrapper /deep/ .el-dialog {
    top: 50% !important;
  }
  .dialog-wrapper /deep/ .el-dialog .el-dialog__body {
    height: 400px;
    overflow-y: auto;
  }
  .dialog-wrapper /deep/ .el-dialog .el-dialog__footer {
    padding: 10px;
  }

  .flex-box {
    display: flex;
    color: #292933;
    font-weight: normal;
    margin: 20px 0 10px 0;
    > span:first-child {
      width: 100px;
      text-align: center;
    }
    > span:last-child {
      flex: 1;
    }
  }
  .vxe-body--row.row--current {
    .el-link.el-link--primary {
      cursor: pointer;
      color: #fff;
    }
  }
}
.sau-form /deep/ {
  display: flex;
  flex-wrap: wrap;
  .el-form-item__label {
    width: 130px !important;
    text-align: right;
    padding-right: 12px;
  }
  .tip {
    width: 100%;
    color: #bababa;
  }
  .el-form-item__content {
    margin-left: 0 !important;
  }
  .el-checkbox-group {
    width: 800px;
    background: #f0f2f5;
    padding-left: 10px;
  }
}
.image-box {
  display: flex;
  margin-bottom: 20px;
  .img-title {
    width: 130px;
    font-size: 14px;
    text-align: right;
    padding: 0 12px;
  }
  .img-content {
    flex: 1;
  }
}
.atrrList /deep/ {
  .el-col {
    width: 400px;
  }
  .el-col:nth-of-type(2n) {
    .el-form-item__content {
      margin-left: 20px !important;
    }
  }
}
.sau-table {
  padding: 20px 40px 100px;
}
.btn {
  padding: 0 10px;
}
</style>