<template>
  <div class="container-receive">
    <div class="search-form-wrap">
      <el-form :model="model" ref="refSearchForm">
        <el-row type="flex">
          <!-- 上报时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="申请时间" prop="time">
              <el-date-picker
                v-model="formData.time"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                clearable
              ></el-date-picker>
            </el-form-item>
          </el-col>

          <!-- 申请人 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="申请人">
              <el-input
                v-model="formData.applyUserName"
                placeholder="请输入申请人"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 审批流程 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="审批流程">
              <el-select
                v-model="formData.approvalProcess"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in approvalProcessList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 详情 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="详情">
              <el-input
                v-model="formData.details"
                placeholder="请输入详情"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 生产厂家 
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="机构">
              <el-input
                v-model="formData.affiliation"
                placeholder=""
                clearable
              ></el-input>
            </el-form-item>
          </el-col> -->

       <!-- 机构 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="机构">
              <el-select
                multiple
                collapse-tags
                v-model="formData.affiliation"
                placeholder="请选择"
                @remove-tag="removeTag1"
                @change="changeSelect1"
              >
               <el-option
                  label="全选"
                  value="全选"
                  @click.native="selectAll1"
                ></el-option>
                
                <el-option
                  v-for="item in deptList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>







          <!-- 是否自营 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="是否自营">
              <el-select
                v-model="formData.preOperateStatus"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" value></el-option>
                <el-option label="是" :value="2"></el-option>
                <el-option label="否" :value="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 按钮 -->
          <el-col :span="6" :offset="18">
            <el-button size="medium" @click="btnResetClick">重置</el-button>
            <el-button type="primary" size="medium" @click="btnSearchClick"
              >查询</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="recieve-wrap">
      <div class="recieve">
        <div class="content">
          <div class="title">任务总数量：{{ total }}个</div>
          <el-select v-model="ReceiveNum" @blur="selectBlur" filterable placeholder="请选择">
            <el-option label="25" :value="25"> </el-option>
            <el-option label="50" :value="50"> </el-option>
            <el-option label="100" :value="100"> </el-option>
            <el-option label="200" :value="200"> </el-option>
          </el-select>
          <el-button
            type="primary"
            class="btn"
            size="mimi"
            @click="handleReceive"
            >批量领取</el-button
          >
        </div>
        <div class="tip">注意：选择数量小于任务总数，将领取剩余的任务数量</div>
      </div>
    </div>
  </div>
</template>

<script>
import { getUserClaimRecordCount, batchClaimReview, getUserDptNameList } from "@/api/workbench.js";

export default {
  name: "prepareReceive",
  props: ["approvalProcessList"],
  data() {
    return {
      formData: {
        affiliation: [],
        applyUserName: "",
        approvalProcess: "",
        details: "",
        time: "",
        preOperateStatus: "",
      },
      deptList: [],
      ReceiveNum: 25,
      total: 0,
    };
  },
  watch: {
    approvalProcessList(val) {
      this.formData.approvalProcess = val[0].key;
      this.btnSearchClick();
    },
  },
  created() {
    this.getDeptList()
  },
  methods: {
    changeSelect1(val) {
      if (!val.includes("全选") && val.length === this.deptList.length) {
        this.formData.affiliation.unshift("全选");
      } else if (
        val.includes("全选") &&
        val.length - 1 < this.deptList.length
      ) {
        console.log(this.formData.affiliation)
        this.formData.affiliation = this.formData.affiliation.filter((item) => {
          return item !== "全选";
        });
      }
    },
    removeTag1(val) {
      if (val === "全选") {
        this.formData.affiliation = [];
      }
    },
    selectAll1() {
      if (this.formData.affiliation.length < this.deptList.length) {
        this.formData.affiliation = [];
        this.deptList.map((item) => {
          this.formData.affiliation.push(item.value);
        });
        this.formData.affiliation.unshift("全选");
      } else {
        this.formData.affiliation = [];
      }
    },
    async getDeptList() {
      const res = await getUserDptNameList()
      this.deptList = res.data
    },
    selectBlur(e) {
      this.ReceiveNum = +e.target.value === 0 || isNaN(+e.target.value) ? '' : +e.target.value
    },
    handleReceive() {
      if (!this.total) {
        this.$message.warning("当前筛选条件无任务");
        return;
      }
      if (!this.ReceiveNum) {
        this.$message.warning("请选择领取数量");
        return;
      }
      const loading = this.$loading({
        lock: true,
        text: "任务领取中...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      let param = Object.assign({}, this.formData);
      param.startTime = param.time[0] ? param.time[0] : "";
      param.endTime = param.time[1] ? param.time[1] : "";
      param.planClaimNum = this.ReceiveNum;
      delete param.time;
      //param.affiliation = param.affiliation.join()
      if (param.affiliation[0] === '全选') { 
        param.affiliation.shift()
        param.affiliation = param.affiliation.join()
      } else {
        param.affiliation = param.affiliation.join()
      }

      batchClaimReview(param).then((res) => {
        if (!res.retCode) {
          loading.close();
          this.$emit("changeTab", "second");
          this.btnResetClick();
          this.btnSearchClick();
          this.ReceiveNum = "";
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      });
    },
    btnSearchClick() {
      let param = Object.assign({}, this.formData);
      param.startTime = param.time && param.time[0] ? param.time[0] : "";
      param.endTime = param.time && param.time[1] ? param.time[1] : "";
      if (param.affiliation[0] === '全选') {
        param.affiliation.shift()
        param.affiliation = param.affiliation.join()
      } else {
        param.affiliation = param.affiliation.join()
      }
      delete param.time;
      getUserClaimRecordCount(param).then((res) => {
        if (!res.retCode) {
          this.total = res.data;
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      });
    },
    btnResetClick() {
      this.formData = {
        applyUserName: "",
        approvalProcess: this.approvalProcessList[0] ? this.approvalProcessList[0].key : '',
        details: "",
        time: "",
        preOperateStatus: "",
        affiliation: [],
      };
      this.btnSearchClick();
    },
  },
};
</script>

<style lang="scss" scoped>
.search-form-wrap {
  width: 100%;
  padding-bottom: 15px;
  border-bottom: 1px dashed #e4e4eb;
  .el-row {
    flex-wrap: wrap;
    .el-col {
      display: flex;
      justify-content: flex-end;
      .el-form-item {
        flex: 1;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        width: 100%;
        margin-right: 0;
        margin-bottom: 15px;
        /deep/ {
          .el-form-item__label {
            width: 96px;
            line-height: normal;
            padding: 0 12px;
            color: #292933;
            font-weight: normal;
          }
          .el-form-item__content {
            flex: 1;
            .el-range-editor.el-input__inner {
              width: 100%;

              .el-range-separator {
                width: 14px;
                padding: 0;
              }
            }
          }
        }
        .el-input {
          width: 100%;
        }
        .el-select {
          width: 100%;
        }
      }
    }
  }
}
.recieve-wrap {
  padding-top: 15px;
}
.recieve {
  width: 625px;
  padding-top: 50px;
  padding-left: 40px;
  background: #e9f7fe;
  margin: 0 auto;
  .content {
    display: flex;
    align-items: center;
    .title {
      padding-right: 50px;
      color: #3b95a8;
    }
    .btn {
      margin-left: 15px;
    }
  }
  .tip {
    font-size: 12px;
    color: #bababa;
    padding: 40px 0;
  }
}
</style>