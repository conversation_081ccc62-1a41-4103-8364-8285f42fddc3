
<template>
  <div class="task-to-be-film-container" v-loading="loading">
    <!-- 查询表单 -->
    <div class="search-form-wrap">
      <el-form :model="model" ref="refSearchForm">
        <el-row type="flex">
          <!-- 申请时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="申请时间" prop="createTime">
              <el-date-picker
                v-model="formData.createTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                clearable
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <!-- 更新时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="更新时间" prop="operateTime">
              <el-date-picker
                v-model="formData.operateTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                clearable
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <!-- 任务类型 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="任务类型" prop="approvalProcess">
              <el-select
                v-model="formData.approvalProcess"
                placeholder="请选择任务类型"
                filterable
                clearable
              >
                <el-option label="全部" value=""></el-option>
                <el-option
                  v-for="item in approvalProcessList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 提交人 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="提交人">
              <el-input
                v-model="formData.applyUserName"
                placeholder="模糊查询"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 来源 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="来源" prop="source">
              <el-select
                multiple
                collapse-tags
                v-model="formData.source"
                @change="changeSelect1"
                @remove-tag="removeTag1"
                placeholder="请选择"
              >
                <el-option
                  label="全选"
                  value="全选"
                  @click.native="selectAll1"
                ></el-option>
                <el-option
                  v-for="item in sourceList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 是否完成-->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="是否完成" prop="isOver">
              <el-select
                v-model="formData.isOver"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" value=""></el-option>
                <el-option label="是" :value="1"></el-option>
                <el-option label="否" :value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="处理人" prop="oaIds">
              <el-select
                multiple
                collapse-tags
                v-model="formData.oaIds"
                @change="changeSelect"
                @remove-tag="removeTag"
                placeholder="请选择"
              >
                <el-option
                  label="全选"
                  value="全选"
                  @click.native="selectAll"
                ></el-option>
                <el-option
                  v-for="item in conductorList"
                  :key="item.oaId"
                  :label="item.realName"
                  :value="item.oaId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 审核结果 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="审核结果" prop="auditState">
              <el-select
                v-model="formData.auditState"
                placeholder="请选择任务类型"
                filterable
                clearable
              >
                <el-option label="全部" value=""></el-option>
                <el-option
                  v-for="item in auditStateList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 按钮 -->
          <el-col :span="6" :offset="18">
            <el-button type="primary" size="medium" @click="btnSearchClick"
              >查询</el-button
            >
            <el-button size="medium" @click="btnResetClick">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="btn-wrap">
      <el-button
        type="primary"
        size="medium"
        :disabled="btnDis"
        @click="handleExpot"
        >导出</el-button
      >
    </div>
    <!-- table表单 -->
    <!-- table -->
    <div class="table-wrap">
      <vxe-table
        border
        highlight-hover-row
        resizable
        height="100%"
        auto-resize
        size="small"
        align="center"
        :tooltip-config="{ enterable: false }"
        :loading="tableLoading"
        :data="tableData"
        :seq-config="{ startIndex: (pageNum - 1) * pageSize }"
        ref="refVxeTable"
      >
        <vxe-table-column
          type="seq"
          title="序号"
          min-width="120"
          show-header-overflow
          show-overflow
          fixed="left"
        ></vxe-table-column>
        <vxe-table-column
          field="applyDay"
          title="申请时间"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="operateDay"
          title="更新时间"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="approvalProcessName"
          title="任务类型"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="applyUserName"
          title="提交人"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="sourceName"
          title="来源"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="isOverName"
          title="是否完成"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="auditStateName"
          title="审核结果"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="reviewerName"
          title="处理人"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="taskNum"
          title="任务数量"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
      </vxe-table>
    </div>
    <div class="total">
      <span class="title">总计</span>
      <span class="num"> {{ totalTask }}</span>
    </div>
    <!-- 分页 -->
    <el-row class="custom-pagination-wrap">
      <el-col>
        <el-pagination
          background
          :current-page.sync="pageNum"
          :page-sizes="[20, 50, 100, 200]"
          :page-size.sync="pageSize"
          :total="total"
          layout="prev, pager, next, jumper,total, sizes"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { hasPermission, getDateRange } from "@/utils/index.js";

import {
  getAllSource,
  getAllApprovalProcess,
  getAllTaskAuditState,
  getTaskBoardList,
  exportTaskBoardList,
  getTaskBoardUsers,
} from "@/api/workbench.js";

export default {
  name: "",
  components: {},
  filters: {},
  props: {},
  data() {
    return {
      loading: false,
      pageNum: 1,
      pageSize: 20,
      total: 0, //总条数
      formData: {
        createTime: getDateRange(7),
        operateTime: getDateRange(7),
        approvalProcess: "",
        applyUserName: "",
        auditState: "",
        source: [],
        isOver: 1,
        oaIds: [],
      },
      tableLoading: false,
      tableData: [],
      sourceList: [],
      approvalProcessList: [],
      auditStateList: [],
      totalTask: 0,
      conductorList: [],
    };
  },
  computed: {},
  watch: {},
  created() {
    this.getAllSource();
    this.getAllApprovalProcess();
    this.getAllTaskAuditState();
    this.getTaskBoardUsers();
    this.searchForm();
  },
  mounted() {},
  methods: {
    getAllSource() {
      getAllSource().then((res) => {
        this.sourceList = res.data;
      });
    },
    getAllApprovalProcess() {
      getAllApprovalProcess().then((res) => {
        this.approvalProcessList = res.data;
      });
    },
    getAllTaskAuditState() {
      getAllTaskAuditState().then((res) => {
        this.auditStateList = res.data;
      });
    },
    getTaskBoardUsers() {
      getTaskBoardUsers().then((res) => {
        this.conductorList = res.data;
      });
    },
    // 搜索
    searchForm() {
      this.tableLoading = true;
      this.getTaskBoardList();
    },

    getTaskBoardList() {
      let param = Object.assign(
        {
          page: this.pageNum,
          limit: this.pageSize,
          startTime: this.formData.createTime
            ? this.formData.createTime[0]
            : "",
          endTime: this.formData.createTime ? this.formData.createTime[1] : "",
          operateStartTime: this.formData.operateTime
            ? this.formData.operateTime[0]
            : "",
          operateEndTime: this.formData.operateTime ? this.formData.operateTime[1] : "",
          oaId: this.formData.oaIds.join(","),
          sourceStr: this.formData.source.join(","),
          isPicture: 0,
        },
        this.formData
      );
      delete param.createTime;
      delete param.operateTime;
      delete param.oaIds;
      delete param.source;
      if (this.formData.oaIds.indexOf("全选") !== -1) {
        param.isOaId = 1;
        delete param.oaId;
      }
      if (this.formData.source.indexOf("全选") !== -1) {
        param.isSource = 1;
        delete param.sourceStr;
      }
      getTaskBoardList(param).then((res) => {
        if (!res.retCode) {
          this.tableData = res.data.list;
          this.total = res.data.total;
          this.totalTask = res.data.list.length ? res.data.list[0].totalNum : 0;
          this.tableLoading = false;
        } else {
          this.$message.error(res.retMsg);
        }
      });
    },

    handleExpot() {
      let param = Object.assign(
        {
          page: this.pageNum,
          limit: this.pageSize,
          startTime: this.formData.createTime
            ? this.formData.createTime[0]
            : "",
          endTime: this.formData.createTime ? this.formData.createTime[1] : "",
          operateStartTime: this.formData.operateTime
            ? this.formData.operateTime[0]
            : "",
          operateEndTime: this.formData.operateTime ? this.formData.operateTime[1] : "",
          oaId: this.formData.oaIds.join(","),
          sourceStr: this.formData.source.join(","),
        },
        this.formData
      );
      delete param.createTime;
      delete param.operateTime;
      delete param.oaIds;
      delete param.source;
      exportTaskBoardList(param).then((res) => {
        if (!res.retCode) {
          let blob = new Blob([res]);
          let blobUrl = window.URL.createObjectURL(blob);
          let a = document.createElement("a");
          a.style.display = "none";
          a.download = "任务看板数据.xlsx";
          a.href = blobUrl;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
        } else {
          this.$message.error(res.retMsg);
        }
      });
    },

    /**
     * 查询按钮点击
     */
    btnSearchClick() {
      this.pageNum = 1;
      this.searchForm();
    },

    /**
     * 重置
     */
    btnResetClick() {
      this.formData = {
        createTime: getDateRange(7),
        operateTime: getDateRange(7),
        approvalProcess: "",
        applyUserName: "",
        auditState: "",
        source: [],
        isOver: 1,
        oaIds: [],
      };
      this.pageNum = 1;
      this.searchForm();
    },
    /**
     * pageSize 改变事件
     */
    handleSizeChange(pageSize) {
      this.pageNum = 1;
      this.pageSize = pageSize;
      this.searchForm();
    },

    /**
     * pageNum 改变事件
     */
    handleCurrentChange(currentPage) {
      this.pageNum = currentPage;
      this.searchForm();
    },
    /**
     * @description: 格式化时间参数
     * @param {date} time 时间戳
     * @return: string 格式化后的时间戳
     */
    hasPermission(str) {
      return hasPermission(str);
    },

    selectAll() {
      if (this.formData.oaIds.length < this.conductorList.length) {
        this.formData.oaIds = [];
        this.conductorList.map((item) => {
          this.formData.oaIds.push(item.oaId);
        });
        this.formData.oaIds.unshift("全选");
      } else {
        this.formData.oaIds = [];
      }
    },
    changeSelect(val) {
      if (!val.includes("全选") && val.length === this.conductorList.length) {
        this.formData.oaIds.unshift("全选");
      } else if (
        val.includes("全选") &&
        val.length - 1 < this.conductorList.length
      ) {
        this.formData.oaIds = this.formData.oaIds.filter((item) => {
          return item !== "全选";
        });
      }
    },
    removeTag(val) {
      if (val === "全选") {
        this.formData.oaIds = [];
      }
    },
    selectAll1() {
      if (this.formData.source.length < this.sourceList.length) {
        this.formData.source = [];
        this.sourceList.map((item) => {
          this.formData.source.push(item.key);
        });
        this.formData.source.unshift("全选");
      } else {
        this.formData.source = [];
      }
    },
    changeSelect1(val) {
      if (!val.includes("全选") && val.length === this.sourceList.length) {
        this.formData.source.unshift("全选");
      } else if (
        val.includes("全选") &&
        val.length - 1 < this.sourceList.length
      ) {
        this.formData.source = this.formData.source.filter((item) => {
          return item !== "全选";
        });
      }
    },
    removeTag1(val) {
      if (val === "全选") {
        this.formData.source = [];
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.task-to-be-film-container {
  /**
   * 查询表单
   */
  .search-form-wrap {
    width: 100%;
    padding: 15px;
    border-bottom: 1px dashed #e4e4eb;

    .el-row {
      flex-wrap: wrap;

      .el-col {
        display: flex;
        justify-content: flex-end;

        .el-form-item {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          width: 100%;
          margin-right: 0;
          margin-bottom: 15px;

          /deep/ {
            .el-form-item__label {
              width: 96px;
              line-height: normal;
              padding: 0 12px;
              color: #292933;
              font-weight: normal;
            }

            .el-form-item__content {
              flex: 1;

              .el-range-editor.el-input__inner {
                width: 100%;

                .el-range-separator {
                  width: 14px;
                  padding: 0;
                }
              }
            }
          }

          .el-input {
            width: 100%;
          }

          .el-select {
            width: 100%;
          }
        }
      }
    }
  }

  .tab-wrap {
    padding: 0 15px;
    margin-top: 15px;
  }
  .table-wrap {
    width: 100%;
    padding: 0 20px;
    min-height: 440px;
    height: calc(100vh - 395px);
  }
}
.total {
  height: 40px;
  line-height: 40px;
  margin: 0 20px;
  display: flex;
  justify-content: space-between;
  font-weight: bold;
  background: #f2f2f2;
  span {
    width: 12.5%;
    text-align: center;
    &.title {
      background: #fff;
      border-left: 1px solid #eee;
      border-bottom: 1px solid #eee;
    }
  }
}
.btn-wrap {
  padding: 15px 0 15px 20px;
}
.radio-wrap {
  .title {
    padding-right: 20px;
  }
}
.active {
  color: #3b95a8;
}
</style>
