<template>
  <div class="container">
    <el-tabs v-model="tabName" type="card" @tab-click="changeTabForLocal">
      <el-tab-pane label="领取任务" name="first">
        <receive @changeTab="changeTab"></receive>
      </el-tab-pane>
      <el-tab-pane label="我的任务" name="second">
        <list ref="prepareList"></list>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import receive from "./components/receive";
import list from "./components/list";

export default {
  name: "prepare",
  components: {
    receive,
    list,
  },
  data() {
    return {
      tabName: "first",
    };
  },
  created() {
    if(this.$route.query.tab) {
      this.tabName = this.$route.query.tab
    }
  },
  mounted(){
   const item = localStorage.getItem('selfSupportTab')
   if(item){
     this.tabName = item
   }
  },
  methods: {
    changeTab(name) {
      this.tabName = name;
      if(name == 'second'){
        this.$refs.prepareList.searchForm();
      }
    },
    changeTabForLocal(tab){
      localStorage.setItem('selfSupportTab', tab.name)
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding: 15px 15px 0;
}
</style>
