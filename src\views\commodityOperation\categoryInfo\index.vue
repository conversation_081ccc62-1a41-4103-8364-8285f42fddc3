<template>
  <div class="wrapper">
    <el-form class="form" label-width="120px" ref="form" inline>
      <el-form-item label="商品ID">
        <el-input placeholder="精准查询" v-model="form.productId"></el-input>
      </el-form-item>
      <el-form-item label="商品编码">
        <el-input
          placeholder="支持商品编码和原商品编码模糊查询"
          v-model="form.productCode"
        ></el-input>
      </el-form-item>
      <el-form-item label="通用名">
        <el-input
          placeholder="支持通用名、商品名及对应的助记码模糊查询"
          v-model="form.generalName"
        ></el-input>
      </el-form-item>
      <el-form-item label="生产厂家">
        <el-input v-model="form.manufacturerName"></el-input>
      </el-form-item>
      <el-form-item label="规格/型号">
        <el-input v-model="form.spec"></el-input>
      </el-form-item>
      <el-form-item label="厂家分类">
        <el-select v-model="form.manufacturerCategory" style="width: 185px">
          <el-option
            v-for="item in manufacturerCategoryList"
            :key="item.id"
            :label="item.dictName"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="品牌分类">
        <el-select v-model="form.brandCategory" style="width: 185px">
          <el-option
            v-for="item in brandCategoryList"
            :key="item.id"
            :label="item.dictName"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="全国4+7入选目录">
        <el-select
          v-model="form.fourPlusSevenSelectedListStatus"
          style="width: 185px"
        >
          <el-option label="全部" value=""> </el-option>
          <el-option label="是" :value="1"> </el-option>
          <el-option label="否" :value="0"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="4+7公布时间">
        <el-date-picker
          v-model="form.fourPlusSevenPublicationTime"
          type="month"
          placeholder="选择日期"
          value-format="yyyyMM"
          style="width: 185px"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="4+7省区">
        <el-select
          multiple
          v-model="form.fourPlusSevenProvinces"
          filterable
          style="width: 185px"
        >
          <el-option
            v-for="item in provinces"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="慢病品种">
        <el-select v-model="form.chronicDiseasesVariety" style="width: 185px">
          <el-option label="全部" value=""> </el-option>
          <el-option label="是" :value="1"> </el-option>
          <el-option label="否" :value="0"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="一致性评价品种">
        <el-select v-model="form.conEvaluateVariety" style="width: 185px">
          <el-option label="全部" value=""> </el-option>
          <el-option label="是" :value="1"> </el-option>
          <el-option label="否" :value="0"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="医院品种">
        <el-select v-model="form.hospitalVariety" style="width: 185px">
          <el-option label="全部" value=""> </el-option>
          <el-option label="是" :value="1"> </el-option>
          <el-option label="否" :value="0"> </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <div style="text-align: right">
      <el-button type="primary" :loading="tableLoading" @click="getTabelData"
        >查询</el-button
      >
      <el-button @click="resetForm">重置</el-button>
      <el-button type="primary" :loading="loadingExport" @click="exportExcel"
        >导出Excel</el-button
      >
    </div>
    <el-divider></el-divider>
    <div style="padding-bottom: 20px">
      <el-button type="primary" @click="updateCategory">修改品类信息</el-button>
    </div>
    <vxe-table
      border
      highlight-hover-row
      resizable
      show-overflow
      size="small"
      align="center"
      max-height="500"
      :loading="tableLoading"
      :data="tableData"
      :column-config="{ minWidth: '100' }"
      :radio-config="{ highlight: true }"
      :seq-config="{ startIndex: (page.page - 1) * page.size }"
      ref="table"
    >
      <vxe-table-column type="radio" width="60" fixed="left">
        <template v-slot:header>
          <vxe-button type="text"></vxe-button>
        </template>
      </vxe-table-column>
      <vxe-table-column
        type="seq"
        title="序号"
        width="60"
        min-width="auto"
      ></vxe-table-column>
      <vxe-table-column field="productId" title="商品ID"></vxe-table-column>
      <vxe-table-column field="skuCode" title="商品编码"></vxe-table-column>
      <vxe-table-column
        field="originalProductCode"
        title="原商品编码"
      ></vxe-table-column>
      <vxe-table-column field="generalName" title="通用名"></vxe-table-column>
      <vxe-table-column field="skuName" title="商品名"></vxe-table-column>
      <vxe-table-column field="spec" title="规格/型号"></vxe-table-column>
      <vxe-table-column
        field="manufacturerName"
        title="生产厂家"
        min-width="180"
      ></vxe-table-column>
      <vxe-table-column
        field="marketAuthor"
        title="上市许可持有人"
        min-width="120"
      ></vxe-table-column>
      <vxe-table-column
        field="manufacturerCategoryName"
        title="厂家分类"
      ></vxe-table-column>
      <vxe-table-column
        field="smallPackageCode"
        title="小包装条码"
      ></vxe-table-column>
      <vxe-table-column field="approvalNo" title="批准文号"></vxe-table-column>
      <vxe-table-column
        field="packageUnitName"
        title="包装单位"
      ></vxe-table-column>
      <vxe-table-column field="dosageFormName" title="剂型"></vxe-table-column>
      <vxe-table-column
        field="firstToSixthClassifyName"
        title="1-6级分类"
        min-width="180"
      ></vxe-table-column>
      <vxe-table-column field="brand" title="品牌/商标"></vxe-table-column>
      <vxe-table-column
        field="brandCategoryName"
        title="品牌分类"
      ></vxe-table-column>
      <vxe-table-column
        field="chronicDiseasesVarietyName"
        title="慢病品种"
      ></vxe-table-column>
      <vxe-table-column
        field="conEvaluateVarietyName"
        title="一致性评价品种"
        min-width="120"
      ></vxe-table-column>
      <vxe-table-column
        field="hospitalVarietyName"
        title="医院品种"
      ></vxe-table-column>
      <vxe-table-column
        field="fourPlusSevenSelectedListStatusName"
        title="全国4+7目录"
      ></vxe-table-column>
      <vxe-table-column
        field="fourPlusSevenPublicationTime"
        title="4+7公布时间"
      ></vxe-table-column>
      <vxe-table-column
        field="fourPlusSevenPrice"
        title="4+7选中价格"
      ></vxe-table-column>
      <vxe-table-column
        field="fourPlusSevenProvincesName"
        title="4+7中标供应省区"
      ></vxe-table-column>
    </vxe-table>
    <vxe-pager
      border
      size="medium"
      :loading="tableLoading"
      :current-page="page.page"
      :page-size="page.size"
      :page-sizes="[20, 50, 100]"
      :total="page.total"
      :layouts="[
        'PrevPage',
        'JumpNumber',
        'NextPage',
        'FullJump',
        'Sizes',
        'Total'
      ]"
      @page-change="onPageChange"
    >
    </vxe-pager>

    <UpdateCategory
      ref="updateCategory"
      :brandCategoryList="brandCategoryList"
      :provinces="provinces"
      @update="getTabelData"
    ></UpdateCategory>
  </div>
</template>

<script>
import UpdateCategory from "./updateCategory.vue";
import api from "@/api/commodityOperation";
import { filterEmptyField, provinces } from "../helper";

const initFormData = () => ({
  productId: "",
  productCode: "",
  generalName: "",
  manufacturerName: "",
  spec: "",
  manufacturerCategory: "",
  brandCategory: "",
  fourPlusSevenSelectedListStatus: "",
  fourPlusSevenPublicationTime: "",
  fourPlusSevenProvinces: [],
  chronicDiseasesVariety: "",
  conEvaluateVariety: "",
  hospitalVariety: ""
});

export default {
  name: "CategoryInfo",
  components: { UpdateCategory },
  data() {
    return {
      form: initFormData(),
      tableLoading: false,
      tableData: [],
      page: {
        page: 1,
        size: 20,
        total: 0
      },
      loadingExport: false,
      manufacturerCategoryList: [],
      brandCategoryList: [],
      provinces
    };
  },
  methods: {
    getTabelData() {
      this.tableLoading = true;
      api
        .getCategoryList(
          filterEmptyField(this.form),
          this.page.page,
          this.page.size
        )
        .then(res => {
          if (res.success) {
            this.tableData = res.data.list;
            this.page.total = res.data.total;
          }
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    resetForm() {
      this.form = initFormData();
    },
    exportExcel() {
      this.loadingExport = true;
      api
        .exportCategory(filterEmptyField(this.form))
        .then(res => {
          this.$alert(res.retMsg);
        })
        .finally(() => {
          this.loadingExport = false;
        });
    },
    updateCategory() {
      const row = this.$refs.table.getRadioRecord();
      if (!row) {
        this.$message("请选择要修改的数据行");
        return;
      }
      this.$refs.updateCategory.show(this.$refs.table.getRadioRecord());
    },
    onPageChange({ currentPage, pageSize }) {
      this.page.page = currentPage;
      this.page.size = pageSize;
      this.getTabelData();
    }
  },
  created() {
    api.getProvinceList().then(res => {
      this.provinces = res;
    });
    this.getTabelData();
    api.getDictList(23).then(res => {
      if (res.retCode === 0) {
        this.manufacturerCategoryList = res.data.list;
      }
    });
    api.getDictList(24).then(res => {
      if (res.retCode === 0) {
        this.brandCategoryList = res.data.list;
      }
    });
  }
};
</script>

<style lang="scss" scoped>
.wrapper {
  padding: 15px;
}
</style>
