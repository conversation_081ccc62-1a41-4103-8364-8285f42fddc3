import request from '@/utils/request'
import qs from "qs" // 请求数据 转成form data 形式
import { _Message } from '@/components/common/resectMessage';
// 获取扩展属性数据
export function getExtendInfo(url, data) {
  return request({
    url: url,
    method: 'post',
    data: qs.stringify(data)
  }).then(res => {
    return res.data
  }).catch((e) => {
    _Message.error('接口报错')
  });
}

// 获取草稿数据
export function getDraftInfo(spuCode) {
  return request({
    url: '/api/spu/get/draftInfo',
    method: 'post',
    data: qs.stringify({
      applyCode: 'draft_add000576',
      spuCode,
    })
  }).then(res => {
    return res.data.sau
  }).catch((e) => {
    _Message.error('接口报错')
  });
}


/*************** sau 属性管理 **************/
// 获取不同商品分类下的sau属性 1.4.2V 商品分类和属性值解除关联 此接口暂时不用
export function getSauAttr(categoryId) {
  return request({
    url: '/api/dict/find/attrCategoryLinkList',
    method: 'post',
    data: { categoryId }
  }).then(res => {
    return res.data
  }).catch((e) => {
    _Message.error('接口报错')
  });
}

/*************修改记录*****************/
// 获取修改记录
export function getModifyRecord(data) {
  return request({
    url: '/api/spu/get/spuDetail',
    method: 'post',
    data: qs.stringify(data)
  }).then(res => {
    return res.data
  }).catch((e) => {
    _Message.error('接口报错')
  });
}

// 查看审批流程
export function getApprovalProcess(id,applyCode) {
  return request({
    url: '/api/worksubstitution/find/record',
    method: 'post',
    data: {
      id,
      applyCode,
      limit: 10,
      page: 1
    }
  }).then(res => {
    return res.data
  }).catch((e) => {
    _Message.error('接口报错')
  });
}

/*************扩展属性*****************/
// 新字典接口

export function getDictList(type, isValid = 1, dictName = "") {
  return request({
    url: '/api/dict/find/dictAllList',
    method: 'post',
    data: { type, isValid, dictName }
  }).then(res => {
    return res.data.list
  }).catch((e) => {
    _Message.error('字典接口异常')
  });
}

// 治疗症状
export function getTreatmentSymptoms(data) {
  return request({
    url: '/api/dict/find/diseaseSymptomLinkList',
    method: 'post',
    data
  }).then(res => {
    return res.data
  }).catch((e) => {
    _Message.error('接口报错')
  });
}

/*************资质属性****************/

export function getProdQualif(data) {
  return request({
    url: '/api/spu/get/prodQualif',
    method: 'post',
    data: qs.stringify(data)
  }).then(res => {
    return res.data
  }).catch((e) => {
    _Message.error('接口报错')
  });
}

/*************销售属性****************/

let da = [
  {
    businessCode: "Y3008242",
    businessType: 0,
    detailPictureList: [
      {
        applyCode: "STP2003000003-2",
        auditStatus: 0,
        createTime: 1584092207000,
        createUser: "丁淑贞",
        createUserMechanism: "D1271",
        id: 260,
        mechanism: "",
        pictureName: "Y3008242-1.jpg",
        pictureOrdinal: 1,
        pictureStatus: 1,
        pictureUrl: "https://files.test.ybm100.com/G1/M00/12/BD/Cgoz1F5rVC-AGkVSAAAYRxhMMx4265.jpg",
        pictureVersion: "202003131736",
        productCode: "Y3008242",
        updateTime: 1584092207000,
        updateUser: "",
        updateUserMechanism: ""
      },
      {
        applyCode: "STP2003000003-2",
        auditStatus: 0,
        createTime: 1584092207000,
        createUser: "丁淑贞",
        createUserMechanism: "D1271",
        id: 255,
        mechanism: "",
        pictureName: "Y3008242-2.jpg",
        pictureOrdinal: 2,
        pictureStatus: 1,
        pictureUrl: "https://files.test.ybm100.com/G1/M00/12/BE/Cgoz015rVC6AXVXxAAJ0RJLdsVY074.jpg",
        pictureVersion: "202003131736",
        productCode: "Y3008242",
        updateTime: 1584092207000,
        updateUser: "",
        updateUserMechanism: ""
      },
      {
        applyCode: "STP2003000003-2",
        auditStatus: 0,
        createTime: 1584092207000,
        createUser: "丁淑贞",
        createUserMechanism: "D1271",
        id: 261,
        mechanism: "",
        pictureName: "Y3008242-3.jpg",
        pictureOrdinal: 3,
        pictureStatus: 1,
        pictureUrl: "https://files.test.ybm100.com/G1/M00/12/BE/Cgoz015rVC-AFrmHAADAQLpmqUc418.jpg",
        pictureVersion: "202003131736",
        productCode: "Y3008242",
        updateTime: 1584092207000,
        updateUser: "",
        updateUserMechanism: ""
      },
      {
        applyCode: "STP2003000003-2",
        auditStatus: 0,
        createTime: 1584092207000,
        createUser: "丁淑贞",
        createUserMechanism: "D1271",
        id: 257,
        mechanism: "",
        pictureName: "Y3008242-4.jpg",
        pictureOrdinal: 4,
        pictureStatus: 1,
        pictureUrl: "https://files.test.ybm100.com/G1/M00/12/BE/Cgoz015rVC6AM7yDAACmJMgPpBQ017.jpg",
        pictureVersion: "202003131736",
        productCode: "Y3008242",
        updateTime: 1584092207000,
        updateUser: "",
        updateUserMechanism: ""
      },
      {
        applyCode: "STP2003000003-2",
        auditStatus: 0,
        createTime: 1584092207000,
        createUser: "丁淑贞",
        createUserMechanism: "D1271",
        id: 258,
        mechanism: "",
        pictureName: "Y3008242-5.jpg",
        pictureOrdinal: 5,
        pictureStatus: 1,
        pictureUrl: "https://files.test.ybm100.com/G1/M00/12/BD/Cgoz1F5rVC6ACo1gAADtjpV2ERQ518.jpg",
        pictureVersion: "202003131736",
        productCode: "Y3008242",
        updateTime: 1584092207000,
        updateUser: "",
        updateUserMechanism: ""
      },
      {
        applyCode: "STP2003000003-2",
        auditStatus: 0,
        createTime: 1584092207000,
        createUser: "丁淑贞",
        createUserMechanism: "D1271",
        id: 254,
        mechanism: "",
        pictureName: "Y3008242-6.jpg",
        pictureOrdinal: 6,
        pictureStatus: 1,
        pictureUrl: "https://files.test.ybm100.com/G1/M00/12/BD/Cgoz1F5rVC6Afpt9AABnZEeK-o8849.jpg",
        pictureVersion: "202003131736",
        productCode: "Y3008242",
        updateTime: 1584092207000,
        updateUser: "",
        updateUserMechanism: ""
      },
      {
        applyCode: "STP2003000003-2",
        auditStatus: 0,
        createTime: 1584092207000,
        createUser: "丁淑贞",
        createUserMechanism: "D1271",
        id: 256,
        mechanism: "",
        pictureName: "Y3008242-7.jpg",
        pictureOrdinal: 7,
        pictureStatus: 1,
        pictureUrl: "https://files.test.ybm100.com/G1/M00/12/BD/Cgoz1F5rVC6Ad5e8AAF60yQrOsE486.jpg",
        pictureVersion: "202003131736",
        productCode: "Y3008242",
        updateTime: 1584092207000,
        updateUser: "",
        updateUserMechanism: ""
      },
      {
        applyCode: "STP2003000003-2",
        auditStatus: 0,
        createTime: 1584092207000,
        createUser: "丁淑贞",
        createUserMechanism: "D1271",
        id: 259,
        mechanism: "",
        pictureName: "Y3008242-8.jpg",
        pictureOrdinal: 8,
        pictureStatus: 1,
        pictureUrl: "https://files.test.ybm100.com/G1/M00/12/BE/Cgoz015rVC-ACD0TAABAzJ3BOdA834.jpg",
        pictureVersion: "202003131736",
        productCode: "Y3008242",
        updateTime: 1584092207000,
        updateUser: "",
        updateUserMechanism: ""
      },
    ],
    detailPictureNum: 8,
    manufactureDate: null,
    mechanismNum: 1,
    pictureVersion: "202003131736",
    productStatus: 3,
    shootTime: 1584091788000,
  }
]

export function findProductSale(data) {
  // return new Promise(resolve => {
  //     resolve(da)
  // })
  return request({
    url: '/api/spu/find/productSale',
    method: 'post',
    data: qs.stringify(data)
  }).then(res => {
    return res.data
  }).catch((e) => {
    _Message.error('接口报错')
  });
}

// 获取商品信息
export function getProductData(params, detailType) {
  let data = {};
  if (detailType == 'all') {
    let { spuCode } = params
    data = {
      productCode: spuCode,
      productType: 1, // 查下属所有
      detailType // 查下属所有
    }
  } else {
    data = params;
  }
  return request({
    url: '/api/spu/get/spuDetail',
    data: qs.stringify(data),
    method: 'POST'
  })
}

// 新获取商品信息
export function getNewProductInfo(params, detailType) {
  let data = {};
  if (detailType == 'all') {
    let { spuCode } = params
    data = {
      productCode: spuCode,
      productType: 1, // 查下属所有
      detailType // 查下属所有
    }
  } else {
    data = params;
  }
  return request({
    url: '/api/sau/get/productInfo',
    data: qs.stringify(data),
    method: 'POST'
  })
}

// 获取审批流信息及申请人信息
export function getApplyInfo(data) {
  return request({
    url: '/api/spu/get/applyInfo',
    data,
    method: 'POST',
  })
}

// 获取草稿信息
export function getDarftData(data) {
  return request({
    url: '/api/spu/get/draftInfo',
    data: qs.stringify(data),
    method: 'POST'
  })
}

// 同步待跟进数据查询
export function getGoodsDataDetail(data) {
  return request({
    url: '/api/followup/get/goodsDataDetail',
    data: qs.stringify(data),
    method: 'POST'
  })
}

// 查询 SPU 列表 https://wiki.int.ybm100.com/pages/viewpage.action?pageId=284920604
export function getSpuList(data) {
  return request({
    url: '/api/spu/find/spuList',
    data,
    method: 'POST'
  })
}

// 新增商品 || 保存草稿 https://wiki.int.ybm100.com/pages/viewpage.action?pageId=284134259
export function productAdd(data) {
  return request({
    url: '/api/spu/add/spu',
    data,
    method: 'POST'
  })
}
// 检查SPU唯一性
export function checkSpuSingleApi(data) {
  return request({
    url: '/api/spu/checkDataSingle',
    data,
    method: 'POST'
  })
}

// 商品修改
export function productModify(data) {
  return request({
    url: '/api/spu/modify/spu',
    data,
    method: 'POST'
  })
}

// spu修改
export function spuModify(data) {
  return request({
    url: '/api/spu/modify/spuOnly',
    data,
    method: 'POST'
  })
}

// 预首营商品修改
export function productModifyOperate(data) {
  return request({
    url: '/api/spu/modify/product/operate',
    data,
    method: 'POST'
  })
}

// 未绑定标品的数量
export function noStandardIdCount(data) {
  return request({
    url: '/api/product/query/noStandardIdCount',
    data,
    method: 'POST'
  })
}

// 审核商品修改 https://wiki.int.ybm100.com/pages/viewpage.action?pageId=331810141
export function productModifyReview(data) {
  return request({
    url: '/api/spu/modify/apply/product',
    data,
    method: 'POST'
  })
}

// 审核驳回商品修改 https://wiki.int.ybm100.com/pages/viewpage.action?pageId=289867110
export function productRejectModify(data) {
  return request({
    url: '/api/spu/do/rejectModify',
    data,
    method: 'POST'
  })
}

// 审核不通过商品结束流程 https://wiki.int.ybm100.com/pages/viewpage.action?pageId=314967063
export function productRejectFinish(data) {
  return request({
    url: '/api/worksubstitution/end',
    data,
    method: 'POST'
  })
}

//更新同步待提交 https://wiki.int.ybm100.com/pages/viewpage.action?pageId=291506889
export function productRejectUpdate(data) {
  return request({
    url: `/api/followup/update/skuStatus?id=${data.id}&statusCode=${data.statusCode}&reviewOpinion=${data.reviewOpinion}`,
    // data:qs.stringify(data),
    method: 'POST'
  })
}

export function productModifyField(data) {
  return request({
    url: `/api/spu/find/fieldModifyRecord`,
    data,
    method: 'POST'
  })
}
// 通过通用名加载扩展属性说明书数据
export function getExtendByGeneralName(data) {
  return request({
    url: `/api/spu/getLastExtendInfo/${data.spuCode}`,
    method: 'GET'
  })
}
// https://wiki.int.ybm100.com/pages/viewpage.action?pageId=372063887
export function sortSku(data) {
  return request({
    url: `/api/spu/get/spuDetailSku`,
    data: qs.stringify(data),
    method: 'POST'
  })
}
// 新品上报详情数据
export function productPresentDetail(data) {
  return request({
    url: `/api/present/findProductPresent`,
    data: qs.stringify(data),
    method: 'POST'
  })
}

// 纠错详情
export function correctionDetail(data) {
  return request({
    url: `/api/correction/findProductCorrection`,
    data: qs.stringify(data),
    method: 'POST'
  })
}

// 纠错驳回
export function correctionReject(data) {
  return request({
    url: `/api/correction/update/reject`,
    data: qs.stringify(data),
    method: 'POST'
  })
}

// 商品上架 允许上架
export function allowShelves(data) {
  return request({
    url: `/api/shelves/update/allow`,
    data: qs.stringify(data),
    method: 'POST'
  })
}

// 数据导出列表查询所有
export function getExportListData(data) {
  return request({
    url: `/api/download/find/downloadRecordList`, ///api/download/find/downloadRecordListSelf
    data,
    method: 'POST'
  })
}
// 数据导出列表查询自身
export function getExportListDataSelf(data) {
  return request({
    url: `api/download/find/downloadRecordListSelf`, ///api/download/find/downloadRecordListSelf
    data,
    method: 'POST'
  })
}
// 根据商品大类获取一级分类列表 https://wiki.int.ybm100.com/pages/viewpage.action?pageId=383243511
export function searchOneCategory(data) {
  return request({
    url: `/api/category/search/searchOneCategory`,
    data: qs.stringify(data),
    method: 'POST'
  })
}
// 修改商品图片版本信息
export function productSaleUpdatePictureVersion(data) {
  return request({
    url: 'api/spu/modify/productSaleUpdatePictureVersion',
    data, method: 'POST'
  })
}

// 驳回新品上报
export function presentReject(data) {
  return request({
    url: `/api/present/update/reject`,
    data: qs.stringify(data),
    method: 'POST'
  })
}

// 合并商品图片版本
export function productSaleMergePictureVersion(data) {
  return request({
    url: 'api/spu/modify/productSaleMergePictureVersion',
    data,
    method: 'POST'
  })
}

export function checkProductId(data) {
  return request({
    url: `/api/present/checkProductId`,
    data: qs.stringify(data),
    method: 'POST'
  })
}

export function productSaleCheckPictureVersion(data) {
  return request({
    url: `/api/spu/modify/productSaleCheckPictureVersion`,
    data,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
// 获取商品列表
export function getProductList(data) {
  return request({
    url: `/api/sau/find/sauList`,
    data,
    method: 'POST',
  })
}

// 取消合并
export function cancelMergeProduct(data) {
  return request({
    url: `/api/productMerge/get/productCancelMergeInfo`,
    data: data,
    headers: {
      contentType: 'application/json;charset=utf-8',
    },
    method: 'POST',
  })
}

// 商品导出
export function exportProductList(data) {
  return request({
    url: `/api/sau/export/productList`,
    data: data,
    method: 'POST',
  })
}

// 商品完整度
//  查询完整度列表
export function getProductCompleteList(data) {
  return request({
    url: '/api/integrity/productIntegrityReport',
    data,
    method: 'POST',
  })
}

//  完整度列表导出
export function exportProductCompleteList(data) {
  return request({
    url: '/api/integrity/export/productIntegrityReport',
    data,
    method: 'POST',
  })
}

//  完整度详情导出
export function exportProductCompleteDetail(data) {
  return request({
    url: '/api/integrity/export/productIntegrityDetailReport',
    data,
    method: 'POST',
  })
}

//  完整度详情列表
export function getProductCompleteDetail(data) {
  return request({
    url: '/api/integrity/productIntegrityDetailReport',
    data,
    method: 'POST',
  })
}




// spu商品移动
// spu导出
export function exportSpuList(data) {
  return request({
    url: "/api/spu/export/spuList",
    data: data,
    method: 'POST',
  })
}
// 生成订单信息
export function getMergeProductApplyInfo() {
  return request({
    url: '/api/spuMerge/get/mergeProductApplyInfo',
    method: 'get',
  })
}

// 发起spu商品移动申请
export function submitSpuMerge(data) {
  return request({
    url: '/api/spuMerge/merge',
    data,
    method: 'post',
  })
}

// spu合并详情
export function getMergeSpuDetail(params) {
  return request({
    url: '/api/spuMerge/get/mergeSpuDetail',
    params,
    method: 'get',
  })
}

// spu合并流程信息
export function getMergeSpuApproval(params) {
  return request({
    url: '/api/spuMerge/get/getMergeSpuApproval',
    params,
    method: 'get',
  })
}


// 获取批准文号校验规则
export function getApprovalNoReg() {
  return request({
    url: '/api/category/getApprovalNoReg',
    method: 'get',
  })
}

/** 
 * author:caoshuwen
 * date: 2021-11-05
 * description:精修图版本停启用
 * **/
export function imgChangeStatus(data) {
  return request({
    url: '/api/productPictureVersion/changeStatus',
    method: 'post',
    data
  })
}

/**
 * author:caoshuwen
 * date: 2021-11-09
 * description:获取角色预首营弹框权限
 * **/
export function getPeopleRole() {
  return request({
    url: "api/spu/check/roleCode",
    method: "get"
  });
}

/** 
 * author:caoshuwen
 * date: 2021-11-23
 * description:获取绑定机构详情
 * **/
export function getBindMechanismDetail(data) {
  return request({
    url: '/api/productPictureMechanism/getBindMechanismDetail',
    method: 'post',
    data
  })
}

/** 
 * author:caoshuwen
 * date: 2021-11-23
 * description:提交换绑任务
 * **/
export function submitUpdataBind(data) {
  return request({
    url: '/api/productPictureMechanism/submit',
    method: 'post',
    data
  })
}

/** 
 * author:caoshuwen
 * date: 2021-12-20
 * description:同步下游
 * **/
export function downstream(data) {
  return request({
    url: `/api/sau/sendProductMq/${data}`,
    method: 'get'
  })
}

/** 
 * author:caoshuwen
 * date: 2021-12-27
 * description:sku和sau编辑权限
 * **/
export function formDisableStatus(data) {
  return request({
    url: 'api/spu/get/disableStatus',
    method: 'post',
    data
  })
}

/** 
 * author:caoshuwen
 * date: 2022-01-18
 * description:新增副商品
 * **/
export function addSau(data) {
  return request({
    url: '/api/sau/add',
    method: 'post',
    data
  })
}

/** 
 * author:caoshuwen
 * date: 2022-01-19
 * description:修改副商品
 * **/
export function editSau(data) {
  return request({
    url: '/api/sau/modify',
    method: 'post',
    data
  })
}

/** 
 * author:caoshuwen
 * date: 2022-01-24
 * description:新spudetail
 * **/
export function modifyedSau(data) {
  return request({
    url: '/api/sau/get/modifyedSau',
    method: 'post',
    data: qs.stringify(data)
  })
}

/** 
 * author:caoshuwen
 * date: 2022-01-24
 * description:重新提交副商品
 * **/
export function reCommitSau(data) {
  return request({
    url: '/api/sau/do/rejectModify',
    method: 'post',
    data
  })
}

/** 
 * author:caoshuwen
 * date: 2022-03-28
 * description:批量修改记录单据列表查询
 * **/
export function getBatchModifyRecordList(data) {
  return request({
    url: '/api/modifyRecord/find/batchModifyRecordList',
    method: 'post',
    data
  })
}

/** 
 * author:caoshuwen
 * date: 2022-03-28
 * description:批量修改记录商品列表查询
 * **/
export function getBatchModifyDetailList(data) {
  return request({
    url: '/api/modifyRecord/find/batchModifyDetailList',
    method: 'post',
    data
  })
}

/** 
 * author:caoshuwen
 * date: 2022-03-31
 * description:批量修改记录单据列表导出excel
 * **/
export function exportBatchModifyDetail(data) {
  return request({
    url: "/api/modifyRecord/export",
    method: 'post',
    data
  })
}

/** 
 * author:caoshuwen
 * date: 2022-03-31
 * description:批量修改记录商品列表导出excel
 * **/
export function exportBatchModifyRecord(data) {
  return request({
    url: "/api/modifyRecord/export/modifyDetailList",
    method: 'post',
    data
  })
}

/** 
 * author:caoshuwen
 * date: 2022-03-31
 * description:批量修改记录下载模板
 * **/
export function downloadBatchModifyTemplate(data) {
  return request({
    url: `/api/modifyRecord/download/bathTemplate/${data}`,
    responseType: 'blob',
    method: 'get'
  })
}

/** 
 * author:yanling
 * date: 2025-06-16
 * description:说明书重新匹配
 * **/
export function reMatchExtend(data) {
  return request({
    url: `api/spu/get/productExtendByGeneralNameAndSpec`,
    method: 'get',
    params: data
  })
}