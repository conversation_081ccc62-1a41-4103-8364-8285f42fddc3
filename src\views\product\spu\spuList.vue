
<template>
  <div class="task-to-be-film-container">
    <!-- 查询表单 -->
    <div class="search-form-wrap">
      <el-form :model="model" ref="refSearchForm" label-width="96x">
        <el-row type="flex" :gutter="20">
          <!-- 商品大类 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="商品大类">
              <el-select
                @change="changeSpuCategory"
                v-model="formData.spuCategory"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" value></el-option>
                <el-option
                  v-for="item in spuCategoryList"
                  :key="item.id"
                  :label="item.dictName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- spu编码 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="spu编码">
              <el-input
                v-model="formData.spuCode"
                placeholder=""
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- 通用名 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="通用名">
              <el-input
                v-model="formData.generalName"
                placeholder=""
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- sku数量 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="sku数量">
              <el-input
                v-model="formData.skuCount"
                placeholder=""
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- 六级分类 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="六级分类">
              <el-cascader
                :options="categoryList"
                :props="{
                  checkStrictly: true,
                  value: 'id',
                  label: 'name',
                  lazy: true,
                  lazyLoad: lazyLoad,
                }"
                clearable
                v-model="category"
                @change="categoryChange"
              ></el-cascader>
            </el-form-item>
          </el-col>
          <!-- 批件规格 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="批件规格">
              <el-input
                v-model="formData.instructionSpec"
                placeholder=""
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- 生产厂家 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="生产厂家">
              <el-input
                v-model="formData.manufacturer"
                placeholder=""
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- 批准文号 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="批准文号">
              <el-input
                v-model="formData.approvalNo"
                placeholder=""
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- 停启用 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="是否启用">
              <el-select
                v-model="formData.disableStatus"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" value></el-option>
                <el-option label="是" :value="1"></el-option>
                <el-option label="否" :value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 按钮 -->
          <el-col :span="6" :offset="18">
            <el-button type="primary" size="medium" @click="btnSearchClick"
              >查询</el-button
            >
            <el-button size="medium" @click="btnResetClick">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="btn-wrap">
      <el-button size="medium" @click="handleExport">导出EXCEL</el-button>
      <vxe-toolbar custom style="display: inline-block"></vxe-toolbar>
    </div>
    <!-- table -->
    <div class="table-wrap">
      <vxe-table
        border
        highlight-hover-row
        highlight-current-row
        resizable
        height="100%"
        auto-resize
        size="small"
        align="center"
        :tooltip-config="{ enterable: false }"
        :loading="tableLoading"
        :data="tableData"
        :seq-config="{ startIndex: (pageNum - 1) * pageSize }"
        ref="refVxeTable"
        @sort-change="sortQueryList"
        @cell-dblclick="cellDBLClickEvent"
      >
        <vxe-table-column
          type="seq"
          title="序号"
          width="60"
          show-header-overflow
          show-overflow
          fixed="left"
        ></vxe-table-column>
        <vxe-table-column
          field="spuCategory"
          title="商品大类"
          width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>

        <vxe-table-column
          field="spuCode"
          title="spu编码"
          width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>

        <vxe-table-column
          field="generalName"
          title="通用名"
          width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>

        <vxe-table-column
          field="instructionSpec"
          title="批件规格"
          width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="manufacturer"
          title="生产厂家"
          width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="dosageForm"
          title="剂型"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="firstCategory"
          title="一级分类"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="secondCategory"
          title="二级分类"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="thirdCategory"
          title="三级分类"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="fourthCategory"
          title="四级分类"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="fiveCategory"
          title="五级分类"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="sixCategory"
          title="六级分类"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="approvalNo"
          title="批准文号/注册证号"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="skuCount"
          title="sku数量"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="createUser"
          title="创建人"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="createTime"
          title="创建时间"
          min-width="120"
          show-header-overflow
          show-overflow
          sortable
        >
          <template v-slot="{ row }">
            {{ row.createTime | parseTime }}
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="disableStatus"
          title="是否启用"
          min-width="120"
          show-header-overflow
          show-overflow
          sortable
        >
          <template v-slot="{ row }">
            {{ ['否','是'][row.disableStatus] }}
          </template>
        </vxe-table-column>
        <vxe-table-column
          title="操作"
          width="150"
          show-header-overflow
          show-overflow
          fixed="right"
        >
          <template v-slot="{ row }">
            <span
              class="btn"
              v-if="hasPermission('spu列表-修改') && !row.skuCount"
            >
              <el-link :underline="false" type="primary" @click.stop="edit(row)"
                >修改</el-link
              >
            </span>
            <span class="btn" v-if="hasPermission('spu列表-修改')">
              <el-link
                :underline="false"
                type="primary"
                @click.stop="handleMove(row)"
                >移动商品</el-link
              >
            </span>
          </template>
        </vxe-table-column>
      </vxe-table>
    </div>
    <!-- 分页 -->
    <el-row class="custom-pagination-wrap">
      <el-col>
        <el-pagination
          background
          :current-page.sync="pageNum"
          :page-sizes="[20, 50, 100, 200]"
          :page-size.sync="pageSize"
          :total="total"
          layout="prev, pager, next, jumper,total, sizes"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { hasPermission } from "@/utils/index.js";
import { getSpuList, exportSpuList } from "@/api/product.js";
import { categoryList, findDictList } from "@/api/dict.js";
export default {
  name: "spuList",
  filters: {},
  props: {},
  data() {
    return {
      pageNum: 1,
      pageSize: 20,
      total: 0, //总条数
      sortFiled: "createTime", // 排序查询
      sortRule: "desc", //(升序-ASC, 降序-DESC)
      formData: {},
      tableLoading: false,
      tableData: [],
      categoryList: [],
      spuCategoryList: [],
      category: [],
    };
  },
  computed: {},
  watch: {},
  created() {
    this.searchForm();
    this.getFixCategoryList({
      isValid: 1,
      level: 1,
      parentId: "",
    });
    this.getCategoryList();
  },
  mounted() {},
  methods: {
    /**
     * 查询table数据
     */
    async searchForm() {
      try {
        this.tableLoading = true;
        let param = Object.assign({}, this.formData);
        param.page = this.pageNum;
        param.limit = this.pageSize;
        param.sortList = this.sortFiled
          ? [
              {
                order: "0",
                columnName: this.sortFiled,
                lift: this.sortRule, //(升序-ASC, 降序-DESC)
              },
            ]
          : [];
        let res = await getSpuList(param);
        this.tableLoading = false;
        if (!res.retCode) {
          this.tableData = res.data.list;
          this.total = res.data.total;
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      } catch (error) {
        console.error(error);
      }
    },
    // 获取六级分类
    getFixCategoryList(data) {
      categoryList(data).then((res) => {
        if (!res.retCode) {
          this.categoryList = res.data;
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      });
    },
    // 获取商品大类
    getCategoryList() {
      findDictList({
        type: 7,
      }).then((res) => {
        this.spuCategoryList = res.data.list;
      });
    },
    // 动态加载六级分类
    lazyLoad(node, resolve) {
      categoryList({
        isValid: 1,
        level: node.level + 1,
        parentId: node.value,
      }).then((res) => {
        if (!res.retCode) {
          if (node.level == 5) {
            res.data.forEach((item) => {
              item.leaf = 6;
            });
          }
          resolve(res.data);
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      });
    },
    // 六级分类改变
    categoryChange(e) {
      this.formData.firstCategory = e[0] ? e[0] : "";
      this.formData.secondCategory = e[1] ? e[1] : "";
      this.formData.thirdCategory = e[2] ? e[2] : "";
      this.formData.fourthCategory = e[3] ? e[3] : "";
      this.formData.fiveCategory = e[4] ? e[4] : "";
      this.formData.sixCategory = e[5] ? e[5] : "";
    },
    /**
     * 查询按钮点击
     */
    btnSearchClick() {
      this.pageNum = 1;
      this.searchForm();
    },

    /**
     * 重置
     */
    btnResetClick() {
      this.formData = {
        spuCategory: "",
        spuCode: "",
        generalName: "",
        skuCount: "",
        firstCategory: "",
        secondCategory: "",
        thirdCategory: "",
        fourthCategoryStr: "",
        instructionSpec: "",
        manufacturer: "",
        approvalNo: "",
      };
      this.category = [];
      this.$refs.refVxeTable.clearSort();
      this.sortFiled = "createTime";
      this.sortRule = "desc";
      this.pageNum = 1;
      this.searchForm();
    },

    /**
     * pageSize 改变事件
     */
    handleSizeChange(pageSize) {
      this.pageNum = 1;
      this.pageSize = pageSize;
      this.searchForm();
    },

    /**
     * pageNum 改变事件
     */
    handleCurrentChange(currentPage) {
      this.pageNum = currentPage;
      this.searchForm();
    },

    // 排序查询
    sortQueryList({ column, property, order }) {
      if (order) {
        this.sortFiled = property;
        this.sortRule = order;
      } else {
        this.sortFiled = "createTime";
        this.sortRule = "desc";
      }
      this.pageNum = 1;
      this.searchForm();
    },

    receive(row) {
      worksubstitutionReceive({
        applyCode: row.applyCode,
        id: row.id,
        receiveStatus: 1,
      }).then((res) => {
        if (!res.retCode) {
          this.searchForm();
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      });
    },

    hasPermission(str) {
      return hasPermission(str);
    },
    // 修改商品
    edit(row) {
      try {
        parent.CreateTab(
          `../static/dist/index.html#/product/editProduct?page=spu&productCode=${row.spuCode}&productType=3&spuCode=${row.spuCode}&from=spuOperate&pageType=edit`,
          "spu修改",
          this
        );
      } catch {
        this.$router.push({
          path: "/product/editProduct",
          query: {
            page: "spu",
            productCode: row.spuCode,
            productType: 3,
            spuCode: row.spuCode,
            pageType: "edit",
            from: "spuOperate",
          },
        });
      }
    },

    handleMove(row) {
      try {
        parent.CreateTab(
          `../static/dist/index.html#/product/mergeSpu?productCode=${row.spuCode}`,
          "移动商品",
          this
        );
      } catch {
        this.$router.push({
          path: "/product/mergeSpu",
          query: {
            productCode: row.spuCode,
          },
        });
      }
    },

    // 查看详情
    cellDBLClickEvent({ row }) {
      try {
        parent.CreateTab(
          `../static/dist/index.html#/product/detailProduct?page=spu&productCode=${row.spuCode}&hideTab=1&dataType=${row.dataType}&productType=1&spuCode=${row.spuCode}&detailType=all&applyCode=&pageType=detail`,
          "商品详情"
        );
      } catch {
        this.$router.push({
          path: "/product/detailProduct",
          query: {
            page: "spu",
            productCode: row.spuCode,
            dataType: row.dataType,
            productType: 1,
            spuCode: row.spuCode,
            detailType: "all",
            applyCode: "",
            pageType: "detail",
            hideTab:1
          },
        });
      }
    },
    // 打开导出商品弹框
    handleExport() {
      let param = Object.assign({}, this.formData);
      const loading = this.$loading({
        lock: true,
        text: "数据导出中...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      exportSpuList(param).then((res) => {
        loading.close();
        if (!res.retCode) {
          this.$message({
            showClose: false,
            type: "success",
            message: res.retMsg,
          });
        } else {
          this.$message({
            showClose: false,
            type: "error",
            message: res.retMsg,
          });
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.task-to-be-film-container {
  /**
   * 查询表单
   */
  .search-form-wrap {
    width: 100%;
    padding: 15px;
    border-bottom: 1px dashed #e4e4eb;

    .el-row {
      flex-wrap: wrap;

      .el-col {
        display: flex;
        justify-content: flex-end;
        .el-form-item {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          width: 100%;
          margin-right: 0;
          margin-bottom: 15px;

          /deep/ {
            .el-form-item__label {
              padding: 0 12px;
              color: #292933;
              font-weight: normal;
            }

            .el-form-item__content {
              flex: 1;
              margin-left: 0 !important;
              .el-range-editor.el-input__inner {
                width: 100%;

                .el-range-separator {
                  width: 14px;
                  padding: 0;
                }
              }
            }
          }
          .el-input {
            width: 100%;
          }

          .el-select {
            width: 100%;
          }
          .el-cascader {
            width: 100%;
          }
        }
      }
    }
  }
  /**
     * 按钮组
     */
  .btn-group-wrap {
    font-size: 0;
    padding: 15px;
    .el-col {
      .el-button,
      .vxe-column-filter-container {
        margin-right: 10px;
      }
    }
  }
  /**
     * table
     */
  .table-wrap {
    width: 100%;
    min-height: 440px;
    height: calc(100vh - 346px);
    padding: 0 15px;
    /deep/ .vxe-table .vxe-body--row.row--current {
      background: #f5f7fa;
      color: #606266;
    }
  }
}
.btn {
  padding: 0 10px;
}
.btn-wrap /deep/ {
  padding: 15px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  .el-button {
    margin-bottom: 15px;
    margin-left: 0;
    margin-right: 15px;
  }
  .vxe-toolbar {
    flex: 1;
    .vxe-button--wrapper {
      display: none;
    }
    .vxe-tools--operate {
      margin-top: 2px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
.tree-wrap /deep/ {
  padding-bottom: 20px;
  ul {
    li {
      line-height: 32px;
      span {
        font-size: 14px !important;
      }
      a {
        height: 32px;
        span {
          display: inline-block;
          vertical-align: middle !important;
          font-size: 14px !important;
          &.node_name {
            line-height: 32px;
          }
        }
      }
    }
  }
}
.tree-btn {
  border-bottom: 1px solid #dbdbdb;
  padding-bottom: 10px;
  span {
    color: #f56c6c;
  }
}
</style>