<template>
  <div class="component-container">
    <div
      class="loading"
      v-loading="productLoading"
      v-show="productLoading"
    ></div>
    <el-tabs type="border-card">
      <el-tab-pane label="基础属性">
        <approval-process :approvalData="approvalData"></approval-process>
        <spu
          ref="spu"
          :spuData="spuData"
          :skuData="skuData"
          :sauData="sauData"
          :changeList="productModifyField.spuFieldList"
          :coorection="coorection"
        ></spu>
        <sku
          ref="sku"
          :skuData="skuData"
          :sauData="sauData"
          :changeList="productModifyField.skuFieldList"
          :coorection="coorection"
        ></sku>
        <sau ref="sau" :sauData="sauData"></sau>
        <modify-record :recordData="recordData"></modify-record>
      </el-tab-pane>
      <el-tab-pane v-if="spuCategory.type == 'GENERAL_MEDICINE'" label="扩展属性">
        <extended-attr
          ref="extend"
          :changeList="productModifyField.extendFieldList"
        ></extended-attr>
      </el-tab-pane>
      <el-tab-pane label="资质属性">
        <qualification-attr></qualification-attr>
      </el-tab-pane>
      <el-tab-pane label="销售属性">
        <sales-attr></sales-attr>
      </el-tab-pane>
      <el-tab-pane v-if="spuCategory.type == 'TRADITIONAL_MEDICINE'" label="拓展属性">
        <extended-attr2
          ref="extend"
          :changeList="productModifyField.extendFieldList"
        ></extended-attr2>
      </el-tab-pane>
    </el-tabs>
    <el-row class="btns" :style="{ top: scrollTop < 40 ? '40px' : '0px' }">
      <!-- 一审和二审 -->
      <el-col
        :span="24"
        class="text-rt"
        v-if="
          operationType == 'auditLevel1' ||
          operationType == 'auditLevel2' ||
          operationType == 'auditLevel3'
        "
      >
        <el-button
          type="primary"
          @click="hang()"
          v-show="showHangBtn && operationType !== 'auditLevel3'"
          >挂起</el-button
        >
        <el-button type="primary" v-show="editState" @click="submit()"
          >修改</el-button
        >
        <el-button
          type="primary"
          v-show="
            editState && $store.getters.spuCategory.type !== 'GENERAL_MEDICINE'
          "
          @click="submit('checkSpu')"
          >spu唯一性校验</el-button
        >
        <el-button @click="review(true)" v-show="!editState"
          >审核通过</el-button
        >
        <!-- 1.4.1V 预首营 审核 -->
        <el-button @click="review(false)">{{
          urlParam.approvalProcess == 6 ? "审核不通过" : "审核驳回"
        }}</el-button>
      </el-col>
      <!-- 预首营 -->
      <!-- <el-col :span="24" class="text-rt" v-if="operationType=='operate'">
        <el-button type="primary" @click="submit()">提交</el-button>
      </el-col>-->
      <!-- 驳回修改 -->
      <el-col :span="24" class="text-rt" v-if="operationType == 'RejectEdit'">
        <el-button @click="review(false, true)">结束流程</el-button>
        <el-button type="primary" @click="submit()">重新提交</el-button>
        <el-button
          type="primary"
          v-show="$store.getters.spuCategory.type !== 'GENERAL_MEDICINE'"
          @click="submit('checkSpu')"
          >spu唯一性校验</el-button
        >
      </el-col>
    </el-row>
    <!-- 审核弹框 -->
    <el-dialog
      title="审核"
      width="500px"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisible"
      :close-on-press-escape="false"
      :show-close="false"
      @close="dialogClose"
    >
      <el-form :model="reviewForm" :rules="rules" ref="reviewForm">
        <el-form-item label="审核状态">
          <el-input
            v-model="reviewForm.state"
            disabled
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="审核意见"
          prop="value"
          v-if="!isImplement || reviewForm.state == '审核通过'"
        >
          <el-input
            type="textarea"
            placeholder="超过100的长度，不能提交"
            v-model="reviewForm.value"
            autocomplete="off"
            maxlength="100"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="原因"
          v-if="reviewForm.state !== '审核通过' && isImplement"
        >
          <el-select
            v-model="reviewForm.rejectState"
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option
              v-for="item in rejectOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="save()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// 商品基础共用信息混入对象
import { productMixinBase } from "@/mixin/productMixinBase.js";
import {
  getProductData, //获取商品信息
  getGoodsDataDetail, // 同步待提交数据
  productModifyOperate, // 预首营商品修改
  getApplyInfo, //审批流信息
  productModifyReview, //审核中商品修改
  productRejectModify, //驳回商品修改
  productRejectFinish, //审核不通过流程
  productModifyField, //获取商品修改变更字段
  checkSpuSingleApi, //检查SPU唯一性
} from "@/api/product";
import {
  singleReview, // 查询单个审核事项数据
  hang, // 挂起一个审核
  review, // 审核商品
} from "@/api/worksubstitution";

import modifyRecord from "./modifyRecord";
import qualificationAttr from "./qualificationAttr";
import salesAttr from "./salesAttr";

export default {
  name: "",
  mixins: [productMixinBase],
  components: { modifyRecord, qualificationAttr, salesAttr },
  computed: {
    // 路由参数
    urlParam() {
      return this.$route.query;
    },
    // 是否为实施提报商品审核
    isImplement() {
      if (
        this.approvalData.applyAttribute &&
        this.approvalData.applyAttribute.productSource
      ) {
        return this.approvalData.applyAttribute.productSource == "实施提报"
          ? true
          : false;
      } else {
        return false;
      }
    },
    spuCategory() {
      return this.$store.state.product.spuCategory;
    },
  },
  data() {
    return {
      coorection: {},
      recordData: [], // 修改记录
      scrollTop: 0,
      editState: false,
      showHangBtn: true,
      dialogFormVisible: false,
      // 实时提报时，驳回原因选项
      rejectOptions: [
        {
          value: 3,
          label: "商品信息不完整，请实施修改后提报",
        },
        {
          value: 2,
          label: "商品无需匹配标准库",
        },
        {
          value: 4,
          label: "提报的商品已有标准库信息",
        },
      ],
      reviewForm: {
        state: "审核通过",
        value: "",
        rejectState: 3,
      },
      rules: {
        value: [
          {
            required: false,
            message: "审核意见不能为空",
            trigger: "blur",
          },
        ],
      },
      productModifyField: {},
    };
  },
  created() {
    this.init();
    window.onscroll = () => {
      this.scrollTop =
        document.documentElement.scrollTop || document.body.scrollTop;
    };
  },
  methods: {
    async init() {
      if (this.urlParam.type && this.urlParam.type == "coorection") {
        this.coorectionData = JSON.parse(
          localStorage.getItem("coorectionData")
        );
        let coorectionObj = {};
        for (let key of this.coorectionData.detailDtoList) {
          coorectionObj[key.correctKey] = key.correctValue;
        }
        this.coorection = coorectionObj;
      }
      // 获取审批流信息
      await this.getApplyInfo();
      // 获取审核信息数据
      await this.getReviewInfo();
      this.getProductInfo();
      // 检测页面数据值修改变化
      this.$bus.$on("productChange", (res) => {
        this.editState = res;
      });
      // 是否显示挂起按钮
      if (this.urlParam.isFromHang) {
        this.showHangBtn = false;
      }
    },
    // 获取商品数据
    async getProductInfo() {
      let resModify = await productModifyField({
        applyCode: this.urlParam.applyCode,
      });
      this.productModifyField = resModify.data;
      let res = await getProductData({
        spuCode: this.urlParam.spuCode,
        productType:
          this.urlParam.approvalProcess != 0 ? this.urlParam.productType : 4,
        applyCode: this.urlParam.applyCode,
        productCode: this.urlParam.productCode,
        detailType: "self",
      });
      this.spuData = Object.freeze(res.data.spu);
      this.skuData = Object.freeze(res.data.sku);
      this.sauData = Object.freeze(res.data.sau);
      this.recordData = Object.freeze(res.data.record);
    },
    // 获取审核信息数据
    async getReviewInfo() {
      if (this.operationType == "operate") {
        return false;
      }
      let res = await singleReview({ id: this.urlParam.id });
      if (res.data) {
        this.reviewInfo = res.data;
      } else {
        this.$message.error("获取审核信息异常");
      }
    },
    /**
     * @description:挂起
     */
    async hang() {
      let res = await hang({
        id: this.reviewInfo.id,
        reviewStatus: this.reviewInfo.reviewStatus,
        hangStatus: this.reviewInfo.hangStatus,
      });
      if (res.code && res.code == "401") {
      } else {
        parent.CreateTab("../static/dist/index.html#/workbench/prepare", "待办事项", true);
      }
    },
    /**
     * @description:审核
     * @param {boolen} pass true : 审核通过
     * @param {boolen} finish  true : 审核不通过
     */
    review(pass, finish) {
      this.dialogFormVisible = true;
      if (finish) {
        this.reviewForm.state = "结束流程";
        this.rules.value[0].required = true;
        return;
      }
      if (pass) {
        this.reviewForm.state = "审核通过";
        this.rules.value[0].required = false;
      } else {
        // 如果是预首营则修改提示文本为审核不通过
        this.reviewForm.state =
          this.urlParam.approvalProcess == 6 ? "审核不通过" : "审核驳回";
        this.rules.value[0].required = true;
      }
      // 实施提报时不做必填校验
      if (this.isImplement) {
        this.rules.value[0].required = false;
      }
    },
    // 审核弹层保存按钮
    save() {
      this.$refs.reviewForm.validate(async (valid) => {
        // 审核通过或审核驳回(预首营审核6：审核不通过)
        if (
          this.reviewForm.state == "审核通过" ||
          this.reviewForm.state == "审核驳回" ||
          this.reviewForm.state == "审核不通过"
        ) {
          if (valid || this.reviewForm.state == "审核通过") {
            this.productLoading = true;
            let reviewOpinion = "";
            let reviewOpinionType = 0;
            // 如果是实施提报，匹配对应的选项值内容至reviewOpinion； isImplement：实施提报
            if (this.reviewForm.state !== "审核通过" && this.isImplement) {
              for (let item of this.rejectOptions) {
                if (item.value == this.reviewForm.rejectState) {
                  reviewOpinion = item.label;
                }
              }
              reviewOpinionType = this.reviewForm.rejectState;
            } else {
              reviewOpinion = this.reviewForm.value;
            }

            let res = await review({
              id: this.reviewInfo.id,
              rejectStatus: this.reviewForm.state == "审核通过" ? 1 : 0,
              reviewStatus: this.reviewInfo.reviewStatus,
              applyCode: this.reviewInfo.applyCode,
              approvalProcess: this.reviewInfo.approvalProcess,
              reviewOpinion,
              reviewOpinionType,
              applyUserScope: this.reviewInfo.applyUserScope,
            });
            this.productLoading = false;
            if (res.retCode != 0) {
              this.$message.error(res.retMsg);
            } else {
              if (this.reviewInfo.hangStatus == 1) {
                parent.CreateTab(
                  "/api/worksubstitution/to/hang",
                  "挂起事项",
                  true
                );
              } else {
                parent.CreateTab(
                  "../static/dist/index.html#/workbench/prepare",
                  "待办事项",
                  true
                );
              }
              parent.CloseTab(
                "../static/dist/index.html#/product/reviewProduct"
              );
            }
          }
        }
        // 结束流程
        if (valid && this.reviewForm.state == "结束流程") {
          this.productLoading = true;
          let res = await productRejectFinish({
            id: this.reviewInfo.id,
            reviewStatus: this.reviewInfo.reviewStatus,
            applyCode: this.reviewInfo.applyCode,
            reviewOpinion: this.reviewForm.value,
            approvalProcess: this.reviewInfo.approvalProcess,
          });
          this.productLoading = false;
         
          if (res.retCode != 0) {
            this.$message.error(res.retMsg);
          } else {
            parent.CreateTab(
              "/api/worksubstitution/to/submit",
              "已提交事项",
              true
            );
            parent.CloseTab("../static/dist/index.html#/product/reviewProduct"); // 关闭当前页面
          }
        }
      });
    },
    dialogClose() {
      this.$refs.reviewForm.resetFields();
    },
    /**
     * @description:商品数据提交
     * @param {string} checkSpuSingle 是否为检查SPU唯一触发
     * @return:
     */
    async submit(checkSpuSingle) {
      let spu = await this.$refs.spu.getSpuData();
      let skuData = this.$refs.sku.getSkuData();
      let sau = this.$refs.sau.getSauData();
      let extendData = {};
      if (this.spuCategory.type == "GENERAL_MEDICINE" || this.spuCategory.type == "TRADITIONAL_MEDICINE") {
        extendData = this.$refs.extend.getExtendData();
      }
      if(spu.data.spuCategory == 5){
        sau = this.sauData;
      }
      if (!spu.state || !skuData || !sau || !extendData) {
        // 未通过 SPU表单校验
        return;
      };
      let spuData = spu.data;
      if (checkSpuSingle) {
        this.productLoading = true;
        let res = await checkSpuSingleApi(spuData);
      
        if (res.success) {
          this.$message.success("spu唯一性校验通过");
        } else {
          this.$message.error(res.retMsg);
        }
        this.productLoading = false;
        return false;
      }  
      // formatSubmitData 函数在 productMixinBase.js中定义
      let submitData = this.formatSubmitData(
        _.cloneDeep(spuData),
        _.cloneDeep(skuData),
        _.cloneDeep(sau),
        _.cloneDeep(extendData)
      );
      //操作类型,
      // (0:保存草稿,
      // 1:新增spu,
      // 2:属性修改,
      // 3:商品合并,
      // 4:商品停用,
      // 5:三方同步,
      // 6:批量修改,
      // 7:草稿删除,
      // 8:预首营修改)

      submitData.operType = 2;

      submitData.prodType = this.urlParam.productType; //商品编码类型(1:spu,2:sku,3:sau)
      submitData.prodCode = this.urlParam.productCode; //商品编码
      submitData.applyCode = this.urlParam.applyCode
        ? this.urlParam.applyCode
        : ""; //单据编号
      this.productLoading = true;
      let res = {};
      // if (this.operationType == "operate") {
      //   //预首营修改
      //   submitData.operType = 8;
      //   this.submitForOperate(submitData);
      //   return false;
      // }
      if (
        this.operationType == "auditLevel1" ||
        this.operationType == "auditLevel2" ||
        this.operationType == "auditLevel3"
      ) {
        //审核
        this.submitForAudit(submitData);
        return false;
      }
      //驳回修改
      if (this.operationType == "RejectEdit") {
        this.submitForReject(submitData);
        return false;
      }
    },
    /**
     * @description:审核修改
     * @param {object} submitData :提交数据
     * @return:
     */
    async submitForAudit(submitData) {
      delete submitData.operType;
      let res = await productModifyReview(submitData);
      this.productLoading = false;
      if (res.success) {
        this.editState = false;
      } else {
        this.$message.error(res.retMsg);
      }
    },
    /**
     * @description:审核驳回修改
     * @param {object} submitData :提交数据
     * @return:
     */
    async submitForReject(submitData) {
      delete submitData.operType;
      let res = await productRejectModify(submitData);
      this.productLoading = false;
      if (res.success) {
        parent.CreateTab("/api/worksubstitution/to/submit", "已提交事项", true);
        parent.CloseTab("../static/dist/index.html#/product/reviewProduct"); // 关闭当前页面
      } else {
        this.$message.error(res.retMsg);
      }
    },
    async getApplyInfo() {
      let res = await getApplyInfo({
        selectType: 3, // 查询类型, (0:草稿, 1:新增, 2:属性修改,3:审核或驳回修改,4:查询详情)
        applyCode: this.urlParam.applyCode ? this.urlParam.applyCode : "", //单据编号
        productCode: this.urlParam.productCode, //商品编码
        productType: this.urlParam.productType, //商品类型(1-SPU;2-SKU;3-SAU)
        spuCode: this.urlParam.spuCode, //SPU编码
      });
      if (res.success) {
        this.approvalData = res.data;
        this.$bus.$emit("auditData", res.data.applyAttribute);
        let approvalProcess = res.data.approvalProcess.nextProcessNode;
        // 如果存在纠错记录信息
        if (this.coorectionData) {
          this.approvalData.coorection = {
            msg: this.coorectionData.correctionMsg,
            imageList: this.coorectionData.mediaDtoList,
          };
        }
        // 判断是否为审核驳回修改
        if (this.urlParam.from == "edit") {
          // 设置商品操作类型为驳回修改
          this.$store.commit("product/SET_OPERATION_TYPE", "RejectEdit");
          return false;
        }
        if (
          res.data.approvalProcess.processList[approvalProcess].optJob ==
          "质管总部"
        ) {
          // 质管总部审核
          this.$store.commit("product/SET_OPERATION_TYPE", "auditLevel2");
        } else if (
          res.data.approvalProcess.processList[approvalProcess].optJob ==
          "运营中心"
        ) {
          // 运营中心审核
          this.$store.commit("product/SET_OPERATION_TYPE", "auditLevel1");
        } else {
          this.$store.commit("product/SET_OPERATION_TYPE", "auditLevel3");
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.component-container {
  padding-top: 0px;
  // padding-bottom: 70px;
  .loading {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
  }
  /deep/.el-tabs__header {
    border-bottom: none;
    background-color: #f2f2f2;
  }
  /deep/ .el-tabs__content {
    padding-top: 72px;
  }
  .el-tabs--border-card {
    border: none;
    box-shadow: none;
  }
  .btns {
    background: #fff;
    width: 100%;
    position: fixed;
    top: 40px;
    padding: 15px;
    z-index: 10;
  }
}
</style>
