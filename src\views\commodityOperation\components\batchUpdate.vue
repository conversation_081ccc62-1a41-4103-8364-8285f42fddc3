<template>
  <el-dialog
    class="wrapper"
    :visible.sync="visible"
    :close-on-click-modal="false"
    title="批量导入"
  >
    <div class="content">
      <el-input :value="filename"></el-input>
    </div>
    <div slot="footer" class="footer">
      <el-button @click="selectFile"
        >选择文件
        <input
          ref="upload"
          type="file"
          @change="onFileChange"
          style="display: none"
      /></el-button>
      <el-button type="primary" :loading="uploading" @click="upload">开始上传</el-button>
      <el-button><a :href="templateUrl" download>下载模板</a></el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "batchUpdate",
  props: {
    templateUrl: {
      type: String,
      default: ""
    },
    uploading: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      visible: false,
      filename: "",
    };
  },
  methods: {
    show() {
      this.visible = true;
    },
    hide() {
      this.visible = false;
    },
    selectFile() {
      this.$refs.upload.value = null;
      this.$refs.upload.click();
    },
    onFileChange(e) {
      const files = e.target.files;
      console.log(files);
      this.filename = Array.from(files).map((file) => file.name).join();
    },
    upload() {
      if (!this.$refs.upload.files.length) {
        return ;
      }
      this.$emit("upload", this.$refs.upload.files);
    },
  },
};
</script>

<style lang="scss" scoped>
.wrapper {
  /deep/ .el-dialog__body {
    overflow: auto;
  }
}
</style>
