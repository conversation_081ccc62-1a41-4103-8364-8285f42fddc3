<template>
  <div class="component-container">
    <div class="loading" v-loading="productLoading" v-show="productLoading"></div>
    <approval-process-new :approvalData="approvalData" :boardData="urlParam"></approval-process-new>
    <!-- spu -->
    <div class="apply">
      <div class="title">SPU</div>
      <vxe-table
        style="margin:0 40px"
        border
        highlight-hover-row
        resizable
        auto-resize
        size="small"
        align="center"
        :tooltip-config="{ enterable: false }"
        :data="spuTableData"
      >
        <vxe-table-column field="spuCode" title="SPU编码" min-width="80" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="largeCategoryName" title="商品大类" min-width="100" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="approvalNo" title="批准文号" min-width="110" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="businessScopeMultiStr" title="经营范围" min-width="110" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="generalName" title="通用名" min-width="110" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="dosageFormName" title="剂型" min-width="70" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="shadingAttrName" title="存储属性" min-width="80" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column title="六级分类" min-width="190" show-header-overflow show-overflow>
          <template v-slot="{ row }">
            <span
              >{{ row.firstCategoryName }}>{{ row.secondCategoryName }}>{{ row.thirdCategoryName }}>{{ row.fourthCategoryName }}>{{
                row.fiveCategoryName
              }}>{{ row.sixCategoryName }}</span
            >
          </template>
        </vxe-table-column>
        <vxe-table-column field="manufacturerName" title="生产厂家" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="instructionSpec" title="批件规格" min-width="120" show-header-overflow show-overflow></vxe-table-column>
      </vxe-table>
    </div>
    <div class="empty"></div>
    <!-- sku -->
    <div class="apply">
      <div class="title">SKU</div>
      <vxe-table
        style="margin:0 40px"
        border
        highlight-hover-row
        resizable
        auto-resize
        size="small"
        align="center"
        :tooltip-config="{ enterable: false }"
        :data="skuTableData"
      >
        <vxe-table-column type="seq" title="序号" min-width="50" show-header-overflow show-overflow> </vxe-table-column>
        <vxe-table-column field="skuCode" title="sku编码" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="skuName" title="商品名" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="brand" title="品牌商标" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="spec" title="规格型号" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="packageUnitName" title="包装单位" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column
          field="prescriptionCategoryName"
          title="处方分类"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column field="storageCondName" title="存储条件" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="validity" title="有效期" min-width="120" show-header-overflow show-overflow>
          <template v-slot="{ row }">
            <span>{{ row.validity }}{{ row.validityUnit | filterUnit }}</span>
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="entrustedManufacturerName"
          title="委托生产厂家"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column field="standardCodes" title="本位码" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="smallPackageCode" title="小包装条码" min-width="120" show-header-overflow show-overflow></vxe-table-column>
      </vxe-table>
    </div>
    <div class="empty"></div>
    <!-- 管理属性 -->
    <div class="apply">
      <div class="title">管理属性</div>
      <vxe-table
        style="margin:0 40px"
        border
        highlight-hover-row
        resizable
        auto-resize
        size="small"
        align="center"
        :tooltip-config="{ enterable: false }"
        :data="sauTableData"
        :editConfig="{ trigger: 'click', mode: 'cell', activeCellMethod: true }"
      >
        <vxe-table-column type="seq" title="序号" min-width="50" show-header-overflow show-overflow> </vxe-table-column>
        <vxe-table-column field="sauCode" title="商品编码" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="skuCode" title="sku" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column title="是否线下业务" min-width="120" show-header-overflow show-overflow>
          <template v-slot="{ row }">
            <el-radio v-model="row.offlineBusinessType" :label="1">是</el-radio>
            <el-radio v-model="row.offlineBusinessType" :label="0">否</el-radio>
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="attrValue"
          title="销售渠道"
          min-width="120"
          show-header-overflow
          show-overflow
          :editRender="{ name: 'input', events: { input: changeComment } }"
        >
          <template #edit="{ row }">
            <el-input v-model="row.attrValue" maxlength="10"></el-input>
          </template>
        </vxe-table-column>
        <vxe-table-column field="mergeProduct" title="合并商品" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column title="操作" min-width="120" show-header-overflow show-overflow>
          <template v-slot="{ $rowIndex }">
            <span v-if="$rowIndex !== 0 && !urlParam.pageType">
              <el-link :underline="false" type="primary" @click.stop="deleteItem($rowIndex)">删除</el-link></span
            >
          </template>
        </vxe-table-column>
      </vxe-table>
    </div>
    <modify-record :recordData="recordData" style="padding-bottom:10px"></modify-record>
    <el-row v-if="urlParam.pageType === 'edit'" class="btns" :style="{ top: scrollTop < 40 ? '40px' : '0px' }">
      <!-- 一审和二审 -->
      <el-col :span="24" class="text-rt">
        <el-button type="info" @click="cancel">取消提交</el-button>
        <el-button v-if="btnAuth.rejectToStart" type="warning" @click="showDialog('backToFirst')">回退到提交人</el-button>
        <el-button v-if="btnAuth.rejectToReturn" type="warning" @click="showDialog('backToLast')">回退到上一级审批人</el-button>
        <el-button v-if="btnAuth.reject" type="danger" @click="showDialog('stopTask')">结束任务</el-button>
        <el-button v-if="btnAuth.agreed" type="success" @click="showDialog('pass')">审核通过</el-button>
        <el-button v-if="urlParam.modify == 1" type="primary" @click="showDialog('reCommit')">重新提交</el-button>
      </el-col>
    </el-row>
    <!-- 审核弹框 -->
    <el-dialog
      title="审核"
      width="500px"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisible"
      :close-on-press-escape="false"
      :show-close="false"
      @close="dialogClose"
    >
      <el-form :model="reviewForm" :rules="rules" ref="reviewForm">
        <el-form-item label="审核意见" prop="value">
          <el-input type="textarea" placeholder="审核意见" v-model="reviewForm.value" autocomplete="off" maxlength="100"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="goSubmit()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// 商品基础共用信息混入对象
import { productMixinBase } from "@/mixin/productMixinBase.js"
import {
  getProductData, //获取商品信息
  reCommitSau, //重新提交副商品
  productRejectFinish //审核不通过流程
} from "@/api/product"
import {
  review // 审核商品
} from "@/api/worksubstitution"

import modifyRecord from "@/views/product/modifyRecord"
import { getAuth, getAddProcessData, auditRejectEnd, auditRejectStart, auditRejectPrev, auditPass, rejectModify } from "@/api/workManage"

export default {
  name: "",
  mixins: [productMixinBase],
  components: { modifyRecord },
  computed: {
    // 路由参数
    urlParam() {
      return this.$route.query
    }
  },
  data() {
    return {
      banEdit: false,
      tabIndex: 0,
      spuTableData: [],
      skuTableData: [],
      sauTableData: [],
      recordData: [],

      productLoading: false,
      btnAuth: {},
      operation: "", //弹框确认的操作
      coorection: {},
      scrollTop: 0,
      editState: false,
      dialogFormVisible: false,
      // 实时提报时，驳回原因选项
      rejectOptions: [
        {
          value: 3,
          label: "商品信息不完整，请实施修改后提报"
        },
        {
          value: 2,
          label: "商品无需匹配标准库"
        },
        {
          value: 4,
          label: "提报的商品已有标准库信息"
        }
      ],
      reviewForm: {
        value: ""
      },
      rules: {
        value: [
          {
            required: true,
            message: "审核意见不能为空",
            trigger: "blur"
          }
        ]
      },
      productModifyField: {}
    }
  },
  created() {
    console.log(this.urlParam)
    if (this.urlParam.modify == 1) {
      this.productLoading = true
    }
    this.getBtnAuth()
    this.init()
    window.onscroll = () => {
      this.scrollTop = document.documentElement.scrollTop || document.body.scrollTop
    }
  },
  methods: {
    // 获取按钮权限
    async getBtnAuth() {
      console.log(this.urlParam)
      let param = {
        procDefId: this.urlParam.procDefId,
        nodeKey: this.urlParam.nodeKey
      }
      try {
        const { data } = await getAuth(param)
        console.log(data)
        this.btnAuth = data
      } catch (error) {
        console.log(error)
      }
    },
    showDialog(e) {
      if (e === "pass" || e === "reCommit") {
        this.rules.value[0].required = false
      } else {
        this.rules.value[0].required = true
      }
      this.dialogFormVisible = true
      this.operation = e
    },
    close() {
      let tab = "second"
      parent.CreateTab(`../static/dist/index.html#/workManage/receiveDeal?tab=${tab}`, "领取与处理", true)
      this.cancel()
    },
    goSubmit() {
      this.$refs["reviewForm"].validate(valid => {
        if (valid) {
          if (this.operation === "pass") {
            this.pass()
          } else if (this.operation === "backToFirst") {
            this.backToFirst()
          } else if (this.operation === "backToLast") {
            this.backToLast()
          } else if (this.operation === "stopTask") {
            this.stopTask()
          } else if (this.operation === "reCommit") {
            console.log("reCommit")
            this.submit()
          }
        }
      })
    },
    // 审核通过
    async pass() {
      let param = {
        skuCode: this.urlParam.applyCode,
        applyCode: this.urlParam.applyCode,
        comment: this.reviewForm.value,
        procInstId: this.urlParam.procInstId,
        procKey: this.urlParam.procKey,
        taskId: this.urlParam.taskId,
        updateFieldMap: {
          offlineBusinessType: this.sauTableData[0].offlineBusinessType,
          attrValue: this.sauTableData[0].attrValue
        }
      }
      if (!this.sauTableData[0].attrValue) {
        this.$message.error("销售渠道不能为空")
        return
      }
      const res = await auditPass(param)
      console.log(res)
      if (res.retCode === 0) {
        this.close()
      } else {
        this.$message.error(res.retMsg)
      }
    },
    // 回退到提交人
    async backToFirst() {
      let param = {
        applyCode: this.urlParam.applyCode,
        comment: this.reviewForm.value,
        procInstId: this.urlParam.procInstId,
        procKey: this.urlParam.procKey,
        taskId: this.urlParam.taskId
      }
      console.log(param)
      const res = await auditRejectStart(param)
      console.log(res)
      if (res.retCode === 0) {
        this.close()
      }
    },
    // 回退到上一级审批人
    async backToLast() {
      let param = {
        applyCode: this.urlParam.applyCode,
        comment: this.reviewForm.value,
        procInstId: this.urlParam.procInstId,
        procKey: this.urlParam.procKey,
        taskId: this.urlParam.taskId
      }
      console.log(param)
      const res = await auditRejectPrev(param)
      console.log(res)
      if (res.retCode === 0) {
        this.close()
      }
    },
    // 结束任务
    async stopTask() {
      let param = {
        applyCode: this.urlParam.applyCode,
        comment: this.reviewForm.value,
        procInstId: this.urlParam.procInstId,
        procKey: this.urlParam.procKey,
        taskId: this.urlParam.taskId
      }
      console.log(param)
      const res = await auditRejectEnd(param)
      console.log(res)
      if (res.retCode === 0) {
        this.close()
      }
    },
    cancel() {
      parent.CloseTab("../static/dist/index.html#/workManage/addDeputyDeal")
    },
    async init() {
      this.banEdit = this.urlParam.pageType === "edit" ? false : true
      // 获取审批流信息
      await this.getApplyInfo()
      this.getSauDetail()
    },
    async getSauDetail() {
      try {
        const res = await getProductData(this.urlParam)
        console.log(res)
        if (res.retCode === 0) {
          this.spuTableData = [res.data.spu]
          this.skuTableData = res.data.sku
          this.sauTableData = res.data.sau
          this.recordData = res.data.record
          this.productLoading = false
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 审核弹层保存按钮
    save() {
      this.$refs.reviewForm.validate(async valid => {
        // 审核通过或审核驳回(预首营审核6：审核不通过)
        if (this.reviewForm.state == "审核通过" || this.reviewForm.state == "审核驳回" || this.reviewForm.state == "审核不通过") {
          if (valid || this.reviewForm.state == "审核通过") {
            this.productLoading = true
            let reviewOpinion = ""
            let reviewOpinionType = 0
            // 如果是实施提报，匹配对应的选项值内容至reviewOpinion； isImplement：实施提报
            if (this.reviewForm.state !== "审核通过" && this.isImplement) {
              for (let item of this.rejectOptions) {
                if (item.value == this.reviewForm.rejectState) {
                  reviewOpinion = item.label
                }
              }
              reviewOpinionType = this.reviewForm.rejectState
            } else {
              reviewOpinion = this.reviewForm.value
            }

            let res = await review({
              id: this.reviewInfo.id,
              rejectStatus: this.reviewForm.state == "审核通过" ? 1 : 0,
              reviewStatus: this.reviewInfo.reviewStatus,
              applyCode: this.reviewInfo.applyCode,
              approvalProcess: this.reviewInfo.approvalProcess,
              reviewOpinion,
              reviewOpinionType,
              applyUserScope: this.reviewInfo.applyUserScope
            })
            this.productLoading = false
            if (res.retCode != 0) {
              this.$message.error(res.retMsg)
            } else {
              this.close()
            }
          }
        }
        // 结束流程
        if (valid && this.reviewForm.state == "结束流程") {
          this.productLoading = true
          let res = await productRejectFinish({
            id: this.reviewInfo.id,
            reviewStatus: this.reviewInfo.reviewStatus,
            applyCode: this.reviewInfo.applyCode,
            reviewOpinion: this.reviewForm.value,
            approvalProcess: this.reviewInfo.approvalProcess
          })
          this.productLoading = false

          if (res.retCode != 0) {
            this.$message.error(res.retMsg)
          } else {
            this.close()
          }
        }
      })
    },
    dialogClose() {
      this.$refs.reviewForm.resetFields()
    },
    // 重新提交副商品
    async submit() {
      let param = {
        applyCode: this.urlParam.applyCode,
        procInstId: this.urlParam.procInstId,
        procKey: this.urlParam.procKey,
        taskId: this.urlParam.taskId,
        comment: this.reviewForm.value,
        sau: this.sauTableData[0]
      }
      this.productLoading = true
      try {
        const res = await reCommitSau(param)
        this.productLoading = false
        if (res.retCode === 0) {
          this.close()
        } else {
          this.$message.error(res.retMsg)
        }
      } catch (error) {
        this.productLoading = false
        console.log(error)
      }
    },
    /**
     * @description:审核修改
     * @param {object} submitData :提交数据
     * @return:
     */
    async submitForAudit(submitData) {
      delete submitData.operType
      console.log(submitData)
      const res = await rejectModify(submitData)
      this.productLoading = false
      console.log(res)
      if (res.retCode === 0) {
        this.close()
      }
    },
    async getApplyInfo() {
      let param = {
        procDefId: this.urlParam.procDefId,
        procInstId: this.urlParam.procInstId
      }
      let res = await getAddProcessData(param)
      console.log(res)
      if (res.success) {
        this.approvalData = res.data
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.component-container {
  padding-top: 0px;
  // padding-bottom: 70px;
  .loading {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
  }
  /deep/.el-tabs__header {
    border-bottom: none;
    background-color: #f2f2f2;
  }
  //   /deep/ .el-tabs__content {
  //     padding-top: 72px;
  //   }
  .el-tabs--border-card {
    border: none;
    box-shadow: none;
  }
  .btns {
    background: #fff;
    width: 100%;
    position: fixed;
    top: 40px;
    padding: 15px;
    z-index: 20;
  }
}
.apply {
  padding-bottom: 15px;
  .title {
    line-height: 50px;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 22px;
    padding: 0 20px;
    border-bottom: 1px solid #e4e4eb;
  }
}
.empty {
  background: rgb(240, 242, 245);
  height: 20px;
}
.basic-info-title {
  font-weight: 600;
  margin-bottom: 22px;
  border-bottom: 1px solid #e4e4eb;
  line-height: 50px;
  padding-left: 20px;
}
.tip {
  margin-left: 20px;
  font-size: 12px;
  color: rgb(197, 197, 197);
}
</style>
