<template>
  <div class="container">
    <el-tabs v-model="tabName" type="card">
      <el-tab-pane label="领取任务" name="first">
        <receive @changeTab="changeTab" :approvalProcessList="approvalProcessList"></receive>
      </el-tab-pane>
      <el-tab-pane label="我的任务" name="second">
        <list ref="prepareList" :approvalProcessList="approvalProcessList"></list>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import { getUserApprovalProcessList } from "@/api/workbench.js";

import receive from "./components/receive";
import list from "./components/list";

export default {
  name: "prepare",
  components: {
    receive,
    list,
  },
  data() {
    return {
      tabName: "first",
      approvalProcessList:[]
    };
  },
  created() {
    getUserApprovalProcessList().then((res) => {
      if (!res.retCode) {
        this.approvalProcessList = res.data;
        this.approvalProcessList.unshift({key:'', value:'全部'})
      } else {
        this.$message({
          showClose: true,
          type: "error",
          message: res.retMsg,
        });
      }
    });
  },
  methods: {
    changeTab(name) {
      this.tabName = name;
      if (name == "second") {
        this.$refs.prepareList.searchForm();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding: 15px 15px 0;
}
</style>
