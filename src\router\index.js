import Vue from "vue";
import Router from "vue-router";
Vue.use(Router);
/* Layout */
import Layout from "@/layout";
import Iframe from "@/iframe";
/**
 * 路由模块
 */
import product from "./modules/product";
import dictManagementRouters from "./modules/dictManagementRouters";
import systemManagementRouters from "./modules/systemManagementRouters";
import productPictureRouters from "./modules/productPictureRouters";
import listRouters from "./modules/list";
import dictRouters from "./modules/dict";
import operation from "./modules/operation";
import workbench from "./modules/workbench";
import follow from "./modules/follow";
import board from "./modules/board";
import workManage from "./modules/workManage";
import searchFriend from "./modules/searchFriend";
/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  /**
   * 商品列表
   */
  product,

  /**
   * 字典管理
   */
  dictManagementRouters,

  /**
   * 系统管理
   */
  systemManagementRouters,

  /**
   * 图片管理
   */
  productPictureRouters,
  /**
   * 列表页
   */
  listRouters,
  dictRouters,
  // 商品运营
  operation,
  // 工作台
  workbench,
  // 待跟进商品
  follow,
  board,
  workManage,
  searchFriend
];
const createRouter = () =>
  new Router({
    // mode: 'history', // require service support
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes
  });

const router = createRouter();

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher; // reset router
}

export default router;
