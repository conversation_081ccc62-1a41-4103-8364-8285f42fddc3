<template>
  <div class="container">
    <el-tabs v-model="tabName" type="card">
      <el-tab-pane label="图片补全转化数据" name="first">
        <pictureCompletionReport @changeTab="changeTab" ref="prepareList"></pictureCompletionReport>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import pictureCompletionReport from "./components/pictureCompletionReport";

export default {
  name: "prepare",
  components: {
    pictureCompletionReport,
  },
  data() {
    return {
      tabName: "first",
    };
  },
  methods: {
    changeTab(name) {
      this.tabName = name;
      this.$refs.prepareList.searchForm();
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding: 15px 15px 0;
}
</style>
