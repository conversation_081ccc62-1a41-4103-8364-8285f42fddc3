
<template>
  <div class="task-to-be-film-container" v-loading="loading">
    <!-- 查询表单 -->
    <div class="search-form-wrap">
      <el-form :model="model" ref="refSearchForm">
        <el-row type="flex">
          <!-- 来源 -->
          <el-col :lg="8" :xs="24"  :sm="12"  :xl="6">
            <el-form-item label="商品大类">
              <el-select v-model="formData.spuCategory" placeholder="请选择">
                <el-option v-for="item in spuCategoryOptions" 
                  :key="'key_spuCategoryOptions_'+item.id" 
                  :label="item.dictName"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 商品编码 -->
          <el-col :lg="8" :xs="24"  :sm="12"  :xl="6">
            <el-form-item label="商品编码">
              <el-input v-model="formData.spuCode" placeholder="商品编码/spu编码/sku/原商品编码/商品id"></el-input>
            </el-form-item>
          </el-col>

          <el-col :lg="8" :xs="24"  :sm="12"  :xl="6">
            <el-form-item label="通用名">
              <el-input v-model="formData.generalName" placeholder="模糊查询"></el-input>
            </el-form-item>
          </el-col>

          <!-- 规格型号 -->
          <el-col :lg="8" :xs="24"  :sm="12"  :xl="6">
            <el-form-item label="规格型号">
              <el-input v-model="formData.spec" placeholder="模糊查询"></el-input>
            </el-form-item>
          </el-col>

          <!-- 生产厂家-->
          <el-col :lg="8" :xs="24"  :sm="12"  :xl="6">
            <el-form-item label="生产厂家">
              <el-input v-model="formData.manufacturer" placeholder="模糊查询"></el-input>
            </el-form-item>
          </el-col>

          <!-- 小包装条码 -->
          <el-col :lg="8" :xs="24"  :sm="12"  :xl="6">
            <el-form-item label="小包装条码">
              <el-input v-model="formData.smallPackageCode" placeholder="请输入小包装条码"></el-input>
            </el-form-item>
          </el-col>

          <!-- 批准文号 -->
          <el-col :lg="8" :xs="24"  :sm="12"  :xl="6">
            <el-form-item label="批准文号">
              <el-input v-model="formData.approvalNo" placeholder="请输入批准文号"></el-input>
            </el-form-item>
          </el-col>

          <el-col :lg="8" :xs="24"  :sm="12"  :xl="6">
            <el-form-item label="处方分类">
              <el-select v-model="formData.prescriptionCategory" placeholder="请选择">
                <el-option label="全部" value=""></el-option>
                <el-option
                  v-for="item in $store.getters.selectOptions.prescriptionCategoryOptions"
                  :key="'key_packageUnitOptions_'+item.id"
                  :label="item.dictName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 按钮 -->
          <el-col :span="6" :offset="18">
            <el-button type="primary" size="medium" @click="btnSearchClick">查询</el-button>
            <el-button size="medium" @click="btnResetClick">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- table表单 -->
    <!-- table -->
    <div class="table-wrap">
      <!-- createStartTime:"", // 开始时间
          createEndTime:"",//结束时间
          sourceDetail:"",//上报来源
          correctionType:"",//纠错类型
          createUser:"",//上报人
          productCode:"",//商品编码
          productId:"",//商品ID
          productName:"",//商品名称
          manufacturerName:"",//生产厂家名称
          reviewStatus:""//处理状态 -->
      <vxe-table border highlight-hover-row resizable height="100%" auto-resize size="small" align="center"
        :tooltip-config="{enterable: false}" 
        :loading="tableLoading" 
        :data="tableData" 
        @cell-dblclick="cellDBLClickEvent" 
        :seq-config="{startIndex: (pageNum-1) * pageSize}"
        ref="refVxeTable">
        <!-- <vxe-table-column field="applyDate" title="申请时间" width="120" show-header-overflow show-overflow>
          <template v-slot="{ row }">
            {{ parseTime(row.applyDate) }}
          </template>
        </vxe-table-column> -->
        <vxe-table-column field="spuCategory" title="商品大类" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="spuCode" title="spu编码" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="generalName" title="通用名" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <!-- <vxe-table-column field="productCode" title="商品编码" min-width="120" show-header-overflow show-overflow></vxe-table-column> -->
        <vxe-table-column field="productId" title="商品ID" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="skuName" title="商品名称" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="spec" title="规格/型号" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="manufacturer" title="生产厂家" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="approvalNo" title="批准文号" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="smallPackageCode" title="小包装条码" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="packageUnit" title="包装单位" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="prescriptionCategory" title="处方分类" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column title="操作" width="120" show-header-overflow show-overflow fixed="right">
          <template v-slot="{ row }">
            <span>
              <el-link :underline="false" type="primary" @click.stop="receive(row)">选择</el-link>
            </span>
          </template>
        </vxe-table-column>
      </vxe-table>
    </div>
    <!-- 分页 -->
    <el-row class="custom-pagination-wrap">
      <el-col>
        <el-pagination background :current-page.sync="pageNum" :page-sizes="[20, 50, 100, 200]" :page-size.sync="pageSize" :total="total"
          layout="prev, pager, next, jumper,total, sizes" @size-change="handleSizeChange" @current-change="handleCurrentChange">
        </el-pagination>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import { parseTimestamp } from '@/utils/index.js'
  import {
    productShootRecordList,
    skuList,
  } from '@/api/list.js'
  import {
    taskDetailChangeProduct, // 1.5.1 审核精修图
  } from "@/api/productPicture.js";
  export default {
    name: "",
    components: {},
    filters: {},
    props: {
      currentSkuData:{
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {
        uploadUrl:process.env.VUE_APP_BASE_API+"/api/picture/imageUploadShootRecord",
        dialogVisible:false,
        loading:false,
        pageNum: 1,
        pageSize: 20,
        total:0, //总条数
        formData: {
          approvalNo:"", // 批准文号
          generalName:"",// 通用名
          manufacturer:"",// 生产厂家
          prescriptionCategory:"",// 处方分类
          smallPackageCode:"",//小包装条码
          spec:"",// 规格型号
          spuCategory:"",//商品大类
          spuCode:"",// 商品编码
        },
        tableLoading:false,
        tableData: []
      };
    },
    computed: {
      spuCategoryOptions(){
        return this.$store.getters.selectOptions.spuCategoryOptions
      },
      
    },
    watch: {
    },
    created() {
      this._formDefault=_.cloneDeep(this.formData)
      this.searchForm();
    },
    mounted() {},
    methods: {
      /**
       * 查询table数据
       */
      async searchForm() {
        try {
          this.tableLoading=true;
          let param=Object.assign({},this.formData);
          param.page=this.pageNum;
          param.limit=this.pageSize;
          let res=await skuList(param)
          this.tableLoading=false;
          console.log(res)
          if(res.retCode==0){
            this.tableData=res.data.list;
            this.total=res.data.total;
          }else{
            this.$message({
                showClose: true,
                type: 'error',
                message: res.retMsg
              });
          }
        } catch (error) {
          console.error(error)
        }
      },

      /**
       * 查询按钮点击
       */
      btnSearchClick(){
        this.pageNum = 1;
        this.searchForm();
      },

      /**
       * 重置
       */
      btnResetClick() {
        // this.$refs["refSearchForm"].resetFields();
        // this.formData.createTime = "";
        this.formData=this._formDefault;
        this.pageNum = 1;
        this.searchForm();
      },

      /**
       * pageSize 改变事件
       */
      handleSizeChange(pageSize) {
        this.pageNum = 1;
        this.pageSize = pageSize;
        this.searchForm();
      },

      /**
       * pageNum 改变事件
       */
      handleCurrentChange(currentPage) {
        this.pageNum=currentPage;
        this.searchForm();
      },
      /**
       * @description: 另选商品处理
       * @param { object } row 当前行的数据
       * @return {type} 
       */
      async receive(row){ 
        try {
          // * 单据编号applyCode
          // * 商品编码productCode
          // * 要转换的商品编码newProductCode
          let res =await taskDetailChangeProduct({
            applyCode:this.currentSkuData.applyCode,
            productCode:this.currentSkuData.productCode,
            newProductCode:row.skuCode
          })
          if(res.success){
            this.$emit("currentSkuData",row)
          }else{
            this.$message.error(res.retMsg)
          }
        } catch (error) {
          console.error(error)
        }
      },
      /**
       * @description: 格式化时间参数
       * @param {date} time 时间戳
       * @return: string 格式化后的时间戳
       */
      parseTime(time){
        return parseTimestamp(time)
      },
      submit(){
        this.$refs.disposeForm.validate((valid) => {
          if (valid) {
            this.uploadSubmit()
          } else {
            console.log('error submit!!');
          }
        });
      },
    }
  };
</script>
<style lang="scss" scoped>
  .task-to-be-film-container {

    /**
   * 查询表单
   */
    .search-form-wrap {
      width: 100%;
      padding: 15px;
      border-bottom: 1px dashed #e4e4eb;

      .el-row {
        flex-wrap: wrap;

        .el-col {
          display: flex;
          justify-content: flex-end;

          .el-form-item {
            flex: 1;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            width: 100%;
            margin-right: 0;
            margin-bottom: 15px;

            /deep/ {
              .el-form-item__label {
                width: 96px;
                line-height: normal;
                padding: 0 12px;
                color: #292933;
                font-weight: normal;
              }

              .el-form-item__content {
                flex: 1;

                .el-range-editor.el-input__inner {
                  width: 100%;

                  .el-range-separator {
                    width:14px;
                    padding: 0;
                  }
                }
              }
            }

            .el-input {
              width: 100%;
            }

            .el-select {
              width: 100%;
            }
          }
        }
      }
    }

    /**
     * table
     */
    .table-wrap {
      width: 100%;
      min-height: 440px;
      height: calc(100vh - 295px);
      padding: 0 15px;
      margin-top: 15px;
    }

  }
</style>
