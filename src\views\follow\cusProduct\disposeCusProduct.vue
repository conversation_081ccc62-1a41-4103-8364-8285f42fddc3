<template>
  <div class="component-container">
    <div class="table-contnt">
      <div class="table-title">客户商品详情</div>
      <div style="width: 900px">
        <vxe-table
          border
          highlight-hover-row
          auto-resize
          resizable
          align="center"
          :data="detailList"
          ref="table"
        >
          <vxe-table-column
            field="smallPackageCode"
            title="小包装条码"
            width="180"
          ></vxe-table-column>
          <vxe-table-column
            field="approvalNo"
            title="批准文号"
            width="120"
          ></vxe-table-column>
          <vxe-table-column
            field="generalName"
            title="通用名"
            width="120"
          ></vxe-table-column>
          <vxe-table-column
            field="spec"
            title="规格型号"
            width="120"
          ></vxe-table-column>
          <vxe-table-column
            field="manufacturerName"
            title="生产厂家"
            width="120"
          ></vxe-table-column>
          <vxe-table-column
            field="customerCode"
            title="药店编码"
            width="120"
          ></vxe-table-column>
          <vxe-table-column
            field="customerProductCode"
            title="药店商品编码"
            width="120"
          ></vxe-table-column>
        </vxe-table>
      </div>

      <div class="table-title bTitle">
        商品中台建议匹配商品<el-button
          v-if="isEdit"
          @click="handleSearch"
          type="primary"
          class="search-btn"
          size="mini"
          icon="el-icon-search"
          style="margin-left: 15px"
          >搜索匹配商品</el-button
        >
      </div>
      <vxe-table
        border
        highlight-hover-row
        auto-resize
        resizable
        align="center"
        row-id="productId"
        :data="matchDetailList"
        max-height="300px"
        ref="table"
        :radio-config="{
          checkMethod: checkRadioMethod,
          checkRowKey: defaultSelecteRow,
        }"
        @radio-change="radioChangeEvent"
      >
        <vxe-table-column type="radio" width="60">
          <template v-slot:header>
            <vxe-button type="text"></vxe-button>
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="smallPackageCode"
          title="小包装条码"
          min-width="120"
        ></vxe-table-column>
        <vxe-table-column
          field="approvalNo"
          title="批准文号"
          min-width="120"
        ></vxe-table-column>
        <vxe-table-column
          field="generalName"
          title="通用名"
          min-width="120"
        ></vxe-table-column>
        <vxe-table-column
          field="spec"
          title="规格型号"
          min-width="120"
        ></vxe-table-column>
        <vxe-table-column
          field="manufacturerName"
          title="生产厂家"
          min-width="120"
        ></vxe-table-column>
        <vxe-table-column
          field="brand"
          title="品牌商标"
          min-width="120"
        ></vxe-table-column>
        <vxe-table-column
          field="delegationProductName"
          title="是否委托生产"
          min-width="120"
        ></vxe-table-column>
        <vxe-table-column
          field="delegationProductVender"
          title="委托生产厂家"
          min-width="120"
        ></vxe-table-column>
        <vxe-table-column
          field="skuPrimaryTypeValue"
          title="主副商品"
          min-width="120"
        ></vxe-table-column>
        <vxe-table-column
          field="packageUnitValue"
          title="包装单位"
          min-width="120"
        ></vxe-table-column>
        <vxe-table-column
          field="sixCategoryUnionName"
          title="六级分类"
          min-width="120"
        ></vxe-table-column>
        <vxe-table-column
          field="prescriptionCategoryValue"
          title="处方分类"
          min-width="120"
        ></vxe-table-column>
        <vxe-table-column
          field="preOperateStatusValue"
          title="预首营状态"
          min-width="120"
        ></vxe-table-column>
        <vxe-table-column
          field="spuCode"
          title="SPU编码"
          min-width="120"
        ></vxe-table-column>
        <vxe-table-column
          field="skuCode"
          title="SKU编码"
          min-width="120"
        ></vxe-table-column>
        <vxe-table-column
          field="productId"
          title="商品ID"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="spuCategoryName"
          title="商品大类"
          min-width="120"
        ></vxe-table-column>
        <vxe-table-column
          field="businessScope"
          title="经营范围"
          min-width="120"
        ></vxe-table-column>
        <vxe-table-column
          field="taxCategoryCode"
          title="税务分类编码"
          min-width="120"
        ></vxe-table-column>
        <vxe-table-column
          field="manufacturerCategoryName"
          title="厂家分类"
          min-width="120"
        ></vxe-table-column>
        <vxe-table-column
          field="originPlace"
          title="产地"
          min-width="120"
        ></vxe-table-column>
        <!-- <vxe-table-column
          field="manufacturerName"
          title="管理属性"
          min-width="120"
        ></vxe-table-column> -->
      </vxe-table>
    </div>

    <div class="operate-wrap">
      <div class="title">{{ isEdit ? "选择操作" : "操作" }}</div>
      <div class="content">
        <div
          class="list"
          :class="operateState == 1 ? 'active' : ''"
          @click="changeOperateState(1)"
          v-if="isEdit || operateState == 1"
        >
          <span class="label">匹配该商品</span>
          <span class="codeInfo" v-if="operateState == 1"
            >标准库ID：{{ selecteInfo.productId }}</span
          >
        </div>
        <div
          class="list"
          :class="operateState == 2 ? 'active' : ''"
          @click="changeOperateState(2)"
          v-if="isEdit || operateState == 2"
        >
          <span class="label">标记为</span>
          <div class="form-wrap">
            <el-form :model="ruleForm" ref="ruleForm" :disabled="!isEdit">
              <el-form-item
                label=""
                prop="type"
                :rules="{
                  required: true,
                  message: '请选择标记类型',
                  trigger: 'change',
                }"
              >
                <el-checkbox-group
                  v-model="ruleForm.type"
                  @change="changeReasonType"
                >
                  <el-checkbox label="2">厂家不全</el-checkbox>
                  <el-checkbox label="3">规格违法</el-checkbox>
                  <el-checkbox label="4">品名不清</el-checkbox>
                  <el-checkbox label="5">自定义内容</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item label="" prop="reason" :rules="reasonRule">
                <el-input class="reason" v-model="ruleForm.reason" />
              </el-form-item>
            </el-form>
          </div>
        </div>
        <div
          class="list"
          :class="operateState == 3 ? 'active' : ''"
          @click="changeOperateState(3)"
          v-if="isEdit || operateState == 3"
        >
          <span class="label">新品上报</span>
        </div>
        <div
          class="list"
          :class="operateState == 4 ? 'active' : ''"
          @click="changeOperateState(4)"
          v-if="isEdit || operateState == 4"
        >
          <span class="label">添加条码</span>
          <span class="codeInfo" v-if="operateState == 4"
            >标准库ID：{{ selecteInfo.productId }}</span
          >
        </div>
      </div>
    </div>
    <div class="bottomBtn" v-if="isEdit">
      <el-button type="primary" @click="handleCancal">取消</el-button>
      <el-button type="primary" @click="submit">提交</el-button>
    </div>
    <div class="bottomBtn" v-else>
      <el-button type="primary" @click="handleCancal">返回</el-button>
    </div>
    <product-drawer-table
      ref="productDrawerTable"
      @saveProduct="saveProduct"
    ></product-drawer-table>
  </div>
</template>

<script>

import {
  getCustomerProductDetail,
  submitDealCustomerProduct,
} from "@/api/follow";
import productDrawerTable from "./components/productDrawerTable";

export default {
  name: "",
  components: {productDrawerTable},
  data() {
    return {
      detailList: [],
      matchDetailList: [],
      selecteInfo: {},
      dialogVisible: false,
      ruleForm: { type: [], reason: "" },
      reasonRule: {
        required: false,
        message: "请填写自定义内容",
        trigger: "blur",
      },
      operateState: 0,
      isEdit: this.$route.query.isEdit,
      defaultSelecteRow: "",
    };
  },
  computed: {
    urlParam: function () {
      return this.$route.query;
    },
  },
  created() {
    this.getCustomerProductDetail();
  },
  methods: {
    // 获取页面详情信息
    getCustomerProductDetail() {
      getCustomerProductDetail({ applyCode: this.urlParam.applyCode }).then(
        (res) => {
          if (!res.retCode) {
            this.detailList.push({ ...res.data });
            this.matchDetailList = res.data.matchDetailList;
            this.operateState = res.data.submitType;
            this.revealDetail(res.data);
          } else {
            this.$message({
              showClose: true,
              type: "error",
              message: res.retMsg,
            });
          }
        }
      );
    },
    // 数据回显
    revealDetail(info) {
      if (info.submitType == 1 || info.submitType == 4) {
        this.selecteInfo.productId = this.defaultSelecteRow = info.productId;
      }
      if (info.submitType == 2) {
        this.ruleForm.type = info.markType.split(",");
        this.ruleForm.reason = info.markText;
      }
    },
    checkRadioMethod({ row }) {
      if (!this.isEdit) {
        return row.productId == this.selecteInfo.productId;
      } else {
        return true;
      }
    },
    // 建议匹配商品选择
    radioChangeEvent({ row }) {
      this.selecteInfo = row;
    },
    // 选择操作
    changeOperateState(state) {
      this.operateState = state;
      this.$refs.ruleForm.clearValidate();
    },
    // 切换标记自定义内容框验证状态
    changeReasonType(val) {
      if (val.indexOf("5") != -1) {
        this.reasonRule.required = true;
      } else {
        this.reasonRule.required = false;
      }
    },
    handleCancal() {
      try {
        parent.CreateTab(
          "../static/dist/index.html#/follow/cusProductImportList",
          "客户商品审核",
          true
        );
        parent.CloseTab("../static/dist/index.html#/follow/disposeCusProduct");
      } catch {
        this.$router.push({
          path: "/follow/cusProductImportList",
        });
      }
    },
    // 提交
    submit() {
      switch (this.operateState) {
        case 1:
          this.handleMatching();
          break;
        case 2:
          this.handleMark();
          break;
        case 3:
          this.handleReported();
          break;
        case 4:
          this.handleAddCode();
          break;
        default:
          this.$message.warning("请选择操作类型");
          return;
      }
    },
    // 商品匹配
    handleMatching() {
      if (!this.selecteInfo.productId) {
        this.$message.warning("请选择中台建议匹配的商品");
        return;
      }
      this.submitDealCustomerProduct({
        submitType: 1,
        productId: this.selecteInfo.productId,
      });
    },
    // 商品标记
    handleMark() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.submitDealCustomerProduct({
            submitType: 2,
            markType: this.ruleForm.type.join(","),
            markText: this.ruleForm.reason,
          });
        }
      });
    },
    // 新品上报
    handleReported(row) {
      this.submitDealCustomerProduct({
        submitType: 3,
      });
    },
    // 添加条码
    handleAddCode(row) {
      if (!this.selecteInfo.productId) {
        this.$message.warning("请选择中台建议匹配的商品");
        return;
      }
      this.submitDealCustomerProduct({
        submitType: 4,
        productId: this.selecteInfo.productId,
      });
    },
    submitDealCustomerProduct(param) {
      submitDealCustomerProduct({
        applyCode: this.detailList[0].applyCode,
        ...param,
      }).then((res) => {
        if (!res.retCode) {
          try {
            parent.CreateTab(
              "../static/dist/index.html#/follow/cusProductImportList",
              "客户商品审核",
              true
            );
            parent.CloseTab(
              "../static/dist/index.html#/follow/disposeCusProduct"
            );
          } catch {
            this.$router.push({
              path: "/follow/cusProductImportList",
            });
          }
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      });
    },
    handleSearch() {
      this.$refs.productDrawerTable.show();
    },
    saveProduct(info){
      this.matchDetailList.unshift(...info);
    }
  },
};
</script>

<style lang="scss" scoped>
.component-container {
  .table-contnt {
    padding: 0 20px;
    .table-title {
      padding: 10px 0 20px;
      font-size: 16px;
      font-weight: bold;
      &.bTitle {
        padding-top: 30px;
      }
    }
    .table-btn {
      padding: 0 6px;
    }
  }
  .operate-wrap {
    padding: 30px 20px 80px;
    .title {
      font-weight: bold;
    }
    .content {
      width: 80%;
      margin: 0 auto;
      .list {
        border: 1px solid #bababa;
        padding: 10px 20px;
        margin-bottom: 10px;
        height: 60px;
        cursor: pointer;
        display: flex;
        align-items: center;
        &:nth-of-type(2) {
          padding-bottom: 20px;
        }
        .label {
          padding-right: 10px;
          font-weight: bold;
        }
        .codeInfo {
          background: #fff;
        }
        &.active {
          background: #dae9ec;
        }
      }
    }
  }
}
.form-wrap /deep/ {
  .el-form {
    display: flex;
    .el-form-item {
      display: flex;
      height: 28px;
      margin-bottom: 0px;
      .el-checkbox-group {
        height: 28px;
        display: flex;
        .el-checkbox {
          height: 100%;
          display: flex;
          align-items: center;
        }
      }
      .reason {
        margin-left: 5px;
        height: 28px;
        display: flex;
        input {
          height: 28px;
          border-top: none;
          border-right: none;
          border-left: none;
        }
      }
    }
  }
}
.bottomBtn {
  background: #f0f2f5;
  width: 100%;
  position: fixed;
  bottom: 0;
  padding: 15px;
  z-index: 10;
  display: flex;
  justify-content: center;
}
</style>