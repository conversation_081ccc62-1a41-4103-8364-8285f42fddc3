<template>
  <div class="putawayList-container" v-loading="loading">
    <!-- 查询表单 -->
    <div class="search-form-wrap">
      <el-form :model="model" ref="refSearchForm">
        <el-row type="flex">
          <!-- 申请来源 -->
          <el-col :lg="8" :xs="24"  :sm="12"  :xl="6">
            <el-form-item label="申请来源">
              <el-select v-model="formData.source" placeholder="请选择">
                <el-option label="荷叶健康" :value="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 申请时间 -->
          <el-col :lg="8" :xs="24"  :sm="12"  :xl="6">
            <el-form-item label="申请时间" prop="createTime">
              <el-date-picker v-model="formData.createTime" value-format="yyyy-MM-dd" type="daterange" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
            </el-form-item>
          </el-col>

          <!-- 审核时间 -->
          <el-col :lg="8" :xs="24"  :sm="12"  :xl="6">
            <el-form-item label="审核时间" prop="createTime">
              <el-date-picker v-model="formData.updateTime" value-format="yyyy-MM-dd" type="daterange" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
            </el-form-item>
          </el-col>

          <!-- 商品编码 -->
          <el-col :lg="8" :xs="24"  :sm="12"  :xl="6">
            <el-form-item label="商品编码">
              <el-input v-model="formData.productCode" placeholder="模糊查询"></el-input>
            </el-form-item>
          </el-col>

          <!-- 审核状态-->
          <el-col :lg="8" :xs="24"  :sm="12"  :xl="6">
            <el-form-item label="审核状态" prop="mechanism">
              <el-select v-model="formData.reviewStatus" placeholder="请选择">
                <el-option label="全部" value=""></el-option>
                <el-option label="审核不通过" :value="2"></el-option>
                <el-option label="审核通过" :value="1"></el-option>
                <el-option label="审核中" :value="0"></el-option>
                <el-option label="待处理" :value="-1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 申请人 -->
          <el-col :lg="8" :xs="24"  :sm="12"  :xl="6">
            <el-form-item label="申请人">
              <el-input v-model="formData.createUser" placeholder="模糊查询"></el-input>
            </el-form-item>
          </el-col>
          <!-- 所属经营范围 -->
          <!-- <el-col :lg="8" :xs="24"  :sm="12"  :xl="6">
            <el-form-item label="所属经营范围" prop="businessScope" ref="businessScope">
                <el-cascader
                    ref="elCascader"
                    style="width: 100%;"
                    v-model="formData.businessScope"
                    :options="businessScopeOptions"
                    :show-all-levels="false"
                    :props="{
                      multiple:true,
                      checkStrictly:true,
                      value:'id',
                      label:'dictName',
                      disabled:'disable'
                      }"
                    filterable
                    >
                    <template slot-scope="{ node, data }">
                      <span>{{ data.dictName }}</span>
                      <span v-if="!node.isLeaf"> ({{ data.children.length }}) </span>
                    </template>
                </el-cascader>
              </el-form-item>
          </el-col> -->

          <!-- 批准文号-->
          <el-col :lg="8" :xs="24"  :sm="12"  :xl="6">
            <el-form-item label="批准文号">
              <el-input v-model="formData.approvalNo" placeholder="模糊查询"></el-input>
            </el-form-item>
          </el-col>

          <!-- 商品名称-->
          <el-col :lg="8" :xs="24"  :sm="12"  :xl="6">
            <el-form-item label="商品名称">
              <el-input v-model="formData.productName" placeholder="模糊查询"></el-input>
            </el-form-item>
          </el-col>

          <!-- 生产厂家-->
          <el-col :lg="8" :xs="24"  :sm="12"  :xl="6">
            <el-form-item label="生产厂家">
              <el-input v-model="formData.manufacturerName" placeholder="模糊查询"></el-input>
            </el-form-item>
          </el-col>

          <!-- 商品ID -->
          <el-col :lg="8" :xs="24"  :sm="12"  :xl="6">
            <el-form-item label="商品ID">
              <el-input v-model="formData.productId" placeholder="模糊查询"></el-input>
            </el-form-item>
          </el-col>

          <!-- 按钮 -->
          <el-col :span="6" :offset="18">
            <el-button type="primary" size="medium" @click="btnSearchClick">查询</el-button>
            <el-button size="medium" @click="btnResetClick">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- table表单 -->
    <!-- table -->
    <div class="table-wrap">
      <!-- createStartTime:"", // 开始时间
          createEndTime:"",//结束时间
          sourceDetail:"",//上报来源
          correctionType:"",//纠错类型
          createUser:"",//上报人
          productCode:"",//商品编码
          productId:"",//商品ID
          productName:"",//商品名称
          manufacturerName:"",//生产厂家名称
          reviewStatus:""//处理状态 -->
      <vxe-table border highlight-hover-row resizable height="100%" auto-resize size="small" align="center"
        :tooltip-config="{enterable: false}" 
        :loading="tableLoading" 
        :data="tableData" 
        :seq-config="{startIndex: (pageNum-1) * pageSize}"
        @cell-dblclick="cellDBLClickEvent" 
        ref="refVxeTable">
        <vxe-table-column type="seq" title="序号" width="60" show-header-overflow show-overflow fixed="left"></vxe-table-column>
        <vxe-table-column field="createUser" title="申请人" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="createTime" title="申请时间" width="120" show-header-overflow show-overflow>
          <template v-slot="{ row }">
            {{ parseTime(row.createTime) }}
          </template>
        </vxe-table-column>
        <vxe-table-column field="source" title="申请来源" min-width="120" show-header-overflow show-overflow>
          <template v-slot="{ row }">
            {{ ["","荷叶健康"][row.source]}}
          </template>
        </vxe-table-column>
        <vxe-table-column field="reviewStatus" title="审核状态" min-width="120" show-header-overflow show-overflow>
          <!-- <el-option label="不通过" :value="2"></el-option>
                <el-option label="已审核" :value="1"></el-option>
                <el-option label="处理中" :value="0"></el-option>
                <el-option label="待处理" :value="-1"></el-option> -->
          <template v-slot="{ row }">
            {{ ["待处理","审核中","审核通过","审核不通过"][row.reviewStatus +1]}}
          </template>
        </vxe-table-column>
        <vxe-table-column field="productCode" title="商品编码" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="productId" title="商品ID" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="generalName" title="通用名" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="skuName" title="商品名称" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="spec" title="规格/型号" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="spuCategoryName" title="商品大类" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="businessScopeMultiName" title="所属经营范围" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="approvalNo" title="批准文号" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="manufacturerName" title="生产厂家" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="packageUnitName" title="包装单位" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="prescriptionCategoryName" title="处方分类" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        
        <vxe-table-column field="receiveUser" title="审核人" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="updateTime" title="审核时间" min-width="120" show-header-overflow show-overflow>
          <template v-slot="{ row }">
            {{ (row.reviewStatus ==1 || row.reviewStatus ==2) ? parseTime(row.updateTime) : "" }}
          </template>
        </vxe-table-column>
        <!-- <vxe-table-column field="correctionType" title="纠错类型" min-width="120" show-header-overflow show-overflow>
          <template v-slot="{ row }">
            {{ ["","基础资料错误","图片错误","说明书错误","缺少精修图","缺少说明书","商品重复"][row.correctionType]}}
          </template>
        </vxe-table-column> -->
        <vxe-table-column title="操作" width="120" show-header-overflow show-overflow fixed="right">
          <template v-slot="{ row }">
            <!-- <el-option label="不通过" :value="2"></el-option>
                <el-option label="已审核" :value="1"></el-option>
                <el-option label="处理中" :value="0"></el-option>
                <el-option label="待处理" :value="-1"></el-option> -->
            <el-link :underline="false" v-if="row.receive==0 && row.reviewStatus==-1" type="primary" @click.stop="receive(row)">领取</el-link>
            <el-link :underline="false" v-if="row.receive==1 && row.reviewStatus==-1" type="primary" @click.stop="goDetail(row)">审核</el-link>
            <el-link disabled v-if="row.reviewStatus===0">-</el-link>
            <el-link disabled v-if="row.reviewStatus===1" >-</el-link>
            <el-link disabled v-if="row.reviewStatus===2" >-</el-link>
          </template>
        </vxe-table-column>
      </vxe-table>
    </div>

    <!-- 分页 -->
    <el-row class="custom-pagination-wrap">
      <el-col>
        <el-pagination background :current-page.sync="pageNum" :page-sizes="[20, 50, 100, 200]" :page-size.sync="pageSize" :total="total"
          layout="prev, pager, next, jumper,total, sizes" @size-change="handleSizeChange" @current-change="handleCurrentChange">
        </el-pagination>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import { parseTimestamp } from '@/utils/index.js'
  import {
    shelvesList,
    shelvesReveive,
  } from '@/api/list.js'
  import {
  correctionDetail // 商品纠错信息
} from "@/api/product";
  import { dictListSearch , getTotalDictionaryTree} from "@/api/dict"
  import { getId } from "@/utils/index.js"
  let businessScopeListOptionsParentId=[];
  export default {
    name: "",
    components: {},
    filters: {},
    props: {},
    data() {
      return {
        loading:false,
        pageNum: 1,
        pageSize: 20,
        total:0, //总条数
        formData: {
          source:"",// 申请来源
          createStartTime:"", // 开始时间
          createEndTime:"",//结束时间
          createTime:"",
          updateStartTime:"",
          updateEndTime:"",
          updateTime:"",
          createUser:"",//申请人
          // sourceDetail:"",//申请来源
          // correctionType:"",//纠错类型
          createUser:"",//上报人
          // businessScope:"",//所属经营范围
          productCode:"",//商品编码
          productId:"",//商品ID
          // productName:"",//商品名称
          manufacturerName:"",//生产厂家名称
          reviewStatus:""//审核状态
        },
        tableLoading:false,
        tableData: [],
        businessScopeOptions:[]
      };
    },
    computed: {},
    watch: {

    },
    created() {
      // this.getDist()
      this._formDefault=_.cloneDeep(this.formData)
      this.searchForm();
    },
    mounted() {},
    methods: {
      async getDist(){
        // 所属经营范围
        let businessScopeOptions = await getTotalDictionaryTree({
        type:"11",
        })
        this.businessScopeOptions=businessScopeOptions.data;
        getId(businessScopeOptions.data,businessScopeListOptionsParentId);
      },
      /**
       * 查询table数据
       */
      async searchForm() {
        try {
          this.tableLoading=true;
          let param=Object.assign({},this.formData);
          param.page=this.pageNum;
          param.limit=this.pageSize;
          param.createStartTime=param.createTime[0];
          param.createEndTime=param.createTime[1];
          delete param.createTime;
          param.updateStartTime=param.updateTime[0];
          param.updateEndTime=param.updateTime[1];
          delete param.updateTime;
          // 所属经营范围提交值格式化
          // let arr=[];
          // let flattenArr=[];
          // for (let item of param.businessScope){
          //   if(Array.isArray(item)){
          //     arr.push(item);
          //   }
          // }
          // // 展开数组
          // flattenArr=_.flattenDeep(param.businessScope)
          // // 移除父ID pull: 移除数组array中所有和给定值相等的元素，使用 SameValueZero 进行全等比较。 参数1 要修改的数组， 参数2 要删除的值
          // _.pull(flattenArr,...businessScopeListOptionsParentId);
          // param._businessScope=flattenArr;

          let res=await shelvesList(param)
          this.tableLoading=false;
          if(res.success){
            this.tableData=res.data.list;
            this.total=res.data.total;
          }else{
            this.$message({
                showClose: true,
                type: 'error',
                message: res.retMsg
              });
          }
        } catch (error) {
          console.error(error)
        }
      },

      /**
       * 查询按钮点击
       */
      btnSearchClick(){
        this.pageNum = 1;
        this.searchForm();
      },

      /**
       * 重置
       */
      btnResetClick() {
        // this.$refs["refSearchForm"].resetFields();
        // this.formData.createTime = null;
        this.formData=this._formDefault;
        this.pageNum = 1;
        this.searchForm();
      },

      /**
       * pageSize 改变事件
       */
      handleSizeChange(pageSize) {
        this.pageNum = 1;
        this.pageSize = pageSize;
        this.searchForm();
      },

      /**
       * pageNum 改变事件
       */
      handleCurrentChange(currentPage) {
        this.pageNum=currentPage;
        this.searchForm();
      },
      async receive(row){
        try {
          let res=await shelvesReveive({uniqueCode:row.uniqueCode})
          console.log(res)
          if(res.success){
            this.btnSearchClick();
          }else{
            this.$message({
              showClose: true,
              type: 'error',
              message: res.data.retMsg
            });
          }
        } catch (error) {
          console.error(error)
        }
      },
      async goDetail(row){
        try {
          parent.CreateTab("../static/dist/index.html#/product/editProduct?type=shelves&sendCode="+row.applyCode+
          "&productCode="+row.productCode +
          "&productType="+ 2 +
          "&spuCode="+row.spuCode +
          "&uniqueCode="+row.uniqueCode       
          , "修改商品", true);
        } catch (error) {
          console.error(error)
          this.$router.push({ 
            name: 'editProduct', 
            query: {
              type:"shelves",
              sendCode:row.applyCode,
              productCode:row.productCode,
              productType:2,
              spuCode:row.spuCode,
              uniqueCode:row.uniqueCode
            }
          })
        }
      },
      /**
       * @description: 格式化时间参数
       * @param {date} time 时间戳
       * @return: string 格式化后的时间戳
       */
      parseTime(time){
        return parseTimestamp(time)
      }
    }
  };
</script>
<style lang="scss" scoped>
  .putawayList-container {

    /**
   * 查询表单
   */
    .search-form-wrap {
      width: 100%;
      padding: 15px;
      border-bottom: 1px dashed #e4e4eb;

      .el-row {
        flex-wrap: wrap;

        .el-col {
          display: flex;
          justify-content: flex-end;

          .el-form-item {
            flex: 1;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            width: 100%;
            margin-right: 0;
            margin-bottom: 15px;

            /deep/ {
              .el-form-item__label {
                width: 116px;
                line-height: normal;
                padding: 0 12px;
                color: #292933;
                font-weight: normal;
              }

              .el-form-item__content {
                flex: 1;

                .el-range-editor.el-input__inner {
                  width: 100%;

                  .el-range-separator {
                    width:14px;
                    padding: 0;
                  }
                }
              }
            }

            .el-input {
              width: 100%;
            }

            .el-select {
              width: 100%;
            }
          }
        }
      }
    }

    /**
     * table
     */
    .table-wrap {
      width: 100%;
      min-height: 440px;
      height: calc(100vh - 350px);
      padding: 0 15px;
      margin-top: 15px;
    }

  }
</style>
