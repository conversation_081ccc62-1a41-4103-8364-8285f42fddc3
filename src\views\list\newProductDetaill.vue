<template>
    <div class="component-container">
        <div class="loading" v-loading="productLoading" v-show="productLoading"></div>

        <el-tabs v-model="activeName" type="border-card">
            <el-tab-pane label="基础属性" name="first">
                <approval-process-new :approvalData="approvalData" :boardData="boardData" :newGoodUp="true"></approval-process-new>
                <spu ref="spu" :formDisable="true" :spuData="spuData" :skuData="skuData"></spu>
                <sku ref="sku" :formDisable="true" :skuData="skuData" :sauData="sauData"></sku>
                <label-attr ref="labelAttr" :skuData="skuData" :spuData="spuData" :loadForm="false"
                    :showEmpty="spuCategory.type == 'GENERAL_MEDICINE' || spuCategory.type == 'TRADITIONAL_MEDICINE' ? false : true">
                </label-attr>
                <extended-attr ref="extend" v-if="spuCategory.type == 'GENERAL_MEDICINE'"></extended-attr>
                <extended-attr2 ref="extend" v-if="spuCategory.type == 'TRADITIONAL_MEDICINE'"></extended-attr2>
                <!-- <modify-record v-if="urlParam.record == 'hide' ? false : true" :recordData="recordData"></modify-record> -->
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script>
// 商品基础共用信息混入对象
import { productMixinBase } from "@/mixin/productMixinBase.js";
import { productPresentDetail } from "@/api/product";
// import { getAddProcessData } from "@/api/workManage";
import { getCustomerProductReportProduct } from "@/api/follow";

import modifyRecord from "@/views/product/modifyRecord";
import qualificationAttrNew from "./components/qualificationAttrNew";
import salesAttrNew from "./components/salesAttrNew";
import { parseTime } from "@/utils";

export default {
    name: "NewProductDetail",
    components: { modifyRecord, qualificationAttrNew, salesAttrNew },
    mixins: [productMixinBase],
    // 是否展示修改记录
    computed: {
        spuCategory() {
            return this.$store.state.product.spuCategory;
        },
    },
    data() {
        return {
            recordData: [],
            imgTitle: "",
            activeName: "first",
            _presentOrigin: "", // 新品上报来源
            boardData: {},
        };
    },
    created() {
        // 设置商品操作类型为 详情 - 这将触发所有子组件的只读模式
        this.$store.commit("product/SET_OPERATION_TYPE", "detail");
        // 获取审批流信息
        // this.getApplyInfo();
        // 获取商品详情数据
        this.getProductDetail();
    },
    methods: {
        statusCodeStrFormatter(statusCode){
            let statusMap = {
                1: "待处理",
                2: "驳回",
                3: "审核中",
                4: "通过",
            }
            return statusMap[statusCode] || "未知状态";
        },
        async getProductDetail() {
            this.productLoading = true;

            try {
                if (!this.urlParam.uniqueCode && !this.urlParam.applyCode) {
                    console.warn('缺少必要的参数 (uniqueCode 或 applyCode) 来获取产品详情');
                    this.productLoading = false;
                    return;
                }

                let res = {};

                if (this.urlParam.uniqueCode) {
                    res = await productPresentDetail({
                        uniqueCode: this.urlParam.uniqueCode,
                    });
                } else if (this.urlParam.applyCode) {
                    res = await getCustomerProductReportProduct({
                        applyCode: this.urlParam.applyCode,
                    });
                }

                if (res.success) {
                    let productData = res.data;
                    this._presentOrigin = productData.productSource;
                    this.boardData = {
                        taskPostCode: productData.taskPostCode,
                        source: productData.productSource,
                        applyCode: productData.applyCode,
                        outProductCode: productData.outProductCode,
                        receiveTimeStr: productData.receiveTimeStr,
                        statusCodeStr: this.statusCodeStrFormatter(productData.statusCode),
                        updateUser: productData.updateUser,
                        updateTimeStr: parseTime(productData.updateTime),
                        productId: productData.productId,
                    }
                    this.spuData = {
                        ...productData,
                        approvalImgList: productData.approvalImgList || [],
                        inRate: productData.inRate === 0 || productData.inRate ? Number(productData.inRate) : productData.inRate,
                        outRate: productData.outRate === 0 || productData.outRate ? Number(productData.outRate) : productData.outRate,
                    };

                    if (productData.packageUnitName) {
                        this.$store.getters.selectOptions.packageUnitOptions.forEach((item) => {
                            if (item.dictName == productData.packageUnitName) {
                                productData.packageUnitName = item.id;
                                return;
                            }
                        });
                        if (typeof productData.packageUnitName == "string") {
                            productData.packageUnitName = "";
                        }
                    }

                    this.skuData = [
                        {
                            tasteName: productData.tasteName || "",
                            sizeName: productData.sizeName || "",
                            colorName: productData.colorName || "",
                            storageCond: productData.storageCond || "",
                            skuName: productData.productName,
                            standardCodes: productData.standardCodes || "",
                            spec: productData.spec,
                            packageUnit: productData.packageUnitName,
                            prescriptionCategory: productData.prescriptionCategory,
                            prescriptionCategoryName: productData.prescriptionCategoryName,
                            smallPackageCodeList: productData.smallPackageCode && productData.smallPackageCode !== '-'
                                ? [productData.smallPackageCode]
                                : [],
                            brand: productData.brand,
                            qualityStandard: productData.qualityStandard,
                            validity: productData.validity,
                            outPackageImgList: productData.outPackageImgList || [],
                            directionImgList: productData.directionImgList || [],
                            piecePackageCodeList: productData.piecePackageCode
                                ? [productData.piecePackageCode]
                                : [],
                            mediumPackageCodeList: productData.mediumPackageCode
                                ? [productData.mediumPackageCode]
                                : [],
                            delegationProduct: productData.delegationProduct ? productData.delegationProduct : 0,
                            noSmallPackageCode: productData.noSmallPackageCode == 1 ? 1 : 0,
                            originPlace: productData.originPlace,
                            entrustedManufacturer: productData.entrustedManufacturerName,
                            netContent: productData.netContent || "",
                            netContentUnit: productData.netContentUnit || "",
                            changeList: [],
                        },
                    ];

                    this.sauData = [
                        {
                            outPackageImgList: productData.outPackageImgList || [],
                            directionImgList: productData.directionImgList || [],
                        },
                    ];

                    if (productData.sku) {
                        this.skuData = [...this.skuData, ...productData.sku];
                    }
                    if (productData.sau) {
                        this.sauData = [...this.sauData, ...productData.sau];
                    }

                    this.recordData = productData.record || [];

                    if (this.skuData[0]) {
                        const sku = this.skuData[0];
                        const smallPackageCode = sku.smallPackageCodeList && sku.smallPackageCodeList[0]
                            ? sku.smallPackageCodeList[0]
                            : '';
                        this.imgTitle = `${sku.skuCode || ''}-${sku.productId || ''}-${smallPackageCode}-${sku.skuName || ''}`;
                    }

                    this.$nextTick(() => {
                        this.$bus.$emit("spuLoading", true);
                    });

                } else {
                    this.$message.error(res.retMsg || '获取产品详情失败');
                }
            } catch (error) {
                console.error('获取产品详情时发生错误:', error);
                this.$message.error('获取产品详情时发生错误');
            } finally {
                this.productLoading = false;
            }
        },

        async getApplyInfo() {
            try {
                if (!this.urlParam.procDefId || !this.urlParam.procInstId) {
                    console.warn('缺少必要的参数 (procDefId 或 procInstId) 来获取审批流程信息');
                    return;
                }

                let param = {
                    procDefId: this.urlParam.procDefId,
                    procInstId: this.urlParam.procInstId
                };

                let res = await getAddProcessData(param);

                if (res.success) {
                    this.approvalData = res.data;
                } else {
                    this.$message.error(res.retMsg || '获取审批流程信息失败');
                }
            } catch (error) {
                console.error('获取审批流程信息时发生错误:', error);
                this.$message.error('获取审批流程信息时发生错误');
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.component-container {
    padding-top: 0px;

    .loading {
        width: 100vw;
        height: 100vh;
        position: fixed;
        top: 0;
        left: 0;
        z-index: 100;
    }

    .readonly-notice {
        background: #f0f9ff;
        border: 1px solid #b3d8ff;
        border-radius: 4px;
        padding: 12px 16px;
        margin-bottom: 16px;
        color: #1f2937;
        display: flex;
        align-items: center;

        i {
            color: #3b82f6;
            margin-right: 8px;
            font-size: 16px;
        }

        span {
            font-size: 14px;
        }
    }

    /deep/.el-tabs__header {
        border-bottom: none;
        background-color: #f2f2f2;
    }

    /deep/.el-tabs__content {
        padding: 0 !important;
    }

    .el-tabs--border-card {
        border: none;
        box-shadow: none;

        /deep/ .el-tabs__content {
            padding-bottom: 80px;
        }
    }

    .bottom-btns {
        background: #fff;
        width: 100%;
        position: fixed;
        bottom: 0;
        padding: 15px;
        z-index: 10;
    }

    // 全局禁用状态样式优化
    /deep/ .el-input.is-disabled .el-input__inner,
    /deep/ .el-select.is-disabled .el-input__inner,
    /deep/ .el-textarea.is-disabled .el-textarea__inner {
        background-color: #f8f9fa;
        border-color: #e9ecef;
        color: #6c757d;
    }

    /deep/ .el-radio.is-disabled .el-radio__label,
    /deep/ .el-checkbox.is-disabled .el-checkbox__label {
        color: #6c757d;
    }

    // 确保复制按钮在详情模式下保持可用
    /deep/ .copy-btn .el-button {
        pointer-events: auto !important;
        opacity: 1 !important;
        cursor: pointer !important;
        background-color: transparent !important;
    }


    // 确保复制按钮不受任何禁用状态影响
    /deep/ .el-form.is-disabled .copy-btn .el-button,
    /deep/ .el-form[disabled] .copy-btn .el-button,
    /deep/ .copy-btn .el-button[disabled] {
        pointer-events: auto !important;
        opacity: 1 !important;
        cursor: pointer !important;
        background-color: transparent !important;
    }
}
</style>