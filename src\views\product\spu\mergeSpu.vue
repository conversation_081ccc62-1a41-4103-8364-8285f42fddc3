<template>
  <div
    class="component-container"
    v-if="processInfo.applyCode"
    :class="urlParam.detailType ? 'pdTop' : ''"
  >
    <approval-process
      ref="approvalProcess"
      :approvalData="approvalData"
      :processInfo="processInfo"
    ></approval-process>
    <el-row
      class="btns"
      v-if="urlParam.detailType"
      :style="{ top: scrollTop < 40 ? '40px' : '0px' }"
    >
      <el-col :span="24" class="text-rt">
        <el-button
          type="primary"
          v-if="processInfo.showHang && !processInfo.hangStatus"
          @click="hang()"
          >挂起</el-button
        >
        <el-button @click="review(true)">审核通过</el-button>
        <el-button @click="review(false)">审核不通过</el-button>
      </el-col>
    </el-row>
    <div class="productDetail">
      <div class="table-contnt">
        <div class="table-title">
          spu编码
          <span>{{
            mergeSpuInfo.spu ? `(${mergeSpuInfo.spu.spuCode})` : ""
          }}</span>
        </div>
        <vxe-table
          border
          highlight-hover-row
          auto-resize
          max-height="300px"
          resizable
          align="center"
          :loading="tableLoading"
          :data="mergeSpuInfo.sku"
          ref="table"
          @checkbox-all="selectAllEvent"
          @checkbox-change="selectChangeEvent"
        >
          <vxe-table-column
            type="checkbox"
            width="60"
            show-header-overflow
            show-overflow
            fixed="left"
            v-if="urlParam.productCode"
          ></vxe-table-column>
          <vxe-table-column
            field="skuCode"
            title="sku编码"
            min-width="120"
            show-header-overflow
            show-overflow
            ><template v-slot="{ row }">
              <span
                :class="row.spuCode != mergeSpuInfo.spu.spuCode ? 'active' : ''"
                >{{ row.skuCode }}</span
              >
            </template></vxe-table-column
          >
          <vxe-table-column
            field="productId"
            title="商品ID"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="skuName"
            title="商品名"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="spec"
            title="规格/型号"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="packageUnitName"
            title="包装单位"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="prescriptionCategoryName"
            title="处方分类"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="smallPackageCodeList"
            title="小包装条码"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="mediumPackageCodeList"
            title="中包装条码"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
          <vxe-table-column
            field="piecePackageCodeList"
            title="件包装条码"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="brand"
            title="品牌/商标"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="validityDate"
            title="有效期"
            min-width="120"
            show-header-overflow
            show-overflow
          >
            <template v-slot="{ row }">{{
              transformValidity(row.validity)
            }}</template>
          </vxe-table-column>
          <vxe-table-column
            field="delegationProduct"
            title="是否委托生产"
            min-width="120"
            show-header-overflow
            show-overflow
          >
            <template v-slot="{ row }">{{
              row.delegationProduct ? "是" : "否"
            }}</template>
          </vxe-table-column>
          <vxe-table-column
            field="entrustedManufacturerName"
            title="受托生产厂家"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
          <vxe-table-column
            field="preOperateStatusName"
            title="自营状态"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
          <vxe-table-column
            field="qualityStandard"
            title="质量标准"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
          <vxe-table-column
            field="brandCategoryName"
            title="品牌分类"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
          <vxe-table-column
            field="conEvaluateVariety"
            title="一致性评价品种"
            min-width="120"
            show-header-overflow
            show-overflow
          >
            <template v-slot="{ row }">{{
              row.conEvaluateVariety == 1 ? "是" : "否"
            }}</template>
          </vxe-table-column>
          <vxe-table-column
            field="hospitalVariety"
            title="医院品种"
            min-width="120"
            show-header-overflow
            show-overflow
          >
            <template v-slot="{ row }">{{
              row.conEvaluateVariety == 1 ? "是" : "否"
            }}</template>
          </vxe-table-column>
          <vxe-table-column
            field="storage"
            title="贮藏"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
          <vxe-table-column
            field="standardCodes"
            title="本位码"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
          <vxe-table-column
            field="storageCondName"
            title="存储条件"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
          <vxe-table-column
            field="skuAliasText"
            title="别名"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
        </vxe-table>

        <div class="moveBtn-box" v-if="urlParam.productCode">
          <div
            class="top btn"
            :class="checkedBMergeList.length > 0 ? 'active' : ''"
            @click="handleMerge"
          >
            <i class="icon el-icon-top"></i>
          </div>
          <div
            class="bottom btn"
            :class="
              checkedMergeList.length > 0 && bMergeSpuInfo.spu ? 'active' : ''
            "
            @click="handleBMerge"
          >
            <i class="icon el-icon-bottom"></i>
          </div>
        </div>

        <div class="table-title bTitle">
          spu编码
          <span>{{
            bMergeSpuInfo.spu ? `(${bMergeSpuInfo.spu.spuCode})` : ""
          }}</span>
          <el-button
            class="search-btn"
            type="primary"
            size="mini"
            icon="el-icon-search"
            @click="handleSearch"
            v-if="urlParam.productCode"
            >搜索</el-button
          >
        </div>
        <vxe-table
          border
          highlight-hover-row
          auto-resize
          resizable
          max-height="300px"
          align="center"
          :data="bMergeSpuInfo.sku"
          ref="table"
          @checkbox-all="selectBAllEvent"
          @checkbox-change="selectBChangeEvent"
        >
          <vxe-table-column
            type="checkbox"
            width="60"
            show-header-overflow
            show-overflow
            fixed="left"
            v-if="urlParam.productCode"
          ></vxe-table-column>
          <vxe-table-column
            field="skuCode"
            title="sku编码"
            min-width="120"
            show-header-overflow
            show-overflow
            ><template v-slot="{ row }">
              <span
                :class="row.spuCode != bMergeSpuInfo.spu.spuCode ? 'active' : ''"
                >{{ row.skuCode }}</span
              >
            </template></vxe-table-column
          >
          <vxe-table-column
            field="productId"
            title="商品ID"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="skuName"
            title="商品名"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="spec"
            title="规格/型号"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="packageUnitName"
            title="包装单位"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="prescriptionCategoryName"
            title="处方分类"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="smallPackageCodeList"
            title="小包装条码"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="mediumPackageCodeList"
            title="中包装条码"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
          <vxe-table-column
            field="piecePackageCodeList"
            title="件包装条码"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="brand"
            title="品牌/商标"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="validity"
            title="有效期"
            min-width="120"
            show-header-overflow
            show-overflow
          >
            <template v-slot="{ row }">{{
              transformValidity(row.validity)
            }}</template>
          </vxe-table-column>
          <vxe-table-column
            field="delegationProduct"
            title="是否委托生产"
            min-width="120"
            show-header-overflow
            show-overflow
          >
            <template v-slot="{ row }">{{
              row.delegationProduct ? "是" : "否"
            }}</template>
          </vxe-table-column>
          <vxe-table-column
            field="entrustedManufacturerName"
            title="受托生产厂家"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
          <vxe-table-column
            field="preOperateStatusName"
            title="自营状态"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
          <vxe-table-column
            field="qualityStandard"
            title="质量标准"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
          <vxe-table-column
            field="brandCategoryName"
            title="品牌分类"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
          <vxe-table-column
            field="conEvaluateVariety"
            title="一致性评价品种"
            min-width="120"
            show-header-overflow
            show-overflow
          >
            <template v-slot="{ row }">{{
              row.conEvaluateVariety == 1 ? "是" : "否"
            }}</template>
          </vxe-table-column>
          <vxe-table-column
            field="hospitalVariety"
            title="医院品种"
            min-width="120"
            show-header-overflow
            show-overflow
          >
            <template v-slot="{ row }">{{
              row.conEvaluateVariety == 1 ? "是" : "否"
            }}</template>
          </vxe-table-column>
          <vxe-table-column
            field="storage"
            title="贮藏"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
          <vxe-table-column
            field="standardCodes"
            title="本位码"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
          <vxe-table-column
            field="storageCondName"
            title="存储条件"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
          <vxe-table-column
            field="skuAliasText"
            title="别名"
            min-width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
        </vxe-table>
      </div>
    </div>
    <div class="bottomBtn" v-if="urlParam.productCode">
      <el-button type="primary" @click="submit">提交</el-button>
    </div>
    <!-- 审核弹框 -->
    <el-dialog
      title="审核"
      width="500px"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisible"
      :close-on-press-escape="false"
      :show-close="false"
      @close="dialogClose"
    >
      <el-form :model="reviewForm" :rules="rules" ref="reviewForm">
        <el-form-item label="审核状态">
          <el-input
            v-model="reviewForm.state"
            disabled
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="审核意见" prop="value">
          <el-input
            type="textarea"
            placeholder="超过100的长度，不能提交"
            v-model="reviewForm.value"
            autocomplete="off"
            maxlength="100"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="save()">确 定</el-button>
      </div>
    </el-dialog>
    <!-- SPU 查询 -->
    <spu-drawer-table
      ref="spuDrawerTable"
      @saveSpu="saveSpu"
    ></spu-drawer-table>
  </div>
</template>


<script>
import approvalProcess from "@/views/product/approvalProcess";
import spuDrawerTable from "./components/spuDrawerTable";

import {
  getProductData,
  getMergeProductApplyInfo,
  submitSpuMerge,
  getMergeSpuDetail,
  getMergeSpuApproval,
} from "@/api/product";
import { review, hang } from "@/api/worksubstitution";

import { parseTime } from "@/filters";

export default {
  name: "mergeSpu",
  components: { approvalProcess, spuDrawerTable },
  data() {
    return {
      approvalData: {},
      processInfo: {},
      tableLoading: false,
      mergeSpuInfo: {},
      checkedMergeList: [],
      bMergeSpuInfo: {},
      checkedBMergeList: [],
      reviewForm: {},
      dialogFormVisible: false,
    };
  },
  computed: {
    urlParam: function () {
      return this.$route.query;
    },
  },
  watch: {},
  created() {
    if (this.urlParam.productCode) {
      this.getProductData();
      this.getMergeProductApplyInfo();
    } else {
      this.getMergeSpuDetail();
      this.getMergeSpuApproval();
    }
  },
  methods: {
    // 发起移动商品获取商品信息
    getProductData() {
      this.tableLoading = true;
      getProductData({ spuCode: this.urlParam.productCode }, "all").then(
        (res) => {
          this.tableLoading = false;
          this.mergeSpuInfo = res.data;
          this.mergeSpuInfo.sku.forEach((item) => {
            item.spuCategoryName = res.data.spu.spuCategoryName;
            item.generalName = res.data.spu.generalName;
            item.approvalNo = res.data.spu.approvalNo;
            item.manufacturerName = res.data.spu.manufacturerName;
            item.dosageFormName = res.data.spu.dosageFormName;
          });
        }
      );
    },
    // 发起移动商品获取申请信息
    getMergeProductApplyInfo() {
      getMergeProductApplyInfo().then((res) => {
        let info = res.data;
        this.processInfo = {
          createTime: parseTime(info.createTime),
          applyCode: info.applyCode,
          createUser: info.applyUserName,
          affiliation: info.affiliation,
        };
      });
    },
    // 表格全选
    selectAllEvent({ checked, records }) {
      this.checkedMergeList = records;
    },
    // 表格单选
    selectChangeEvent({ checked, records }) {
      this.checkedMergeList = records;
    },
    // 表格全选
    selectBAllEvent({ checked, records }) {
      this.checkedBMergeList = records;
    },
    // 表格单选
    selectBChangeEvent({ checked, records }) {
      this.checkedBMergeList = records;
    },
    // 打开spu搜索弹框
    handleSearch() {
      this.$refs.spuDrawerTable.show();
    },

    // 下移
    handleBMerge() {
      if (!this.bMergeSpuInfo.spu) {
        this.$message.warning("请选择spu");
        return;
      }
      if (this.checkedMergeList.length == 0) {
        this.$message.warning("请选择需要往下移动的商品");
        return;
      }
      this.checkedMergeList.forEach((item) => {
        if (item.spuCode != this.bMergeSpuInfo.spu.spuCode) {
          item.operationType = 1;
        } else {
          item.operationType = 0;
        }
      });
      this.bMergeSpuInfo.sku.unshift.apply(
        this.bMergeSpuInfo.sku,
        this.checkedMergeList
      );
      this.checkedMergeList.forEach((item) => {
        var index = this.mergeSpuInfo.sku.findIndex(
          (sku) => sku.skuCode == item.skuCode
        );
        this.mergeSpuInfo.sku.splice(index, 1);
      });
      this.checkedMergeList = [];
    },
    // 上移
    handleMerge() {
      if (!this.bMergeSpuInfo.spu) {
        this.$message.warning("请选择spu");
        return;
      }
      if (this.checkedBMergeList.length == 0) {
        this.$message.warning("请选择需要往上移动的商品");
        return;
      }
      this.checkedBMergeList.forEach((item) => {
        if (item.spuCode != this.mergeSpuInfo.spu.spuCode) {
          item.operationType = 1;
        } else {
          item.operationType = 0;
        }
      });
      this.mergeSpuInfo.sku.unshift.apply(
        this.mergeSpuInfo.sku,
        this.checkedBMergeList
      );
      this.checkedBMergeList.forEach((item) => {
        var index = this.bMergeSpuInfo.sku.findIndex(
          (sku) => sku.skuCode == item.skuCode
        );
        this.bMergeSpuInfo.sku.splice(index, 1);
      });
      this.checkedBMergeList = [];
    },
    // 选择合并商品保存
    saveSpu(spuInfo) {
      if (spuInfo.spuCode == this.mergeSpuInfo.spu.spuCode) {
        this.$message.warning("spuCode重复");
        return;
      }
      const loading = this.$loading({
        lock: true,
        text: "数据获取中...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      getProductData({ spuCode: spuInfo.spuCode }, "all").then((res) => {
        this.bMergeSpuInfo = res.data;
        this.bMergeSpuInfo.sku.forEach((item) => {
          item.spuCategoryName = res.data.spu.spuCategoryName;
          item.generalName = res.data.spu.generalName;
          item.approvalNo = res.data.spu.approvalNo;
          item.manufacturerName = res.data.spu.manufacturerName;
          item.dosageFormName = res.data.spu.dosageFormName;
        });
        loading.close();
        this.$refs.spuDrawerTable.cancelSpuCode();
      });
    },
    // 发起商品移动
    submit() {
      this.$refs.approvalProcess.$refs.spuForm.validate((valid) => {
        if (valid) {
          if (!this.bMergeSpuInfo.spu) {
            this.$message.warning("请选择一条spu进行合并");
            return;
          }
          let arr = this.mergeSpuInfo.sku.filter(
            (item) => item.operationType == 1
          );
          let arr1 = this.bMergeSpuInfo.sku.filter(
            (item) => item.operationType == 1
          );
          if (arr.length == 0 && arr1.length == 0) {
            this.$message.warning("至少有一个商品移动才能生成单据");
            return;
          }
          const param = this.filterParam(this.mergeSpuInfo, this.bMergeSpuInfo);
          submitSpuMerge(param).then((res) => {
            if (!res.retCode) {
              this.$message.success("操作成功");
              try {
                parent.CreateTab(
                  "../static/dist/index.html#/product/spuList",
                  "spu列表",
                  true
                );
                parent.CloseTab("../static/dist/index.html#/product/mergeSpu");
              } catch {
                this.$router.push({
                  path: "/product/spuList",
                });
              }
            } else {
              this.$message.error(res.retMsg);
            }
          });
        } else {
          this.$message.warning("请输入申请原因");
        }
      });
    },
    // 格式化发起商品移动接口参数
    filterParam(source, target) {
      let param = {
        applyCode: this.processInfo.applyCode,
        applyReason: this.processInfo.applyReason,
        createTime: new Date(this.processInfo.createTime).getTime(),
        source: {
          moveSkuParamList: [],
          spuCode: source.spu.spuCode,
        },
        target: {
          moveSkuParamList: [],
          spuCode: target.spu.spuCode,
        },
      };
      source.sku.forEach((item) => {
        if (item.operationType == 1) {
          param.target.moveSkuParamList.push({
            operationType: -1,
            skuCode: item.skuCode,
          });
        } else {
          item.operationType = 0;
        }
        param.source.moveSkuParamList.push({
          operationType: item.operationType,
          skuCode: item.skuCode,
        });
      });
      target.sku.forEach((item) => {
        if (item.operationType == 1) {
          param.source.moveSkuParamList.push({
            operationType: -1,
            skuCode: item.skuCode,
          });
        } else {
          item.operationType = 0;
        }
        param.target.moveSkuParamList.push({
          operationType: item.operationType,
          skuCode: item.skuCode,
        });
      });
      return param;
    },

    // 格式化有效期
    transformValidity(validity) {
      // -1 对应 - ;0 对应 *;
      switch (validity) {
        case -1:
          return "-";
        case 0:
          return "*";
        default:
          return validity + "月";
      }
    },

    // 商品移动审核

    // 获取审核详情
    getMergeSpuDetail() {
      getMergeSpuDetail({ applyCode: this.urlParam.applyCode }).then((res) => {
        const data = res.data;
        this.mergeSpuInfo = {
          spu: {
            spuCode: data.source.spuCode,
          },
          sku: data.source.skuBusinessVoList,
        };
        this.bMergeSpuInfo = {
          spu: {
            spuCode: data.target.spuCode,
          },
          sku: data.target.skuBusinessVoList,
        };
      });
    },
    // 获取审核订单流程信息
    getMergeSpuApproval() {
      getMergeSpuApproval({ applyCode: this.urlParam.applyCode }).then(
        (res) => {
          this.approvalData.approvalProcess = res.data.approvalProcessDto;
          this.processInfo = {
            ...res.data.goodsView,
            createTime: parseTime(res.data.goodsView.createTime),
            applyReason: res.data.goodsView.applyContent,
          };
        }
      );
    },
    review(flag) {
      this.dialogFormVisible = true;
      if (flag) {
        this.reviewForm.state = "审核通过";
      } else {
        this.reviewForm.state = "审核不通过";
      }
    },
    save() {
      review({
        id: this.processInfo.id,
        applyCode: this.processInfo.applyCode,
        applyUserScope: this.processInfo.applyUserScope,
        approvalProcess: this.processInfo.approvalProcess,
        rejectStatus: this.reviewForm.state == "审核通过" ? 1 : 0,
        reviewOpinion: this.reviewForm.value,
        reviewStatus: this.processInfo.reviewStatus,
      }).then((res) => {
        this.dialogFormVisible = false;
        if (res.retCode == 0) {
          this.$message({
            message: "操作成功！",
            type: "success",
          });
          if (this.processInfo.hangStatus) {
            parent.CreateTab("/api/worksubstitution/to/hang", "挂起事项", true);
          } else {
            parent.CreateTab(
              "../static/dist/index.html#/workbench/prepare",
              "待办事项",
              true
            );
          }
          parent.CloseTab("../static/dist/index.html#/product/mergeSpu");
        } else {
          this.$message.error(res.retMsg);
        }
      });
    },
    hang() {
      hang({
        hangStatus: 1,
        id: this.processInfo.id,
        reviewStatus: this.processInfo.reviewStatus,
      }).then((res) => {
        if (res.retCode == 0) {
          this.$message({
            message: "操作成功！",
            type: "success",
          });
          parent.CreateTab(
            "../static/dist/index.html#/workbench/prepare",
            "待办事项",
            true
          );
          parent.CloseTab("../static/dist/index.html#/product/mergeSpu");
        } else {
          this.$message.error(res.retMsg);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.component-container {
  padding-bottom: 50px;
  &.pdTop {
    padding-top: 50px;
  }
}
.table-contnt {
  padding: 0 20px;
  .table-title {
    display: flex;
    align-items: center;
    padding: 10px 0 20px;
    font-size: 16px;
    font-weight: bold;
    &.bTitle {
      padding-top: 30px;
    }
    .search-btn {
      margin-left: 10px;
    }
  }
}
.moveBtn-box {
  display: flex;
  justify-content: center;
  padding-top: 30px;
  .btn {
    height: 40px;
    width: 40px;
    border: 2px solid #c0c4cc;
    border-radius: 50%;
    margin: 0 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    &.active {
      border-color: #409eff;
      cursor: pointer;
      .icon {
        color: #409eff;
      }
    }
    .icon {
      color: #c0c4cc;
      font-weight: bold;
      font-size: 22px;
    }
  }
}
.active {
  color: #409eff;
}
.bottomBtn {
  background: #f0f2f5;
  width: 100%;
  position: fixed;
  bottom: 0;
  padding: 15px;
  z-index: 10;
  display: flex;
  justify-content: center;
}
.btns {
  background: #fff;
  width: 100%;
  position: fixed;
  top: 40px;
  padding: 15px;
  z-index: 10;
}
</style>