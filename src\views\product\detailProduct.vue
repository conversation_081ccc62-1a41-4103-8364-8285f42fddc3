<template>
  <div class="component-container">
    <div
      class="loading"
      v-loading="productLoading"
      v-show="productLoading"
    ></div>
    <el-tabs v-model="activeName" type="border-card">
      <el-tab-pane label="基础属性" name="first">
        <approval-process :approvalData="approvalData"></approval-process>
        <spu ref="spu" :spuData="spuData" :skuData="skuData"></spu>
        <sku ref="sku" :skuData="skuData" :sauData="sauData"></sku>
        <label-attr ref="labelAttr" :skuData="skuData" :spuData="spuData" :loadForm="false"
        :showEmpty="spuCategory.type == 'GENERAL_MEDICINE' || spuCategory.type == 'TRADITIONAL_MEDICINE' ? false : true"></label-attr>
        <extended-attr ref="extend" v-if="spuCategory.type == 'GENERAL_MEDICINE'"></extended-attr>
        <extended-attr2 ref="extend" v-if="spuCategory.type == 'TRADITIONAL_MEDICINE'"></extended-attr2>
        <extended-attr3 ref="extend" v-if="spuCategory.type == 'MEDICAL_INSTRUMENT'"></extended-attr3>
        <vxe-table
          v-if="!urlParam.hideTab"
          style="margin-top:72px"
          border
          resizable
          auto-resize
          size="small"
          align="center"
          :row-class-name="rowClassName"
          :tooltip-config="{ enterable: false }"
          :data="sauData"
          :editConfig="{ trigger: 'click', mode: 'cell', activeCellMethod: true }"
        >
          <vxe-table-column type="seq" title="序号" min-width="50" show-header-overflow show-overflow> </vxe-table-column>
          <vxe-table-column field="sauCode" title="商品编码" min-width="120" show-header-overflow show-overflow></vxe-table-column>
          <vxe-table-column field="skuCode" title="sku" min-width="120" show-header-overflow show-overflow></vxe-table-column>
          <vxe-table-column title="是否线下业务" min-width="120" show-header-overflow show-overflow>
            <template v-slot="{ row }">
              <el-radio disabled v-model="row.offlineBusinessType" :label="1"
                >是</el-radio
              >
              <el-radio disabled v-model="row.offlineBusinessType" :label="0"
                >否</el-radio
              >
            </template>
          </vxe-table-column>
          <vxe-table-column
            field="attrValue"
            title="销售渠道"
            min-width="120"
            show-header-overflow
            show-overflow
            :editRender="{ name: 'input', events: { input: changeComment } }"
          >
            <template #edit="{ row }">
              <el-input disabled v-model="row.attrValue" maxlength="10"></el-input>
            </template>
          </vxe-table-column>
          <vxe-table-column
            v-if="urlParam.pageType"
            field="mergeProduct"
            title="合并商品"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column title="操作" min-width="120" show-header-overflow show-overflow>
            <template v-slot="{ row, $rowIndex }">
              <span v-if="row.isNew && !urlParam.pageType">
                <el-link :underline="false" type="primary" @click.stop="deleteItem($rowIndex)">删除</el-link></span
              >
            </template>
          </vxe-table-column>
        </vxe-table>
        <modify-record
          v-if="urlParam.record == 'hide' ? false : true"
          :recordData="recordData"
        ></modify-record>
      </el-tab-pane>
      <el-tab-pane v-if="!urlParam.hideTab" label="资质属性" name="third">
        <qualification-attr></qualification-attr>
      </el-tab-pane>
      <el-tab-pane v-if="!urlParam.hideTab" label="精修图版本" name="fourth">
        <sales-attr :imgTitle="imgTitle" :showChange="true"></sales-attr>
      </el-tab-pane>
    </el-tabs>
    <el-row class="bottom-btns">
          <div :span="24" class="bottom-btn-wrap">
            <el-button type="primary" @click="editProduct">修改</el-button>
          </div>
    </el-row>
  </div>
</template>

<script>
// 商品基础共用信息混入对象
import { productMixinBase } from "@/mixin/productMixinBase.js";
import {
  getNewProductInfo, //新获取商品信息
  getProductData, //获取商品信息
  getApplyInfo, //审批流信息
} from "@/api/product";

import modifyRecord from "./modifyRecord";
import qualificationAttr from "./qualificationAttr";
import salesAttr from "./salesAttr";
export default {
  name: "",
  components: { modifyRecord, qualificationAttr, salesAttr },
  mixins: [productMixinBase],
  // 是否展示修改记录
  computed: {
    spuCategory() {
      return this.$store.state.product.spuCategory;
    },
  },
  data() {
    return {
      recordData: [],
      imgTitle: "",
      activeName: "first",
    };
  },
  created() {
    // 设置商品操作类型为 详情
    this.$store.commit("product/SET_OPERATION_TYPE", "detail");
    // 获取审批流信息
    this.getApplyInfo();
    // 获取商品详情数据
    this.getProductDetail();
  },
  methods: {
    editProduct(){
      let productCode, productType
      productCode = this.urlParam.productCode
      productType = this.urlParam.productType
      try {
        parent.CreateTab(
          "../static/dist/index.html#/product/"+(productType == 2 ? 'editProduct' : 'addDeputyProduct')+"?productCode=" +
            productCode +
            "&sauCode=" +
            this.sauData[0].sauCode +
            "&preOperateStatus=" +
            this.sauData[0].preOperateStatus +
            "&productType=" +
            productType +
            "&spuCode=" +
            this.urlParam.spuCode +
            "&pageType=edit",
          "商品修改",
        );
      } catch {
        this.$router.push({
          path: `/product/${productType == 2 ? 'editProduct' : 'addDeputyProduct'}`,
          query: {
            productCode: productCode,
            productId: this.sauData[0].productId,
            productType: productType,
            spuCode: this.urlParam.spuCode,
            sauCode: this.sauData[0].sauCode,
            preOperateStatus:this.sauData[0].preOperateStatus,
            pageType:"edit",
          },
        });
      }
    },
    async getProductDetail() {
      this.productLoading = true;
      if (this.urlParam.detailType == "editImg") {
        this.urlParam.detailType = "self";
      }
      let res = await getNewProductInfo(this.urlParam);
      // console.log(res);
      this.spuData = Object.freeze(res.data.spu);
      this.skuData = Object.freeze(res.data.sku);
      // this.sauData = Object.freeze(res.data.sau);
      res.data.sau.map(item => {
        if (item.skuCode === this.urlParam.skuCode) {
          this.sauData.push(item)
        }
      })
      this.recordData = Object.freeze(res.data.record);
      if(!this.skuData[0]) return;
      this.imgTitle = `${this.skuData[0].skuCode}-${this.skuData[0].productId}-${this.skuData[0].smallPackageCode[0]}-${this.skuData[0].skuName}`;
    },
    async getApplyInfo() {
      let param = {
        selectType: 4, // 查询类型, (0:草稿, 1:新增, 2:属性修改,3:审核或驳回修改,4:查询详情)
        applyCode: this.urlParam.applyCode ? this.urlParam.applyCode : "", //单据编号
        productCode: this.urlParam.productCode, //商品编码
        productType: this.urlParam.productType, //商品类型(1-SPU;2-SKU;3-SAU)
        spuCode: this.urlParam.spuCode, //SPU编码
      };
      let res = await getApplyInfo(param);
      if (res.success) {
        this.approvalData = res.data;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.component-container {
  padding-top: 0px;
  .loading {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100;
  }
  /deep/.el-tabs__header {
    border-bottom: none;
    background-color: #f2f2f2;
  }
  /deep/.el-tabs__content{
    padding: 0 !important;
  }
  .el-tabs--border-card {
    border: none;
    box-shadow: none;
    /deep/ .el-tabs__content {
      padding-bottom: 80px;
    }
  }
  .bottom-btns {
    background: #fff;
    width: 100%;
    position: fixed;
    bottom: 0;
    padding: 15px;
    z-index: 10;
  }
  .bottom-btn-wrap {
  display: flex;
  justify-content: center;
  }
}
</style>
