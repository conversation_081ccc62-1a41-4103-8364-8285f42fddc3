import Iframe from '@/iframe'
const workManage = {
  path: '/workManage',
  name: 'workManage',
  component: Iframe,
  redirect: 'noRedirect',
  meta: { title: '', icon: 'nested' },
  children: [
    {
      path: 'receiveDeal',
      name: 'receiveDeal',
      component: () => import('@/views/workManage/receiveDeal'),
      meta: { title: '领取与处理' }
    },
    {
      path: 'myAudit',
      name: 'myAudit',
      component: () => import('@/views/workManage/myAudit'),
      meta: { title: '我的已审' }
    },
    {
      path: 'myApply',
      name: 'myApply',
      component: () => import('@/views/workManage/myApply'),
      meta: { title: '我的申请' }
    },
    {
      path: 'addGoodsDetail',
      name: 'addGoodsDetail',
      component: () => import('@/views/workManage/components/addGoodsDetail'),
      meta: { title: '任务详情' }, //新的 商品新增的详情
      hidden: true
    },
    {
      path: 'newGoodsReport',
      name: 'newGoodsReport',
      component: () => import('@/views/workManage/components/newGoodsReport'),
      meta: { title: '商品新增' }, //新的 新品上报详情
      hidden: true
    },
    {
      path: 'newGoodsReportDeal',
      name: 'newGoodsReportDeal',
      component: () => import('@/views/workManage/components/newGoodsReportDeal'),
      meta: { title: '新品上报处理' }, //新的 新品上报处理页
      hidden: true
    },
    {
      path: 'addGoodsDeal',
      name: 'addGoodsDeal',
      component: () => import('@/views/workManage/components/addGoodsDeal'),
      meta: { title: '商品新增处理' }, //新的 商品新增处理页
      hidden: true
    },
    {
      path: 'addDeputyDeal',
      name: 'addDeputyDeal',
      component: () => import('@/views/workManage/components/addDeputyDeal'),
      meta: { title: '副商品新增处理' }, //副商品新增处理页
      hidden: true
    },
    {
      path: 'finishingDeal',
      name: 'finishingDeal',
      component: () => import('@/views/workManage/components/finishingDeal'),
      meta: { title: '改精修图-处理' }, //改精修图-处理 节点1
      hidden: true
    },
    {
      path: 'finishingAudit',
      name: 'finishingAudit',
      component: () => import('@/views/workManage/components/finishingAudit'),
      meta: { title: '改精修图-审核' }, //改精修图-审核 节点2
      hidden: true
    },
    {
      path: 'mergeGoodsDeal',
      name: 'mergeGoodsDeal',
      component: () => import('@/views/workManage/components/mergeGoodsDeal'),
      meta: { title: '商品审核明细页' }, //商品合并流程处理页
      hidden: true
    },
    {
      path: 'imgVersionDeal',
      name: 'imgVersionDeal',
      component: () => import('@/views/workManage/components/imgVersionDeal'),
      meta: { title: '精修图停启用审核明细页' }, //精修图停启用审核明细页
      hidden: true
    }
  ]
}

export default workManage
