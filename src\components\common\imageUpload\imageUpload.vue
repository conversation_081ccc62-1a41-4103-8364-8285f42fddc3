<template>
  <div class='image-upload-container'>
    <custom-upload
      list-type="picture-card"
      :action="fileUploadUrl"
      :auto-upload="true"
      :on-success="uploadSuccess"
      :on-error="uploadError"
      :on-remove="fileRemove"
      :file-list="fileListforNative"
      :limit="10"
      :before-upload="beforeUpload"
      :multiple="true">
        <i slot="default" class="el-icon-plus"></i>
    </custom-upload>
  </div>
</template>
<script>
  import customUpload from './index';
  let testImage=[{
				"id": 84512,
				"mediaCode": "36f252d286b548c08becb9030a3be77c",
				"meidiaType": 0,
				"mediaName": "数字8.jpg",
				"mediaUrl": "https://files.test.ybm100.com/G1/M00/0F/E6/Cgoz014cFpyARIvrAABiWqt5nww233.jpg",
				"createUser": "覃燕",
				"createTime": 1578899236000
			}, {
				"id": 84513,
				"mediaCode": "3fc15cffbb9f4fb7ba863c4c17ecc6ab",
				"meidiaType": 0,
				"mediaName": "数字7.jpg",
				"mediaUrl": "https://files.test.ybm100.com/G1/M00/0F/E5/Cgoz1F4cFpyAVsxXAACAS3Kb4GQ610.jpg",
				"createUser": "覃燕",
				"createTime": 1578899236000
      },
      // {
			// 	"id": 84515,
			// 	"mediaCode": "3fc15cffbb9f4fb7ba863c4c17ecc6ab",
			// 	"meidiaType": 0,
			// 	"mediaName": "数字7.jpg",
			// 	"mediaUrl": "https://files.test.ybm100.com/G2/M00/10/71/Cgo0014ezR2AE_OuAAA1LdLdurM566.jpg",
			// 	"createUser": "覃燕",
			// 	"createTime": 1578899236000
      // },
      // {
			// 	"id": 84516,
			// 	"mediaCode": "3fc15cffbb9f4fb7ba863c4c17ecc6ab",
			// 	"meidiaType": 0,
			// 	"mediaName": "数字7.jpg",
			// 	"mediaUrl": "https://files.test.ybm100.com/G2/M00/10/6D/Cgo01F4ezR2AQxVHAABhceGwRnw757.jpg",
			// 	"createUser": "覃燕",
			// 	"createTime": 1578899236000
      // },
      // {
			// 	"id": 84517,
			// 	"mediaCode": "3fc15cffbb9f4fb7ba863c4c17ecc6ab",
			// 	"meidiaType": 0,
			// 	"mediaName": "数字7.jpg",
			// 	"mediaUrl": "https://files.test.ybm100.com/G2/M00/10/6D/Cgo01F4ezR2AUQlrAABEZHIJ27I798.jpg",
			// 	"createUser": "覃燕",
			// 	"createTime": 1578899236000
			// },
      {
				"id": 84514,
				"mediaCode": "35d0f63dffdf46ea86d7c61ac07f498e",
				"meidiaType": 0,
				"mediaName": "数字6.jpg",
				"mediaUrl": "https://files.test.ybm100.com/G1/M00/0F/E6/Cgoz014cFpyAcyPiAAAUY6XA6eo726.jpg",
				"createUser": "覃燕",
				"createTime": 1578899236000
			}]
  export default {
    name: "",
    components: {
      customUpload
    },
    filters: {},
    props: {
      maxSize:{
        type: Number,
        default:10240*1024,
      }
    },
    data() {
      return {
        items: [1,2,3,4,5,6,7,8,9],
        nextNum: 10,
        aaa:JSON.parse(JSON.stringify(testImage)),
        fileUploadUrl:process.env.VUE_APP_BASE_API+"/api/file/upload/fileAndName",
        // 原始的选择文件列表
        fileListforNative:testImage,
        // 上传后处理过的文件列表
        fileListforUpload:[],
      }
    },
    computed: {
      // fileListforNative:function(){
      //   return this.fileListforUpload;
      // }
    },
    watch: {
      fileListforUpload:function(){

      }
    },
    created() {
      
    },
    mounted() {},
    methods: {
      moveTest(index,arr){
        let moveIndex=index-1;
        // arr[moveIndex]=arr.splice(index,1,arr[moveIndex])[0];
        let item=arr.splice(index,1,arr[moveIndex])[0];
        arr.splice(moveIndex,1,item)
        console.log(arr)
        // this.aaa=arr;
      },
      randomIndex: function () {
        return Math.floor(Math.random() * this.items.length)
      },
      add: function () {
        this.items.splice(this.randomIndex(), 0, this.nextNum++)
      },
      remove: function () {
        this.items.splice(this.randomIndex(), 1)
      },
      shuffle: function () {
        this.items.reverse();
      },
      /**
       * @description: 文件上传成功时的钩子
       * @param {object} response
       * @param {file} file 当前上传成功的文件
       * @param {fileList} fileList 总共上传成功的文件
       * @return: 
       */  
      uploadSuccess(response,file,fileList){
        console.log(response,file,fileList)
      },
      uploadError(err, file, fileList){
        console.log(err)
      },
      beforeUpload(file){
        // console.log(file)
        if(file.size>this.maxSize){
          this.$message.error(`名称为 ——${file.name}—— 文件超过最大限制`)
          return false;
        }
      },
      fileRemove(file, fileList){
        console.log(file,fileList)
      },
      reversal(){
        this.aaa.reverse();
      },
      // remove(file) {
      //   console.log(file);
      // },
      moveLeft(file) {
        console.log(file);
      },
      moveRight(file) {
        console.log(file);
      },
    }
  }
</script>

<style lang="scss" scoped>
  .image-upload-container{
    .test{
      display: flex;
      .lis{
        flex: none;
        width: 200px;
      }
    }
    .list-item {
      display: inline-block;
      margin-right: 10px;
    }
    .list-enter-active, .list-leave-active {
      transition: all 1s;
    }
    .list-enter, .list-leave-to
    /* .list-leave-active for below version 2.1.8 */ {
      opacity: 0;
      transform: translateY(30px);
    }
    .list-move {
      transition: transform 1s;
    }
  }
</style>
