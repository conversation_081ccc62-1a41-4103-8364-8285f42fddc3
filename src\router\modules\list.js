import Iframe from '@/iframe'
const listRouters = {
  path: '/list',
  name: 'list',
  component: Iframe,
  redirect: 'noRedirect',
  meta: { title: '', icon: 'nested' },
  children: [
    {
      path: 'correction',
      name: 'correction',
      component: () => import('@/views/list/correction'),
      meta: { title: '商品纠错记录' }
    },
    {
      path: 'newProductSubmit',
      name: 'newProductSubmit',
      component: () => import('@/views/list/newProductSubmit'),
      meta: { title: '新品上报' }
    },
    {
      path: 'shelvesList',
      name: 'shelvesList',
      component: () => import('@/views/list/shelvesList'),
      meta: { title: '商品上架申请列表' }
    },
    {
      path: 'exportRecordList',
      name: 'exportRecordList',
      component: () => import('@/views/list/exportRecordList'),
      meta: { title: '数据导出列表' }
    },
    {
      path: 'applyProductList',
      name: 'applyProductList',
      component: () => import('@/views/list/applyProduct/index'),
      meta: { title: '已申请商品列表' }
    },
    {
      path: 'refinedProductList',
      name: 'refinedProductList',
      component: () => import('@/views/list/refinedProductList'),
      meta: { title: '待精修商品列表' }
    },
    {
      path: 'newProductDetaill',
      name: 'newProductDetaill',
      component: () => import('@/views/list/newProductDetaill'),
      meta: { title: '新品详情' }
    },
  ]
}

export default listRouters
