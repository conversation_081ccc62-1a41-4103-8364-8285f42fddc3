<template>
  <div class="component-container" v-if="!isGIFT">
    <el-form
      :disabled="formDisable"
      :model="sauForm"
      ref="sauForm"
      label-width="100px"
      class="border-bottom-dashed"
    >
      <el-row>
        <el-col :span="24">
          <!-- :rules="{
              type: 'array',
              required: true,
              message: '请至少选择一个属性，最多不超过四个',
              trigger: 'change',
            }" -->
          <el-form-item class="sau-form" label="请选择属性" prop="attr">
            <span class="tip"
              >1、每个商品最多可维护4组属性；2、每组属性最多个维护5个属性值；3：属性值不可重复且不可超过10个汉字</span
            >
            <el-checkbox-group
              v-model="sauForm.attr"
              :max="4"
              :disabled="checkOperatePermission('attr')"
            >
              <el-checkbox
              :disabled="checkboxStatus === 1 && item.attrName === '合并商品' || checkboxStatus === 2 && item.attrName !== '合并商品' ? true : false"
                v-for="(item, index) in sauItems"
                :key="index"
                :label="item.attrId"
                @change="selectedSauAttrItem"
                >{{ item.attrName }}</el-checkbox
              >
            </el-checkbox-group>
            <el-link v-if="!sauItems.length" type="danger" :underline="false"
              >该商品分类下无属性</el-link
            >
          </el-form-item>
        </el-col>
      </el-row>

      <el-row
        v-for="(selected, index) in sauForm.selected"
        :key="index"
        class="atrrList"
      >
        <el-col>
          <el-form-item
            :label="index == 0 ? '请填写属性值' : ''"
            required
            label-width="130px"
          >
            <el-input v-model="selected.attrName" :disabled="true"></el-input>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item
            label=""
            :prop="'selected.' + index + '.attrValue'"
            :rules="{
              required: true,
              message: '必填',
              trigger: ['blur', 'change'],
            }"
            :show-message="false"
            style="width: 100%"
          >
            <!-- 受托生产厂家 -->
            <template v-if="selected.attrId == 21">
              <el-select
                v-model="sauForm.selected[index].attrValue"
                :filter-method="searchEntrustedManufacturer"
                placeholder="请选择"
                filterable
                @change="
                  manufacturerChange(index, sauForm.selected[index].attrValue)
                "
                style="width: 100%"
                :disabled="
                  checkOperatePermission(sauForm.selected[index].attrValue)
                "
              >
                <el-option
                  v-for="item in manufacturerOptions"
                  :key="'key_manufacturerOptions_' + item.id"
                  :label="item.dictName"
                  :value="item.id"
                  :disabled="!item.isValid"
                ></el-option>
              </el-select>
            </template>
            <!-- 处方分类 -->
            <template v-else-if="selected.attrId == 22">
              <el-select
                v-model="sauForm.selected[index].attrValue"
                placeholder="请选择"
                style="width: 100%"
                :disabled="
                  checkOperatePermission(sauForm.selected[index].attrValue)
                "
              >
                <el-option
                  v-for="item in $store.getters.selectOptions
                    .prescriptionCategoryOptions"
                  :key="'key_packageUnitOptions_' + item.id"
                  :label="item.dictName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </template>
            <!-- 有效期 -->
            <template v-else-if="selected.attrId == 23">
              <el-input
                v-model="sauForm.selected[index].attrValue"
                 @input="
                  (val) => {
                    !/^(-|\*)?$|^[1-9]{1}\d{0,3}$/g.test(val) &&
                      (sauForm.selected[index].attrValue = '');
                  }"
                type="text"
                maxlength="4"
                style="width: 100%"
                :disabled="
                  checkOperatePermission(sauForm.selected[index].attrValue)
                "
              >
                <template slot="append">月</template>
              </el-input>
            </template>
            <!-- 包装单位 -->
            <template v-else-if="selected.attrId == 25">
              <el-select
                v-model="sauForm.selected[index].attrValue"
                placeholder="请选择"
                style="width: 100%"
                :disabled="
                  checkOperatePermission(sauForm.selected[index].attrValue)
                "
              >
                <el-option
                  v-for="item in $store.getters.selectOptions
                    .packageUnitOptions"
                  :key="'key_packageUnitOptions_' + item.id"
                  :label="item.dictName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </template>
            <!-- 存储条件 -->
            <template v-else-if="selected.attrId == cctjID">
              <el-select
                v-model="sauForm.selected[index].attrValue"
                placeholder="请选择"
                style="width: 100%"
                :disabled="
                  checkOperatePermission(sauForm.selected[index].attrValue)
                "
              >
                <el-option
                  v-for="item in $store.getters.selectOptions
                    .storageCondOptions"
                  :key="'key_packageUnitOptions_' + item.id"
                  :label="item.dictName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </template>
            <!-- 质量标准 -->
            <template v-else-if="selected.attrId == 24">
              <el-input
                v-model="sauForm.selected[index].attrValue"
                maxlength="255"
                :disabled="
                  checkOperatePermission(sauForm.selected[index].attrValue)
                "
              ></el-input>
            </template>
            <!-- 其他属性值 -->
            <template v-else>
              <el-input
                v-model="sauForm.selected[index].attrValue"
                maxlength="16"
                :disabled="
                  checkOperatePermission(sauForm.selected[index].attrValue)
                "
              ></el-input>
            </template>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="radio-wrap">
      <div class="lable">是否线下业务</div>
      <el-radio-group :disabled="formDisable" v-model="sauForm.offlineBusinessType">
        <el-radio :label="1">是</el-radio>
        <el-radio :label="0">否</el-radio>
      </el-radio-group>
    </div>
    <!-- <div class="image-box">
      <div class="img-title">
        外包装图片
        <br /><span style="color: #bababa">(最多60张)</span>
      </div>
      <div class="img-content">
        <upload-img
          :preview="true"
          :fileList="sauForm.outPackageImgList"
          :limit="60"
          :uploadUrl="ImageUploadUrl"
          @change="outPackageImgUploadSuccess"
          disabled
        ></upload-img>
      </div>
    </div>
    <div class="image-box">
      <div class="img-title">
        说明书图片
        <br /><span style="color: #bababa">(最多60张)</span>
      </div>
      <div class="img-content">
        <upload-img
          disabled
          :preview="true"
          :fileList="sauForm.directionImgList"
          :limit="60"
          :uploadUrl="ImageUploadUrl"
          @change="directionImgUploadSuccess"
          size="small"
        ></upload-img>
      </div>
    </div> -->
  </div>
</template>

 <script>
import uploadImg from "../../../components/uploadImg/index";
import { dictSearchTypeAndName } from "@/api/dict";
import { findNameByOptions } from "@/utils/index.js";
export default {
  name: "",
  components: { uploadImg },
  props: {
    formDisable:{
      type: Boolean,
      default: false
    },
    sauItems: {
      type: Array,
      default: () => [],
    },
    sauForm: {
      type: Object,
      default: () => {
        return {};
      },
    },
    cctjID: {
      type: Number,
      default: 0,
    },
    manufacturerOptions: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      checkboxStatus: 0, // 请选择属性的状态 0全部可选 1选择了合并商品以外的 2选择了合并商品
      // isInit: true, // 初始化
      isGIFT: false,
      sauAllData: [], // all
      originalData: [], // 原始数据
      skuCurrentRow: {}, // sku 选中数据
      categoryId: "", // 商品分类id
      // 图片上传路径
      ImageUploadUrl:
        process.env.VUE_APP_BASE_API + "/api/file/upload/fileAndName",
      sauCurrentRow: null,
      sauTableData: [],
      sauDlgVisible: false,
      operType: "新增",
      previewDlgVisible: false, // 预览图片
      previewImgList: [], // 预览图片
    };
  },
  computed: {
    // 商品分类
    spuCategory() {
      return this.$store.getters.spuCategory;
    },

    // 操作类型
    operationType() {
      return this.$store.getters.operationType;
    },
    // 路由参数
    urlParam() {
      return this.$route.query;
    },
  },
  watch: {
    'sauForm.attr'(val) {
      if(!val.length) {
        // 没选择
        this.checkboxStatus = 0
      } else if(val.indexOf(6) != -1) {
        // 选了合并商品
        this.checkboxStatus = 2
      } else {
        // 选了合并商品以外的
        this.checkboxStatus = 1
      }
    }
  },
  created() {
    if(!this.sauForm.attr.length) {

    } else if(this.sauForm.attr.indexOf(6) != -1) {
      // 选了合并商品
      this.checkboxStatus = 2
    } else {
      // 选了合并商品以外的
      this.checkboxStatus = 1
    }
  },
  mounted() {},
  methods: {
    async searchEntrustedManufacturer(inputValue) {
      let distManufacturer = await dictSearchTypeAndName({
        dictName: inputValue ? inputValue : "药",
        type: 12,
      });
      this.manufacturerOptions = distManufacturer.list;
    },
    /**
     * @description: 委托生产厂家选项变更
     * @param {Number} index 当前操作的属性下标
     * @param {Number} value 生产厂家ID
     */
    manufacturerChange(index, value) {
      let attrValueName = findNameByOptions(
        value,
        "id",
        this.manufacturerOptions
      );
      this.sauForm.selected[index].attrValueName = attrValueName;
    },
    // 选中 sau 属性 选项 处理数据
    selectedSauAttrItem(checked, e) {
      // 当前值
      let val = e.target.value;
      // 当前值 在sauItems 对应的对象
      let obj = this.sauItems.filter((item) => {
        return item.attrId == val;
      })[0];
      // 当前值 在sauForm.selected 中是否存在
      let index = this.sauForm.selected.findIndex((item) => item.attrId == val);
      // 不存在 且 选中
      if (index == -1 && checked) {
        this.sauForm.selected.push({
          attrId: obj.attrId,
          attrName: obj.attrName,
          attrValue: "",
          attrValueName: "",
        });
        
        // 存在 且 不选中
      } else if (index != -1 && !checked) {
        this.sauForm.selected.splice(index, 1);
      }
    },
    // 上传成功 外包装图片
    outPackageImgUploadSuccess(imgList) {
      this.sauForm.outPackageImgList = imgList;
    },

    // 上传成功 说明书图片
    directionImgUploadSuccess(imgList) {
      console.log(imgList);
      this.sauForm.directionImgList = imgList;
    },

    // 处理图片接口结构
    handleImgData(filed) {
      let flag = true;
      let arr = [];

      for (let item of this.sauForm[filed]) {
        // 防止图片未上传完成
        if (item.status == "uploading") {
          this.$message.error("图片上传中，请稍后再试...");
          flag = false;
          break;
        }

        if (item.status == "success") {
          if (item.response) {
            let { mediaName, mediaUrl, meidiaType } = item.response.data;
            arr.push({ mediaName, mediaUrl, meidiaType: 0 });
          } else {
            let { name, url } = item;
            arr.push({
              mediaName: name,
              mediaUrl: url,
              meidiaType: 0, // 媒体类型(0:图片, 1:视频)
            });
          }
          flag = true;
        }
      }
      return { flag, arr };
    },
    // 预览图片
    previewImg(list) {
      if (list.length) {
        this.previewImgList = list;
        this.previewDlgVisible = true;
      }
    },

    // 关闭批件图片预览
    closeApprovalViewer() {
      this.previewImgList = [];
      this.previewDlgVisible = false;
    },
    checkOperatePermission(skuFormRulesKey) {
      // if (this.operationType == "operate") {
      //   return this.skuFormRules[skuFormRulesKey][0].required;
      // } else {
      //   // 不是预首营时，小包装条码额外判断
      //   if (skuFormRulesKey == "smallPackageCodeList") {
      //     return this.skuForm.noSmallPackageCode;
      //   }
      //   return false;
      // }
      if (this.operationType == "detail") {
        return true;
      }
    },
  },
};
</script>

 <style lang="scss" scoped>
.component-container {
  padding-bottom: 70px;
  .sau-title {
    font-weight: 600;
    margin-bottom: 22px;
    border-bottom: 1px solid #e4e4eb;
    line-height: 50px;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
  }

  .info-suffix-icon {
    margin: 12px 0 0 5px;
    color: #77787e;
    cursor: pointer;
  }

  .marbot20 {
    margin-bottom: 20px;
  }

  .el-dialog__wrapper /deep/ .el-dialog {
    top: 50% !important;
  }
  .el-dialog__wrapper.has-footer-dlg /deep/ .el-dialog .el-dialog__body {
    height: 500px;
    overflow-y: auto;
  }
  .el-dialog__wrapper.has-footer-dlg /deep/ .el-dialog .el-dialog__footer {
    padding: 10px;
  }

  .flex-box {
    display: flex;
    color: #292933;
    font-weight: normal;
    margin: 20px 0 10px 0;
    > span:first-child {
      width: 100px;
      text-align: center;
    }
    > span:last-child {
      flex: 1;
    }
  }
  .vxe-body--row.row--current {
    .el-link.el-link--primary {
      cursor: pointer;
      color: #fff;
    }
  }
}
.sau-form /deep/ {
  display: flex;
  flex-wrap: wrap;
  .el-form-item__label {
    width: 130px !important;
    text-align: right;
    padding-right: 12px;
  }
  .tip {
    width: 100%;
    color: #bababa;
  }
  .el-form-item__content {
    margin-left: 0 !important;
  }
  .el-checkbox-group {
    width: 800px;
    background: #f0f2f5;
    padding-left: 10px;
  }
}
.image-box {
  display: flex;
  margin-bottom: 20px;
  .img-title {
    width: 130px;
    font-size: 14px;
    text-align: right;
    padding: 0 12px;
    font-weight: bold;
  }
  .img-content {
    flex: 1;
  }
}
.atrrList /deep/ {
  .el-col {
    width: 400px;
  }
  .el-col:nth-of-type(2n) {
    .el-form-item__content {
      margin-left: 20px !important;
    }
  }
}
.radio-wrap {
  display: flex;
  align-items: center;
  padding-left: 40px;
  padding-bottom: 30px;
  .lable {
    padding-right: 10px;
    font-weight: bold;
  }
}
</style>
