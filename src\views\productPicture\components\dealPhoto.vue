<template>
  <!-- 处理精修图页面 -->
  <div class="container">
    <div class="productInfo">
      <div class="tabTitle">商品信息</div>
      <div class="product-content">
        <vxe-table border highlight-hover-row auto-resize width="720px" resizable align="center" :data="productInfo" ref="table">
          <vxe-table-column field="productId" title="标准库id" min-width="120" show-header-overflow show-overflow></vxe-table-column>
          <vxe-table-column field="productCode" title="商品编码" min-width="120" show-header-overflow show-overflow></vxe-table-column>
          <vxe-table-column field="originalProductCode" title="原商品编码" min-width="120" show-header-overflow show-overflow></vxe-table-column>
          <vxe-table-column field="generalName" title="通用名" min-width="120" show-header-overflow show-overflow></vxe-table-column>
          <vxe-table-column field="spuCategoryName" title="商品大类" min-width="120" show-header-overflow show-overflow></vxe-table-column>
          <vxe-table-column field="manufacturerName" title="生产厂家" min-width="120" show-header-overflow show-overflow></vxe-table-column>
          <vxe-table-column field="spec" title="规格型号" min-width="120" show-header-overflow show-overflow></vxe-table-column>
          <vxe-table-column field="approvalNo" title="批准文号" min-width="120" show-header-overflow show-overflow></vxe-table-column>
          <vxe-table-column field="smallPackageCode" title="小包装条码" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        </vxe-table>
      </div>
    </div>
    <div class="partition-content" style="height: 20px; background: #f0f2f5; padding-left: -20px"></div>
    <div class="disposeInfo">
      <div class="tabTitle">处理信息</div>
      <div class="dispose-content">
        <el-form :model="disposeForm" ref="disposeForm" label-position="top">
          <el-row>
            <el-col
              ><drag-img
                ref="dragImg"
                :needDeal="true"
                :imgList="imgList"
                :productInfo="productInfo[0]"
                :morePicList="morePicList"
                @updateImgList="updateImgList"
              ></drag-img
            ></el-col>
            <el-col v-if="disposeForm.usePictureStatus == 2">
              <drag-img
                ref="dragBase64Img"
                :needDeal="true"
                :imgList="productPictureBase64List"
                :productInfo="productInfo[0]"
                :morePicList="[]"
              ></drag-img>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <div class="bottomBtn">
      <el-button type="" @click="handleBack" :disabled="btnDis">返回</el-button>
      <el-button type="primary" @click="submit(1)" :disabled="btnDis">提交</el-button>
      <el-button type="primary" @click="refuse" :disabled="btnDis">驳回</el-button>
    </div>
  </div>
</template>
<script>
import { getRetouchTaskReceivedDetail, auditRetouchTaskReceived } from "@/api/productPicture.js"
import dragImg from "../selfSupport/dragImg"

export default {
  name: "dealPhoto",
  components: { dragImg },
  data() {
    return {
      disposeForm: {
        fileName: "",
        productRecognizeMethod: 1,
      },
      imgList: [],
      productPictureBase64List: [],
      productInfo: [],
      btnDis: false,
      morePicList: [],
      auditOption: "",
    }
  },
  created() {
    getRetouchTaskReceivedDetail(this.$route.query.id).then((res) => {
      if (!res.retCode) {
        this.imgList = res.data ? res.data.productPictureList : []
        this.imgList.map((item) => {
          item.dealOperation = {}
          item.offlineRetouchStatus = 0
        })
        this.productInfo.push(res.data)
      } else {
        this.$message.error(res.retMsg)
      }
    })
  },
  methods: {
    updateImgList(e) {
      e.map((item, i) => {
        this.imgList[i].pictureUrl = item.pictureUrl
      })
    },
    // 驳回
    refuse() {
      this.$prompt("审核不通过原因", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPattern: /\S/,
        inputErrorMessage: "审核不通过原因必填",
      })
        .then(({ value }) => {
          this.auditOption = value
          this.submit(2)
        })
        .catch(() => {
          this.auditOption = ""
          this.$message({
            type: "info",
            message: "取消输入",
          })
        })
    },
    submit(auditStatus) {
      let obj = {}
      obj = this.$refs.dragImg.getSubmitData()
      // console.log(obj.imgList)
      let param = []
      obj.imgList.map((item) => {
        // 传普通url的链接
        let pictureUrl = ""

        pictureUrl = item.pictureUrl
        param.push({
          pictureId: item.pictureId,
          pictureName: item.pictureName,
          pictureOrdinal: item.pictureOrdinal,
          pictureBase64: item.dealOperation.dealSrc,
          pictureUrl,
          deleteStatus: item.deleteStatus,
          offlineRetouchStatus: item.offlineRetouchStatus,
          onlineRetouchStatus: item.onlineRetouchStatus || null,
        })
      })
      if (!obj.isFlag) {
        return
      }
      console.log(param)
      if (obj.unsaveList.length) {
        let msg = `<span>第</span><span class="red">${obj.unsaveList.join()}</span><span>张图片没有做任何标记</span>`
        this.$confirm(msg, "请确认:", {
          dangerouslyUseHTMLString: true,
        }).then(() => {
          this.auditRetouchTaskReceived(param, auditStatus)
        })
      } else {
        this.auditRetouchTaskReceived(param, auditStatus)
      }
    },
    // 图片审核接口
    auditRetouchTaskReceived(imgList, auditStatus) {
      this.btnDis = true
      auditRetouchTaskReceived({
        auditStatus,
        auditOption: this.auditOption,
        id: this.$route.query.id,
        productPictureList: imgList,
      }).then((res) => {
        this.btnDis = false
        if (!res.retCode) {
          this.$message.success("操作成功")
          let tab = "second"
          try {
            parent.CreateTab(this.getBackFullPath(), "待精修任务列表", true)
          } catch {
            this.$router.push({
              path: this.getHashPath(),
              query: {
                page: this.$route.query.page,
                tab: "second",
              },
            })
          }
          parent.CloseTab("../static/dist/index.html#/productPicture/dealphoto")
          this.closeTabForfk();
        } else {
          this.$message.error(res.retMsg)
        }
      })
    },
    handleBack() {
      try {
        parent.CreateTab(this.getBackFullPath(), "待精修任务列表", true)
      } catch {
        this.$router.push({
          path: this.getHashPath(),
          query: {
            page: this.$route.query.page,
            tab: "second",
          },
        })
      }
      parent.CloseTab("../static/dist/index.html#/productPicture/dealphoto")
      this.closeTabForfk()
    },
    
    getBackFullPath() {
      const from = this.$route.query.from
      if (from === "selfSupport") {
        return "../static/dist/index.html#/productPicture/selfApplyProductList"
      } else {
        return "../static/dist/index.html#/productPicture/taskToBeRefined"
      }
    },
    getHashPath() {
      const from = this.$route.query.from
      if (from === "selfSupport") {
        return "/productPicture/selfApplyProductList"
      } else {
        return "/productPicture/taskToBeRefined"
      }
    },
    closeTabForfk(){
      const from = this.$route.query.from
      if (from === "selfSupport") {
        parent.CloseTab(
            "../static/dist/index.html#/productPicture/applyProductDispose"
          );
      }
    }
  },
}
</script>
<style lang="scss" scoped>
.container {
  padding-bottom: 80px;
  .tabTitle {
    line-height: 50px;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 22px;
    padding: 0 20px;
    border-bottom: 1px solid #e4e4eb;
  }
}
.productInfo {
  .product-content {
    padding: 0 40px 20px;
  }
}
.disposeInfo {
  .dispose-content /deep/ {
    width: 1000px;
    margin: 0 auto;
    .el-dialog__body {
      height: 500px;
      overflow-y: scroll;
    }
    .el-form-item {
      display: flex;
      height: 40px;
      padding: 0 50px;
      .el-form-item__content {
        width: 100%;
        position: relative;
        .usePictureStatus-tip {
          color: #f56c6c;
          position: absolute;
          top: 25px;
        }
      }
      .btn-wrap {
        width: 100%;
        display: flex;
        justify-content: center;
      }
    }
    .el-form-item__label {
      width: 200px;
    }
  }
}
.bottomBtn {
  background: #f0f2f5;
  width: 100%;
  position: fixed;
  bottom: 0;
  padding: 15px;
  z-index: 10;
  display: flex;
  justify-content: center;
}
.checkbox-wrap /deep/ {
  height: auto !important;
  .el-form-item__content {
    line-height: normal;
    .el-checkbox-group {
      display: flex;
      flex-wrap: wrap;
      .el-checkbox {
        padding: 10px 0;
      }
    }
  }
}
.reason-wrap /deep/ {
  padding: 0 10px !important;
  .el-input__inner {
    border-top: none;
    border-left: none;
    border-right: none;
  }
}
</style>
<style lang="scss">
.red {
  color: red !important;
}
</style>

