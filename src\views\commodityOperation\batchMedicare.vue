<template>
  <div class="wrapper">
    <el-form class="search-wrapper" label-width="100px" ref="form" inline>
      <el-form-item label="申请时间">
        <el-date-picker
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          v-model="date"
          value-format="yyyy-MM-dd"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="单据编号">
        <el-input v-model="form.applyCode"></el-input>
      </el-form-item>
      <el-form-item label="申请人">
        <el-input v-model="form.applyer"></el-input>
      </el-form-item>
      <el-form-item label="申请类型">
        <el-select v-model="form.modifyType" style="width: 185px">
          <el-option label="国家医保" :value="1"> </el-option>
          <el-option label="省份医保" :value="2"> </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <div style="text-align: right;">
      <el-button type="primary" :loading="tableLoading" @click="search"
        >查询</el-button
      >
      <el-button @click="resetForm">重置</el-button>
    </div>
    <el-divider></el-divider>
    <div style="padding-bottom:20px;">
      <el-button type="primary" @click="showBatchUpdate(1)"
        >修改国家医保</el-button
      >
      <el-button type="primary" @click="showBatchUpdate(2)"
        >修改省份医保</el-button
      >
    </div>
    <vxe-table
      border
      highlight-hover-row
      resizable
      show-overflow
      size="small"
      align="center"
      max-height="500"
      :loading="tableLoading"
      :data="tableData"
      :column-config="{ minWidth: '100' }"
      ref="table"
    >
      <vxe-table-column
        type="seq"
        title="序号"
        width="60"
        min-width="auto"
      ></vxe-table-column>
      <vxe-table-column field="applyDate" title="申请时间" min-width="120">
        <template v-slot="{ row }">
          {{ dayjs(row.applyDate).format("YYYY-MM-DD HH:mm:ss") }}
        </template>
      </vxe-table-column>
      <vxe-table-column field="applyer" title="申请人"></vxe-table-column>
      <vxe-table-column field="modifyType" title="申请类型">
        <template v-slot="{ row }">
          <span v-if="row.modifyType == 1">国家医保</span>
          <span v-if="row.modifyType == 2">省份医保</span>
        </template>
      </vxe-table-column>
      <vxe-table-column field="applyCode" title="单据编号"></vxe-table-column>
      <vxe-table-column field="applyTotalNum" title="提交商品数量">
        <template v-slot="{ row }">
          <template v-if="row.originalFileUrl"
            ><el-link
              type="success"
              :underline="false"
              :href="
                `/api/base/download?filePath=${row.originalFileUrl}&fileName=${row.originalFileName}`
              "
              >{{ row.applyTotalNum }}</el-link
            ></template
          >
          <template v-else>{{ row.applyTotalNum }} </template>
        </template>
      </vxe-table-column>
      <vxe-table-column field="applyAbnormalNum" title="异常商品数量">
        <template v-slot="{ row }">
          <template v-if="row.abnormalFileUrl"
            ><el-link
              type="success"
              :underline="false"
              :href="
                `/api/base/download?filePath=${row.abnormalFileUrl}&fileName=${row.abnormalFileName}`
              "
              >{{ row.applyAbnormalNum }}</el-link
            ></template
          >
          <template v-else>{{ row.applyAbnormalNum }} </template>
        </template>
      </vxe-table-column>
      <vxe-table-column field="analysisStatus" title="解析状态">
        <template v-slot="{ row }">
          <span v-if="row.analysisStatus == 0">失败</span>
          <span v-if="row.analysisStatus == 1">成功</span>
          <span v-if="row.analysisStatus == 2">解析中</span>
        </template>
      </vxe-table-column>
      <vxe-table-column
        field="analysisEndDate"
        title="解析完成时间"
        min-width="120"
      >
        <template v-slot="{ row }">
          {{ dayjs(row.analysisEndDate).format("YYYY-MM-DD HH:mm:ss") }}
        </template>
      </vxe-table-column>
    </vxe-table>
    <vxe-pager
      border
      size="medium"
      :loading="tableLoading"
      :current-page="page.page"
      :page-size="page.size"
      :page-sizes="[20, 50, 100]"
      :total="page.total"
      :layouts="[
        'PrevPage',
        'JumpNumber',
        'NextPage',
        'FullJump',
        'Sizes',
        'Total'
      ]"
      @page-change="onPageChange"
    >
    </vxe-pager>

    <batchUpdate
      ref="batchUpdate"
      :templateUrl="
        medicareType == 1
          ? '/static/assets/excel/批量修改国家医保模板.xlsx'
          : '/static/assets/excel/批量修改省份医保模板.xlsx'
      "
      :uploading="uploading"
      @upload="onFileUpload"
    ></batchUpdate>
  </div>
</template>

<script>
import batchUpdate from "./components/batchUpdate.vue";
import api from "@/api/commodityOperation";
import { filterEmptyField } from "./helper";
import dayjs from "dayjs";

const initFormData = () => ({
  applyStartDate: "",
  applyEndDate: "",
  applyCode: "",
  applyer: "",
  modifyType: ""
});

export default {
  name: "BatchMedicare",
  components: { batchUpdate },
  data() {
    return {
      form: initFormData(),
      tableLoading: false,
      tableData: [],
      page: {
        page: 1,
        size: 20,
        total: 0
      },
      uploading: false,
      // 1是国家医保2是省份医保
      medicareType: 1
    };
  },
  computed: {
    date: {
      get() {
        return [this.form.applyStartDate, this.form.applyEndDate];
      },
      set(val) {
        [this.form.applyStartDate, this.form.applyEndDate] = val || ["", ""];
      }
    }
  },
  methods: {
    search() {
      this.tableLoading = true;
      api
        .getMedicareBatchList(
          filterEmptyField(this.form),
          this.page.page,
          this.page.size
        )
        .then(res => {
          if (res.success) {
            this.tableData = res.data.list;
            this.page.total = res.data.total;
          }
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    showBatchUpdate(medicareType) {
      this.medicareType = medicareType;
      this.$refs.batchUpdate.show();
    },
    onFileUpload(file) {
      this.uploading = true;
      const formdata = new FormData();
      formdata.append("file", file[0]);
      formdata.append("medicareType", this.medicareType);
      api
        .batchMedicare(formdata)
        .then(res => {
          this.$message(res.retMsg);
          if (res.success) {
            this.search();
            this.$refs.batchUpdate.hide();
          }
        })
        .finally(() => {
          this.uploading = false;
        });
    },
    resetForm() {
      this.form = initFormData();
    },
    onPageChange({ currentPage, pageSize }) {
      this.page.page = currentPage;
      this.page.size = pageSize;
      this.search();
    },
    dayjs
  },
  created() {
    this.search();
  }
};
</script>

<style lang="scss" scoped>
.wrapper {
  padding: 15px;
  /deep/ .el-range-separator {
    width: auto;
  }
}
</style>
