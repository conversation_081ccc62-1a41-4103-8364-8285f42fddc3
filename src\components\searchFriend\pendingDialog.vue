
<template>
  <el-dialog title="搜索友商库-查看待定图片" :before-close='closePending' :visible.sync="dlgVisible" fullscreen :modal='false'>
    <div class="page-container">
      <div class="search-form-wrap">
        <el-row type="flex">
            <el-col :lg="4" :xs="6" :sm="6" :xl="4" v-for="(item, index) in list" :key="index">
              <div class="img-box" style="width:160px;height:160px;position:relative;display:flex;justify-content:center;align-items:center">
                <img @mouseover="showBigCard(item.pictureUrl)" @mouseout="showBigCard(0)" :src="item.pictureUrl" alt="" style="max-width:160px;max-height:160px">
                <el-button v-show="!item.marked" @click.prevent="mark(index)" type="primary" size="mini" style="position:absolute;right:0;bottom:0">标记为待定图</el-button>
                <el-button v-show="item.marked" @click.prevent="mark(index)" size="mini" style="position:absolute;right:0;bottom:0">已标记</el-button>
              </div>
            </el-col>
          </el-row>
      </div>
      <div class="btn-wrap">
        <el-button @click="closeAllDialog" type="danger" size="medium">不保存返回任务页</el-button>
        <el-button @click="closePending" type="info" size="medium">返回到搜索页</el-button>
        <el-button @click="commit" type="primary" size="medium">提交到任务页</el-button>
      </div>

    </div>
    <img v-show='showCard' class="big-img" :src="imgSrc" alt="">
  </el-dialog>
</template>

<script>
export default {
  name: "",
  components: { },
  filters: {},
  props: {},
  data() {
    return {
      list:[], // 处理后的待定图片
      dlgVisible: false,
      showCard:false, //是否显示放大的图片
      imgSrc:'', //放大图片的地址
    };
  },
  computed: {},
  watch: {},
  created() {},
  methods: {
    //   打开本组件
    openDlg(e) {
      this.list = _.cloneDeep(e)
      this.list.map(item => {
          item.marked = true
      })
      this.dlgVisible = true;
    },
    // 鼠标悬浮显隐大图
    showBigCard(e) {
      this.showCard = e ? true : false
      this.imgSrc = e || ''
    },
    // 标记、取消标记
    mark(e) {
      this.list[e].marked = !this.list[e].marked
      this.$forceUpdate()
    },
    // 获取处理后的待定图片
    getNewPendingImgList() {
      let temp = []
      this.list.map(item => {
        if(item.marked) {
          item.pictureOrdinal = 0
          item.deleteStatus = 0
          temp.push(item)
        }
      })
      return temp
    },
    // 不保存返回任务页
    closeAllDialog() {
      this.dlgVisible = false
      this.$emit('closeAllDialog')
    },
    // 返回到搜索页
    closePending() {
      this.$emit('updatePendingImgList', this.getNewPendingImgList())
      this.dlgVisible = false
    },
    // 提交到任务页
    commit() {
      this.$emit('commit', this.getNewPendingImgList())
      this.dlgVisible = false
    },
  },
};
</script>

<style lang="scss" scoped>
.page-container {
  height: 100%;
  position: relative;
  /**
   * 查询表单
   */
  .search-form-wrap {
    width: 100%;
    height: 100%;
    padding: 15px;
    border-bottom: 1px dashed #e4e4eb;

    .el-row {
      flex-wrap: wrap;
      overflow: auto;
      height: calc(100% - 50px);
      padding-bottom: 50px;

      .el-col {
        margin: 10px;
        display: flex;
        justify-content: flex-end;
      }
    }
  }
}
.btn-wrap /deep/ {
  position: fixed;
  bottom: 0px;
  padding: 15px;
  left: calc(50% - 200px);
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  .el-button {
    margin-bottom: 15px;
    margin-left: 0;
    margin-right: 15px;
  }
  .vxe-toolbar {
    flex: 1;
    .vxe-button--wrapper {
      display: none;
    }
    .vxe-tools--operate {
      margin-top: 2px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
.big-img {
  pointer-events: none;
  position: absolute;
  right: 20%;
  top:20%;
  z-index: 3002;
  max-width:60%;
  max-height: 60%;
}
</style>
<style lang='scss'>
.vxe-table--tooltip-wrapper {
  z-index: 3000!important;
}
</style>
