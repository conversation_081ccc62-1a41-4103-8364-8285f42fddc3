<template>
  <div class="component-container">
    <div class="basic-info-title" @click="showForm = !showForm">扩展属性
      <i style="color:#3B95A8" :class="showForm ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
    </div>
    <!-- 说明书 -->
    <div  v-show="showForm" class="border-bottom-dashed marbot20">
      <div class="title-container">
        <h4 class="detail-title martop0">说明书</h4>
        <el-button type="primary" size="mini" @click="reMatchs" v-if="urlParam.pageType != 'detail'" :loading="matching">{{ matching? '匹配中...' : '重新匹配' }}</el-button>
      </div>
    </div>

    <el-form :disabled="formDisable" v-show="showForm" :model="form" :rules="rules" ref="form" label-width="120px">
      <el-row class="border-bottom-dashed">
        <!-- 功能主治 -->
        <el-col :span="24">
          <el-form-item
            label="功能主治"
            prop="majorFunction"
            :class="{ 'is-change': changeList.includes('majorFunction') }"
          >
            <template slot="label">
              <div class="label-with-copy-vertical">
                <span>功能主治</span>
                <p
                  @click="copyToClipboard(form.majorFunction, '功能主治')"
                  title="复制功能主治"
                  class="copy-btn-below"
                  v-if="shouldShowCopyButton"
                >
                  复制
                </p>
              </div>
            </template>
            <el-input
              type="textarea"
              placeholder="请输入内容"
              v-model="form.majorFunction"
              rows="4"
              maxlength="5000"
              show-word-limit
              :disabled="disabled"
            ></el-input>
          </el-form-item>
        </el-col>
        <!-- 用法用量 -->
        <el-col :span="24">
          <el-form-item
            label="用法用量"
            prop="usageDosage"
            :class="{ 'is-change': changeList.includes('usageDosage') }"
          >
            <template slot="label">
              <div class="label-with-copy-vertical">
                <span>用法用量</span>
                <p
                  @click="copyToClipboard(form.usageDosage, '用法用量')"
                  title="复制用法用量"
                  class="copy-btn-below"
                  v-if="shouldShowCopyButton"
                >
                  复制
                </p>
              </div>
            </template>
            <el-input
              type="textarea"
              placeholder="请输入内容"
              v-model="form.usageDosage"
              rows="4"
              maxlength="5000"
              show-word-limit
              :disabled="disabled"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import {
  getExtendInfo,
  getExtendByGeneralName,
  reMatchExtend
} from "@/api/product";

export default {
  name: "",
  components: {},
  filters: {},
  props: {
    modify: {
      type: String,
      default: ''
    },
    // 新版本的审批流
    newAudit: {
      type: Boolean,
      default: false,
    },
    // 商品修改改变字段
    changeList: {
      type: Array,
      required: false,
      default: () => [],
    },
    formDisable:{
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      showForm: true,
      isInit: false, // 初始化
      originalObj: {}, // 原数据
      disabled: false,
      form: {
        majorFunction: '', // 主治功能
        usageDosage: "", // 用法用量
      },
      symptomsTags: [], // 选中症状
      rules: {
        majorFunction: [
          { message: "请输入主治功能", trigger: "blur" },
        ],
        usageDosage: [
          { message: "请输入用法用量", trigger: "blur" },
        ]
      },
      matching: false, // 匹配中
      spec: this.$store.state.product.spec,  //规格
      generalName: this.$store.state.product.generalName //通用名
    };
  },
  computed: {
    // 商品分类
    spuCategory() {
      return this.$store.getters.spuCategory;
    },

    // 操作类型
    operationType() {
      return this.$store.getters.operationType;
    },

    // 路由参数
    urlParam() {
      return this.$route.query;
    },
    firstCategory() {
      return this.$store.state.product.firstCategory;
    },
    // 新增：复制按钮显示条件
    shouldShowCopyButton() {
      // 主要条件：operationType为detail或edit
      if (this.operationType === 'detail' || this.operationType === 'edit') {
        return true;
      }
      
      // 备用条件：根据URL参数判断
      const urlParam = this.$route.query;
      
      // 如果是纠错类型或上架类型，显示复制按钮（这些通常是detail模式）
      if (urlParam.type === 'coorection' || urlParam.type === 'shelves') {
        return true;
      }
      
      // 如果URL中没有明确的新增标识，且有productCode，可能是编辑或详情模式
      if (urlParam.productCode && !urlParam.isAdd) {
        return true;
      }

      if(urlParam.procKey === '新品上报流程'){
        return true;
      }
      
      return false;
    }
  },
  watch: {
    firstCategory(val) {
      if (val == 100002) {
        Object.keys(this.rules).forEach((item) => {
          if (
            item == "medicationMethod" ||
            item == "medicationFrequency" ||
            item == "medicationDosage" ||
            item == "miniUnit"
          ) {
            this.rules[item][0].required = true;
          }
        });
      } else {
        Object.keys(this.rules).forEach((item) => {
          if (
            item == "medicationMethod" ||
            item == "medicationFrequency" ||
            item == "medicationDosage" ||
            item == "miniUnit"
          ) {
            this.rules[item][0].required = false;
          }
        });
      }
      this.$refs.form.clearValidate();
    },
    form: {
      handler: function (val) {
        if (this.operationType == "detail") return;
        let result = _.isEqual(val, this.originalObj.form);
        this.$bus.$emit("productChange", !result);
        if (!result && !this.loading) {
          this.findDiffExtendedAttr(val);
        }
      },
      deep: true,
    },
    // 修改商品分类
    spuCategory(newValue, oldValue) {
      // 1.4.2V 修改商品分类 不清空扩展属性值
      // 初始化完成后 常规切换分类 => 清空
      // if (
      //   this.isInit &&
      //   (this.operationType == "add" || this.operationType == "draft")
      // ) {
      //   this.clearExtendedAttr();
      // }
    },
    // 操作类型
    operationType(newValue) {
      // console.log(newValue, "newValue");
      if (
        newValue == "auditLevel1" ||
        newValue == "auditLevel2" ||
        newValue == "auditLevel3" ||
        newValue == "RejectEdit"
      ) {
        this.getData("/api/spu/get/spuExtend/spuCode", {
          applyCode: this.urlParam.applyCode ? this.urlParam.applyCode : "",
          spuCode: this.urlParam.productCode ? this.urlParam.productCode : "",
        });
      }

      // 二审 扩展属性不能编辑
      if (this.operationType == "auditLevel2") {
        this.disabled = true;
      }
    },
  },
  created() {

    // 进入页面spu 加载完成触发
    this.$bus.$on("spuLoading", (loading) => {
      // console.log(loading, "spuLoading加载完成");
      if (loading) {
        this.isInit = true;
      }
    });

    // 监听 复用事件 1.4.2V 复用不清空扩展属性值
    // this.$bus.$on("reuseEvent", data => {
    //  this.clearExtendedAttr();
    // });
    if (this.operationType == "add" || this.operationType == "draft") {
      this.originalObj = Object.assign(
        {},
        {
          form: _.cloneDeep(this.form),
          drugsGuideTableData: _.cloneDeep(this.drugsGuideTableData),
          symptomsTags: _.cloneDeep(this.symptomsTags),
        }
      );
    }

    // 获取扩展属性
    // console.log(this.operationType, this.urlParam);
    if (this.operationType == "draft") {
      this.getData("/api/spu/get/spuExtend/spuCode", {
        applyCode: this.urlParam.applyCode ? this.urlParam.applyCode : "",
      });
    }
    if (this.operationType == "edit" || this.operationType == "operate") {
      this.getData("/api/spu/get/spuExtend/spuCode", {
        spuCode: this.urlParam.productCode ? this.urlParam.productCode : "",
      });
      // 预首营 扩展属性不能编辑
      if (this.operationType == "operate") {
        this.disabled = true;
      }
    }

    // 详情 扩展属性不能编辑
    if (
      this.operationType == "detail" ||
      this.operationType == "auditLevel1" ||
      this.operationType == "auditLevel2" ||
      this.operationType == "auditLevel3" ||
      this.operationType == "RejectEdit" ||
      this.newAudit
    ) {
      this.getData("/api/spu/get/spuExtend/businessCode", this.urlParam);
      if (this.operationType != "RejectEdit" && this.modify != 1) {
        this.disabled = true;
      }
    }
  },
  mounted() {
    this.$bus.$on("generalNameChange", (generalName) => {
      if (generalName) {
        this.getGeneralNameData(generalName);
      }
    });
    this.$bus.$on('reMatchBySpec', (data) => {
      this.spec = data 
    })
    this.$bus.$on('reMatchByGaeneralName', (data) => {
      this.generalName = data 
    })
  },
  methods: {
      // 重新匹配
      async reMatchs(){
      // console.log('====================================');
      // console.log(this.generalName, this.spec);
      // console.log(this.changeList, 'changeList');
      // console.log('====================================');
      if(!this.generalName || !this.spec) {
        this.$message.error("请输入通用名及规格")
        return false
      }
      this.matching = true
      const params = {
        generalName: this.generalName,
        spec: this.spec,
      }
      try{
        const res = await reMatchExtend(params)
        if(res.retCode === 0){
          this.matching = false
          if(res.data){
            this.$set(this.form, 'majorFunction', res.data.majorFunction)
            this.$set(this.form, 'usageDosage', res.data.usageDosage)
          }else {
            this.$message.error("未找到匹配数据")
          }
        }else {
          this.matching = false
          this.$message.error(res.retMsg)
        }
      }catch(err){
        console.log(err, 'err');
        this.matching = false
      }
    },
    // 根据商品通用名获取扩展属性数据
    async getGeneralNameData(spuCode) {
      let res = await getExtendByGeneralName({ spuCode });
      if (res.data) {
        this.handleExtendData(res.data)
      }
    },
    // 获取扩展属性数据
    getData(url, paramsObj) {
      this.loading = true;
      getExtendInfo(url, paramsObj).then((res) => {
        if (res) {
          this.loading = false;
          this.handleExtendData(res);
        }
      });
    },

    // 暴露给父组件 所有数据 可通过ref
    getExtendData(isDraft) {
      let isValid = true;
      if (!isDraft) {
        this.$refs.form.validate((valid) => {
          if (!valid) {
            isValid = false;
            this.$message.warning("请完善扩展属性说明书信息");
          }
        });
      }
      if (!isValid) return false;
      
      // 处理选中的治疗症状提交数据
      let symptomsArr = this.symptomsTags.map((item) => {
        let { department, diseases, symptom, treatId } = item;
        return { department, diseases, symptom, treatId };
      });
      const allData = Object.assign({}, this.form, {
        productExtendGuidanceList: this.drugsGuideTableData,
        productExtendTreatList: symptomsArr,
      });

      // console.log(allData, "---扩展数据全部提交数据");
      allData.changeList = this.changeList;
      return allData;
    },

    // 处理props接收的数据
    handleExtendData(data) {
      let {
        productExtendGuidanceList,
        productExtendTreatList,
        usageDosage,
        majorFunction
      } = data;

      let form = {
        usageDosage,
        majorFunction
      };
      let drugsGuideTableData = [];
      if (productExtendGuidanceList.length) {
        drugsGuideTableData = productExtendGuidanceList.map((item) => {
          let {
            treatId,
            treatName,
            treatCourse, //疗程
            usageDay, //使用天数
            usageDosage, //用法用量
            usageNote, //使用注意
            takeCycle, //服用周期
            forgetTake, //忘记服用
            overdoseTake, //过量服用
            usageFrequency, //usageFrequency
            usageMethod, //使用方法
            precautionsTaboos, //注意事项和禁忌
            pharmacistSuggestions, //药师建议
            lifeAdvice, //生活建议
          } = item;
          return {
            treatId,
            treatName,
            treatCourse: treatCourse ? treatCourse : "", //疗程
            usageDay: usageDay ? usageDay : "", //使用天数
            usageDosage, //用法用量
            usageNote, //使用注意
            takeCycle, //服用周期
            forgetTake, //忘记服用
            overdoseTake, //过量服用
            usageFrequency, //usageFrequency
            usageMethod, //使用方法
            precautionsTaboos, //注意事项和禁忌
            pharmacistSuggestions, //药师建议
            lifeAdvice, //生活建议
          };
        });
      }

      // 处理选中症状字段
      let symptomsTags = productExtendTreatList.map((item) => {
        let treatName = `${item.symptomName}-${item.diseasesName}-${item.departmentName}`;
        let { treatId, department, diseases, symptom } = item;
        return {
          treatId,
          treatName,
          department,
          diseases,
          symptom,
        };
      });

      if (
        this.operationType == "auditLevel1" ||
        this.operationType == "auditLevel2" ||
        this.operationType == "auditLevel3" ||
        this.operationType == "operate" ||
        this.operationType == "RejectEdit" ||
        this.operationType == "edit"
      ) {
        // 处理后的数据 拷贝一份为原数据
        this.originalObj = Object.assign(
          {},
          {
            form,
            drugsGuideTableData,
            symptomsTags,
          }
        );
      }

      this.form = _.cloneDeep(form);
      this.drugsGuideTableData = _.cloneDeep(drugsGuideTableData);
      this.symptomsTags = _.cloneDeep(symptomsTags);
      // 备份一份初始化完成时的数据
      this._initializedForm = _.cloneDeep(form);
    },

    // 父组件商品分类切换 暴露给外层 操作内部数据的方法 使用 this.$refs.sau.$children.clearExtendedAttr()
    clearExtendedAttr() {
      this.$refs["form"].resetFields();
      // 手动清空
      this.form.medicationFrequency = "";
      this.form.miniUnit = "";
      this.symptomsTags = [];
      this.drugsGuideTableData = [];
      this.currentRow = null;

      this.originalObj = Object.assign(
        {},
        {
          form: _.cloneDeep(this.form),
          drugsGuideTableData: _.cloneDeep(this.drugsGuideTableData),
          symptomsTags: _.cloneDeep(this.symptomsTags),
        }
      );
    },

    // 对象-数组包含关系
    objComparator(obj, original) {
      let currObj = _.cloneDeep(obj);
      let originalArr = _.cloneDeep(original);
      let resultArr = [];
      delete currObj._XID;
      originalArr.forEach((item) => {
        delete item._XID;
        let res = _.isEqual(currObj, item);
        resultArr.push(res);
      });
      // return resultArr.includes(true) ? "重复" : "不重复";
      // console.log(resultArr, "---对比结果");
      return resultArr;
    },
    /**
     * @description: 查找扩展属性修改变动的key
     * @param {object} newValue 扩展属性 this.form 对象
     * @return:
     */
    findDiffExtendedAttr(newValue) {
      // console.log(newValue, this._initializedForm)
      if (
        this.operationType == "edit" ||
        this.operationType == "operate" ||
        // 审核及审核驳回修改时需要判断是否为修改商品
        (this.operationType == "auditLevel1" &&
          this.$route.query.approvalProcess == 1) ||
        (this.operationType == "auditLevel2" &&
          this.$route.query.approvalProcess == 1) ||
        // 判断是否为商品上架状态修改
        (this.operationType == "auditLevel2" &&
          this.$route.query.type == "shelves") ||
        (this.operationType == "RejectEdit" &&
          this.$route.query.approvalProcess == 1)
      ) {
        for (let key in newValue) {
          // initializedSpuModel 初始化后备份的SPU数据对象
          let res = _.isEqual(newValue[key], this._initializedForm[key]);
          if (!res && !this.changeList.includes(key)) {
            this.changeList.push(key);
          }
        }
      }
    },
     // 复制到剪贴板
    copyToClipboard(text, fieldName) {
      if (!text || text.trim() === '') {
        this.$message.warning(`${fieldName}内容为空，无法复制`)
        return
      }

      // 创建临时文本域元素
      const textarea = document.createElement('textarea')
      textarea.value = text
      document.body.appendChild(textarea)
      textarea.select()

      try {
        // 执行复制命令
        const successful = document.execCommand('copy')
        if (successful) {
          this.$message.success(`${fieldName}内容已复制到剪贴板`)
        } else {
          this.$message.error('复制失败，请手动复制')
        }
      } catch (err) {
        this.$message.error('复制失败，请手动复制')
      } finally {
        // 清理临时元素
        document.body.removeChild(textarea)
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.component-container {
  padding-bottom: 80px;
  .basic-info-title {
    font-weight: 600;
    margin-bottom: 22px;
    border-bottom: 1px solid #e4e4eb;
    line-height: 50px;
    padding-left: 20px;
  }
  .el-form-item {
    &.nMr {
      /deep/ .el-form-item__content {
        margin-left: 0 !important;
        .el-select {
          width: 100% !important;
        }
      }
    }
    &.width100 {
      /deep/ .el-select {
        width: 100% !important;
      }
      /deep/ .el-input {
        width: 100% !important;
      }
    }
    /deep/ {
      .el-form-item__label {
        color: #292933;
        font-weight: normal;
      }
    }
  }
  .is-change {
    /deep/ {
      // 调整修改项lable样式
      .el-form-item__label {
        color: #f56c6c;
        font-weight: bold;
      }
    }
  }
  .border-bottom-dashed {
    border-bottom: 1px dashed #e4e4eb;
  }

  .title-container{
    display: flex;
    justify-content: start;
    align-items: center;
  }

  .detail-title {
    margin: 20px;
    font-size: 16px;
    color: #292933;
  }

  .martop0 {
    margin-top: 0;
  }
  .marbot20 {
    margin-bottom: 20px;
  }
}

// 自定义label样式 - 垂直布局
.label-with-copy-vertical {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;

  > span {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
  }

  .copy-btn-below {
    // 确保按钮不影响表单验证样式
    padding: 0 !important;
    margin: 0 !important;
    border: none !important;
    background: none !important;
    line-height: 1 !important;
    cursor: pointer;
    color: #4A95A9;
    &:focus {
      outline: none;
    }
  }
}

// 修复必填标识与label文本对齐问题
.el-form-item {
  // 针对使用自定义label模板的表单项
  &.is-required /deep/ .el-form-item__label {
    // 对于包含自定义label模板的情况，隐藏默认必填标识
    &:has(.label-with-copy-vertical)::before {
      display: none !important;
    }

    // 在自定义label的span前添加必填标识
    .label-with-copy-vertical > span::before {
      content: "*";
      color: #F56C6C;
      margin-right: 4px;
      display: inline-block;
      vertical-align: middle;
      font-weight: normal;
    }

    // 确保默认必填标识正确显示（针对普通label）
    &::before {
      display: inline-block !important;
      vertical-align: middle !important;
      margin-right: 4px !important;
      line-height: 1 !important;
    }
  }
}

// 兼容性更好的解决方案：为自定义label添加特定类名
.el-form-item.has-custom-label.is-required /deep/ .el-form-item__label {
  &::before {
    display: none !important;
  }
}
</style>