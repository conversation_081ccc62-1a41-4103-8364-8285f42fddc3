
<template>
  <div class="page-container">
    <!-- 查询表单 -->
    <div class="search-form-wrap">
      <el-form :model="model" ref="refSearchForm">
        <el-row>审核流程</el-row>
        <el-row type="flex">
          <!-- 工作流 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="工作流">
              <el-select
                filterable
                v-model="searchFormData.procKey"
                placeholder="请选择"
                @change="changeProcKey"
              >
                <el-option label="全部" :value="null"></el-option>
                <el-option
                  v-for="item in procKeyList"
                  :key="item.procKey"
                  :label="item.procKeyName"
                  :value="item.procKey"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 审核结果 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="审核结果">
              <el-select
              filterable
                v-model="searchFormData.approvalStatus"
                placeholder="请选择"
              >
               <el-option label="全部" :value="null"></el-option>
                <el-option label="审核通过" :value="1"></el-option>
                <el-option label="审核驳回" :value="2"></el-option>
                <el-option label="审核中" :value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 是否领取 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="是否领取">
              <el-select
                v-model="searchFormData.claimCheck"
                placeholder="请选择"
              >
               <el-option label="全部" :value="null"></el-option>
                <el-option label="已被领取" :value="2"></el-option>
                <el-option label="未被领取" :value="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 批量类型 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="searchFormData.procKey === 'meBatchModify'">
            <el-form-item label="批量类型">
              <el-select v-model="searchFormData.batchType" placeholder="请选择">
                <el-option label="全部" :value="null"></el-option>
                <el-option label="基础信息" :value="1"></el-option>
                <el-option label="sku信息" :value="25"></el-option>
                <el-option label="税率" :value="14"></el-option>
                <el-option label="标签信息" :value="24"></el-option>
                <el-option label="扩展信息" :value="2"></el-option>
                <el-option label="用药指导" :value="6"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-show="!isFold">发起人</el-row>
        <el-row v-show="!isFold">
          <!-- 发起人 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="发起人">
              <el-input v-model="searchFormData.taskCreater"></el-input>
            </el-form-item>
          </el-col>
          <!-- 所属机构 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="所属机构" prop="mechanism">
              <div class="pop-tip" v-show="showTip">{{nameStr}}</div>
              <el-input @mouseover.native="showTip1"  @mouseleave.native="hideTip" v-model="nameStr"
                @click.native="showTree = !showTree; isShowPeople = false" readonly clearable>
                <el-button slot="append" :icon="showTree ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
                @click.stop="showTree = !showTree; isShowPeople = false"></el-button>
              </el-input>
              <el-tree v-if="showTree" class="tree-box" :data="deptList" :props="defaultProps" :default-expanded-keys="['1']" node-key="lev">
                <span slot-scope="{ data }">
                  <span class="line mr10">{{ data.dptName }}</span>
                  <el-link v-if="+data.lev > 1" type="primary" class="lh40" @click="showPeople(data.dptCode)">选人</el-link>
                </span>
              </el-tree>
              <div class="people-box" v-if="isShowPeople">
                <el-select
                  ref="autoSelect"
                  multiple
                  collapse-tags
                  v-model="currentPeopleList"
                  placeholder="请选择"
                  @remove-tag="removeTag1"
                  @change="changeSelect1"
                >
                  <el-option label="全选" value="全选" @click.native="selectAll1"></el-option>
                  <el-option v-for="item in peopleList" :key="item.oaId" :label="item.realname" :value="item.oaId"></el-option>
                </el-select>
                <div style="margin-top:130px;text-align:center;">请展开下拉框选人</div>
              </div>
            </el-form-item>
          </el-col>
          <!-- 发起时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="发起时间">
              <el-date-picker
                v-model="searchFormData.createTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <!-- 角色类型 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="角色类型">
              <el-select
              filterable
                v-model="searchFormData.taskCreateRoleCode"
                placeholder="请选择"
              >
                <el-option label="全部" :value="null"></el-option>
                <el-option
                  v-for="item in roleList"
                  :key="item.roleCode"
                  :label="item.roleName"
                  :value="item.roleCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-show="!isFold">任务</el-row>
        <el-row v-show="!isFold">
          <!-- 任务来源 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="任务来源">
              <el-select
              filterable
                v-model="searchFormData.source"
                placeholder="请选择"
              >
               <el-option label="全部" :value="null"></el-option>
                <el-option
                  v-for="item in sourceList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 最后更新时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="最后更新时间">
              <el-date-picker
                v-model="searchFormData.updateTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <!-- 通用名 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="通用名">
              <el-input v-model="searchFormData.generalName"></el-input>
            </el-form-item>
          </el-col>
          <!-- 批准文号 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="批准文号">
              <el-input v-model="searchFormData.approvalNo"></el-input>
            </el-form-item>
          </el-col>
          <!-- 生产厂家 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="生产厂家">
              <el-input v-model="searchFormData.manufacturer"></el-input>
            </el-form-item>
          </el-col>
           <!-- 版本号 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="版本号">
              <el-input v-model="searchFormData.pictureVersion"></el-input>
            </el-form-item>
          </el-col>
          <!-- 规格 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="规格">
              <el-input v-model="searchFormData.spec"></el-input>
            </el-form-item>
          </el-col>
          <!-- 小包装条码 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="小包装条码">
              <el-input v-model="searchFormData.smallPackageCode"></el-input>
            </el-form-item>
          </el-col>
          <!-- 商品编码 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="商品编码">
              <el-input v-model="searchFormData.productCode"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <!-- 按钮 -->
          <el-col :span="6" :offset="18">
            <el-button type="primary" size="medium" @click="isFold = !isFold"
              >展开/收起</el-button
            >
            <el-button type="primary" size="medium" @click="exportList"
              >导出</el-button
            >
            <el-button type="primary" size="medium" @click="btnSearchClick(1)"
              >查询</el-button
            >
            <el-button size="medium" @click="btnResetClick">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- table -->
    <div class="table-wrap">
      <vxe-table
        border
        highlight-hover-row
        resizable
        height="100%"
        auto-resize
        size="small"
        align="center"
        :tooltip-config="{ enterable: false }"
        :loading="tableLoading"
        :data="tableData"
        :seq-config="{ startIndex: (searchFormData.pageNum - 1) * searchFormData.pageSize }"
        ref="refVxeTable"
      >
        <vxe-table-column
          type="seq"
          title="序号"
          width="60"
          show-header-overflow
          show-overflow
          fixed="left"
        ></vxe-table-column>
        <vxe-table-column
          field="procKey"
          title="工作流"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="taskNodeName"
          title="当前审核节点"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="claimCheck"
          title="是否领取"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="approvalStatus"
          title="审核结果"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="startTimeStart"
          title="发起时间"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="taskCreater"
          title="发起人"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="taskPostCode"
          title="所属机构"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="taskCreaterRoleCode"
          title="角色类型"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="source"
          title="任务来源"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="updateTimeStart"
          title="最后更新时间"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="sauCode"
          title="商品编码"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="generalName"
          title="通用名"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="approvalNo"
          title="批准文号"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="preOperateStatus"
          title="是否自营"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="pictureVersion"
          title="版本号"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
         <vxe-table-column
          field="spec"
          title="规格"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="manufacturerName"
          title="厂家"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="smallPackageCode"
          title="小包装条码"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          title="操作"
          width="120"
          show-header-overflow
          show-overflow
          fixed="right"
        >
          <template v-slot="{ row }">
            <span v-if="row.procKey === '商品新增流程'">
              <el-link
                :underline="false"
                type="primary"
                @click.stop="openDetail(row)"
                >查看</el-link
              >
            </span>
            <span v-else>-</span>
          </template>
        </vxe-table-column>
      </vxe-table>
    </div>

    <!-- 分页 -->
    <el-row class="custom-pagination-wrap">
      <el-col>
        <el-pagination
          background
          :current-page.sync="searchFormData.pageNum"
          :page-sizes="[20, 50, 100, 200]"
          :page-size.sync="searchFormData.pageSize"
          :total="total"
          layout="prev, pager, next, jumper,total, sizes"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { userApprovalProcessList, userRoleList, taskSourceList, getMyFinishProcessList, exportMyFinishProcessExportList,
getOrganizeList, getEmployee } from "@/api/workManage"
let setTime = 0
let tt = false
export default {
  name: "",
  components: {},
  filters: {},
  props: {},
  data() {
    return {
      showTip: false,
      isShowPeople: false,
      peopleList:[],
      currentPeopleList:[],
      nameList:[],
      showTree: false,
      isFold:true,
      searchFormData: {
        procKey:null,//工作流
        batchType: null, //批量类型
        approvalStatus:null,//审核结果
        claimCheck:null,//是否领取
        createTime:[],//发起时间
        taskCreater:'',//发起人
        taskPostCodeList:[],//所属机构 入参
        taskCreateRoleCode:null,//角色类型
        source:null,//任务来源
        updateTime:[],//最后更新时间
        generalName:'',//通用名
        approvalNo:'',//批准文号
        manufacturer:'',//生产厂家
        pictureVersion:'',//版本号
        // nodeKey:'',//当前审核节点
        spec:'',//规格
        smallPackageCode:'',//小包装条码
        productCode:'',//商品编码
        pageNum: 1,
        pageSize: 20,
      },
      procKeyList:[], //工作流列表
      roleList:[],//角色类型列表
      sourceList:[],//任务来源列表
      deptList:[],//所属机构列表
      tableLoading: false,
      tableData: [],
      total: 0
    };
  },
  computed: {
    nameStr() {
      return this.nameList.join()
    }
  },
  watch: {},
  created() {
    this.getUserApprovalProcessList()
    this.getUserRoleList()
    this.getTaskSourceList()
    this.getDeptList()
    this.btnSearchClick();
  },
  methods: {
    // 修改工作流查询条件
    changeProcKey(e) {
      if (e !== "meBatchModify") {
        this.searchFormData.batchType = null
      }
    },
    showTip1() {
      tt = true
      clearTimeout(setTime)
      this.showTip = true
    },
    hideTip() {
      this.showTip = false
      /* tt = false
      setTime = setTimeout(() => {
        if (!tt) {
          this.showTip = false
          tt = false
        }
      }, 1500); */
    },
    async showPeople(e) {
      try {
        this.currentPeopleList = this.currentPeopleList.filter(item => {
          return item !== "全选"
        })
        this.isShowPeople = true
        const res = await getEmployee(e)
        this.peopleList = res.data
        this.$refs.autoSelect.toggleMenu()
        let temp = []
        this.peopleList.map((item, index) => {
          if (this.searchFormData.taskPostCodeList.indexOf(item.oaId) !== -1) {
            temp.push(index)
          }
        })
        this.currentPeopleList = []
        temp.map(item => {
          this.currentPeopleList.push(this.peopleList[item].oaId)
        })
        if (this.currentPeopleList.length === this.peopleList.length) {
          this.currentPeopleList.unshift("全选")
        }
      } catch (error) {
        console.log(error);
      }
    },
    // 获取工作流列表
    async getUserApprovalProcessList (){
      try{
        const res = await userApprovalProcessList()
        if(res.retCode === 0) {
          this.procKeyList = res.data
        }
      }
      catch(error) {
        console.log(error);
      }
    },
    // 获取角色类型列表
    async getUserRoleList() {
      try{
        const res = await userRoleList()
        if(res.retCode === 0) {
          this.roleList = res.data
        }
      }
      catch(error) {
        console.log(error);
      }
    },
    // 获取任务来源列表
    async getTaskSourceList() {
      try{
        const res = await taskSourceList()
        if(res.retCode === 0) {
          this.sourceList = res.data
        }
      }
      catch(error) {
        console.log(error);
      }
    },
    changeSelect1(val) {
      // 追加所有已经选中的人
      let temp = []
      let deleteList = []
      let deleteNameList = []
      let tempNameList = []
      this.peopleList.map(item => {
        if (this.currentPeopleList.indexOf(item.oaId) !== -1) {
          temp.push(item.oaId)
          tempNameList.push(item.realname)
        } else {
          deleteList.push(item.oaId)
          deleteNameList.push(item.realname)
        }
      })
      temp = temp.concat(this.searchFormData.taskPostCodeList)
      this.searchFormData.taskPostCodeList = Array.from(new Set(temp))
      tempNameList = tempNameList.concat(this.nameList)
      this.nameList = Array.from(new Set(tempNameList))
      let deleteIndexList = []
      let deleteNameIndexList = []
      deleteList.map(item => {
        if (this.searchFormData.taskPostCodeList.indexOf(item) !== -1) {
          deleteIndexList.push(this.searchFormData.taskPostCodeList.indexOf(item))
        }
      })
      for (let i = deleteIndexList.length - 1; i >= 0; i--) {
        this.searchFormData.taskPostCodeList.splice(deleteIndexList[i], 1)
      }
      deleteNameList.map(item => {
        if (this.nameList.indexOf(item) !== -1) {
          deleteNameIndexList.push(this.nameList.indexOf(item))
        }
      })
      for (let i = deleteNameIndexList.length - 1; i >= 0; i--) {
        this.nameList.splice(deleteNameIndexList[i], 1)
      }
      // 单点到全选
      if (!val.includes("全选") && val.length === this.peopleList.length) {
        this.currentPeopleList.unshift("全选")
      } else if (val.includes("全选") && val.length - 1 < this.peopleList.length) { //全选点到不全选
        // 当前下拉框移除全选
        this.currentPeopleList = this.currentPeopleList.filter(item => {
          return item !== "全选"
        })
      }
    },
    // 下拉框移除值
    removeTag1(val) {
      if (val === "全选") {
        let temp = []
        this.peopleList.map((item, index) => {
          if (this.searchFormData.taskPostCodeList.indexOf(item.oaId) !== -1) {
            temp.push(index)
          }
        })
        for (let i = temp.length; index >= 0; i--) {
          this.searchFormData.taskPostCodeList.splice(temp[i], 1)
          this.nameList.splice(temp[i], 1)
        }
        this.currentPeopleList = []
      } else {
        this.searchFormData.taskPostCodeList.map((item, i) => {
          if (item === val) {
            this.searchFormData.taskPostCodeList.splice(i, 1)
            this.nameList.splice(i, 1)
          }
        })
      }
    },
    selectAll1() {
      let temp = []
      // 全选
      if (this.currentPeopleList.length < this.peopleList.length) {
        this.currentPeopleList = []
        this.searchFormData.taskPostCodeList = []
        this.peopleList.map(item => {
          temp.push(item.realname)
          this.currentPeopleList.push(item.oaId)
          this.searchFormData.taskPostCodeList.push(item.oaId)
        })
        this.currentPeopleList.unshift("全选")
        this.searchFormData.taskPostCodeList = Array.from(new Set(this.searchFormData.taskPostCodeList))
        this.nameList = this.nameList.concat(temp)
        this.nameList = Array.from(new Set(this.nameList))
        // this.searchFormData.taskPostCodeList.unshift("全选")
      } else {
        //取消全选
        this.currentPeopleList.shift()
        this.peopleList.map(item => {
          for (let index = this.currentPeopleList.length -1; index >= 0; index--) {
            if (this.currentPeopleList[index] === item.oaId) {
              temp.push(item.oaId)
              this.currentPeopleList.splice(index, 1)
              this.nameList.splice(index, 1)
            }
          }
        })
        let deleteIndexList = []
        temp.map(item => {
          if (this.searchFormData.taskPostCodeList.indexOf(item) !== -1) {
            deleteIndexList.push(this.searchFormData.taskPostCodeList.indexOf(item))
          }
        })
        for (let i = deleteIndexList.length - 1; i >= 0; i--) {
          this.searchFormData.taskPostCodeList.splice(deleteIndexList[i], 1)
        }
      }
    },
    // 获取所属机构列表
    async getDeptList() {
      try{
        const res = await getOrganizeList()
        this.deptList = res.data
      }
      catch(error) {
        console.log(error);
      }
    },
    async getListData() {
      let param = Object.assign({}, this.searchFormData)
      if(param.createTime && param.createTime.length) {
        param.startTimeStart = param.createTime[0]
        param.startTimeEnd = param.createTime[1]
      }
      if(param.updateTime && param.updateTime.length) {
        param.updateTimeStart = param.updateTime[0]
        param.updateTimeEnd = param.updateTime[1]
      }
      if (param.taskPostCodeList[0] === '全选') {
        param.taskPostCodeList.shift()
      }
      delete param.createTime
      delete param.updateTime
      console.log(param)
      try{
        const res = await getMyFinishProcessList(param)
        if(res.retCode === 0) {
          this.tableData = res.data.list
          this.total = res.data.total
        }
      }
       catch(error) {
        console.log(error);
      }
    },
    // 导出
    exportList() {
      let param = Object.assign({}, this.searchFormData)
      if(param.createTime.length) {
        param.startTimeStart = param.createTime[0]
        param.startTimeEnd = param.createTime[1]
      }
      if(param.updateTime.length) {
        param.updateTimeStart = param.updateTime[0]
        param.updateTimeEnd = param.updateTime[1]
      }
      if (param.taskPostCodeList[0] === '全选') {
        param.taskPostCodeList.shift()
      }
      delete param.createTime
      delete param.updateTime
      delete param.pageNum
      delete param.pageSize
      console.log(param)
      exportMyFinishProcessExportList(param)
        .then((res) => {
          if (!res.retCode) {
            this.$message.success(res.retMsg)
          } else {
            this.$message({
              showClose: true,
              type: "error",
              message: res.retMsg,
            })

          }
        })
        .catch((err) => {
          this.$message({
            showClose: true,
            type: "error",
            message: "导出发生错误，请重试",
          });
        });
    },
    /**
     * 查询按钮点击
     */
    btnSearchClick(e) {
      if(e) {
        this.searchFormData.pageNum = 1
      }
      this.getListData();
    },
    /**
     * 重置
     */
    btnResetClick() {
      this.nameList = []
      this.currentPeopleList = []
      this.searchFormData = {
        procKey:null,//工作流
        batchType: null, //批量类型
        approvalStatus:null,//审核结果
        claimCheck:null,//是否领取
        createTime:[],//发起时间
        taskCreater:'',//发起人
        taskPostCodeList:[],//所属机构 入参
        taskCreateRoleCode:null,//角色类型
        source:null,//任务来源
        updateTime:[],//最后更新时间
        generalName:'',//通用名
        approvalNo:'',//批准文号
        manufacturer:'',//生产厂家
        pictureVersion:'',//版本号
        // nodeKey:'',//当前审核节点
        spec:'',//规格
        smallPackageCode:'',//小包装条码
        productCode:'',//商品编码
        pageNum: 1,
        pageSize: 20,
      };
      this.getListData();
    },
    // 查看详情
    openDetail(e) {
      console.log(e)
      let { procDefId, procInstId, productCode, applyCode, taskPostCode, source } = e;
      try {
        parent.CreateTab(
          "../static/dist/index.html#/workManage/addGoodsDetail?procInstId=" +
            procInstId +
            "&procDefId=" +
            procDefId +
            "&taskPostCode=" +
            taskPostCode +
            "&source=" +
            source +
            "&applyCode=" +
            applyCode +
            "&productType=" +
            2 +
            "&productCode=" +
            productCode +
            "&detailType=self" +
            "&pageType=detail",
          "任务详情"
        );
      } catch (error) {
        this.$router.push({
          path: "/workManage/addGoodsDetail",
          query: {
            procInstId,
            procDefId,
            applyCode,
            productType:2,
            productCode,
            detailType: "self",
            pageType: "detail",
            taskPostCode,
            source
          },
        });
      }
    },
    /**
     * pageSize 改变事件
     */
    handleSizeChange(pageSize) {
      this.searchFormData.pageNum = 1;
      this.searchFormData.pageSize = pageSize;
      this.getListData();
    },

    /**
     * pageNum 改变事件
     */
    handleCurrentChange(currentPage) {
      console.info(currentPage);
      this.searchFormData.pageNum = currentPage;
      this.getListData();
    }
  },
};
</script>

<style lang="scss" scoped>
.page-container {
  /**
   * 查询表单
   */
  .search-form-wrap {
    width: 100%;
    padding: 15px;
    border-bottom: 1px dashed #e4e4eb;

    .el-row {
      flex-wrap: wrap;

      .el-col {
        display: flex;
        justify-content: flex-end;

        .el-form-item {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          width: 100%;
          margin-right: 0;
          margin-bottom: 15px;

          /deep/ {
            .el-form-item__label {
              width: 116px;
              line-height: normal;
              padding: 0 12px;
              color: #292933;
              font-weight: normal;
            }

            .el-form-item__content {
              flex: 1;

              .el-range-editor.el-input__inner {
                width: 100%;

                .el-range-separator {
                  width: 14px;
                  padding: 0;
                }
              }
            }
          }

          .el-input {
            width: 100%;
          }

          .el-select {
            width: 100%;
          }
        }
      }
    }
  }

  /**
     * table
     */
  .table-wrap {
    width: 100%;
    min-height: 440px;
    height: calc(100vh - 296px);
    padding: 0 15px;
    margin-top: 15px;
  }
}
.tree-box{
  width:100%;
  height:40px;
  z-index:20;
  overflow:auto;
  height:330px;
  position:absolute;
  top:60px;
  border-radius:4px;
  border: 1px solid #DCDFE6;
}
.people-box{
  position:absolute;
  left:100%;
  top:60px;
  background:#a19f9f;
  width:100%;
  height:330px;
  z-index:20;
}
.mr10 {
  margin-right: 10px;
}
.lh40 {
  /deep/ .el-link--inner{
    line-height: 40px;
  }
}
/deep/ .el-tree-node {
  background: #fff;
  .el-tree-node__content{
    font-size: 14px;
    padding: 0 20px;
    position: relative;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #606266;
    height: 34px;
    line-height: 34px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    cursor: pointer;
  }
}
.pop-tip {
  position:absolute;
  z-index:30;
  top:-110px;
  width:230%;
  height:100px;
  overflow: hidden;
  background:#fff;
  border-radius:5px;
  border:1px solid #DCDFE6;
  line-height: 20px;
}
</style>
