<template>
  <div class="component-container">    
    <!-- 销售属性 -->
    <vxe-table
      border
      highlight-hover-row
      auto-resize
      resizable
      width="100%"
      align="center"
      :tooltip-config="{ enterable: false }"
      :data="tableData"
      ref="table"
      @checkbox-change="checkboxChange"
      @radio-change="radioChange"
    >
      <vxe-table-column
        type="index"
        title="序号"
        width="60"
        show-header-overflow
        show-overflow
      ></vxe-table-column>
      <vxe-table-column field="businessCode" title="商品编码" min-width="100">
        <!-- <template v-slot="{ row }">{{ row.shootTime | dateTime }}</template> -->
      </vxe-table-column>
      <vxe-table-column
        field="pictureVersion"
        title="版本号"
        min-width="100"
      ></vxe-table-column>
      <vxe-table-column
        field="detailPictureList"
        title="精修主图"
        min-width="100"
      >
        <template v-slot="{ row, seq }">
          <preview-img
            v-if="!showChecked"
            :row-data="{ row, seq }"
            :imgTitle="imgTitle"
            :isEdit="false"
            field="detailPictureList"
          />
        </template>
      </vxe-table-column>
      <vxe-table-column
        field="detailPictureNum"
        title="图片数量"
        min-width="100"
      ></vxe-table-column>
      <vxe-table-column field="productStatus" title="图片状态" min-width="100">
        <!-- 停启用状态,1启用,0停用 -->
        <template v-slot="{ row }">{{row.pictureEnable ? "启用" : "停用"}}</template>
      </vxe-table-column>
      <vxe-table-column
        field="mechanismNum"
        title="已绑定机构数量"
        min-width="130"
      >
        <template v-slot="{ row }">
          <!-- :content="row.mechanismList.join('-')" -->
          <el-tooltip class="item" effect="dark" placement="top">
            <template v-slot:content>
              <p v-for="(item, key) in row.mechanismList" :key="key">
                {{ item }}
              </p>
            </template>
            <span>{{ row.mechanismNum }}</span>
          </el-tooltip>
        </template>
      </vxe-table-column>
    </vxe-table>
  </div>
</template>

<script>
import { parseTimestamp } from "@/utils/index.js";
import previewImg from "@/components/uploadImg/preview";

export default {
  name: "",
  components: { previewImg },
  filters: {
    dateTime: function (value) {
      if (!value) return "";
      return parseTimestamp(value);
    },
  },
  props: {
    showChange: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  computed: {
    // 路由参数
    urlParam() {
      return this.$route.query;
    },
    // 商品操作类型
    operationType: function () {
      return this.$store.getters.operationType;
    },
    tableData() {
      return this.$store.state.product.productSaleInfo;
    },
  },
  watch: { },
  created() {
    this.init();
  },
  mounted() {},
  methods: {
    init() {
      let { productCode, productType } = this.urlParam;
      let businessType = "";
      // businessType 商品类型：0:sku, 1:sau
      if (productType == 2 || productType == 3) {
        businessType = parseInt(productType) - 2;
      }
      this.$store.dispatch("product/getSaleInfoAction", {
        businessCode: productCode,
        businessType,
      });
    },
    openDragImg(row, seq) {
      this.$refs.salesImgDrag.openDlg(JSON.parse(JSON.stringify(this.tableData)),seq - 1);
    },
  },
};
</script>
<style lang="scss" scoped>
.button-wrap {
  padding-bottom: 10px;
}
.img-s{
  width: 80px;
  height: 80px;
}
</style>