<template>
  <div class="approval-process"> 
    <div class="title" @click="showImg = !showImg">审批流 
      <i v-if='approvalData.baseImg' style="color:#3B95A8" :class="showImg ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
    </div>
    <div class="approval" v-show="approvalData.baseImg && showImg">
      <img :src="approvalData.baseImg" alt @mouseover="showCard=true" @mouseout="showCard=false"/>
      <div class="card-box"  @mouseover="showCard2=true" @mouseout="showCard2=false" v-show="showCard || showCard2">
          <el-card class="card-item"  v-for="(item,index) in approvalData.processRecordList" :key="index">
            <div style="display:flex;justify-content:space-between">
              <div>
                  <span>{{ item.nodeName }} </span>
                  <span 
                  :class="{'green':item.statusStr==='审核通过' || item.statusStr==='修改并提交',
                  'red':item.statusStr==='审核驳回' || item.statusStr==='回退至发起人' ||item.statusStr==='回退上一节点', 
                  'blue':item.statusStr === '未处理' || !item.statusStr}"> {{ item.statusStr || '未处理' }} </span>
                  <span> {{approvalData.procKey}}</span>
              </div>
              <span style="color:#3B95A8" v-if="approvalData.processRecordList[index+1]">下一步:{{approvalData.processRecordList[index+1].nodeName}}</span>
            </div>
            <p v-if="item.detail">审核意见:{{ item.detail }}</p>
            <p v-if="item.detail">{{ item.handler }}提交于{{ item.createTimeStr }}</p>
          </el-card>
      </div>
      <!-- <div class="approval-box">
        <el-steps
          :active="approvalProcess.nextProcessNode"
          finish-status="success"
          align-center
        >
          <el-step
            :icon="!item.exceptionFlag ? 'el-icon-warning-outline' : ''"
            :class="{ isReject: !item.isReject }"
            v-for="(item, index) in approvalProcess.processList"
            :key="index"
            :title="
              item.optName ? item.optJob + ':' + item.optName : item.optJob
            "
            :description="
              item.optTime ? item.optTime + ':' + item.optAction : ''
            "
          ></el-step>
        </el-steps>
      </div> -->
    </div>
    <div
      class="partition-content"
      style="height: 20px; background: #f0f2f5"
      v-if="approvalProcess.showFlow && !isAdd"
    ></div>
    <div class="apply" v-if="!isAdd">
      <div class="title">申请信息</div>
      <el-row type="flex" class="info">
        <el-col :lg="6" :md="6" :xs="24" :sm="12" :xl="6"
          >发起人所属机构 ：{{ boardData.taskPostCode || '--' }}</el-col
        >
        <el-col :lg="6" :md="6" :xs="24" :sm="12" :xl="6"
          >任务来源 ：{{ boardData.source || '--' }}</el-col
        >
        <el-col :lg="6" :md="6" :xs="24" :sm="12" :xl="6"
          >任务单号 ：{{ boardData.applyCode || "--" }}</el-col
        >
        <el-col :lg="6" :md="6" :xs="24" :sm="12" :xl="6"
          >业务商品编码 ：{{ boardData.outProductCode || "--" }}</el-col
        >
        <!-- <el-col :lg="6" :md="6" :xs="24" :sm="12" :xl="6"
          >商品来源 ：{{ applyAttribute.productSource }}</el-col
        > -->
        <!-- <el-col span="12" v-if="coorection">
          <span style="color: red">上报留言：</span>
          {{ coorection.msg }}
        </el-col>
        <el-col span="12" v-if="coorection">
          <div class="img-correction-box">
            <div class="image-title" style="color: red">上报图片：</div>
            <div class="image-box">
              <el-image
                class="img-correction"
                v-for="item in coorection.imageList"
                :key="item.id"
                :src="item.mediaUrl"
                :preview-src-list="[item.mediaUrl]"
              ></el-image>
            </div>
          </div>
        </el-col> -->
      </el-row>
      <el-row type="flex" class="info" v-if="newGoodUp">
        <el-col :lg="6" :md="6" :xs="24" :sm="12" :xl="6"
          >领取时间 ：{{ boardData.receiveTimeStr || "--" }}</el-col
        >
        <el-col :lg="6" :md="6" :xs="24" :sm="12" :xl="6"
          >处理状态 ：{{ boardData.statusCodeStr || "--" }}</el-col
        >
        <el-col :lg="6" :md="6" :xs="24" :sm="12" :xl="6"
          >处理人 ：{{ boardData.updateUser || "--" }}</el-col
        >
        <el-col :lg="6" :md="6" :xs="24" :sm="12" :xl="6"
          >处理意见 ：{{ boardData.remark || "--" }}</el-col
        >
      </el-row>
            <el-row type="flex" class="info" v-if="newGoodUp">
        <el-col :lg="6" :md="6" :xs="24" :sm="12" :xl="6"
          >最后更新时间 ：{{ boardData.updateTimeStr || "--" }}</el-col
        >
        <el-col :lg="6" :md="6" :xs="24" :sm="12" :xl="6"
          >标准库ID ：{{ boardData.productId || "--" }}</el-col
        >
      </el-row>
    </div>
    <div
      class="partition-content"
      style="height: 20px; background: #f0f2f5"
    ></div>
  </div>
</template>

<script>
import { parseTimestamp } from "@/utils/index.js";
import { getProcessCofig } from "@/utils/processConfig.js";
export default {
  name: "",
  data() {
    return {
      showImg: false, //是否显示流程图
      showCard:false,
      showCard2:false,
      processConfig: {
        processList: [],
        active: 0,
      },
      rules: {
        applyReason: [
          { required: true, message: "请输入申请原因", trigger: "blur" },
        ],
      },
    };
  },
  props: {
    /**
     * sku渲染数据
     */
    approvalData: {
      type: Object,
      required: false,
      default: () => {},
    },
    boardData: {
      type: Object,
      required: false,
      default: () => {},
    },
    isCorrection: {
      type: Boolean,
      required: false,
      default: true,
    },
    processInfo: {
      type: Object,
      required: false,
      default: () => {
        return {};
      },
    },
    isAdd: {
      type: Boolean,
      default: false,
    },
    //新品上报详情
    newGoodUp: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    applyAttribute: function () {
      if (this.approvalData.applyAttribute) {
        let applyAttribute = this.approvalData.applyAttribute;
        applyAttribute.applyTime = parseTimestamp(applyAttribute.applyTime);
        return applyAttribute;
      } else {
        return {};
      }
    },
    approvalProcess: function () {
      if (
        !!this.approvalData.approvalProcess &&
        !!this.approvalData.approvalProcess.processList
      ) {
        this.approvalData.approvalProcess.processList.forEach((item) => {
          item.optTime = parseTimestamp(item.optTime);
        });
        return this.approvalData.approvalProcess;
      } else {
        return {};
      }
    },
    // coorection: function () {
    //   if (this.approvalData.coorection) {
    //     return this.approvalData.coorection;
    //   } else {
    //     return false;
    //   }
    // },
  },
  created() {
    // this.getApplyInfo()
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
.approval-process {
  .title {
    font-weight: 600;
    margin-bottom: 22px;
    border-bottom: 1px solid #e4e4eb;
    line-height: 50px;
    padding-left: 20px;
  }
  .approval {
    width: 1070px;
    position: relative;
    margin: 10px 0 20px;
    padding-top: 30px;
    // background: #ccc;
    // height: 110px;
    img {
      display: block;
      margin: 0 auto;
    }
    .approval-box /deep/ {
      display: flex;
      flex-wrap: nowrap;
      justify-content: center;
      .el-step {
        width: 300px !important;
        .el-icon-warning-outline {
          color: #f56c6c;
        }
        .is-process {
          border-color: #3b95a8;
          .is-text {
            background: #3b95a8;
            color: #fff;
          }
        }
        .el-step__title {
          font-size: 14px;
          font-weight: bold;
          color: #333;
        }
        .el-step__description {
          color: #aeaebf !important;
        }
      }
    }
  }
  .apply {
    padding-bottom: 15px;
    /deep/ .el-form-item {
      display: flex;
      width: 100%;
      .el-form-item__content {
        flex: 1;
        padding-right: 20px;
      }
    }
    .title {
      line-height: 50px;
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 22px;
      padding: 0 20px;
      border-bottom: 1px solid #e4e4eb;
    }
    .img-correction-box {
      display: flex;
      .image-title {
        flex: none;
      }
      .image-box {
        flex: auto;
        display: flex;
        flex-wrap: wrap;
        margin-left: 5px;
        .img-correction {
          width: 80px;
          height: 80px;
          margin-right: 10px;
          margin-bottom: 10px;
          border-radius: 2px;
        }
      }
    }
    .info {
      padding: 0 40px;
      color: #292933;
      font-size: 14px;
    }
    .el-row {
      flex-wrap: wrap;
      .el-col {
        display: flex;
        justify-content: flex-start;
        margin-bottom: 10px;
      }
    }
  }
}
.card-box {
    // pointer-events: none;
    position: absolute;
    right: 0;
    top:100px;
    z-index: 3000;
    .card-item {
        width: 500px;
        margin: 15px;
        .green{
            color:#039D12;
        }
        .red{
            color:#C00017;
        }
        .blue{
            color:#0099CC;
        }
    }
}
</style>