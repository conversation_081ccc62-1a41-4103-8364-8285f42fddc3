import request from "@/utils/request";
import qs from "qs";

export default {
  /**
   * 查询商品品类信息列表
   * @param {object} data
   * @param data.productId 商品ID
   * @param data.productCode 商品编码/原商品编码
   * @param data.generalName 通用名/通用名助记码/商品名/商品名助记码
   * @param data.manufacturerName 生产厂家名称/助记码
   * @param data.spec 规格/型号
   * @param data.manufacturerCategory 厂家分类
   * @param data.brandCategory 品牌分类
   * @param data.fourPlusSevenSelectedListStatus 全国4+7入选目录：（-1无;0否;1是）
   * @param data.fourPlusSevenPublicationTime 4+7公布时间
   * @param data.fourPlusSevenProvinces 4+7省区: 字符串数组
   * @param data.chronicDiseasesVariety  慢病品种（-1无;0否;1是）
   * @param data.conEvaluateVariety 一致性评价品种（-1无;0否;1是）
   * @param data.hospitalVariety 医院品种（-1无;0否;1是）
   * @param page
   * @param limit 页大小
   */
  getCategoryList(data, page, limit) {
    return request({
      url: `/api/productCategory/find/productCategoryList`,
      data: { ...data, page, limit },
      method: "POST"
    });
  },
  /**
   * 导出商品品类信息列表
   * @param {object} data
   * @param data.productId 商品ID
   * @param data.productCode 商品编码/原商品编码
   * @param data.generalName 通用名/通用名助记码/商品名/商品名助记码
   * @param data.manufacturerName 生产厂家名称/助记码
   * @param data.spec 规格/型号
   * @param data.manufacturerCategory 厂家分类
   * @param data.brandCategory 品牌分类
   * @param data.fourPlusSevenSelectedListStatus 全国4+7入选目录：（-1无;0否;1是）
   * @param data.fourPlusSevenPublicationTime 4+7公布时间
   * @param data.fourPlusSevenProvinces 4+7省区: 字符串数组
   * @param data.chronicDiseasesVariety 慢病品种（-1无;0否;1是）
   * @param data.conEvaluateVariety 一致性评价品种（-1无;0否;1是）
   * @param data.hospitalVariety 医院品种（-1无;0否;1是）
   */
  exportCategory(data) {
    return request({
      url: `/api/productCategory/export/productCategoryList`,
      data,
      method: "POST"
    });
  },
  /**
   * 导出医保商品信息列表
   * @param {object} data
   * @param data.productId 商品ID
   * @param data.mixedQueryProductCode 商品编码/原商品编码
   * @param data.mixedQueryGeneralName 通用名/通用名助记码/商品名/商品名助记码
   * @param data.manufacturerName 生产厂家名称/助记码
   * @param data.spec 规格/型号
   * @param data.cnProductCode 国家药品代码
   * @param data.provinceProductCode 省份药品代码
   * @param data.provinceCoded 省份代码
   * @param medicareType 1是国家医保2是省份医保
   */
  exportMedicareList(data, medicareType) {
    return request({
      url: `	/api/medicare/export/productMedicareList`,
      data: { ...data, medicareType },
      method: "POST"
    });
  },
  /**
   * 查询批量更新品类列表
   * @param {object} data
   * @param data.applyStartDate 申请开始时间
   * @param data.applyEndDate 申请结束时间
   * @param data.applyCode 单据编号
   * @param data.applyer 申请人
   * @param data.applyReason 申请原因
   * @param page
   * @param limit
   */
  getCategoryBatchList(data, page, limit) {
    return request({
      url: `/api/productCategory/find/batchModifyCategoryList`,
      data: { ...data, page, limit },
      method: "POST"
    });
  },
  /**
   * 批量更新品类（通过excel上传）
   * @param {FormData} data
   */
  batchCategory(data) {
    return request({
      url: `/api/productCategory/batch/modifyCategory`,
      data,
      method: "POST"
    });
  },
  /**
   * 修改商品品类信息
   * @param {object} data
   * @param data.skuCode 商品编码/原商品编码
   * @param data.brandCategory 品牌分类
   * @param data.fourPlusSevenSelectedListStatus 全国4+7入选目录：（-1无;0否;1是）
   * @param data.fourPlusSevenPrice 4+7中选价格
   * @param data.fourPlusSevenPublicationTime 4+7公布时间
   * @param data.fourPlusSevenProvinces 4+7省区: 字符串数组
   * @param data.chronicDiseasesVariety 慢病品种（-1无;0否;1是）
   * @param data.conEvaluateVariety 一致性评价品种（-1无;0否;1是）
   * @param data.hospitalVariety 医院品种（-1无;0否;1是）
   */
  updateCategory(data) {
    return request({
      url: `/api/productCategory/modify/productCategory`,
      data,
      method: "POST"
    });
  },
  /**
   * 批量更新医保信息记录表
   * @param {object} data
   * @param data.productId 商品ID
   * @param page
   * @param limit
   */
  getMedicareBatchList(data, page, limit) {
    return request({
      url: `/api/medicare/find/productMedicareReocrdList`,
      data: { ...data, page, limit },
      method: "POST"
    });
  },
  /**
   * 字典分类列表
   * @param type 24 查询品牌分类 23 查询厂家分类
   */
  getDictList(type) {
    return request({
      url: `/api/dict/findCache/dictList`,
      data: { type },
      method: "POST"
    });
  },
  /**
   * 商品医保信息列表
   * @param medicareType 1是国家医保2是省份医保
   * @param {object} data
   * @param data.productId 商品ID
   * @param data.productCode 商品编码/原商品编码
   * @param data.generalName 通用名/通用名助记码/商品名/商品名助记码
   * @param data.manufacturerName 生产厂家名称/助记码
   * @param data.spec 规格/型号
   * @param page
   * @param limit
   */
  getMedicareList(medicareType, data, page, limit) {
    return request({
      url: `/api/medicare/find/productMedicareList`,
      data: { medicareType, ...data, page, limit },
      method: "POST"
    });
  },
  /**
   * 省份列表
   */
  getProvinceList() {
    return request({
      url: `/api/medicare/find/productAreaList`
    });
  },
  /**
   * 批量上传医保信息（Excel上传）
   * @param {FormData} data
   * @param data.medicareType 1是国家医保2是省份医保
   */
  batchMedicare(data) {
    return request({
      url: `/api/medicare/add/batchModifyMedicare`,
      data,
      method: "POST"
    });
  },
  // 删除医保
  productMedicare(data) {
    return request({
      url: `/api/medicare/delete/productMedicare`,
      data,
      method: "POST"
    });
  }
};
