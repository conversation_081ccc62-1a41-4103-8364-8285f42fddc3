<template>
  <div class="preview-original">
    <!-- 预览原图对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="show"
      width="1100px"
      height="80vh"
      class="dlg1"
      @close="close"
    >
      <div class="preview-box">
        <span
          v-if="skuIndex != 0"
          class="circle circle-lf"
          @click="handlePicturePreview(skuIndex - 1)"
        >
          <i class="el-icon-arrow-left"></i>
        </span>
        <div class="drag-wrap" style="background: #ffffff">
          <el-row :gutter="10"
            ><el-col
              :span="row.title == '器械许可证图片列表' ? 24 : 8"
              v-for="(row, parIndex) in draggableData"
              :key="parIndex"
            >
              <el-card>
                <div class="title">{{ row.title }}</div>
                <div class="img-wrap">
                  <el-col
                    :span="row.title == '器械许可证图片列表' ? 8 : 24"
                    v-for="(item, index) in row.imgList"
                    :key="index"
                  >
                    <el-card>
                      <span
                        v-if="item.auditStatus == 2"
                        class="watermark reject"
                        >已驳回</span
                      >
                      <img
                        @click="previewImg(index, parIndex)"
                        :class="{
                          abnormal: item.styleState == 1,
                          normal: item.styleState == 2,
                        }"
                        :src="item.pictureUrl"
                        alt=""
                      />
                      <div
                        class="img-des"
                        v-if="receiptsInfo.applyCode.indexOf('STP') == -1"
                      >
                        <span>{{ item.pictureName }}</span>
                        <span>
                          分辨率：{{ item.width + "*" + item.height }}
                        </span>
                        <span class="tip" v-if="item.isTip">
                          素材不符合精修
                        </span>
                      </div>
                    </el-card>
                  </el-col>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
        <span
          v-if="skuIndex != allData.length - 1"
          class="circle circle-rt"
          @click="handlePicturePreview(skuIndex + 1)"
        >
          <i class="el-icon-arrow-right"></i>
        </span>
      </div>
      <div class="btn-wrap">
        <el-button-group style="">
          <el-button
            type="danger"
            v-if="
              !!allData[skuIndex] &&
              allData[skuIndex].previewPictureList.length != 0
            "
            @click="rejectReasonDlg = true"
            >设计驳回</el-button
          >
          <el-button
            type="success"
            style="margin: 0 10px"
            @click="handleDownload"
            >下载原图</el-button
          >
          <el-button
            type="primary"
            v-if="
              !!allData[skuIndex] &&
              allData[skuIndex].previewPictureList.length != 0
            "
            @click="handlePass"
            >设计通过</el-button
          >
        </el-button-group>
      </div>
    </el-dialog>
    <preview-img
      ref="previewImg"
      :imgList="preImgList"
      :currImgIndex="preIndex"
      :smallPackageCode="
        !!allData[skuIndex] && allData[skuIndex].smallPackageCode
      "
    ></preview-img>
    <!-- 选择驳回原因 -->
    <el-dialog
      title="请选择驳回原因"
      :visible.sync="rejectReasonDlg"
      width="600px"
      @close="rejectReasonDlgClose"
    >
      <el-form :model="ruleForm" ref="ruleForm">
        <el-form-item label="选择驳回原因" prop="reason">
          <el-select
            v-model="ruleForm.type"
            @change="changeReasonType"
            placeholder="请选择"
          >
            <el-option
              v-for="item in reasonDict"
              :key="item"
              :value="item.label"
              :label="item.label"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="驳回原因"
          prop="reason"
          :rules="{ required: true, message: '必选', trigger: 'blur' }"
        >
          <el-input
            v-model="ruleForm.reason"
            type="textarea"
            placeholder="请输入内容"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button size="medium" @click="rejectReasonDlg = false"
          >取 消</el-button
        >
        <el-button
          size="medium"
          type="primary"
          @click="changeRejectStatus('ruleForm')"
          >确 定</el-button
        >
        <div class="tip">*必须选择原因或填写自定义内容才能提交</div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  postRejectOriginal,
  approvedOriginalPicture
} from "@/api/productPicture.js";
import draggable from "vuedraggable";
import previewImg from "@/components/uploadImg/previewImg";
import skuList from "@/components/common/skuList";
export default {
  name: "",
  components: {
    draggable,
    previewImg,
    skuList,
  },
  filters: {},
  props: ["imgTitle"],
  watch: {},
  data() {
    return {
      draggableData: [],
      title: "",
      preImgList: [],
      preIndex: 0,
      allData: [],
      skuIndex: 0,
      show: false,
      reason: "",
      receiptsInfo: {},
      rejectReasonDlg: false,
      ruleForm: {
        type: "",
        reason: "", // 驳回原因
      },
      reasonDict: [
        { value: "1", label: "拍摄不完整" },
        { value: "2", label: "拍摄曝光" },
        { value: "3", label: "文字拍摄不清晰" },
        { value: "4", label: "不需要" }, //
        { value: "5", label: "图片缺失" }, // 1.5.3 新增 图片缺失
      ],
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    previewImg(index, parIndex) {
      let flag = true;
      this.preIndex = 0;
      this.preImgList = [];
      this.draggableData.forEach((list, dParIndex) => {
        list.imgList.forEach((item, dIndex) => {
          if (dParIndex == parIndex && dIndex == index) {
            flag = false;
          }
          if (flag) {
            this.preIndex++;
          }
          this.preImgList.push(item);
        });
      });
      this.$refs.previewImg.openDlg(this.preImgList, this.preIndex);
    },
    // 关闭的回调
    closeDlg() {
      this.show = false;
    },
    // 打开的回调
    openDlg(allData, skuIndex, receiptsInfo) {
      this.allData = allData;
      this.skuIndex = skuIndex;
      this.receiptsInfo = receiptsInfo;
      this.getDragDate();
      this.getDiaTitle();
      this.show = true;
    },
    // 切换sku
    handlePicturePreview(index) {
      if (index >= 0 && index < this.allData.length) {
        this.skuIndex = index;
      }
      let arr = this.allData[this.skuIndex].previewPictureList.filter(
        (item) => {
          return item.auditStatus == 2;
        }
      );
      this.getDragDate();
      this.getDiaTitle();
    },
    getDiaTitle() {
      const data = this.allData[this.skuIndex];
      if (this.receiptsInfo.originalUser) {
        this.title = `${this.receiptsInfo.applyCode}_${
          this.receiptsInfo.originalUser
        }_${this.skuIndex + 1}/${this.allData.length}_${data.productCode}`;
      } else {
        this.title = `${this.receiptsInfo.applyCode}_${this.skuIndex + 1}/${
          this.allData.length
        }_${data.productCode}`;
      }
    },
    getDragDate() {
      if (this.receiptsInfo.applyCode.indexOf("STP") != -1) {
        this.draggableData = [{ imgList: [], title: "器械许可证图片列表" }];
      } else {
        this.draggableData = [
          { imgList: [], title: "主图" },
          { imgList: [], title: "外包装" },
          { imgList: [], title: "说明书" },
        ];
      }
      this.allData[this.skuIndex].previewPictureList.forEach((item) => {
        if (this.receiptsInfo.applyCode.indexOf("STP") != -1) {
          if (item.auditStatus != 1) {
            this.draggableData[0].imgList.push(item);
          }
        } else {
          if (item.auditStatus == 1) return;
          let img = new Image();
          img.src = item.pictureUrl;
          img.onload = () => {
            item.width = img.width;
            item.height = img.height;
            if (item.auditStatus != 1) {
              if (item.pictureOrdinal == 1) {
                if (img.width != 800 && img.height != 800) {
                  item.isTip = true;
                } else {
                  item.isTip = false;
                }
                this.$set(this.draggableData, 0, this.draggableData[0]);
              } else if (item.pictureOrdinal >= 2 && item.pictureOrdinal <= 5) {
                if (img.width != 800 && img.height != 800) {
                  item.isTip = true;
                } else {
                  item.isTip = false;
                }
                this.$set(this.draggableData, 1, this.draggableData[1]);
              } else {
                if (img.width != 1280) {
                  item.isTip = true;
                } else {
                  item.isTip = false;
                }
                this.$set(this.draggableData, 2, this.draggableData[2]);
              }
            }
          };
          if (item.pictureOrdinal == 1) {
            this.draggableData[0].imgList.push(item);
          } else if (item.pictureOrdinal >= 2 && item.pictureOrdinal <= 5) {
            this.draggableData[1].imgList.push(item);
          } else {
            this.draggableData[2].imgList.push(item);
          }
        }
      });
    },

    handleDownload() {
      this.$emit("downloadOriginal");
    },

    handlePass() {
      let currentPictureList = JSON.parse(
          JSON.stringify(this.allData[this.skuIndex].previewPictureList)
        ),
        arr = [];
      currentPictureList.forEach((item) => {
        if (item.readonly != 1 && item.auditStatus != 1) {
          item.auditStatus = 1;
          arr.push(item);
        }
      });
      this.passSubmit(arr);
    },

    passSubmit(arr) {
      this.$confirm(
        "您确认直接通过当前sku精修任务吗？任务图片将上传到正式环境，线上业务可直接使用！",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(async () => {
          const loading = this.$loading({
            lock: true,
            text: "数据提交中...",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          // 整理数据 提交
          let params = {
            applyCode: this.receiptsInfo.applyCode, // 	单据编号
            approvedOriginalPictureList: arr,
          };
          let data = await approvedOriginalPicture(params);
          loading.close();
          if (data.retCode == 0) {
            this.$message.success("提交成功！");
            // 提交成功 要刷新列表（操作父组件）
            this.allData.splice(this.skuIndex, 1);
            if (this.allData.length == this.skuIndex) {
              this.skuIndex = 0;
            }
            if (this.allData.length == 0) {
              this.closeDlg();
            } else {
              this.getDragDate();
              this.getDiaTitle();
            }
            this.$emit("refresh", true);
          } else {
            this.$message.error("操作失败！");
          }
        })
        .catch((_) => {});
    },

    rejectReasonDlgClose() {
      this.$refs.ruleForm.resetFields();
      this.ruleForm = {
        type: "",
        reason: "", // 驳回原因
      };
    },

    // 修改驳回状态
    changeReasonType(val) {
      this.ruleForm.reason = val;
    },

    // 修改为驳回状态
    changeRejectStatus(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // ------驳回当前sku商品所有图片 并过滤 readonly == 1只读数据
          let currentPictureList = JSON.parse(
            JSON.stringify(this.allData[this.skuIndex].previewPictureList)
          );
          currentPictureList.forEach((item) => {
            if (item.readonly != 1 && item.auditStatus != 1) {
              item.auditStatus = 2;
              item.rejectReason = this.ruleForm.reason;
            }
          });
          this.rejectReasonDlg = false;
          let arr = currentPictureList.filter((item) => item.auditStatus == 2);
          this.rejectSubmit(arr);
        } else {
          return false;
        }
      });
    },

    // 提交
    rejectSubmit(rejectArr) {
      this.$confirm(
        `是否确认驳回当前sku的${rejectArr.length}张图片？`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(async () => {
          const loading = this.$loading({
            lock: true,
            text: "数据提交中...",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });
          let reasonArr = rejectArr.map((item) => {
            return item.rejectReason;
          });
          let reasonStr = [...new Set(reasonArr)].join(";");

          // 整理数据 提交
          let params = {
            applyCode: this.receiptsInfo.applyCode, // 	单据编号
            originalRejectReason: reasonStr, //	原图驳回原因 去重拼接
            rejectOriginalPictureList: rejectArr,
          };
          let data = await postRejectOriginal(params);
          loading.close();
          if (data.retCode == 0) {
            this.$message.success("提交成功！");
            // 提交成功 要刷新列表（操作父组件）
            this.allData.splice(this.skuIndex, 1);
            if (this.allData.length == this.skuIndex) {
              this.skuIndex = 0;
            }
            if (this.allData.length == 0) {
              this.closeDlg();
            } else {
              this.getDragDate();
              this.getDiaTitle();
            }
            this.$emit("refresh", true);
          } else {
            this.$message.error("操作失败！");
          }
        })
        .catch((_) => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.drag-wrap /deep/ {
  padding: 0 10px;
  width: 100%;
  .el-card__body {
    padding: 10px;
  }
  .title {
    text-align: center;
    font-size: 20px;
    font-weight: bold;
    padding-bottom: 10px;
  }
}
.img-wrap /deep/ {
  .el-card__body {
    display: flex;
    align-items: center;
    justify-content: center;
    // padding-left: 50px;
    // flex-direction: de;
    position: relative;
    .watermark {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 9;
      color: #fff;
      font-size: 12px;
      text-align: center;
      &.delete {
        background: #f56c6c;
      }
      &.reject {
        background: #ffcc33;
      }
      &.pass {
        background: #108f40;
      }
    }
    .delete-btn {
      position: absolute;
      top: 0;
      right: 0;
      font-size: 12px;
      color: #fff;
      padding: 5px;
      background: #f56c6c;
      border-top-right-radius: 5px;
      &.cancel {
        background: #909399;
      }
    }
    span {
      flex: 1;
      font-size: 12px;
      text-align: left;
      position: relative;
      padding-left: 5px;
      &.tip {
        color: #f56c6c;
      }
    }
  }
  img {
    height: 100px;
    width: 100px;
    border: 5px solid #fff;
    &.abnormal {
      border-color: #f56c6c;
    }
    &.normal {
      border-color: #67c23a;
    }
  }
}
.preview-box {
  display: flex;
  align-items: center;
}
.circle {
  z-index: 2000;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  opacity: 0.5;
  background-color: #ccc;
  text-align: center;
  font-size: 22px;
  line-height: 30px;
  transition: opacity 0.3s;
}
.circle-lf {
  left: 20px;
}
.circle-rt {
  right: 20px;
}
.circle:hover {
  opacity: 1;
}
.btn-wrap {
  display: flex;
  justify-content: flex-end;
  padding: 20px 10px 10px 0;
}
.img-des {
  height: 100px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.tip {
  padding-top: 10px;
  color: #f56c6c;
  font-size: 14px;
}
</style>