<template>
  <div class="component-container">
    <div
      class="loading"
      v-loading="productLoading"
      v-show="productLoading"
    ></div>
    <approval-process-new :approvalData="approvalData" :boardData="urlParam" style="margin-top:72px"></approval-process-new>
    <el-row
      class="btns"
      :style="{ top: scrollTop < 40 ? '40px' : '0px' }"
    >
      <el-col :span="24" class="text-rt">
        <el-button @click="cancel">取消提交</el-button>
        <el-button type="success" @click="review(true)">审核通过</el-button>
        <el-button @click="review(false)">审核不通过</el-button>
      </el-col>
    </el-row>
    <div class="table-contnt" v-show="!productLoading">
      <div class="table-title">商品信息</div>
      <vxe-table
        border
        highlight-hover-row
        auto-resize
        resizable
        align="center"
        :data="mergeData"
        ref="table"
      >
        <vxe-table-column
          field="productId"
          title="商品ID"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="generalName"
          title="通用名"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="spuCategoryName"
          title="商品大类"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="manufacturerName"
          title="生产厂家"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="spec"
          title="规格型号"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="approvalNo"
          title="批准文号"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="smallPackageCode"
          title="小包装条码"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
      </vxe-table>
    </div>

    <div style="width:1000px;margin:0 auto">
    <el-row>
      <el-col>
       <drag-img
        ref="dragImg"
        :productInfo="mergeData[0]"
        :imgList="imgList"
    ></drag-img>
        </el-col>
    </el-row>
 </div>

    <!-- 审核弹框 -->
    <el-dialog
      title="审核"
      width="500px"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisible"
      :close-on-press-escape="false"
      :show-close="false"
      @close="dialogClose"
    >
      <el-form :model="reviewForm" :rules="rules" ref="reviewForm">
        <el-form-item label="审核状态">
          <el-input
            v-model="reviewForm.state"
            disabled
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item label="审核意见" prop="value">
          <el-input
            type="textarea"
            placeholder="超过100的长度，不能提交"
            v-model="reviewForm.value"
            autocomplete="off"
            maxlength="100"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false;reviewForm = {}">取 消</el-button>
        <el-button type="primary" @click="save()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// 商品基础共用信息混入对象
import { productMixinBase } from "@/mixin/productMixinBase.js";
import dragImg from "@/views/productPicture/selfSupport/dragImg";
import { getAddProcessData, auditPass, auditRejectEnd, imgVersionDetail } from "@/api/workManage"

export default {
  name: "",
  mixins: [productMixinBase],
  components: { dragImg },
  computed: {
    // 路由参数
    urlParam() {
      return this.$route.query;
    },
  },
  data() {
    return {
      scrollTop: 0,
      approvalData: {},
      mergeData: [],
      imgList:[],
      dialogFormVisible: false,
      reviewForm: {},
      rules: {
        value: [
          {
            required: false,
            message: "请输入审核意见",
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    this.getApplyInfo()
    this.init();
    window.onscroll = () => {
      this.scrollTop =
        document.documentElement.scrollTop || document.body.scrollTop;
    }
  },
  methods: {
    async init() {
        this.productLoading = true;
        try {
            const res = await imgVersionDetail({applyCode: this.urlParam.applyCode})
            if (res.retCode === 0) {
                console.log(res.data);
                this.mergeData.push(res.data.productBriefInfo);
                this.imgList = res.data.picInfo
            } else {
                this.$message.error(res.retMsg)
            }
            this.productLoading = false;
        } catch (error) {
            
        }
    },
    review(flag) {
      this.dialogFormVisible = true;
      if (flag) {
        this.reviewForm.state = "审核通过";
        this.rules.value[0].required = false
      } else {
        this.reviewForm.state = "审核不通过";
        this.rules.value[0].required = true
      }
    },
    save() {
        this.$refs.reviewForm.validate(async (valid) => {
            if (valid) {
                let param = {
                    applyCode:this.urlParam.applyCode,
                    comment:this.reviewForm.value,
                    procInstId:this.urlParam.procInstId,
                    procKey:this.urlParam.procKey,
                    taskId:this.urlParam.taskId,
                }
                try {
                let res = null
                if(this.reviewForm.state === '审核通过') {
                    res = await auditPass(param)
                } else {
                    res = await auditRejectEnd(param)
                }
                console.log(res);
                if(res.retCode === 0) {
                    this.reviewForm.value = ''
                    let tab = 'second'
                    parent.CreateTab(
                        `../static/dist/index.html#/workManage/receiveDeal?tab=${tab}`,
                        "领取与处理",
                        true
                    );
                    parent.CloseTab(
                        "../static/dist/index.html#/workManage/imgVersionDeal"
                    );
                } else {
                    this.$message.error(res.retMsg)
                }
                } catch (error) {
                    console.log(error);
                }
            }
        })
    },
    async getApplyInfo() {
      let param = {
        procDefId: this.urlParam.procDefId,
        procInstId: this.urlParam.procInstId
      };
      let res = await getAddProcessData(param);
      console.log(res);
      if (res.success) {
        this.approvalData = res.data;
      }
    },
    cancel() {
      parent.CloseTab(
          "../static/dist/index.html#/workManage/imgVersionDeal"
      );
    }
  },
};
</script>

<style lang="scss" scoped>
.component-container {
  padding-top: 40px;
  .loading {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
  }
  /deep/.el-tabs__header {
    border-bottom: none;
    background-color: #f2f2f2;
  }
  /deep/ .el-tabs__content {
    padding-top: 72px;
  }
  .el-tabs--border-card {
    border: none;
    box-shadow: none;
  }
  .btns {
    background: #fff;
    width: 100%;
    position: fixed;
    top: 40px;
    padding: 15px;
    z-index: 10;
  }
}
.table-contnt {
  margin-bottom: 10px;
  padding: 0 20px;
  .table-title {
    padding: 10px 0 20px;
    font-size: 16px;
    font-weight: bold;
    &.bTitle {
      padding-top: 30px;
    }
  }
}
</style>