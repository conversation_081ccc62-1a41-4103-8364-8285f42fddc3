import { trim } from "xe-utils/methods"
import { isRepeat } from "./index"

// SPU data.model 数据
export let dataModel = {
  //商品分类
  spuCategory: "",

  //商品大类
  // largeCategory: "",

  //spu编码,商品编码
  spuCode: "",

  //通用名
  generalName: "",

  //生产许可证或备案凭证编号
  manufacturingLicenseNo: "",

  //通用名助记码
  generalNameCode: "",

  //批件规格
  instructionSpec: "",

  // 规格型号（赠品）
  spec: "",

  //批准文号
  approvalNo: "",

  //生产厂家
  manufacturer: "",
  manufacturerName: "",

  // 厂家分类
  manufacturerCategoryName: "",

  // 是否委托生产
  delegationProduct: "",

  // 委托生产厂家
  entrustedManufacturer: "",
  entrustedManufacturerName: "",

  //生产地址
  // address: "",

  //有效期
  validity: "",
  validityTransition: "",

  // 有效期单位
  validityUnit: "2",

  //剂型
  dosageForm: "",

  //所属经营范围
  businessScopeList: [],
	//化妆品备案人/注册人
  filingsAuthor: '',

	//存储条件	
  storageCond: "",

  //产地
  originPlace: "",

  //建议零售价
  suggestedPrice: "",

  //特殊属性
  specialAttrList: [],

  //一级分类
  firstCategory: "",

  //二级分类
  secondCategory: "",

  //三级分类
  thirdCategory: "",

  //四级分类
  fourthCategory: "",

  //五级分类
  fiveCategory: "",

  // 六级分类
  sixCategory: "",

  // 六级分类编码
  categoryCode: "",

  //税务分类编码
  taxCategoryCode: "",

  //进项税率
  inRate: "",

  //销项税率
  outRate: "",

  //上市许可持有人
  marketAuthor: "",

  //厂家地址
  productionAddress: "",

  //上市许可持有人地址
  marketAuthorAddress: "",

  //质量标准
  // qualityStandard: "",

  //包装单位
  packageUnit: "",

  //品牌商标
  brand: "",

  // 品牌厂家
  brandName: "",

  // 无小包装条码
  noSmallPackageCode: 0,

  //小包装条码
  smallPackageCodeList: [],

  //中包装条码
  mediumPackageCodeList: [],

  //件包装条码
  piecePackageCodeList: [],

  // 存储属性
  shadingAttr: "",

  // 贮藏
  // storage: "",

  // 是否监管
  whetherSupervision: "",

  // 慢病品种
  chronicDiseasesVariety: "",

  // 批件图片
  approvalImgList: [],

  // 每单位净含量
  netContent: "",
  // 每单位净含量单位
  netContentUnit: "",
  // 本位码
  // standardCodeList: [],
}
// SPU data.modelAttr 数据 跟 model对象对应，增加选项，展示控制等其他属性
export let dataModelAttr = {
	//化妆品备案人/注册人
	filingsAuthorDisplay: true,
	filingsAuthorDisabled: false,
  //商品分类
  spuCategoryDisplay: true,
  spuCategoryDisabled: false,
  spuCategoryOptions: [],

  //商品大类
  largeCategoryDisplay: true,
  largeCategoryDisabled: false,
  largeCategoryOptions: [],

  //spu编码,商品编码
  spuCodeDisplay: true,
  spuCodeDisabled: true,
  spuCodeBtnDisplay: true, //复用查询按钮

  //通用名
  generalNameDisplay: true,
  generalNameDisabled: false,

  //生产许可证或备案凭证编号
  manufacturingLicenseDisabled: true,
  manufacturingLicenseNoDisabled: false,

  //通用名助记码
  generalNameCodeDisplay: true,
  // generalNameCodeDisabled: true,

  //批件规格
  instructionSpecDisplay: true,
  instructionSpecDisabled: false,

  // 规格型号 （赠品）
  specDisplay: true,
  specDisabled: false,

  //批准文号
  approvalNoDisplay: true,
  approvalNoDisabled: false,

  //生产厂家
  manufacturerName: "",
  manufacturerDisplay: true,
  manufacturerDisabled: false,
  manufacturerOptions: [],

  // 厂家分类
  manufacturerCategoryNameDisplay: true,
  manufacturerCategoryNameDisabled: true,

  // 是否委托生产
  delegationProductDisplay: true,
  delegationProductDisabled: false,

  // 委托生产厂家
  entrustedManufacturerName: "",
  entrustedManufacturerDisplay: true,
  entrustedManufacturerDisabled: false,
  entrustedManufacturerOptions: [],

  //生产地址
  // addressDisplay: true,
  // addressDisabled: false,

  //有效期
  validityDisplay: true,
  validityDisabled: false,

  // 有效期单位
  validityUnitDisplay: true,
  validityUnitDisabled: true,
  validityUnitOptions: [
    // {
    //   value: "0",
    //   label: "年"
    // },
    {
      value: "2",
      label: "月",
    },
    // {
    //   value: "2",
    //   label: "日"
    // }
  ],

  //剂型
  dosageFormDisplay: true,
  dosageFormDisabled: false,
  dosageFormOptions: [],

  //所属经营范围
  businessScopeListDisplay: true,
  businessScopeListDisabled: false,
  businessScopeListOptions: [],

  //存储条件
  storageCondDisplay: true,
  storageCondDisabled: false,
  storageCondOptions: [],

  // 贮藏
  // storageDisplay: true,
  // storageDisabled: false,

  //产地
  originPlaceDisplay: true,
  originPlaceDisabled: false,

  //建议零售价
  suggestedPriceDisplay: true,
  suggestedPriceDisabled: false,

  //特殊属性
  specialAttrListDisplay: true,
  specialAttrListDisabled: false,
  specialAttrListOptions: [],

  //一级分类
  firstCategoryDisplay: true,
  firstCategoryDisabled: true,
  firstCategoryOptions: [],
  firstCategoryLoad: false,

  //二级分类
  secondCategoryDisplay: true,
  secondCategoryDisabled: true,
  secondCategoryOptions: [],
  secondCategoryLoad: false,

  //三级分类
  thirdCategoryDisplay: true,
  thirdCategoryDisabled: true,
  thirdCategoryOptions: [],
  thirdCategoryLoad: false,

  //四级分类
  fourthCategoryDisplay: true,
  fourthCategoryDisabled: true,
  fourthCategoryOptions: [],
  fourthCategoryLoad: false,

  //五级分类
  fiveCategoryDisplay: true,
  fiveCategoryDisabled: true,
  fiveCategoryOptions: [],
  fiveCategoryLoad: false,

  //六级分类
  sixCategoryDisplay: true,
  sixCategoryDisabled: true,
  sixCategoryOptions: [],
  sixCategoryLoad: false,

  //税务分类编码
  taxCategoryCodeDisplay: true,
  taxCategoryCodeDisabled: false,

  //进项税率
  inRateDisplay: true,
  inRateDisabled: false,
  inRateOptions: [],

  //销项税率
  outRateDisplay: true,
  outRateDisabled: false,
  outRateOptions: [],

  //上市许可持有人
  marketAuthorDisplay: true,
  marketAuthorDisabled: false,

  //上市许可持有人地址
  marketAuthorAddressDisplay: true,
  marketAuthorAddressDisabled: false,

  //厂家地址
  productionAddressDisplay: true,
  productionAddressDisabled: false,

  //质量标准
  // qualityStandardDisplay: true,
  // qualityStandardDisabled: false,

  //包装单位
  packageUnitDisplay: true,
  packageUnitDisabled: false,
  packageUnitOptions: [],

  //品牌/商标
  brandDisplay: true,
  brandDisabled: false,

  // 无小包装条码
  noSmallPackageCodeDisplay: true,
  noSmallPackageCodeDisabled: false,

  //小包装条码
  smallPackageCodeListDisplay: true,
  smallPackageCodeListDisabled: false,

  //本位码
  standardCodeListDisplay: true,
  standardCodeListDisabled: false,

  // 中包装条码
  mediumPackageCodeListDisplay: true,
  mediumPackageCodeListDisabled: false,

  //件包装条码
  piecePackageCodeListDisplay: true,
  piecePackageCodeListDisabled: false,

  //遮光属性
  shadingAttrDisplay: true,

  // 是否禁用
  shadingAttrDisabled: false,
  shadingAttrOptions: [
    {
      dictName: "无",
      id: 0,
    },
    {
      dictName: "避光",
      id: 1,
    },
    {
      dictName: "遮光",
      id: 2,
    },
    {
      dictName: "凉暗",
      id: 3,
    },
  ],

  // 是否监管
  whetherSupervisionDisplay: true,
  whetherSupervisionDisabled: false,

  // 慢病品种
  chronicDiseasesVarietyDisplay: true,
  chronicDiseasesVarietyDisabled: true,

  // 批件图片
  approvalImgListDisplay: true,
  approvalImgListDisabled: false,
  approvalImgUpload: false,
  approvalImgPreview: false,
}
// SPU data.rules 数据
export let dataRules = {
  //商品分类
  spuCategory: [
    {
      required: true,
      message: "请选择商品分类",
      trigger: "change",
    },
  ],
  // largeCategory: [{
  //   required: true,
  //   message: "请选择商品大类",
  //   trigger: "change"
  // }],
  //spu编码,商品编码
  // spuCode: [
  //   {
  //     required: true,
  //     message: " ",
  //     trigger: "change"
  //   }
  // ],
  //通用名
  generalName: [
    {
      required: true,
      message: "请输入通用名",
      trigger: "blur",
    },
  ],
	filingsAuthor: [
		{
			require: true,
			message: '请输入化妆品备案人/注册人',
			trigger: 'blur'
		}
	],
  //生产许可证或备案凭证编号
  // manufacturingLicenseNo: [{ required: true, message: "请输入生产许可证或备案凭证编号", trigger: "blur" }],
  //通用名助记码
  // generalNameCode: [{
  //   required: true,
  //   message: " ",
  //   trigger: "blur"
  // }],
  //批件规格
  instructionSpec: [
    {
      required: true,
      message: "请输入批件规格或型号",
      trigger: "blur",
    },
  ],

  // 规格型号（赠品）
  spec: [
    {
      required: false,
      message: "请输入批件规格或型号",
      trigger: "blur",
    },
  ],
  //批准文号
  approvalNo: [
    {
      required: true,
      message: "请输入批准文号",
      trigger: "blur",
    },
  ],
  //生产厂家
  manufacturer: [
    {
      required: true,
      message: "请选择生产厂家，手工输入无效",
      trigger: "change",
    },
  ],
  // 委托生产厂家
  entrustedManufacturer: [
    {
      required: true,
      message: "请选择委托生产厂家",
      trigger: "change",
    },
  ],
  // 是否委托生产
  delegationProduct: [
    {
      required: true,
      message: "请选择是否委托生产",
      trigger: "change",
    },
  ],
  //剂型
  dosageForm: [
    {
      required: true,
      message: "请选择剂型",
      trigger: "change",
    },
  ],
  //所属经营范围
  businessScopeList: [
    {
      required: true,
      message: "请选择所属经营范围",
      trigger: "change",
    },
  ],
  //存储条件
  storageCond: [
    {
      required: true,
      message: "请选择存储条件",
      trigger: "change",
    },
  ],
  //贮藏
  // storage: [{
  //   required: true,
  //   message: "请输入贮藏",
  //   trigger: "change"
  // }],
  //产地
  // originPlace: [{
  //   required: false,
  //   message: "请输入产地",
  //   trigger: "blur"
  // }],
  // //一级分类
  // firstCategory: [{
  //   required: true,
  //   message: "请选择一级分类",
  //   trigger: "change"
  // }],
  // //二级分类
  // secondCategory: [{
  //   required: true,
  //   message: "请选择二级分类",
  //   trigger: "change"
  // }],
  // //三级分类
  // thirdCategory: [{
  //   required: true,
  //   message: "请选择三级分类",
  //   trigger: "change"
  // }],
  // //四级分类
  // fourthCategory: [{
  //   required: true,
  //   message: "请输入四级分类",
  //   trigger: "blur"
  // }],
  // //五级分类
  // fiveCategory: [{
  //   required: true,
  //   message: "请输入五级分类",
  //   trigger: "blur"
  // }],
  //六级分类
  sixCategory: [
    {
      required: true,
      message: "请输入六级分类",
      trigger: "change",
    },
  ],
  //税务分类编码
  taxCategoryCode: [
    {
      required: true,
      message: "请输入税务分类编码",
      trigger: "blur",
    },
  ],
  //进项税率
  inRate: [
    {
      required: true,
      message: "请选择进项税率",
      trigger: "change",
    },
  ],
  //销项税率
  outRate: [
    {
      required: true,
      message: "请选择销项税率",
      trigger: "change",
    },
  ],
  // 小包装条码
  // smallPackageCodeList:[{
  //   required: false,
  //   message: "请添加小包装条码",
  //   trigger: "change"
  // }],
  //包装单位
  packageUnit: [
    {
      required: true,
      message: "请选择包装单位",
      trigger: "change",
    },
  ],
  //遮光属性
  shadingAttr: [
    {
      required: true,
      message: "请选择遮光属性",
      trigger: "change",
    },
  ],
  // 是否监管
  whetherSupervision: [
    {
      required: true,
      message: "请选择是否监管",
      trigger: "change",
    },
  ],
  // 品牌商标
  brand: [
    {
      required: true,
      message: "请填写品牌商标",
      trigger: "blur",
    },
  ],
  // 本位码
  // standardCodes: [{
  //   required: true,
  //   message: "请填写本位码",
  //   trigger: "blur"
  // }],
  //上市许可持有人
  marketAuthor: [
    {
      required: true,
      message: "请选择上市许可持有人",
      trigger: "blur",
    },
  ],

  //厂家地址
  productionAddress: [
    {
      required: true,
      message: "请输入厂家地址",
      trigger: "blur",
    },
  ],

  //上市许可持有人地址
  marketAuthorAddress: [
    {
      required: true,
      message: "上市许可持有人地址",
      trigger: "blur",
    },
  ],
  // 小包装条码
  smallPackageCodeList: [
    {
      required: true,
      message: "请填写小包装条码",
      trigger: "change",
    },
  ],
}

// spu 复用查询数据
export let spuDrawerForm = {
  approvalNo: "", // 批准文号
  firstCategory: "", // 一级分类
  fourthCategoryStr: "", //四级分类
  generalName: "", // 通用名
  instructionSpec: "", // 批件规格
  limit: 20,
  manufacturer: "", // 生产厂家
  page: 1,
  pageNum: 1,
  pageSize: 10,
  secondCategory: "", // 二级分类
  skuCount: "", // sku 数量
  spuCategory: "", // 商品分类
  spuCode: "", // spu编码
  thirdCategory: "", // 三级分类
  fourthCategory: "",
  fiveCategory: "",
  sixCategory: "",
  total: 0,
}

// SKU 表单
export let skuForm = {
  // sku编码
  skuCode: "",
  //商品id
  productId: "",
  // 商品名
  skuName: "",
  // 规则型号
  spec: "",
  tasteName: "", //口味
  sizeName: "", //尺码
  colorName: "", //颜色
  originPlace: "", //产地
  // 包装单位
  packageUnit: "",
  packageUnitName: "",
  // 处方分类
  prescriptionCategory: "",
  prescriptionCategoryName: "",
  // 无小包装条码
  noSmallPackageCode: 0,
  // 小包装条码
  smallPackageCodeList: [],
  // 中包装条码
  mediumPackageCodeList: [],
  // 件包装条码
  piecePackageCodeList: [],
  // 品牌/商标
  brand: "",
  // 有效期
  validity: "",
  validityTransition: "",
  validityUnit: 2,
  // 是否委托生产
  delegationProduct: 0,
  // 委托生产厂家
  entrustedManufacturer: "",
  entrustedManufacturerName: "",
  // 自营状态
  preOperateStatusName: "",
  // 别名
  alias: "",
  // 外包装图片
  outPackageImgList: [],
  // 说明书图片
  directionImgList: [],
  // 质量标准
  qualityStandard: "",
  // 品牌分类
  brandCategory: "",
  brandCategoryName: "",
  // 一致性评价品种
  conEvaluateVariety: "",
  // 医院品种
  hospitalVariety: "",
  // 贮藏
  storage: "",
  // 本位码
  standardCodes: "",
  // 存储条件
  storageCond: "",
  storageCondName: "",
//  受托生产厂家地址
delegationProductAddress:"",
  netContent: "",
  netContentUnit: ""
}

const validateValidity = (rule, value, callback) => {
  if (value === "") {
    callback(new Error("请填写有效期"))
  } else {
    var reg = /^\d+(\.\d+)?$/
    if (!reg.test(value) && value !== "-" && value !== "*") {
      callback(new Error("请填写合法的有效期"))
    }
    callback()
  }
}
const checkPrescription = (rule, value, callback) => {
  if ((value === "" || value === "空") && rule.required) {
    callback(new Error("请选择处方分类"))
  } else {
    callback()
  }
}

const validateValidity2 = (rule, value, callback) => {
  if ((value === "" || typeof value === "string") && rule.required) {
    callback(new Error("请选择委托生产厂家"))
  } else {
    callback()
  }
}
const validateUnitNetContent = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入净含量'));
  } else if (!/^\d+(\.\d+)?$/.test(value)) {
    callback(new Error('请输入有效的数字'));
  } else if (parseFloat(value) <= 0) {
    callback(new Error('净含量必须大于0'));
  } else if (parseFloat(value) > 999999) {
    callback(new Error('净含量超出范围'));
  } else {
    callback();
  }
}
// SKU 表单校验
export let skuFormRules = {
  //商品名
  skuName: [
    {
      required: true,
      message: "请填写商品名称",
      trigger: "blur",
    },
  ],
  //品牌/商标
  brand: [
    {
      required: true,
      message: "请填写品牌商标",
      trigger: "blur",
    },
  ],
  //规格型号
  spec: [
    {
      required: true,
      message: "请填写规格型号",
      trigger: "blur",
    },
  ],
  // 产地
  originPlace: [
    {
      required: true,
      message: "请输入产地",
      trigger: "blur",
    },
  ],
  //包装单位
  packageUnit: [
    {
      required: true,
      message: "请选择包装单位",
      trigger: "change",
    },
  ],
  //处方分类
  prescriptionCategory: [
    {
      required: true,
      validator: checkPrescription,
      message: "请选择处方分类",
      trigger: "change",
    },
  ],
  //是否委托生产
  delegationProduct: [
    {
      required: true,
      message: "请选择是否委托生产",
      trigger: "change",
    },
  ],
  //委托生产厂家
  entrustedManufacturer: [
    {
      required: false,
      // message: "请选择委托生产厂家",
      validator: validateValidity2,
      trigger: "change",
    },
  ],
  // 有效期
  validity: [
    {
      required: true,
      validator: validateValidity,
      trigger: "blur",
    },
  ],
  // 无小包装条码
  noSmallPackageCode: [
    {
      required: true,
      message: "请选择是否无小包装条码",
      trigger: "change",
    },
  ],
  // 小包装条码
  smallPackageCodeList: [
    {
      required: true,
      message: "请填写小包装条码",
      trigger: "blur",
    },
  ],
  //质量标准
  qualityStandard: [
    {
      required: false,
      message: "请填写质量标准",
      trigger: "blur",
    },
  ],
  //本位码
  standardCodes: [
    {
      required: true,
      message: "请填写本位码",
      trigger: "blur",
    },
  ],
  // 存储条件
  storageCond: [
    {
      required: true,
      message: "请选择存储条件",
      trigger: "change",
    },
  ],
  delegationProductAddress: [
    {
      required: false,
      message: "填写受托生产厂家地址",
      trigger: "change",
    },
  ],
  netContent: [
    {
      required: true,
      validator: validateUnitNetContent,
      trigger: 'blur'
    }
  ],
  netContentUnit: [
    {
      required: true,
      message: "请选择净含量单位",
      trigger: "change"
    }
  ]
}

//判断六级分类，实现处方药控制慢病品种
export const validateChronicDiseasesVariety = (prescriptionCategory, sixCategoryName, cfyId) => {
  let arr = [
    "中西成药,肝胆疾病用药,肝病治疗用药,病毒性肝炎",
    "中西成药,精神类用药,非典型抗精神病药物,抑郁症",
    "中西成药,糖尿病用药,2型糖尿病,糖苷酶抑制剂",
    "中西成药,风湿镇痛用药,风湿和类风湿类,类风湿性关节炎",
    "中西成药,呼吸系统用药,平喘药,支气管哮喘",
    "中西成药,精神类用药,典型的抗精神病药物,精神分裂症",
    "中西成药,心脑血管用药,高血压类,血管紧张素转化酶拮抗剂",
    "中西成药,心脑血管用药,高血压类,血管紧张素受体拮抗剂",
    "中西成药,抗肿瘤用药,生殖系统肿瘤,乳腺癌",
    "中西成药,心脑血管用药,心脑血管用药,其他类",
    "中西成药,抗肿瘤用药,肿瘤其他用药,肿瘤止痛药",
    "中西成药,精神类用药,非典型抗精神病药物,焦虑症",
    "中西成药,抗肿瘤用药,肿瘤其他用药,放化疗后辅助用药",
    "中西成药,血液系统用药,血小板类用药,抑制血小板聚集",
    "中西成药,心脑血管用药,高血压类,β受体阻滞剂",
    "中西成药,心脑血管用药,高血脂类药物,他汀类",
    "中西成药,皮肤病用药,湿疹皮炎类,银屑病",
    "中西成药,中枢神经系统类,脑神经用药,神经性疼痛",
    "中西成药,泌尿生殖系统类,肾病用药,肾炎",
    "中西成药,心脑血管用药,高血脂类药物,烟酸类",
    "中西成药,抗肿瘤用药,泌尿系统肿瘤,肾癌",
    "中西成药,心脑血管用药,高血压类,钙离子通道拮抗剂",
    "中西成药,心脑血管用药,心脑血管用药,血小板减少症",
    "中西成药,血液系统用药,白细胞减少症和粒细胞减少症,白细胞减少症",
    "中西成药,安神助眠类,肝郁化火,神经衰弱",
    "中西成药,抗肿瘤用药,肿瘤其他用药,中成抗肿瘤药",
    "中西成药,心脑血管用药,高血压类,其他高血压类",
    "中西成药,中枢神经系统类,其他,其他中枢神经系统类",
    "中西成药,抗肿瘤用药,消化系统肿瘤,食道癌",
    "中西成药,心脑血管用药,高血压类,利尿药",
    "中西成药,皮肤病用药,细菌感染皮肤病,麻风病",
    "中西成药,五官科用药,眼科用药,白内障",
    "中西成药,中枢神经系统类,脑神经用药,癫痫",
    "中西成药,中枢神经系统类,脑神经用药,阿尔兹海默症",
    "中西成药,皮肤病用药,遗传和代谢类皮肤病,鱼鳞病",
    "中西成药,心脑血管用药,冠心病类,心绞痛",
    "中西成药,心脑血管用药,冠心病类,宽胸通痹",
    "中西成药,呼吸系统用药,呼吸系统用药,肺结核",
    "中西成药,肝胆疾病用药,胆囊疾病用药,胆囊炎",
    "中西成药,皮肤病用药,皮肤色素异常类,白癜风",
    "中西成药,中枢神经系统类,脑神经用药,面瘫",
    "中西成药,呼吸系统用药,支气管用药,支气管炎",
    "中西成药,抗肿瘤用药,抗肿瘤用药,白血病",
    "中西成药,妇科用药,更年期调理,更年期综合症",
    "中西成药,心脑血管用药,高血压类,肝阳上亢",
    "中西成药,抗肿瘤用药,消化系统肿瘤,肝癌",
    "中西成药,妇科用药,更年期调理,雌激素类调节药",
    "中西成药,中枢神经系统类,脑神经用药,偏头痛",
    "中西成药,中枢神经系统类,运动神经用药,帕金森",
    "中西成药,心脑血管用药,高血脂类药物,痰湿症",
    "中西成药,五官科用药,眼科用药,青光眼",
    "中西成药,中枢神经系统类,脑神经用药,眩晕症",
    "中西成药,糖尿病用药,2型糖尿病,二肽基肽酶-4抑制剂",
    "中西成药,妇科用药,更年期调理,其他更年期用药",
    "中西成药,风湿镇痛用药,痛风用药,抑制尿酸合成",
    "中西成药,心脑血管用药,高血脂类药物,贝特类",
    "中西成药,抗肿瘤用药,泌尿系统肿瘤,前列腺癌",
    "中西成药,糖尿病用药,2型糖尿病,双胍类",
    "中西成药,中枢神经系统类,脑神经用药,脑功能损伤",
    "中西成药,风湿镇痛用药,风湿和类风湿类,关节红肿发热疼痛",
    "中西成药,风湿镇痛用药,痛风用药,促尿酸排泄药",
    "中西成药,内分泌类,甲状腺疾病,甲状腺亢进症",
    "中西成药,呼吸系统用药,肺部用药,慢性阻塞性肺病",
    "中西成药,风湿镇痛用药,风湿和类风湿类,筋骨痿软，行走困难",
    "中西成药,抗肿瘤用药,呼吸系统肿瘤,肺癌",
    "中西成药,糖尿病用药,其他糖尿病用药,消渴症",
    "中西成药,血液系统用药,贫血,缺铁性贫血",
    "中西成药,糖尿病用药,糖尿病并发症,糖尿病神经病变",
    "中西成药,妇科用药,乳腺疾病类,乳腺增生",
    "中西成药,抗肿瘤用药,肿瘤其他用药,肿瘤营养药",
    "中西成药,肝胆疾病用药,肝病治疗用药,脂肪肝",
    "中西成药,骨科用药,骨质增生类,骨质增生",
    "中西成药,肝胆疾病用药,肝病治疗用药,黄疸性肝炎",
    "中西成药,心脑血管用药,心律失常类,其他类心率失常药",
    "中西成药,心脑血管用药,脑卒中类,缺血性脑卒中",
    "中西成药,妇科用药,子宫疾病类,子宫内膜异位症",
    "中西成药,风湿镇痛用药,风湿和类风湿类,关节痛有定处",
    "中西成药,妇科用药,子宫疾病类,子宫肌瘤",
    "中西成药,心脑血管用药,心脑血管用药,胆固醇吸收抑制剂",
    "中西成药,血液系统用药,其他血液系统疾病,其他血液系统疾病",
    "中西成药,泌尿生殖系统类,肾病用药,尿毒症",
    "中西成药,血液系统用药,贫血,营养性贫血",
    "中西成药,糖尿病用药,糖尿病并发症,糖尿病性视网膜病变",
    "中西成药,肝胆疾病用药,肝病治疗用药,肝硬化",
    "中西成药,糖尿病用药,其他糖尿病用药,其他",
    "中西成药,呼吸系统用药,肺部用药,肺炎",
    "中西成药,抗肿瘤用药,其他肿瘤类,其他肿瘤类用药",
    "中西成药,风湿镇痛用药,风湿和类风湿类,关节疼痛厉害",
    "中西成药,抗肿瘤用药,消化系统肿瘤,胃癌",
    "中西成药,抗肿瘤用药,消化系统肿瘤,直肠癌",
    "中西成药,肝胆疾病用药,肝病治疗用药,肝纤维化",
    "中西成药,心脑血管用药,脑卒中类,出血性脑卒中",
    "中西成药,糖尿病用药,2型糖尿病,磺脲类促泌剂",
    "中西成药,风湿镇痛用药,痛风用药,关节红肿发热疼痛",
    "中西成药,中枢神经系统类,脑神经用药,神经官能症",
    "中西成药,血液系统用药,贫血,再生障碍性贫血",
    "中西成药,妇科用药,乳腺疾病类,乳腺炎",
    "中西成药,其他药品,器官移植类,肝肾移植",
    "中西成药,抗肿瘤用药,消化系统肿瘤,胃癌",
    "中西成药,抗肿瘤用药,消化系统肿瘤,直肠癌",
    "中西成药,肝胆疾病用药,肝病治疗用药,肝纤维化",
    "中西成药,心脑血管用药,脑卒中类,出血性脑卒中",
    "中西成药,糖尿病用药,2型糖尿病,磺脲类促泌剂",
    "中西成药,风湿镇痛用药,痛风用药,关节红肿发热疼痛",
    "中西成药,中枢神经系统类,脑神经用药,神经官能症",
    "中西成药,血液系统用药,贫血,再生障碍性贫血",
    "中西成药,妇科用药,乳腺疾病类,乳腺炎",
    "中西成药,其他药品,器官移植类,肝肾移植",
    "中西成药,抗肿瘤用药,生殖系统肿瘤,宫颈癌",
    "中西成药,其他药品,器官移植类,其他器官移植",
    "中西成药,内分泌类,甲状腺疾病,甲状腺减退症",
    "中西成药,糖尿病用药,2型糖尿病,噻唑烷二酮类药物",
    "中西成药,妇科用药,子宫疾病类,其他子宫疾病",
    "中西成药,皮肤病用药,寄生虫类皮肤病,红斑狼疮",
    "中西成药,心脑血管用药,高血脂类药物,胆酸螯合剂",
    "中西成药,中枢神经系统类,脑神经用药,肝性脑病",
    "中西成药,心脑血管用药,冠心病类,心肌梗死",
    "中西成药,肝胆疾病用药,肝病治疗用药,酒精性肝炎",
    "中西成药,心脑血管用药,高血脂类药物,其他高血脂类",
    "中西成药,肝胆疾病用药,胰腺疾病用药,急性胰腺炎",
    "中西成药,风湿镇痛用药,痛风用药,急性痛风用药",
    "中西成药,心脑血管用药,冠心病类,其他冠心病类",
    "中西成药,糖尿病用药,2型糖尿病,餐时血糖调节剂",
    "中西成药,精神类用药,非典型抗精神病药物,躁狂症",
    "中西成药,五官科用药,耳部用药,美尼尔氏综合症",
    "中西成药,风湿镇痛用药,痛风用药,其他痛风用药",
    "中西成药,中枢神经系统类,运动神经用药,重症肌无力",
    "中西成药,心脑血管用药,心脑血管其他,动脉粥样硬化症",
    "中西成药,抗肿瘤用药,消化系统肿瘤,大肠癌",
    "中西成药,心脑血管用药,心律失常类,钠通道阻滞药",
    "中西成药,心脑血管用药,心律失常类,延长动作电位时程药",
    "中西成药,中枢神经系统类,运动神经用药,舞蹈症",
  ]
  if (prescriptionCategory == cfyId && arr.indexOf(sixCategoryName) != -1) {
    return true
  } else {
    return false
  }
}
