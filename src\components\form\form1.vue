<template>
<el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px">
    <el-form-item label="第一个表单" prop="region">
        <el-select v-model="ruleForm.region" placeholder="请选择表单" @change="changeSelect">
            <el-option v-for="(item,key) in selectOptions" 
            :key="key"
            :label="item.label"
            :value="item.value"
            ></el-option>
        </el-select>
    </el-form-item>   
    <el-form-item label="我是必填" prop="name">
        <el-input v-model="ruleForm.name"></el-input>
    </el-form-item>
    <el-form-item>
        <el-button type="primary" @click="submitForm('ruleForm')">立即创建</el-button>
        <el-button @click="resetForm('ruleForm')">重置</el-button>
    </el-form-item>
</el-form> 
</template>
<script>
  export default {
    props: {
        selectValue:{
            type:String,
            default:''
        }
    },
    data() {
      return {
        selectOptions: [
          {
            value:"1",
            label: '表单1'
          }, 
          {
            value: "2",
            label: '表单2'
          }
        ], 
        ruleForm: {
          name: '',
          region: '',
        },
        rules: {
          name: [
            { required: true, message: '请输入活动名称', trigger: 'blur' },
            { min: 3, max: 5, message: '长度在 3 到 5 个字符', trigger: 'blur' }
          ],
          region: [
            { required: true, message: '请选择表单名称', trigger: 'change' }
          ]
        }
      };
    },
    mounted(){
        this.ruleForm.region=this.selectValue;
    },
    methods: {
      submitForm(formName) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            alert('submit!');
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      },
      resetForm(formName) {
        this.$refs[formName].resetFields();
      },
      changeSelect(value){
          this.$emit("changeForm",value)
      }
    }
  }
</script>
<style scoped lang="scss">
</style>