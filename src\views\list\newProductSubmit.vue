
<template>
  <div class="page-container">
    <!-- 查询表单 -->
    <div class="search-form-wrap">
      <el-form :model="model" ref="refSearchForm">
        <el-row type="flex">
          <!-- 最后同步时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="上报时间">
              <el-date-picker
                v-model="searchFormData.createTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <!-- 通用名 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="通用名">
              <el-input
                v-model="searchFormData.generalName"
                placeholder="通用名/商品名/助记码"
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- 小包装条码 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="小包装条码">
              <el-input
                v-model="searchFormData.smallPackageCode"
                placeholder="请输入小包装条码"
              ></el-input>
            </el-form-item>
          </el-col>
          <!-- 处方分类 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="处方分类">
              <el-select
                v-model="searchFormData.prescriptionCategory"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in prescriptionCategoryOption"
                  :key="item.id"
                  :label="item.dictName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 规格/型号 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="规格/型号">
              <el-input v-model="searchFormData.spec"></el-input>
            </el-form-item>
          </el-col>
          <!-- 生产厂家 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="生产厂家">
              <el-input v-model="searchFormData.manufacturerName"></el-input>
            </el-form-item>
          </el-col>
          <!-- 批准文号 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="批准文号">
              <el-input v-model="searchFormData.approvalNo"></el-input>
            </el-form-item>
          </el-col>
          <!-- 首次同步时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="最后更新时间">
              <el-date-picker
                v-model="searchFormData.updateTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <!-- 商品来源 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="商品来源">
              <el-select v-model="searchFormData.source" placeholder="请选择">
                <el-option label="全部" value=""></el-option>
                <el-option label="荷叶健康" :value="1"></el-option>
                <el-option label="SAAS智鹿" :value="2"></el-option>
                <el-option label="实施提报" :value="3"></el-option>
                <el-option label="POP" :value="4"></el-option>
                <el-option label="商品中台" :value="5"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 处理状态 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="处理状态">
              <el-select v-model="searchFormData.operateStatus" placeholder="请选择">
                <el-option label="所有" value="null"></el-option>
                <el-option label="待处理" :value="1"></el-option>
                <el-option label="审核中" :value="3"></el-option>
                <el-option label="处理完成" :value="4"></el-option>
                <el-option label="驳回" :value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 处理人 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="处理人">
              <el-input v-model="searchFormData.updateUser"></el-input>
            </el-form-item>
          </el-col>

          <!-- 标品库ID -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="标品库ID">
              <el-input v-model="searchFormData.productId"></el-input>
            </el-form-item>
          </el-col>

          <!-- 商品业务编码 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="商品业务编码">
              <el-input v-model="searchFormData.outProductCode"></el-input>
            </el-form-item>
          </el-col>
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="复制品编码" class="green">
              <el-input v-model="searchFormData.barcodes" >
                <el-button slot="append" @click="searchCodes">查询原商品编码</el-button>
                <!-- <template slot="append" ><div style="height:100%"  @click="searchCodes">查询原商品编码</div></template> -->
              </el-input>
            </el-form-item>
          </el-col>



          <!-- 来源明细 -->
          <!-- <el-col :lg="8" :xs="24"  :sm="12"  :xl="6">
            <el-form-item label="来源明细">
              <el-input v-model="searchFormData.sourceDetail"></el-input>
            </el-form-item>
          </el-col> -->
          <!-- 实施名称 -->
          <!-- <el-col :lg="8" :xs="24"  :sm="12"  :xl="6">
            <el-form-item label="实施名称">
              <el-input v-model="searchFormData.implementerJobName"></el-input>
            </el-form-item>
          </el-col> -->
          <!-- 按钮 -->
          <el-col :span="6" :offset="18">
            <el-button type="primary" size="medium" @click="productExport">导出</el-button>
            <el-button type="primary" size="medium" @click="btnSearchClick(1)"
              >查询</el-button
            >
            <el-button size="medium" @click="btnResetClick">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- table表单 -->
    <!-- table -->
    <div class="table-wrap">
      <vxe-table
        border
        highlight-hover-row
        resizable
        height="100%"
        auto-resize
        size="small"
        align="center"
        :tooltip-config="{ enterable: false }"
        :loading="tableLoading"
        :data="tableData"
        :seq-config="{ startIndex: (pageNum - 1) * searchFormData.limit }"
        @cell-dblclick="cellDBLClickEvent" 
        ref="refVxeTable"
        @sort-change="sortQueryList"
      >
        <vxe-table-column
          type="seq"
          title="序号"
          width="60"
          show-header-overflow
          show-overflow
          fixed="left"
        ></vxe-table-column>
        <vxe-table-column
          field="spuCategory"
          title="商品大类"
          min-width="120"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">
            {{ row.spuCategory | filterSpuCategory }}
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="statusCode"
          title="处理状态"
          min-width="120"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">
            <!-- 1 ：待处理 2：驳回 3：审核中 4 完成 -->
            {{ ["待处理", "驳回", "审核中", "处理完成"][row.statusCode - 1] }}
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="updateTime"
          title="最后更新时间"
          width="120"
          show-header-overflow
          show-overflow
            sortable
        >
          <template v-slot="{ row }">
            {{ parseTime(row.updateTime) }}
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="smallPackageCode"
          title="小包装条码"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="outProductCode"
          title="业务编码"
          min-width="120"
          :formatter="outProductCodeInit"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="generalName"
          title="通用名"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="spec"
          title="规格/型号"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="manufacturerName"
          title="生产厂家"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="packageUnitName"
          title="包装单位"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="dosageFormName"
          title="剂型"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="approvalNo"
          title="批准文号"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="prescriptionCategoryName"
          title="处方分类"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          title="商品来源"
          min-width="120"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">
            {{ ["", "荷叶健康", "SAAS智鹿", "实施提报", "POP","商品中台"][row.source] }}
          </template>
        </vxe-table-column>
        <!-- <vxe-table-column field="sourceDetail" title="来源明细" min-width="120" show-header-overflow show-overflow></vxe-table-column> -->
        <vxe-table-column
          field="createTime"
          title="上报时间"
          min-width="120"
          show-header-overflow
          show-overflow
           sortable
        >
         
          <template v-slot="{ row }">
            {{ parseTime(row.createTime) }}
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="remark"
          title="意见"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="updateUser"
          title="处理人"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="productId"
          title="标准库ID"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <!-- <vxe-table-column
          field="filingsAuthor"
          title="化妆品备案人/注册人"
          min-width="180"
          show-header-overflow
          show-overflow
        ></vxe-table-column> -->
        <!-- <vxe-table-column
          title="操作"
          width="120"
          show-header-overflow
          show-overflow
          fixed="right"
        >
          <template v-slot="{ row }">
            <span v-if="row.statusCode == 1">
              <el-link
                :underline="false"
                type="primary"
                v-if="row.receive == 0"
                @click="receive(row)"
                >领取并处理</el-link
              >
              <el-link
                :underline="false"
                type="primary"
                v-if="row.receive == 1"
                @click="goDetail(row)"
                >处理</el-link
              >
            </span>
            <span v-else>
              <el-link disabled>-</el-link>
            </span>
          </template>
        </vxe-table-column> -->
      </vxe-table>
    </div>

    <!-- 分页 -->
    <el-row class="custom-pagination-wrap">
      <el-col>
        <el-pagination
          background
          :current-page.sync="searchFormData.page"
          :page-sizes="[20, 50, 100, 200]"
          :page-size.sync="searchFormData.limit"
          :total="total"
          layout="prev, pager, next, jumper,total, sizes"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { parseTimestamp } from "@/utils/index.js";
import { findProductPresentList, presentReceive, exportProductPresentList ,queryOriginBarcodes} from "@/api/list.js";
import { dictListSearch } from "@/api/dict";
import axios from "axios";
export default {
  name: "",
  components: {},
  filters: {},
  props: {},
  data() {
    return {
      searchFormData: {
        page: 1,
        limit: 20,
        sortList: [
          {
            order: 0,
            columnName: "createTime",
            lift: "ASC",
          },
        ],
        createTime: [],
        updateTime: [],
        generalName: "", //商品通用名
        smallPackageCode: "", //小包装条码
        manufacturerName: "", //生产厂家
        approvalNo: "", // 批准文号
        createStartTime: "", // 首次同步开始时间
        createEndTime: "", //首次同步结束时间
        updateStartTime: "", //最后同步开始时间
        updateEndTime: "", //最后同步结束时间
        source: "", // 来源 1-荷叶健康
        // sourceDetail: "", //来源明细
        spec: "", //规格商品
        prescriptionCategory: "", //处方分类
        // implementerJobName: "",
        operateStatus: 1, // 处理状态
        updateUser:"", //处理人
        barcodes:"",//复制品编码
        outProductCode: "", //商品业务编码
        productId: "", //标品库ID
      },
      tableLoading: false,
      tableData: [],
      total: 0,
      prescriptionCategoryOption: [],
    };
  },
  computed: {},
  watch: {},
  created() {
    this.getDist();
    this.btnResetClick();
  },
  mounted() {
    console.log( process.env,111)
  },
  methods: {
   async searchCodes(){
    let dataNum=this.searchFormData.barcodes.split(",")
      if(dataNum.length>200){
        this.$message.error("最多输入200条");
        return 
      }
      let url="" //https://pop-admin.test.ybm100.com/inner/query/queryOriginBarcodes
      if(process.env.NODE_ENV=="development"||process.env.NODE_ENV=="test"){
        url="https://pop-admin.test.ybm100.com/inner/query/queryOriginBarcodes"
      }else if(process.env.NODE_ENV=="production"){
        url="https://pop-admin.ybm100.com/inner/query/queryOriginBarcodes"
      }
    let res=await axios({
        url:`${url}?barcodes=${this.searchFormData.barcodes}`,
        method:"post"
      })
      res=res.data
      if(res.code==0){
        this.searchFormData.outProductCode=Array.isArray(res.data)?res.data.join(","):""
        
      }else{
        this.$message.error(res.message);
      }
     
    },
    outProductCodeInit(row) {
      if(!row.row.outProductCode){
        return '-'
      }
      
      return row.row.outProductCode;
    },
    cellDBLClickEvent(e){
      // //似乎没有什么卵用
      // return
      let { procDefId, taskNode, procInstId, productCode, applyCode, taskPostCode, source, uniqueCode, productType, outProductCode } = e.row
      try {
          parent.CreateTab(
            "../static/dist/index.html#/list/newProductDetaill" +
              "?procInstId=" +
              procInstId +
              "&procDefId=" +
              procDefId +
              "&nodeKey=" +
              taskNode +
              "&taskPostCode=" +
              taskPostCode +
              "&source=" +
              source +
              "&applyCode=" +
              applyCode +
              "&uniqueCode=" +
              uniqueCode +
              "&productType=" +
              2 +
              "&productCode=" +
              productCode +
              "&outProductCode=" +
              outProductCode +
              "&detailType=self" +
              "&pageType=detail",
            "新品详情"
          )
        } catch (error) {
          this.$router.push({
            path: "/list/newProductDetaill",
            query: {
              procInstId,
              nodeKey: taskNode,
              procDefId,
              applyCode,
              productType: 2,
              productCode,
              detailType: "self",
              taskPostCode,
              pageType: "detail",
              source,
              uniqueCode,
              outProductCode,
            },
          })
        }
    },
    // 导出
    productExport() {
      let param = _.cloneDeep(this.searchFormData);
       
      if (param.createTime && param.createTime.length > 0) {
          param.createStartTime = param.createTime[0];
          param.createEndTime = param.createTime[1];
        } else {
          param.createStartTime = ''
          param.createEndTime = ''
        }
        if (param.updateTime && param.updateTime.length > 0) {
          param.updateStartTime = param.updateTime[0];
          param.updateEndTime = param.updateTime[1];
        } else {
          param.updateStartTime = ''
          param.updateEndTime = ''
        }
      delete param.createTime;
      delete param.updateTime;
      delete param.page
      delete param.limit
      exportProductPresentList(param)
        .then((res) => {
          if (!res.retCode) {
            this.$message.success(res.retMsg)
          } else {
            this.$message({
              showClose: true,
              type: "error",
              message: res.retMsg,
            })

          }
        })
        .catch((err) => {
          this.$message({
            showClose: true,
            type: "error",
            message: "导出发生错误，请重试",
          });
        });
    },
    async getDist() {
      try {
        // 处方分类
        let res = await dictListSearch({
          type: 9,
        });
        this.prescriptionCategoryOption = res.data.list;
      } catch (error) {}
    },
    /**
     * @description: 加载列表数据
     * @param {type}
     * @return:
     */
    async getListData() {
      try {
        this.tableLoading = true;
        let param = _.cloneDeep(this.searchFormData);
       
        if (param.createTime && param.createTime.length > 0) {
          param.createStartTime = param.createTime[0];
          param.createEndTime = param.createTime[1];
        } else {
          param.createStartTime = ''
          param.createEndTime = ''
        }
        if (param.updateTime && param.updateTime.length > 0) {
          param.updateStartTime = param.updateTime[0];
          param.updateEndTime = param.updateTime[1];
        } else {
          param.updateStartTime = ''
          param.updateEndTime = ''
        }
        delete param.createTime;
        delete param.updateTime;
        let res = await findProductPresentList(param);
         this.tableLoading = false;
        if (res.success) {
          this.tableData = res.data.list;
          this.total = res.data.total;
        }
       
      } catch (error) {
        console.log(error);
      }
    },
    /**
     * 查询按钮点击
     */
    btnSearchClick(e) {
      if(e) {
        this.searchFormData.page = 1
        this.pageNum = 1
      }
      this.getListData();
    },
// 排序查询
    sortQueryList({ column, property, order }) {
     this.searchFormData.sortList[0].columnName=property;
       this.searchFormData.sortList[0].lift=order || "ASC";
     
     this.handleCurrentChange(1);
    },
    /**
     * 重置
     */
    btnResetClick() {
      this.searchFormData = {
        page: 1,
        limit: 20,
        sortList: [
          {
            order: 0,
            columnName: "createTime",
            lift: "ASC",
          },
        ],
        createTime: [],
        updateTime: [],
        generalName: "", //商品通用名
        smallPackageCode: "", //小包装条码
        manufacturerName: "", //生产厂家
        approvalNo: "", // 批准文号
        createStartTime: "", // 首次同步开始时间
        createEndTime: "", //首次同步结束时间
        updateStartTime: "", //最后同步开始时间
        updateEndTime: "", //最后同步结束时间
        source: "", // 来源 1-荷叶健康
        // sourceDetail: "", //来源明细
        spec: "", //规格商品
        prescriptionCategory: "", //处方分类
        // implementerJobName: "",
        operateStatus: 1,
        barcodes:"",//复制品编码
        outProductCode:"",
        updateUser: ""
      };
      this.getListData();
    },
    async receive(row) {
      try {
        let res = await presentReceive({ uniqueCode: row.uniqueCode });
        console.log(res);
        if (res.success) {
          this.btnSearchClick();
          this.goDetail(row)
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.data.retMsg,
          });
        }
      } catch (error) {}
    },
    goDetail(row) {
      try {
        parent.CreateTab(
          "../static/dist/index.html#/product/addProduct?uniqueCode=" +
            row.uniqueCode +
            "&type=present",
          "商品新增",
          true
        );
      } catch (error) {
        console.error(error);
        this.$router.push({
          name: "addProduct",
          query: {
            uniqueCode: row.uniqueCode,
            type: "present",
          },
        });
      }
    },
    /**
     * pageSize 改变事件
     */
    handleSizeChange(pageSize) {
      this.searchFormData.page = 1;
      this.searchFormData.limit = pageSize;
      this.getListData();
    },

    /**
     * pageNum 改变事件
     */
    handleCurrentChange(currentPage) {
      console.info(currentPage);
      this.searchFormData.page = currentPage;
      this.pageNum = currentPage
      this.getListData();
    },
    /**
     * 下载失败原因excel
     */
    downloadExcel(rowData) {
      console.log(rowData);
      //下载原因
      fileDownLoad(rowData.analysisFailUrl, rowData.annexName)
        .then((resp) => {
          if (resp.data && resp.data.retCode) {
            //失败
            this.$message({
              showClose: true,
              type: "error",
              message: resp.data.retMsg,
            });
          } else {
            let blob = new Blob([resp]);
            let blobUrl = window.URL.createObjectURL(blob);
            let a = document.createElement("a");
            a.style.display = "none";
            a.download = rowData.annexName;
            a.href = blobUrl;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
          }
        })
        .catch((err) => {
          this.$message({
            showClose: true,
            type: "error",
            message: "下载失败，请重试",
          });
        });
    },
    /**
     * @description: 格式化时间参数
     * @param {date} time 时间戳
     * @return: string 格式化后的时间戳
     */
    parseTime(time) {
      return parseTimestamp(time);
    },
  },
};
</script>

<style lang="scss" scoped>
.page-container {
  /**
   * 查询表单
   */
  .search-form-wrap {
    width: 100%;
    padding: 15px;
    border-bottom: 1px dashed #e4e4eb;

    .el-row {
      flex-wrap: wrap;

      .el-col {
        display: flex;
        justify-content: flex-end;

        .el-form-item {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          width: 100%;
          margin-right: 0;
          margin-bottom: 15px;

          /deep/ {
            .el-form-item__label {
              width: 116px;
              line-height: normal;
              padding: 0 12px;
              color: #292933;
              font-weight: normal;
            }

            .el-form-item__content {
              flex: 1;

              .el-range-editor.el-input__inner {
                width: 100%;

                .el-range-separator {
                  width: 14px;
                  padding: 0;
                }
              }
            }
          }

          .el-input {
            width: 100%;
          }

          .el-select {
            width: 100%;
          }
        }
      }
    }
  }

  /**
     * table
     */
  .table-wrap {
    width: 100%;
    min-height: 440px;
    height: calc(100vh - 296px);
    padding: 0 15px;
    margin-top: 15px;
  }
}
</style>
<style>
.green .el-input-group__append{
background-color: rgb(59,149,168);
color:white;
cursor: pointer;
}
</style>
