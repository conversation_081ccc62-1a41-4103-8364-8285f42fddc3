@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './custom-vxe-table.scss';
@import './custom-dialog.scss';
@import './custom-pagination.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

// main-container global css
.app-container {
  padding: 20px;
}
// 滚动条样式调整
::-webkit-scrollbar {
  /*高宽分别对应竖横滚动条的尺寸*/
  width: 5px;
  height: 10px;
}
::-webkit-scrollbar-thumb {/*滚动条里面小方块*/
  border-radius: 2px;
  box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
  background: #a19f9f;
}
::-webkit-scrollbar-thumb:hover{
  background: #3d3d3d;
}
::-webkit-scrollbar-track {/*滚动条里面轨道*/
  background: #EDEDED;
}

// 禁用掉 input number 加减箭头 谷歌
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
  margin: 0;
}
// 火狐
input[type="number"]{
  -moz-appearance:textfield;
}

.text-rt{
  text-align: right;
}
.tox-silver-sink{
  position: fixed !important;
  z-index: 9999 !important;
  .tox-dialog-wrap__backdrop{
    background-color: rgba(0,0,0,0.5) !important;
  }
}