<!--
 * @Descripttion: 
 * @Version: 1.0
 * @Author: zyh
 * @Date: 2025-02-09 13:53:24
 * @LastEditors: zyh
 * @LastEditTime: 2025-02-18 14:08:48
-->

<template>
    <div class="container">
        <el-dialog title="拓展值配置"
                   :visible.sync="dialogVisible"
                   width="700px">
            <div class="search-form-box">
                <el-form :inline="true"
                         :model="formData">
                    <el-form-item label="商品大类">
                        <el-select v-model="formData.spuCategory"
                                   placeholder="请选择"
                                   disabled
                                   clearable>
                            <el-option v-for="item in categoryCodeList"
                                       :key="item.id"
                                       :label="item.dictName"
                                       :value="item.id"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="通用名">
                        <el-input v-model.trim="formData.generalName"
                                  clearable
                                  placeholder="通用名"></el-input>
                    </el-form-item>
                    <el-form-item style="margin-left:15px">
                        <el-button size="medium"
                                   type="primary"
                                   @click="getSpuGeneralNameList">查询</el-button>
                        <el-button size="medium"
                                   type="primary"
                                   @click="onSure">确定</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <div class="table-wrap">
                <vxe-table border
                           highlight-hover-row
                           resizable
                           auto-resize
                           size="small"
                           align="center"
                           :tooltip-config="{ enterable: false }"
                           :loading="tableLoading"
                           :data="tableData"
                           :seq-config="{ startIndex: (pageNum - 1) * pageSize }"
                           ref="refVxeTable"
                           @radio-change="radioChangeEvent"
                           @checkbox-change="selectChangeEvent">
                    <vxe-table-column type="radio"
                                      width="60"
                                      fixed="left"></vxe-table-column>
                    <vxe-table-column type="seq"
                                      title="序号"
                                      width="60"
                                      show-header-overflow
                                      show-overflow></vxe-table-column>
                    <vxe-table-column field="spuCategoryName"
                                      title="商品大类"
                                      width="180"
                                      show-header-overflow
                                      show-overflow>
                    </vxe-table-column>
                    <vxe-table-column field="generalName"
                                      title="通用名"
                                      show-header-overflow
                                      show-overflow>
                    </vxe-table-column>
                </vxe-table>
            </div>
        </el-dialog>
        <ExpandFieldConfig ref="expandFieldConfig" />
    </div>
</template>
<script>
import { apiGetCategoryDict, apiGetSpuGeneralNameList } from "@/api/dict.js";
import ExpandFieldConfig from './ExpandFieldConfig.vue'
import { parseTimestamp } from "@/utils/index.js";
export default {
    name: "",
    components: {
        ExpandFieldConfig
    },
    data () {
        return {
            isEditing: false,
            dialogVisible: false,
            tableData: [],
            tableLoading: false,
            categoryCodeList: [],
            formData: {
                spuCategory: 2,//商品大类 默认中药2
                generalName: "",//通用名
            },

            selecteInfo: null,
        };
    },
    methods: {
        async getCategoryDict () {
            const res = await apiGetCategoryDict();
            if (res.retCode == 0) {
                this.categoryCodeList = res.data;
            } else {
                this.$message({
                    showClose: true,
                    type: "error",
                    message: res.retMsg,
                });
            }
        },
        async getSpuGeneralNameList () {
            this.tableData = [];
            if (!this.formData.generalName) return this.$message.info("请先输入通用名")
            this.tableLoading = true;
            let res = await apiGetSpuGeneralNameList({
                // limit: 40,
                // page: 1,
                ...this.formData,
            });
            this.tableLoading = false;
            if (res.retCode == 0) {
                this.tableLoading = false;
                this.tableData = res.data.list || [];
                this.total = res.data.total;
            } else {
                this.$message({
                    showClose: true,
                    type: "error",
                    message: res.retMsg,
                });
            }
        },
        open () {
            this.getCategoryDict();
            this.formData.generalName = '';
            this.tableData = [];
            this.selecteInfo = null;
            this.dialogVisible = true;
        },
        radioChangeEvent ({ row }) {
            this.selecteInfo = row;
        },
        onSure () {
            if (!this.selecteInfo) return this.$message.info("请先查询之后选择编辑行");
            this.$refs.expandFieldConfig.open(this.selecteInfo);
        }
    },
};
</script>
<style lang="scss" scoped>
.table-wrap {
    padding-bottom: 50px;
}
.search-form-box {
    margin-bottom: 12px;
    .el-form-item {
        margin-right: 20px;
        margin-bottom: 15px;
        .el-input {
            width: 150px;
        }
        .el-select {
            width: 140px;
        }
    }
}
</style>
<style lang="scss">
.vxe-table--tooltip-wrapper  {
  z-index: 3000!important;
}

</style>