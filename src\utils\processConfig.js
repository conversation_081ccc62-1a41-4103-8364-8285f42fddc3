import { parseTimestamp } from "@/utils/index.js";


export function getProcessCofig(info) {
    let result = {
        processList: [],
        active: 0
    };
    switch (info.flowNum) {
        case 0:
            result = {
                processList:[{
                    title: approvalTitle(info.applyJob, info.applyUserName),
                    description: approvalDetail(info.applyTime, info.applyAction),
                }, {
                    title: approvalTitle(info.firstJob, info.firstReviewName),
                    description: approvalDetail(info.firstTime, info.firstAction),
                }, {
                    title: approvalTitle(info.secondJob, info.secondReviewName),
                    description: approvalDetail(info.secondTime, info.secondAction)
                }],
                active: 0
            }
            break;
        case 1:
            result = {
                processList:[{
                    title: approvalTitle(info.applyJob, info.applyUserName),
                    description: approvalDetail(info.applyTime, info.applyAction)
                }, {
                    title: approvalTitle(info.firstJob, info.firstReviewName),
                    description: approvalDetail(info.firstTime, info.firstAction)
                }, {
                    title: approvalTitle(info.secondJob, info.secondReviewName),
                    description: approvalDetail(info.secondTime, info.secondAction)
                }],
                active: 1
            }
            break;
        case 2:
            result = {
                processList:[{
                    title: approvalTitle(info.applyJob, info.applyUserName),
                    description: approvalDetail(info.applyTime, info.applyAction)
                }, {
                    title: approvalTitle(info.firstJob, info.firstReviewName),
                    description: approvalDetail(info.firstTime, info.firstAction),
                }, {
                    title: approvalTitle(info.secondJob, info.secondReviewName),
                    description: approvalDetail(info.secondTime, info.secondAction),
                }],
                active: 2,
            }
            break;
        case 3:
            result = {
                processList:[{
                    title: approvalTitle(info.applyJob,info.applyUserName),
                    description: approvalDetail(info.applyTime,info.applyAction)
                }, {
                    title: approvalTitle(info.firstJob,info.firstReviewName),
                    description: approvalDetail(info.firstTime,info.firstAction),
                    icon: 'el-icon-warning-outline'
                }, {
                    title: approvalTitle(info.secondJob,info.secondReviewName),
                    description: approvalDetail(info.secondTime,info.secondAction)
                }],
                active: 0,
            }
            break;
        case 4:
            result = {
                processList:[{
                    title: approvalTitle(info.applyJob,info.applyUserName),
                    description: approvalDetail(info.applyTime,info.applyAction),
                    icon: 'el-icon-warning-outline'
                }, {
                    title: approvalTitle(info.firstJob,info.firstReviewName),
                    description: approvalDetail(info.firstTime,info.firstAction),
                    icon: 'el-icon-warning-outline'
                }, {
                    title: approvalTitle(info.secondJob,info.secondReviewName),
                    description: approvalDetail(info.secondTime,info.secondAction)
                }],
                active: 0,
            }
            break;
        case 5:
            result = {
                processList:[{
                    title: approvalTitle(info.applyJob,info.applyUserName),
                    description: approvalDetail(info.applyTime,info.applyAction)
                }, {
                    title: approvalTitle(info.firstJob,info.firstReviewName),
                    description: approvalDetail(info.firstTime,info.firstAction)
                }, {
                    title: approvalTitle(info.secondJob,info.secondReviewName),
                    description: approvalDetail(info.secondTime,info.secondAction)
                }],
                active: 3,
            }
            break;
        case 6:
            result = {
                processList:[{
                    title: approvalTitle(info.applyJob,info.applyUserName),
                    description: approvalDetail(info.applyTime,info.applyAction)
                }, {
                    title: approvalTitle(info.firstJob,info.firstReviewName),
                    description: approvalDetail(info.firstTime,info.firstAction),
                    icon: 'el-icon-circle-check',
                }, {
                    title: approvalTitle(info.secondJob,info.secondReviewName),
                    description: approvalDetail(info.secondTime,info.secondAction),
                    icon: 'el-icon-warning-outline'
                }],
                active: 0,
            }
            break;
        case 7:
            result = {
                processList:[{
                    title: approvalTitle(info.applyJob,info.applyUserName),
                    description: approvalDetail(info.applyTime,info.applyAction),
                    icon: 'el-icon-warning-outline'
                }, {
                    title: approvalTitle(info.firstJob,info.firstReviewName),
                    description: approvalDetail(info.firstTime,info.firstAction),
                    icon: 'el-icon-circle-check',
                }, {
                    title: approvalTitle(info.secondJob,info.secondReviewName),
                    description: approvalDetail(info.secondTime,info.secondAction),
                    icon: 'el-icon-warning-outline'
                }],
                active: 0,
            }
            break;
        case 8:
            result = {
                processList:[{
                    title: approvalTitle(info.applyJob,info.applyUserName),
                    description: approvalDetail(info.applyTime,info.applyAction)
                }, {
                    title: approvalTitle(info.firstJob,info.firstReviewName),
                    description: approvalDetail(info.firstTime,info.firstAction)
                }],
                active: 0,
            }
            break;
        case 9:
            result = {
                processList:[{
                    title: approvalTitle(info.applyJob,info.applyUserName),
                    description: approvalDetail(info.applyTime,info.applyAction)
                }, {
                    title: approvalTitle(info.firstJob,info.firstReviewName),
                    description: approvalDetail(info.firstTime,info.firstAction)
                }],
                active: 1,
            }
            break;
        case 10:
            result = {
                processList:[{
                    title: approvalTitle(info.applyJob,info.applyUserName),
                    description: approvalDetail(info.applyTime,info.applyAction)
                }, {
                    title: approvalTitle(info.firstJob,info.firstReviewName),
                    description: approvalDetail(info.firstTime,info.firstAction)
                }],
                active: 2,
            }
            break;
        case 11:
            result = {
                processList:[{
                    title: approvalTitle(info.applyJob,info.applyUserName),
                    description: approvalDetail(info.applyTime,info.applyAction)
                }, {
                    title: approvalTitle(info.firstJob,info.firstReviewName),
                    description: approvalDetail(info.firstTime,info.firstAction),
                    icon: 'el-icon-warning-outline'
                }],
                active: 0,
            }
            break;
        case 12:
            result = {
                processList:[{
                    title: approvalTitle(info.applyJob,info.applyUserName),
                    description: approvalDetail(info.applyTime,info.applyAction),
                    icon: 'el-icon-warning-outline'
                }, {
                    title: approvalTitle(info.firstJob,info.firstReviewName),
                    description: approvalDetail(info.firstTime,info.firstAction),
                    icon: 'el-icon-warning-outline'
                }],
                active: 0,
            }
            break;
        default:
            break;
    }
    return result;
}


/**
     * @description:流程标题文本
     * @param {string} job 审核单位
     * @param {string} name 审核人
     * @return: {string}
     */
function approvalTitle(job, name) {
    if (name) {
        return `${job}:${name}`;
    } else {
        return job;
    }
}

/**
     * @description:审批描述文本
     * @param {string}  time 时间戳
     * @param {string} action 描述
     * @return: {string}
     */
function approvalDetail(time, action) {
    if (time) {
        return parseTimestamp(time) + " " + action;
    } else {
        return "";
    }
}