<template>
  <div class="container">
    <el-dialog title="批量导入商品图片补全申请" :visible.sync="dialogVisible" :before-close="close" width="800px">
      <el-form :model="formData" :rules="rules" ref="ruleForm" class="form-wrap">
        <el-form-item label="上传文件类型" prop="type" class="radio-wrap">
          <el-radio-group v-model="formData.type" @change="changeType">
            <el-radio :label="1">zip压缩包</el-radio>
            <el-radio :label="2">Excel</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="商品识别方式" class="radio-wrap">
          <el-radio-group v-model="formData.businessField">
            <el-radio :label="1">标准库id</el-radio>
            <el-radio :label="2">商品编码</el-radio>
            <el-radio :label="3">原商品编码</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="检验是否含图任务" class="radio-wrap">
          <el-radio-group v-model="formData.checkPictureVersion">
            <el-radio :label="1">校验，不允许含有线上版本的商品提交</el-radio>
            <el-radio :label="0">不校验</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="文件名称" prop="fileName" class="upload-wrap">
          <el-input v-model="formData.fileName" disabled="" placeholder="请通过右侧按钮选择文件">
            <el-upload
              slot="append"
              class="upload-wrapper"
              ref="refUpload"
              :action="no"
              :multiple="formData.type == 1 ? true : false"
              :accept="fileAccept"
              :show-file-list="false"
              :auto-upload="false"
              :on-change="uploadOnChange"
            >
              选择文件
            </el-upload>
          </el-input>
        </el-form-item>
      </el-form>
      <el-progress class="upload_progress" style="width:99.6%" v-if="showUploadProgress" :percentage="uploadProgress"></el-progress>
      <div class="tip-wrap">
        <div>*注意：</div>
        <div>1、上传文件时，可选择压缩后的图片文件夹；</div>
        <div>
          2、图片文件压缩包内，每个商品图片放在一个文件夹内，文件夹名称为商品标识-机构号，最多不可超过3000个商品，每个商品的图片数量不可超过10张；
        </div>
        <div>3、上传excel时，需先下载excel模板，按照模板维护需导入的商品任务；</div>
        <div>4、excel中每行/zip文件中的每个文件夹，为一个商品版本任务，如果同一个商品有多行（机构号不同）则生成多个图片版本；</div>
        <div>注：图片名称格式：商品识别方式+“-”+数字，商品编码类型需要与文件夹保持一致。</div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleDownLoad" :disabled="btnDis">下载模板</el-button>
        <el-button type="primary" @click="handleSumit" :disabled="btnDis">开始上传</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
var COS = require("cos-js-sdk-v5")
const JSZip = require("jszip")
import { picComplementUploadExcel, picComplementUploadZip, getTempKey, picComplementCos } from "@/api/follow.js"
import { createDownloadElement } from "@/utils/index.js"
import dayjs from "dayjs"

export default {
  data() {
    return {
      tempKey: {},
      dialogVisible: false,
      uploadProgress: 0,
      showUploadProgress: false,
      formData: {
        type: "",
        fileName: "",
        businessField: 1,
        checkPictureVersion: 1,
      },
      rules: {
        type: [
          {
            required: true,
            message: "请选择上传文件类型",
            trigger: "change",
          },
        ],
        fileName: [
          {
            required: true,
            message: "请上传文件",
            trigger: "change",
          },
        ],
      },
      fileAccept: ".zip,.ZIP,.jpg,.png,.jpeg,.bmp",
      btnDis: false,
      cos: null,
      file: null,
      productPictureBase64List: [],
    }
  },
  methods: {
    async getTempKeyApi() {
      try {
        const { data } = await getTempKey()
        this.tempKey = data
        window.tempKey = data

        this.cos = new COS({
          // getAuthorization 必选参数
          getAuthorization: function (options, callback) {
            callback({
              TmpSecretId: window.tempKey.tmpSecretId,
              TmpSecretKey: window.tempKey.tmpSecretKey,
              SecurityToken: window.tempKey.sessionToken,
              // 建议返回服务器时间作为签名的开始时间，避免用户浏览器本地时间偏差过大导致签名错误
              StartTime: window.tempKey.startTime, // 时间戳，单位秒，如：1580000000
              ExpiredTime: window.tempKey.expiredTime, // 时间戳，单位秒，如：1580000000
            })
          },
        })
      } catch (error) {
        console.log(error)
      }
    },
    open() {
      this.btnDis = false
      this.dialogVisible = true
      this.showUploadProgress = false
      this.uploadProgress = 0
      this.getTempKeyApi()
      this.$nextTick(() => {
        this.$refs.ruleForm.clearValidate()
        this.$refs.refUpload.clearFiles()
        // this.$refs.refUpload.$children[0].$refs.input.webkitdirectory = true
      })
    },
    close() {
      this.formData = {
        type: "",
        businessField: 1,
        checkPictureVersion: 1,
        fileName: "",
      }
      this.dialogVisible = false
    },
    changeType(val) {
      if (val == 1) {
        this.fileAccept = ".zip,.ZIP,.jpg,.png,.jpeg,.bmp"
      } else {
        this.fileAccept = ".xlsx,.jpg,.png,.jpeg,.bmp"
      }
    },
    uploadError(err, file, fileList) {
      console.log("err", err)
      this.showUploadProgress = false
      this.$alert(`上传文件失败，错误 ${err}`, "错误", {
        confirmButtonText: "确定",
      })
        .then(() => {})
        .catch(() => {})
    },
    uploadSuccess(res, file, fileList) {
      this.showUploadProgress = false
      this.$message.success(`上传文件成功`)
    },
    updateProgress(event, file, fileList) {
      console.log(event.percent)
    },
    uploadOnChange(file) {
      const that = this
      if (!this.formData.type) {
        this.$message.warning("请先选择上传文件的类型")
        return
      }
      if (this.fileAccept.indexOf(file.name.substr(file.name.lastIndexOf(".") + 1)) == -1) {
        this.$message.warning(`请上传${this.fileAccept}格式的文件`)
        return
      }

      if (this.formData.type == 1 && file.size / 1000 / 1000 / 1000 > 1) {
        this.$message.warning(`压缩包体积过大（超过1G），请重新处理后再上传`)
        return
      }

      this.formData.fileName = file.name
      this.formData.file = file.raw
    },
    handleSumit() {
      let that = this
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.btnDis = true

          // 解析zip或者excel
          if (that.formData.type == 1) {
            // zip
            this.showUploadProgress = true
            this.productPictureBase64List = []
            try {
              this.uploadTXCloud()
            } catch (error) {
              this.$message.error(`上传异常：${error}`)
            }
          } else {
            // excel
            let formData = new FormData()
            formData.append("file", this.formData.file)
            formData.append("businessField", this.formData.businessField)
            formData.append("checkPictureVersion", this.formData.checkPictureVersion)
            picComplementUploadExcel(formData).then((res) => {
              this.btnDis = false
              if (!res.retCode) {
                this.$message.success("上传成功")
                this.close()
              } else {
                this.$message.error(res.retMsg)
              }
            })
          }
        }
      })
    },

    // 解压缩并上传腾讯云
    uploadTXCloud(){
      const that = this
      let file_count = 0, // 将上传数
        file_reded_count = 0 // 已读取数
      let fileblobList = []
      const jszip = new JSZip()
      /* 目前： zip -- 图片
                    --  图片
        --------------------------------------------
         原本： zip --  文件夹  -- 图片
                    --  文件夹  -- 图片

          来实现批量补全商品图片
      */
      const zipName = that.formData.file.name;
      jszip.loadAsync(that.formData.file).then((zip) => {
        // 读取zip数量
        for (let key in zip.files) {
          if (!zip.files[key].dir) {
            file_count++ // 文件数
          }
        }

        // 读取zip
        for (let key in zip.files) {
          // 判断是否是目录
          if (!zip.files[key].dir) {
            let filename = zip.files[key].name;
            if (/\.(png|PNG|jpg|JPG|jpeg|bmp)$/.test(filename)) {
              // let base = zip.file(filen).async("base64") // 将图片转化为base64格式
              let blobimg = zip.file(filename).async("blob")
              let nameCob = filename.split("/").reverse()

              blobimg.then((res) => {
                file_reded_count++ // 已读取文件数
                fileblobList.push({
                  Bucket: this.tempKey.bucket,
                  Region: this.tempKey.region,
                  Key: `BMP/product/${this.guid()}__${nameCob[1]}__${nameCob[0]}`,
                  Body: res,
                })

                // 文件全部已读则调用补全接口
                if (file_count === file_reded_count) {
                  that.cos.uploadFiles(
                    {
                      files: fileblobList,
                      SliceSize: 1024 * 1024 * 5 /* 触发分块上传的阈值，超过5MB使用分块上传，非必须 */,
                      onProgress: function (info) {
                        var percent = Math.floor(info.percent * 10000) / 100
                        // var speed = Math.floor((info.speed / 1024 / 1024) * 100) / 100
                        // console.log("进度：" + percent + "%; 速度：" + speed + "Mb/s;")
                        that.uploadProgress = percent
                      },
                      onFileFinish: function (err, data, options) {
                        console.log(options.Key + " 上传" + (err ? "失败" : "完成"))
                      },
                    },
                    function (err, data) {
                      console.log("uploadFiles:", err || data)
                      if (err) {
                        that.uploadError(err)
                      } else if (data && data.files) {
                        const orgCode = that.formData.file.name.split('.')[0];
                        data.files.forEach((file) => {
                          let filePath = file.data.Location.split("__")

                          that.productPictureBase64List.push({
                            pictureName: filePath[2],
                            pictureUrl: window.tempKey.host +'/'+ file.options.Key, // 上传腾讯云后的图片url
                            productIdent: filePath[1].split("-")[0],
                            orgCode: orgCode,
                          })
                        })

                        let params = {
                          createTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
                          productMarkType: that.formData.businessField,
                          validHasPic: that.formData.checkPictureVersion,
                          pictureInfoList: that.productPictureBase64List,
                        }
                        // 请求图片补全接口
                        picComplementCos(params).then((res) => {
                          if (res.retCode === 0) {
                            that.uploadSuccess()
                            setTimeout(() => {
                              that.close()
                            }, 300)
                            // that.$message.success("操作成功！")
                          }
                        })
                      }
                    }
                  )
                }
              })
            } else {
              this.$message.error("上传图片只能是jpg，png，jpeg，bmp格式！")
              // break
            }
          }
        }
      })

    },
    handleDownLoad() {
      createDownloadElement("../../../static/assets/excel/图片补全商品模板.xlsx", "批量导入商品图片补全模板.xlsx")
    },
    guid() {
      return "xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx".replace(/[xy]/g, function (c) {
        let r = (Math.random() * 16) | 0,
          v = c == "x" ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.form-wrap /deep/ {
  .radio-wrap /deep/ {
    .el-form-item {
      margin-bottom: 22px;
    }
    .el-form-item__label,
    .el-form-item__content {
      line-height: 20px;
    }
  }
  .upload-wrap.el-form-item {
    display: flex;
    .el-form-item__content {
      flex: 1;
    }
  }
}
.dialog-footer {
  display: flex;
  justify-content: center;
}
.tip-wrap {
  line-height: 24px;
}
</style>
