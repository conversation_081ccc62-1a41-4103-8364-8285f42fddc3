
<template>
  <div class="task-to-be-film-container" v-loading="loading">
    <!-- 查询表单 -->
    <div class="search-form-wrap">
      <el-form :model="model" ref="refSearchForm">
        <el-row type="flex">
          <!-- 提交人 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="申请人">
              <el-input
                v-model="formData.applyUser "
                placeholder="模糊查询"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 来源 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="来源" prop="source">
              <el-select
                multiple
                collapse-tags
                v-model="formData.source"
                @change="changeSelect1"
                @remove-tag="removeTag1"
                placeholder="请选择"
              >
                <el-option
                  label="全选"
                  value="全选"
                  @click.native="selectAll1"
                ></el-option>
                <el-option
                  v-for="item in sourceList"
                  :key="item.key"
                  :label="item.value"
                  :value="item.key"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 按钮 -->
          <el-col :span="6" :offset="18">
            <el-button type="primary" size="medium" @click="btnSearchClick"
              >查询</el-button
            >
            <el-button size="medium" @click="btnResetClick">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="btn-wrap">
      <el-button
        type="primary"
        size="medium"
        :disabled="btnDis"
        @click="handleExpot"
        >导出</el-button
      >
    </div>
    <!-- table表单 -->
    <!-- table -->
    <div class="table-wrap">
      <vxe-table
        border
        highlight-hover-row
        resizable
        height="100%"
        auto-resize
        size="small"
        align="center"
        highlight-current-row
        :tooltip-config="{ enterable: false }"
        :loading="tableLoading"
        :data="tableData"
        :seq-config="{ startIndex: (pageNum - 1) * pageSize }"
        ref="refVxeTable"
      >
        <vxe-table-column
          type="seq"
          title="序号"
          min-width="120"
          show-header-overflow
          show-overflow
          fixed="left"
        ></vxe-table-column>
        <vxe-table-column
          field="sourceName"
          title="来源"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="applyUser"
          title="申请人"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="submitTaskNum"
          title="原提交任务数"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="spiderTaskNum"
          title="爬图任务数"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="spiderFinishRate"
          title="爬图完成率(%)"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="applyTaskNum"
          title="已申请的数量"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="omRejectNum"
          title="运营审核驳回数量/图片数量"
          min-width="200"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">
            {{ row.omRejectNum + '/' + row.omRejectPicNum }}
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="omPassNum"
          title="运营审核通过数量/图片数量"
          min-width="200"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">
            {{ row.omPassNum + '/' + row.omPassPicNum }}
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="omProcessRate"
          title="运营审核进度(%)"
          min-width="140"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="omPassRate"
          title="运营审核通过率(%)"
          min-width="140"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="needCompleteNum"
          title="需精修商品数量/图片数量"
          min-width="200"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">
            {{ row.needCompleteNum + '/' + row.needCompletePicNum }}
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="needCompletionRate"
          title="精修占比(%)"
          min-width="110"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
        <vxe-table-column
          field="finishCompleteNum"
          title="已完成精修商品数量/图片数量"
          min-width="200"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">
            {{ row.finishCompleteNum + '/' + row.finishCompletePicNum }}
          </template>
        </vxe-table-column>
         <vxe-table-column
          field="completionProcessRate"
          title="精修进度(%)"
          min-width="110"
          show-header-overflow
          show-overflow
        >
        </vxe-table-column>
      </vxe-table>
    </div>
    <!-- 分页 -->
    <el-row class="custom-pagination-wrap">
      <el-col>
        <el-pagination
          background
          :current-page.sync="pageNum"
          :page-sizes="[20, 50, 100, 200]"
          :page-size.sync="pageSize"
          :total="total"
          layout="prev, pager, next, jumper,total, sizes"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { hasPermission, getDateRange } from "@/utils/index.js";

import {
  getAllPictureSource,
  getAllBusinessType,
  getTaskBoardList,
  exportTaskBoardList,
} from "@/api/board.js";

export default {
  name: "",
  components: {},
  filters: {},
  props: {},
  data() {
    return {
      loading: false,
      pageNum: 1,
      pageSize: 20,
      total: 0, //总条数
      formData: {
        createTime: ["",""],
        applyUser : "",
        source: [],
      },
      tableLoading: false,
      tableData: [],
      sourceList: [],
      conductorList: [],
    };
  },
  computed: {},
  watch: {},
  created() {
    this.getAllSource();
    this.searchForm();
  },
  mounted() {},
  methods: {
    getAllSource() {
      getAllPictureSource().then((res) => {
        this.sourceList = res.data;
      });
    },
    // 搜索
    searchForm() {
      this.tableLoading = true;
      this.getTaskBoardList();
    },

    getTaskBoardList() {
      let param = Object.assign(
        {
          page: this.pageNum,
          limit: this.pageSize,
          applyUser : this.formData.applyUser,
          sourceStr: this.formData.source.join(","),
        },
        this.formData
      );
      delete param.createTime;
      delete param.source;
      if (this.formData.source.indexOf("全选") !== -1) {
        param.isSource = 1;
        delete param.sourceStr;
      } 
      getTaskBoardList(param).then((res) => {
        if (!res.retCode) {
          this.tableData = res.data.list;
          this.tableData.map(item => {
              item.spiderFinishRate+='%'
              item.omProcessRate+='%'
              item.omPassRate+='%'
              item.needCompletionRate+='%'
              item.completionProcessRate+='%'
          })
          this.total = res.data.total;
          this.tableLoading = false;
        } else {
          this.$message.error(res.retMsg);
        }
      });
    },

    handleExpot() {
      let param = Object.assign(
        {
          page: this.pageNum,
          limit: this.pageSize,
          applyUser : this.formData.applyUser,
          sourceStr: this.formData.source.join(","),
        },
        this.formData
      );
      delete param.createTime;
      delete param.source;
      if (this.formData.source.indexOf("全选") !== -1) {
        param.isSource = 1;
        delete param.sourceStr;
      } 
      exportTaskBoardList(param).then((res) => {
        if (!res.retCode) {
          this.$message.success(res.retMsg);
        } else {
          this.$message.error(res.retMsg);
        }
      });
    },

    /**
     * 查询按钮点击
     */
    btnSearchClick() {
      this.pageNum = 1;
      this.searchForm();
    },

    /**
     * 重置
     */
    btnResetClick() {
      this.formData = {
        createTime: ["",""],
        applyUser : "",
        source: [],
      };
      this.pageNum = 1;
      this.searchForm();
    },
    /**
     * pageSize 改变事件
     */
    handleSizeChange(pageSize) {
      this.pageNum = 1;
      this.pageSize = pageSize;
      this.searchForm();
    },

    /**
     * pageNum 改变事件
     */
    handleCurrentChange(currentPage) {
      this.pageNum = currentPage;
      this.searchForm();
    },
    /**
     * @description: 格式化时间参数
     * @param {date} time 时间戳
     * @return: string 格式化后的时间戳
     */
    hasPermission(str) {
      return hasPermission(str);
    },

    selectAll1() {
      if (this.formData.source.length < this.sourceList.length) {
        this.formData.source = [];
        this.sourceList.map((item) => {
          this.formData.source.push(item.key);
        });
        this.formData.source.unshift("全选");
      } else {
        this.formData.source = [];
      }
    },
    changeSelect1(val) {
      if (!val.includes("全选") && val.length === this.sourceList.length) {
        this.formData.source.unshift("全选");
      } else if (
        val.includes("全选") &&
        val.length - 1 < this.sourceList.length
      ) {
        this.formData.source = this.formData.source.filter((item) => {
          return item !== "全选";
        });
      }
    },
    removeTag1(val) {
      if (val === "全选") {
        this.formData.source = [];
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.task-to-be-film-container {
  /**
   * 查询表单
   */
  .search-form-wrap {
    width: 100%;
    padding: 15px;
    border-bottom: 1px dashed #e4e4eb;

    .el-row {
      flex-wrap: wrap;

      .el-col {
        display: flex;
        justify-content: flex-end;

        .el-form-item {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          width: 100%;
          margin-right: 0;
          margin-bottom: 15px;

          /deep/ {
            .el-form-item__label {
              width: 96px;
              line-height: normal;
              padding: 0 12px;
              color: #292933;
              font-weight: normal;
            }

            .el-form-item__content {
              flex: 1;

              .el-range-editor.el-input__inner {
                width: 100%;

                .el-range-separator {
                  width: 14px;
                  padding: 0;
                }
              }
            }
          }

          .el-input {
            width: 100%;
          }

          .el-select {
            width: 100%;
          }
        }
      }
    }
  }

  .tab-wrap {
    padding: 0 15px;
    margin-top: 15px;
  }
  .table-wrap {
    width: 100%;
    padding: 0 20px;
    min-height: 440px;
    height: calc(100vh - 395px);
  }
}
.total {
  height: 40px;
  line-height: 40px;
  margin: 0 20px;
  display: flex;
  justify-content: space-between;
  font-weight: bold;
  background: #f2f2f2;
  span {
    width: 12.5%;
    text-align: center;
    &.title {
      background: #fff;
      border-left: 1px solid #eee;
      border-bottom: 1px solid #eee;
    }
  }
}
.btn-wrap {
  padding: 15px 0 15px 20px;
}
.radio-wrap {
  .title {
    padding-right: 20px;
  }
}
.active {
  color: #3b95a8;
}
</style>
