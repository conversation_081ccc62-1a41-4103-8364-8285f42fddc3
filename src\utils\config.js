export const reviewStatusList = [{
    value: -1,
    label: '录入中'
}, {
    value: 0,
    label: '运营中心审核中'
}, {
    value: 1,
    label: '质管总部审核中'
}, {
    value: 2,
    label: '审核驳回'
}, {
    value: 3,
    label: '审核通过'
}, {
    value: 4,
    label: '审核不通过'
}, {
    value: 5,
    label: '财务部审核中'
}];

export const exportProductDictList = [
    {
        "id": "001",
        "title": "状态",
        "parentId": "0",
        "checked": false,
        "children": [
            {
                "id": "停用状态",
                "title": "停用状态",
                "parentId": "001",
                "checked": true,
            }, {
                "id": "自营状态",
                "title": "自营状态",
                "parentId": "001",
                "checked": true,
            }, {
                "id": "已首营机构数量",
                "title": "已首营机构数量",
                "parentId": "001",
                "checked": false,
            }, {
                "id": "商品类型",
                "title": "商品类型",
                "parentId": "001",
                "checked": false,
            }]
    },
    {
        "id": "002",
        "title": "编码",
        "parentId": "0",
        "checked": false,
        "children": [{
            "id": "商品ID",
            "title": "商品ID",
            "checked": true,
        }, {
            "id": "商品编码",
            "title": "商品编码",
            "checked": true,
        }, {
            "id": "原商品编码",
            "title": "原商品编码",
            "parentId": "002",
            "checked": true,
        }, {
            "id": "SKU编码",
            "title": "SKU编码",
            "parentId": "002",
            "checked": true,
        }, {
            "id": "SPU编码",
            "title": "SPU编码",
            "parentId": "002",
            "checked": false,
        }, {
            "id": "小包装条码",
            "title": "小包装条码",
            "parentId": "002",
            "checked": true,
        }, {
            "id": "中包装条码",
            "title": "中包装条码",
            "parentId": "002",
            "checked": false,
        }, {
            "id": "件包装条码",
            "title": "件包装条码",
            "parentId": "002",
            "checked": false,
        }, {
            "id": "分类编码",
            "title": "分类编码",
            "parentId": "002",
            "checked": false,
        }]
    }, {
        "id": "003",
        "title": "商品五要素",
        "parentId": "0",
        "checked": true,
        "children": [{
            "id": "通用名",
            "title": "通用名",
            "parentId": "003",
            "checked": true,
        }, {
            "id": "商品名",
            "title": "商品名",
            "parentId": "003",
            "checked": true,
        }, {
            "id": "批准文号",
            "title": "批准文号",
            "parentId": "003",
            "checked": true,
        }, {
            "id": "生产厂家",
            "title": "生产厂家",
            "parentId": "003",
            "checked": true,
        }, {
            "id": "规格/型号",
            "title": "规格/型号",
            "parentId": "003",
            "checked": true,
        }]
    }, {
        "id": "004",
        "title": "质管18要素",
        "parentId": "0",
        "checked": false,
        "children": [{
            "id": "商品大类",
            "title": "商品大类",
            "parentId": "004",
            "checked": true,
        }, {
            "id": "所属经营范围",
            "title": "所属经营范围",
            "parentId": "004",
            "checked": true,
        }, {
            "id": "包装单位",
            "title": "包装单位",
            "parentId": "004",
            "checked": true,
        }, {
            "id": "剂型",
            "title": "剂型",
            "parentId": "004",
            "checked": true,
        }, {
            "id": "处方分类",
            "title": "处方分类",
            "parentId": "004",
            "checked": true,
        }, {
            "id": "特殊属性",
            "title": "特殊属性",
            "parentId": "004",
            "checked": false,
        }, {
            "id": "上市许可持有人",
            "title": "上市许可持有人",
            "parentId": "004",
            "checked": false,
        }, {
            "id": "有效期",
            "title": "有效期",
            "parentId": "004",
            "checked": false,
        }, {
            "id": "批件规格",
            "title": "批件规格",
            "parentId": "004",
            "checked": false,
        }, {
            "id": "质量标准",
            "title": "质量标准",
            "parentId": "004",
            "checked": false,
        }, {
            "id": "产地",
            "title": "产地",
            "parentId": "004",
            "checked": true,
        }, {
            "id": "药品本位码",
            "title": "药品本位码",
            "parentId": "004",
            "checked": false,
        }, {
            "id": "是否监管",
            "title": "是否监管",
            "parentId": "004",
            "checked": false,
        }, {
            "id": "委托生产厂家",
            "title": "委托生产厂家",
            "parentId": "004",
            "checked": true,
        }, {
            "id": "贮藏",
            "title": "贮藏",
            "parentId": "004",
            "checked": false,
        }, {
            "id": "存储条件",
            "title": "存储条件",
            "parentId": "004",
            "checked": false,
        }, {
            "id": "存储属性",
            "title": "存储属性",
            "parentId": "004",
            "checked": false,
        }, {
            "id": "批件图片数量",
            "title": "批件图片数量",
            "parentId": "004",
            "checked": false,
        }, {
          "id": "注册人/备案人",
          "title": "注册人/备案人",
          "parentId": "004",
          "checked": true,
        }]
    }, {
        "id": "005",
        "title": "运营十五要素",
        "parentId": "0",
        "checked": false,
        "children": [{
            "id": "一级分类",
            "title": "一级分类",
            "parentId": "005",
            "checked": true,
        }, {
            "id": "二级分类",
            "title": "二级分类",
            "parentId": "005",
            "checked": true,
        }, {
            "id": "三级分类",
            "title": "三级分类",
            "parentId": "005",
            "checked": true,
        }, {
            "id": "四级分类",
            "title": "四级分类",
            "parentId": "005",
            "checked": true,
        }, {
            "id": "五级分类",
            "title": "五级分类",
            "parentId": "005",
            "checked": true,
        }, {
            "id": "六级分类",
            "title": "六级分类",
            "parentId": "005",
            "checked": true,
        }, {
            "id": "品牌商标",
            "title": "品牌商标",
            "parentId": "005",
            "checked": true,
        }, {
            "id": "厂家分类",
            "title": "厂家分类",
            "parentId": "005",
            "checked": false,
        }, {
            "id": "品牌厂家",
            "title": "品牌厂家",
            "parentId": "005",
            "checked": false,
        }, {
            "id": "品牌分类",
            "title": "品牌分类",
            "parentId": "005",
            "checked": false,
        }, {
            "id": "属性&属性值",
            "title": "属性&属性值",
            "parentId": "005",
            "checked": false,
        }, {
            "id": "图片版本数量",
            "title": "图片版本数量",
            "parentId": "005",
            "checked": false,
        }, {
            "id": "一致性评价品种",
            "title": "一致性评价品种",
            "parentId": "005",
            "checked": false,
        }, {
            "id": "医院品种",
            "title": "医院品种",
            "parentId": "005",
            "checked": false,
        }, {
            "id": "慢病品种",
            "title": "慢病品种",
            "parentId": "005",
            "checked": false,
        }]
    }, {
        "id": "006",
        "title": "财务三要素",
        "parentId": "0",
        "checked": false,
        "children": [{
            "id": "税务分类编码",
            "title": "税务分类编码",
            "parentId": "006",
            "checked": false,
        }, {
            "id": "进项税率",
            "title": "进项税率",
            "parentId": "006",
            "checked": false,
        }, {
            "id": "销项税率",
            "title": "销项税率",
            "parentId": "006",
            "checked": false,
        }]
    }, {
        "id": "007",
        "title": "说明书",
        "parentId": "0",
        "checked": false,
        "children": [{
            "id": "成份",
            "title": "成份",
            "parentId": "007",
            "checked": false,
        }, {
            "id": "适应症",
            "title": "适应症",
            "parentId": "007",
            "checked": false,
        }, {
            "id": "用法用量",
            "title": "用法用量",
            "parentId": "007",
            "checked": false,
        }, {
            "id": "不良反应",
            "title": "不良反应",
            "parentId": "007",
            "checked": false,
        }, {
            "id": "注意事项",
            "title": "注意事项",
            "parentId": "005",
            "checked": false,
        }, {
            "id": "禁忌",
            "title": "禁忌",
            "parentId": "007",
            "checked": false,
        }, {
            "id": "用药方式",
            "title": "用药方式",
            "parentId": "007",
            "checked": false,
        }, {
            "id": "用药频次",
            "title": "用药频次",
            "parentId": "007",
            "checked": false,
        }, {
            "id": "每次服用数量",
            "title": "每次服用数量",
            "parentId": "007",
            "checked": false,
        }, {
            "id": "最小使用单位",
            "title": "最小使用单位",
            "parentId": "007",
            "checked": false,
        }]
    }, {
        "id": "008",
        "title": "创建信息",
        "parentId": "0",
        "checked": false,
        "children": [{
            "id": "创建人",
            "title": "创建人",
            "parentId": "008",
            "checked": false,
        }, {
            "id": "创建时间",
            "title": "创建时间",
            "parentId": "008",
            "checked": false,
        }, {
            "id": "创建人机构",
            "title": "创建人机构",
            "parentId": "008",
            "checked": false,
        }, {
            "id": "最后修改时间",
            "title": "最后修改时间",
            "parentId": "008",
            "checked": false,
        }]
    }]

export const dictTypeList = [{
    value: 5,
    label: '剂型'
}, {
    value: 6,
    label: '存储条件'
}, {
    value: 8,
    label: '税率'
}, {
    value: 9,
    label: '处方分类'
}, {
    value: 10,
    label: '包装单位'
}, {
    value: 11,
    label: '经营范围'
}, {
    value: 12,
    label: '生产厂家'
}, {
    value: 13,
    label: '特殊管理属性'
}, {
    value: 14,
    label: '科室'
}, {
    value: 19,
    label: '商品大类'
}, {
    value: 20,
    label: '频次'
}, {
    value: 21,
    label: '用药方式'
}, {
    value: 23,
    label: '厂家分类'
}, {
    value: 24,
    label: '品牌分类'
}, {
    value: 25,
    label: '最小使用单位'
},{
    value: 27,
    label: '中药限品种'
},
{
    value: 28,
    label: '上市许可持有人'
},];

export const approvalProcessList = [
    { value: -1, label: '条形码修改' },
    { value: 0, label: '商品新增' },
    { value: 1, label: '商品修改' },
    { value: 2, label: '商品合并' },
    { value: 3, label: '批量停用' },
    { value: 4, label: '批量扩展' },
    { value: 5, label: '批量修改' },
    { value: 6, label: '预首营审核' },
    { value: 7, label: '批量新增' },
    { value: 8, label: '批量用药指导' },
    { value: 9, label: '数据梳理更新' },
    { value: 10, label: '取消合并' },
    { value: 12, label: '商品上架修改' },
    { value: 13, label: '商品纠错修改' },
    { value: 14, label: '批量修改税率' },
    { value: 15, label: '商品去重' },
    { value: 16, label: '移动商品' },
    { value: 17, label: 'spu停启用' }
]

export const receiveStatusList = [
    { value: 0, label: '待领取' },
    { value: 1, label: '待审核' }
]
