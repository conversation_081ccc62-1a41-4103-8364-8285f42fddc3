{"name": "vue-cropper-example", "description": "A Vue.js project", "author": "goodboy <<EMAIL>>", "private": true, "scripts": {"dev": "webpack-dev-server --inline --hot", "build": "cross-env NODE_ENV=production webpack --progress --hide-modules"}, "dependencies": {"babel-polyfill": "^6.26.0", "exif-js": "^2.3.0", "vue": "^2.5.17", "vue-cropper": "^0.4.9", "vue-loader": "^14.2.2", "vue-template-compiler": "^2.5.17"}, "devDependencies": {"babel-core": "^6.0.0", "babel-loader": "^6.0.0", "babel-preset-es2015": "^6.13.2", "cross-env": "^1.0.6", "css-loader": "^0.23.1", "file-loader": "^0.8.4", "webpack": "^2.3.2", "webpack-dev-server": "^3.1.11"}, "browserify": {"transform": ["vueify"]}}