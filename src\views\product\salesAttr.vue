<template>
  <div class="component-container">    
    <!-- 销售属性 -->
    <vxe-table
      border
      highlight-hover-row
      auto-resize
      resizable
      width="100%"
      align="center"
      :tooltip-config="{ enterable: false }"
      :data="tableData"
      ref="table"
      @checkbox-change="checkboxChange"
      @radio-change="radioChange"
    >
      <vxe-table-column
        type="index"
        title="序号"
        width="60"
        show-header-overflow
        show-overflow
      ></vxe-table-column>
      <vxe-table-column field="businessCode" title="商品编码" min-width="100">
        <!-- <template v-slot="{ row }">{{ row.shootTime | dateTime }}</template> -->
      </vxe-table-column>
      <vxe-table-column
        field="pictureVersion"
        title="版本号"
        min-width="100"
      ></vxe-table-column>
      <vxe-table-column
        field="detailPictureList"
        title="精修主图"
        min-width="100"
      >
        <template v-slot="{ row, seq }">
          <preview-img
            v-if="!showChecked"
            :row-data="{ row, seq }"
            :imgTitle="imgTitle"
            :isEdit="false"
            field="detailPictureList"
          />
        </template>
      </vxe-table-column>
      <vxe-table-column
        field="detailPictureNum"
        title="图片数量"
        min-width="100"
      ></vxe-table-column>
      <vxe-table-column field="productStatus" title="图片状态" min-width="100">
        <!-- 停启用状态,1启用,0停用 -->
        <template v-slot="{ row }">{{row.pictureEnable ? "启用" : "停用"}}</template>
      </vxe-table-column>
      <vxe-table-column
        field="mechanismNum"
        title="已绑定机构数量"
        min-width="130"
      >
        <template v-slot="{ row }">
          <!-- :content="row.mechanismList.join('-')" -->
          <el-tooltip class="item" effect="dark" placement="top">
            <template v-slot:content>
              <p v-for="(item, key) in row.mechanismList" :key="key">
                {{ item }}
              </p>
            </template>
            <span>{{ row.mechanismNum }}</span>
          </el-tooltip>
        </template>
      </vxe-table-column>
      <vxe-table-column
          title="操作"
          width="180"
          show-header-overflow
          show-overflow
          fixed="right"
        >
          <template v-slot="{ row }">
            <!-- 停启用状态  0停用 1启用 -->
            <span class="btn" v-if="showChange && !isDeputy">
              <el-link
              :underline="false"
              type="primary"
              @click.stop="action(row)"
              >{{row.pictureEnable ? '停用' : '启用'}}</el-link
            ></span>
            <span class="btn" v-if="showChange && !isDeputy">
            <el-link
              :underline="false"
              type="primary"
              @click.stop="edit(row)"
              >修改</el-link
            ></span>
            <span class="btn" v-if="showChange && row.pictureEnable && row.mechanismNum">
            <el-link
              :underline="false"
              type="primary"
              @click.stop="updataBind(row)"
              >更换绑定</el-link
            ></span>
          </template>
        </vxe-table-column>
    </vxe-table>
    <updata-bind ref='updateBindDlg'></updata-bind>
  </div>
</template>

<script>
import { parseTimestamp } from "@/utils/index.js";
import previewImg from "@/components/uploadImg/preview";
import updataBind from "./updateBind.vue"
import { addTask } from "@/api/workManage"
import { imgChangeStatus, getBindMechanismDetail } from "@/api/product"

export default {
  name: "",
  components: { previewImg, updataBind },
  filters: {
    dateTime: function (value) {
      if (!value) return "";
      return parseTimestamp(value);
    },
  },
  props: {
    showChange: {
      type: Boolean,
      default: false,
    },
    isDeputy: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  computed: {
    // 路由参数
    urlParam() {
      return this.$route.query;
    },
    // 商品操作类型
    operationType: function () {
      return this.$store.getters.operationType;
    },
    tableData() {
      return this.$store.state.product.productSaleInfo;
    },
  },
  watch: { },
  created() {
    this.init();
  },
  mounted() {},
  methods: {
     // 停启用
    action(e) {
      console.log(e)
      let msg = e.pictureEnable ? '<p class="red">注意！请确认是否停用该版本号，停用前请先确认该版本是否绑定机构，停用后是否影响业务平台商品上线！</p>' : '<p class="green">注意！请确认是否启用该版本号，启用后仓库人员会看到该版本，版本号过多会影响仓库操作效率。</p>'
      this.$confirm(msg, `是否${ e ? '停用' : '启用' }该版本号？`, {
        confirmButtonText: "确定",
        cancelButtonText: "我再想想",
        dangerouslyUseHTMLString: true
      })
        .then(async() => {
          let param = {
            productCode:e.businessCode,
            pictureVersion:e.pictureVersion,
            status:e.pictureEnable ? 0 : 1
          }
          try {
            console.log(param);
            const res = await imgChangeStatus(param)
            console.log(res);  
            if (res.retCode === 0) {
              this.$message.success(res.retMsg)
            } else {
              this.$message.error(res.retMsg)
            }       
          } catch (error) {
            console.log(error);
          }
        })
        .catch(error => {
          console.log(error)
        })
    },
    // 修改
    edit(e) {
      console.log('edit',e)
      this.$confirm('请确认是否修改精修图版本？点“确认”将新建该商品的修改精修图审批流，审批流完成前，同一商品不能进行停启用或修改操作', '是否修改该版本号？', {
        confirmButtonText: "确定",
        cancelButtonText: "我再想想",
        dangerouslyUseHTMLString: true
      })
        .then(async() => {
          const res = await addTask({productCode:e.businessCode,pictureVersion:e.pictureVersion})
          console.log(res);
          if(res.retCode === 0) {
            this.$message({
              message: '修改成功',
              type: 'success'
            });
          } else {
            this.$message({
              message: res.retMsg,
              type: 'error'
            });
          }
        })
        .catch(error => {
          console.log(error)
        })
    },
    init() {
      let { productCode, productType } = this.urlParam;
      let businessType = "";
      // businessType 商品类型：0:sku, 1:sau
      if (productType == 2 || productType == 3) {
        businessType = parseInt(productType) - 2;
      }
      this.$store.dispatch("product/getSaleInfoAction", {
        businessCode: productCode,
        businessType,
      });
    },
    openDragImg(row, seq) {
      this.$refs.salesImgDrag.openDlg(JSON.parse(JSON.stringify(this.tableData)),seq - 1);
    },
    // 更换绑定
    async updataBind(e) {
      try {
        const res = await getBindMechanismDetail({productCode: e.businessCode, pictureVersion: e.pictureVersion})
        if (res.retCode === 0) {
          this.$refs.updateBindDlg.openDlg(res.data)
        } else {
          this.$message.error(res.retMsg)
        }
      } catch (error) {
        console.log(error);
      }
    },
  },
};
</script>
<style lang="scss">
// .el-message-box__message, .el-message-box__title{
//   color: red!important;
// }
.red{
  color: red!important;
}
.green {
  color: #03B915!important;
}
</style>

<style lang="scss" scoped>
.button-wrap {
  padding-bottom: 10px;
}
.img-s{
  width: 80px;
  height: 80px;
}
.btn {
  margin: 0 5px;
}
</style>