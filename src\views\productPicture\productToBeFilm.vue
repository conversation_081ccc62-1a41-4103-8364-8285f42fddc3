<template>
  <div class="product-to-be-film-container">
    <!-- 查询表单 -->
    <div class="search-form-wrap">
      <el-form :model="model" ref="refSearchForm">
        <el-row type="flex">
          <!-- 所属机构 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="所属机构" prop="mechanism">
              <el-select 
               multiple
               collapse-tags
               v-model="model.mechanism" placeholder="请选择"
               @remove-tag="removeTag1"
               @change="changeSelect1"
               clearable
              >

                            <el-option
                  label="全选"
                  value="全选"
                  @click.native="selectAll1"
                ></el-option>

                <el-option
                  v-for="item in model.mechanismOptions"
                  :key="'key_mechanismOptions_'+item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 拍摄原因 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="拍摄原因" prop="applyReason">
              <el-select v-model="model.applyReason" placeholder="请选择">
                <el-option
                  v-for="item in model.filmReasonOptions"
                  :key="'key_filmReasonOptions_'+item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 外包装图片 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="外包装图片">
              <el-select v-model="model.hasOutPackagePicture" placeholder="请选择">
                <el-option
                  v-for="item in model.outPackageImgOptions"
                  :key="'key_outPackageImgOptions_'+item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 说明书图片 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="说明书图片">
              <el-select v-model="model.hasInstructionPicture" placeholder="请选择">
                <el-option
                  v-for="item in model.directionImgOptions"
                  :key="'key_directionImgOptions_'+item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 商品编码 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="商品编码">
              <el-input clearable v-model="model.productCode" placeholder="模糊查询"></el-input>
            </el-form-item>
          </el-col>
          <!-- 小包装条码 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="小包装条码">
              <el-input clearable v-model="model.smallPackageCode" placeholder="模糊查询"></el-input>
            </el-form-item>
          </el-col>
          <!-- 通用名 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="通用名">
              <el-input clearable v-model="model.generalName" placeholder="模糊查询"></el-input>
            </el-form-item>
          </el-col>
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="生产厂家">
              <el-select
                v-model="model.manufacturer"
                placeholder="模糊查询"
                filterable
                clearable
                :filter-method="remoteMethod"
                @change="changeManufacturer"
              >
                <el-option
                  v-for="item in model.manufacturerOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 图片状态 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="图片状态">
              <el-select v-model="taskStatus">
               <el-option label="全部" :value="null"></el-option>
               <el-option label="待拍摄" :value="'1'"></el-option>
               <el-option label="已拍摄初审中" :value="'2'"></el-option>
               <el-option label="已初审精修中" :value="'3'"></el-option>
               <el-option label="已上线" :value="'4'"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 商品大类 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="商品大类">
              <el-select v-model="spuCategory">
               <el-option label="全部" value></el-option>
                <el-option
                  v-for="item in spuCategoryList"
                  :key="item.id"
                  :label="item.dictName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 按钮 -->
          <el-col :span="6" :offset="18">
            <el-button type="primary" size="medium" @click="btnSearchClick">查询</el-button>
            <el-button size="medium" @click="btnResetClick">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- table上方按钮组 -->
    <el-row class="btn-group-wrap">
      <el-col>
        <vxe-column-filter tableRefName="refVxeTable"></vxe-column-filter>
        <el-button size="medium" @click="exportExcel">导出excel</el-button>
      </el-col>
    </el-row>

    <!-- table -->
    <div class="table-wrap">
      <vxe-table
        border
        highlight-hover-row
        resizable
        height="100%"
        auto-resize
        size="small"
        align="center"
        :tooltip-config="{enterable: false}"
        :loading="tableLoading"
        :data="tableData"
        :seq-config="{ startIndex: (pageNum - 1) * pageSize }"
        ref="refVxeTable"
      >
        <vxe-table-column
          type="seq"
          title="序号"
          width="60"
          show-header-overflow
          show-overflow
          fixed="left"
        ></vxe-table-column>
        <vxe-table-column
          field="bingMechanismName"
          title="所属机构"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="applyReasonName"
          title="拍摄原因"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="taskStatusName"
          title="图片状态"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="pictureVersion"
          title="图片版本"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="packagingPictureAmount"
          title="外包装图片数量"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="instructionPictureAmount"
          title="说明书图片数量"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="generalName"
          title="通用名"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="spec"
          title="规格/型号"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="manufacturerName"
          title="生产厂家"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="spuCategoryName"
          title="商品大类"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="productCode"
          title="商品编码"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="originalProductCode"
          title="原商品编码"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="productId"
          title="商品id"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="smallPackageCode"
          title="小包装条码"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="skuName"
          title="商品名"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="packageUnitName"
          title="包装单位"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
      </vxe-table>
    </div>

    <!-- 分页 -->
    <el-row class="custom-pagination-wrap">
      <el-col>
        <el-pagination
          background
          :current-page.sync="pageNum"
          :page-size="pageSize"
          :total="total"
          layout="prev,pager,next,jumper,total,slot"
          @current-change="paginationCurrentChange"
        >
          <span class="page-size-custom">
            <el-input clearable size="mini" v-model.number="pageSizeInput"></el-input>
            <span class="tips">条/页</span>
            <el-button
              size="mini"
              type="primary"
              @mousedown.native="btnConfirmMousedown($event)"
              @mouseup.native="btnConfirmMouseup($event)"
              @click="pageSizeInputChange"
            >确定</el-button>
          </span>
        </el-pagination>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import VxeColumnFilter from "@/components/common/vxeColumnFilter";
import {
  getMechanismList,
  productShootListFind,
  productShootListExport
} from "@/api/productPicture.js";
import { dictSearchTypeAndName, findDictList } from "@/api/dict.js";
export default {
  name: "",
  components: {
    VxeColumnFilter,
  },
  filters: {},
  props: {},
  data() {
    return {
      taskStatus: null, // 图片状态
      spuCategory: null, // 商品大类
      spuCategoryList: [], // 商品大类列表
      pageNum: 1,
      pageSize: 20,
      pageSizeInput: 20,
      total: 0, //总条数

      model: {
        //所属机构
        mechanism: [],
        mechanismOptions: [],

        //拍摄原因
        applyReason: null,
        filmReasonOptions: [
          {
            value: null,
            label: "全部",
          },
          {
            value: "1",
            label: "新品上架",
          },
          {
            value: "2",
            label: "图片审核驳回",
          },
          {
            value: "5",
            label: "图片补全",
          },
          {
            value: "6",
            label: "更新新老包装",
          },
          {
            value: "7",
            label: "新品再上架",
          },
        ],

        //外包装图片
        hasOutPackagePicture: null,
        outPackageImgOptions: [
          {
            value: null,
            label: "全部",
          },
          {
            value: "1",
            label: "有",
          },
          {
            value: "2",
            label: "无",
          },
        ],

        //说明书图片
        hasInstructionPicture: null,
        directionImgOptions: [
          {
            value: null,
            label: "全部",
          },
          {
            value: "1",
            label: "有",
          },
          {
            value: "2",
            label: "无",
          },
        ],

        //商品编码
        productCode: "",

        //小包装条码
        smallPackageCode: "",

        //通用名
        generalName: "",

        //生产厂家
        manufacturer: null,
        manufacturerName: null,
        manufacturerOptions: [],
        manufacturerSearchLoading: false,
      },
      tableLoading: false,
      tableData: []
    };
  },
  computed: {},
  watch: {},
  created() {
    this.getCategoryList()
    /**
     * 查询机构
     */
    getMechanismList({
      codeType: "dept",
    })
      .then((resp) => {
        if (resp.retCode === 0) {
          resp.data.forEach((item) => {
            if (item.mechanismId) {
              this.model.mechanismOptions.push({
                value: item.mechanismId,
                label: item.mechanismName,
              });
            }
          });
        }
      })
      .catch((err) => {
        console.log(err);
      });

    /**
     * 查询生产厂家
     */
    this.remoteMethod("药");

    this.searchForm();
  },
  mounted() {},
  methods: {

    changeSelect1(val) {
      if (!val.includes("全选") && val.length === this.model.mechanismOptions.length) {
        this.model.mechanism.unshift("全选");
      } else if (
        val.includes("全选") &&
        val.length - 1 < this.model.mechanismOptions.length
      ) {
        console.log(this.model.mechanism)
        this.model.mechanism = this.model.mechanism.filter((item) => {
          return item !== "全选";
        });
      }
    },
    removeTag1(val) {
      if (val === "全选") {
        this.model.mechanism = [];
      }
    },
    selectAll1() {
      if (this.model.mechanism.length < this.model.mechanismOptions.length) {
        this.model.mechanism = [];
        this.model.mechanismOptions.map((item) => {
          this.model.mechanism.push(item.value);
        });
        this.model.mechanism.unshift("全选");
      } else {
        this.model.mechanism = [];
      }
    },

    changeManufacturer(e) {
      if(e) {
        this.model.manufacturerOptions.map(item => {
          if(item.value === e) {
            this.model.manufacturerName = item.label
          }
        })
      } else {
        this.model.manufacturerName = ''
      }
    },
    // 获取商品大类
    getCategoryList() {
      findDictList({
        type: 7,
      }).then((res) => {
        this.spuCategoryList = res.data.list;
      });
    },
    /**
     * 查询table数据
     */
    searchForm() {
      this.tableLoading = true;
      let tempmMechanism = ''
      let param = Object.assign({}, this.model)
      if (param.mechanism[0] === '全选') {
        tempmMechanism = param.mechanism.shift()
        tempmMechanism = param.mechanism.join()
      } else {
        tempmMechanism = param.mechanism.join()
      }
      productShootListFind({
        mechanism: tempmMechanism,
        applyReason: this.model.applyReason,
        hasOutPackagePicture: this.model.hasOutPackagePicture,
        hasInstructionPicture: this.model.hasInstructionPicture,
        productCode: this.model.productCode,
        smallPackageCode: this.model.smallPackageCode,
        generalName: this.model.generalName,
        manufacturer: this.model.manufacturer,
        manufacturerName: this.model.manufacturerName,
        taskStatus: this.taskStatus,
        spuCategory: this.spuCategory,
        page: this.pageNum,
        limit: this.pageSize,
      })
        .then((resp) => {
          if (resp.retCode === 0) {
            this.total = resp.data.total;
            this.tableData = resp.data.list
          } else {
            this.$message({
              showClose: true,
              type: "error",
              message: resp.retMsg,
            });
          }
        })
        .catch((err) => {
          this.$message({
            showClose: true,
            type: "error",
            message: "请求发生错误，请重试",
          });
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },

    /**
     * 生产厂家模糊搜索
     */
    remoteMethod(query) {
      let dictName = query || "药";
      dictSearchTypeAndName({
        dictName: dictName,
        type: "12",
      })
        .then((resp) => {
          if (resp.list && resp.list.length) {
            let list = [];
            resp.list.forEach((item, index) => {
              list.push({
                label: item.dictName,
                value: item.id,
              });
            });
            this.model.manufacturerOptions = list;
          }
        })
        .catch((err) => {
          this.$message({
            showClose: true,
            type: "error",
            message: "请求发生错误，请重试",
          });
        });
    },

    /**
     * 查询按钮点击
     */
    btnSearchClick() {
      this.pageNum = 1;
      this.searchForm();
    },

    /**
     * 重置
     */
    btnResetClick() {
      this.model.mechanism = []
      this.model.applyReason = null
      this.model.hasOutPackagePicture = null
      this.model.hasInstructionPicture = null
      this.model.productCode = ''
      this.model.smallPackageCode = ''
      this.model.generalName = null
      this.model.manufacturer = null
      this.model.manufacturerName = ''
      this.taskStatus = null
      this.spuCategory = null
      this.pageNum = 1;
      this.searchForm();
      this.remoteMethod("药");
    },

    /**
     * 导出excel
     */
    exportExcel() {
      let tempMechanism = this.model.mechanism.join()
      console.log(this.model.mechanism)
      let data = {
        mechanism: tempMechanism,
        applyReason: this.model.applyReason,
        hasOutPackagePicture: this.model.hasOutPackagePicture,
        hasInstructionPicture: this.model.hasInstructionPicture,
        productCode: this.model.productCode,
        smallPackageCode: this.model.smallPackageCode,
        generalName: this.model.generalName,
        manufacturer: this.model.manufacturer,
        manufacturerName: this.model.manufacturerName,
        taskStatus: this.taskStatus,
        spuCategory: this.spuCategory
      }

      productShootListExport(data)
        .then((res) => {
          if (!res.retCode) {
            this.$message.success(res.retMsg)
          } else {
            this.$message({
              showClose: true,
              type: "error",
              message: res.retMsg,
            });
          }
        })
        .catch((err) => {
          this.$message({
            showClose: true,
            type: "error",
            message: "导出发生错误，请重试",
          });
        });
    },

    /**
     * pageSizeInput 分页确定按钮
     */
    pageSizeInputChange() {
      let pageSizeInput = this.pageSizeInput;
      if (!pageSizeInput.toString()) {
        this.$message({
          showClose: true,
          type: "error",
          message: "请输入每页条数",
        });
        return false;
      }

      if (isNaN(pageSizeInput)) {
        this.$message({
          showClose: true,
          type: "error",
          message: "请输入200以内的正整数",
        });
        return false;
      }

      if (pageSizeInput <= 0 || pageSizeInput > 200) {
        this.$message({
          showClose: true,
          type: "error",
          message: "请输入200以内的正整数",
        });
        return false;
      }
      this.pageSize = pageSizeInput;
      this.pageNum = 1;
      this.searchForm();
    },

    /**
     * currentPage 改变时会触发
     */
    paginationCurrentChange(currentPage) {
      this.pageNum = currentPage;
      this.searchForm();
    },

    /**
     * 分页按钮特效
     */
    btnConfirmMousedown(e) {
      let className = e.currentTarget.className;
      if (!className.includes("active")) {
        e.currentTarget.className += " active";
      }
    },

    /**
     * 分页按钮特效
     */
    btnConfirmMouseup(e) {
      let className = e.currentTarget.className;
      e.currentTarget.className = className.slice(
        0,
        className.lastIndexOf(" active")
      );
    }
  },
};
</script>

<style lang="scss" scoped>
.product-to-be-film-container {
  /**
     * 查询表单
     */
  .search-form-wrap {
    width: 100%;
    padding: 15px;
    border-bottom: 1px dashed #e4e4eb;

    .el-row {
      flex-wrap: wrap;

      .el-col {
        display: flex;
        justify-content: flex-end;

        .el-form-item {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          width: 100%;
          margin-right: 0;
          margin-bottom: 15px;

          /deep/ {
            .el-form-item__label {
              width: 96px;
              line-height: normal;
              padding: 0 12px;
              color: #292933;
              font-weight: normal;
            }

            .el-form-item__content {
              flex: 1;
            }
          }

          .el-input {
            width: 100%;
          }

          .el-select {
            width: 100%;
          }
        }
      }
    }
  }

  /**
     * 按钮组
     */
  .btn-group-wrap {
    font-size: 0;
    padding: 15px;
    .el-col {
      .el-button,
      .vxe-column-filter-container {
        margin-right: 10px;
      }
    }
  }

  /**
     * table
     */
  .table-wrap {
    width: 100%;
    min-height: 440px;
    height: calc(100vh - 347px);
    padding: 0 15px;
  }

  /**
     * 分页样式
     */
  .page-size-custom {
    font-size: 0;
    margin-left: 5px;
    font-size: 0;

    .el-input {
      width: 80px;
      height: 28px;
    }

    .tips {
      margin: 0 5px;
      font-size: 13px;
      font-weight: normal;
      color: #606266;
    }

    .el-button {
      background-color: #3b95a8;
      border-color: #3b95a8;

      &:hover {
        background-color: #62aab9;
        border-color: #62aab9;
        color: #fff;
      }

      &.active {
        background-color: #358697 !important;
        border-color: #358697 !important;
        color: #fff !important;
        outline: none;
      }

      &:focus {
        background-color: #62aab9;
        border-color: #62aab9;
        color: #fff;
      }
    }
  }

  /**
     * dialog
     */
  .el-dialog__wrapper {
    /deep/ {
      .el-dialog.custom-dialog {
        width: 28%;

        .el-dialog__body {
          .export-count {
            font-size: 15px;
            margin: 30px 0;

            .el-link {
              font-size: 16px;
              font-weight: bolder;
            }
          }
        }

        .el-dialog__footer {
          .dialog-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .vxe-checkbox {
              font-size: 15px;
              color: #606266;
              font-weight: normal;
            }

            .btn-group-footer {
              font-size: 0;
            }
          }
        }
      }
    }
  }
}
</style>
