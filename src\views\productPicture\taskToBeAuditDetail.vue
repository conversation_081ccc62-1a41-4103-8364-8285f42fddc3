<template>
  <div class="detail-container">
    <div class="text-rt">
      <el-button
        type="primary"
        size="medium"
        @click="submit()"
        :disabled="subBtnState"
        >提交</el-button
      >
    </div>
    <el-form :model="form" ref="form" label-width="130px">
      <el-row class="border-bottom-dashed">
        <el-col :span="24">
          <h4 class="detail-title">申请信息</h4>
        </el-col>

        <!-- 申请时间 -->
        <el-col :span="6">
          <el-form-item label="申请时间" prop="createTime">
            <el-input v-model="form.createTime" :disabled="true"></el-input>
          </el-form-item>
        </el-col>

        <!-- 单据编号 -->
        <el-col :span="6">
          <el-form-item label="单据编号" prop="billNo">
            <el-input v-model="form.billNo" :disabled="true"></el-input>
          </el-form-item>
        </el-col>

        <!-- 申请人 -->
        <el-col :span="6">
          <el-form-item label="申请人" prop="applicant">
            <el-input v-model="form.applicant" :disabled="true"></el-input>
          </el-form-item>
        </el-col>

        <!-- 申请人所属机构 -->
        <el-col :span="6">
          <el-form-item label="申请人所属机构" prop="applicantOrganization">
            <el-input
              v-model="form.applicantOrganization"
              :disabled="true"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <h4 class="detail-title">商品明细</h4>
        </el-col>

        <el-col :span="24" class="detail-query-condition">
          <!-- 查询条件 -->
          <span class="child">
            <el-checkbox v-model="query.isZero"
              >只看图片版本数量不为0的商品</el-checkbox
            >
          </span>

          <!-- 查询条件 -->
          <span class="child">
            <el-input
              v-model="query.name"
              placeholder="商品名称/小包装条码/批准文号"
              style="width: 100%"
            ></el-input>
          </span>

          <!-- 查询条件 -->
          <span class="child">
            <el-button type="primary" size="medium" @click="queryList"
              >查询</el-button
            >
            <!-- <el-button type="primary" size="medium" @click="changeAuditStatus(1)">审核通过</el-button>
            <el-button type="primary" size="medium" @click="changeAuditStatus(2)">审核不通过</el-button>-->
          </span>
        </el-col>
      </el-row>

      <div class="table-wrap">
        <vxe-table
          border
          highlight-hover-row
          auto-resize
          resizable
          min-height="500px"
          align="center"
          class="mytable-style"
          :row-class-name="rowClassName"
          :tooltip-config="{ enterable: false }"
          :data="tableData"
          ref="refVxeTable"
        >
          <!-- <vxe-table-column type="checkbox" width="60"></vxe-table-column> -->

          <vxe-table-column
            type="index"
            title="序号"
            width="60"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="auditStatus"
            title="审核状态"
            min-width="100"
            show-header-overflow
            show-overflow
          >
            <template v-slot="{ row }">
              <!-- 审核状态(0:待审核 1:通过,其他:不通过) -->
              <span v-if="row.auditStatus == 1 || row.auditStatus == 11"
                >审核通过</span
              >
              <span v-else-if="row.auditStatus == 9">转精修</span>
              <span v-else-if="!row.auditStatus">待审核</span>
              <span v-else>审核不通过</span>
            </template>
          </vxe-table-column>
          <vxe-table-column
   
            field="historyVersionDetailPicture"
            title="审核图片预览"
            min-width="120"
          >
            <template v-slot="{ row, seq }">
              <div
                v-if="row.historyVersionDetailPicture.length"
                class="img-box"
                @click="openPreviewAudit(row, seq)"
              >
                <img
                  :src="row.historyVersionDetailPicture[0].pictureUrl"
                  class="img-s"
                />
                <span class="img-actions">
                  <i class="el-icon-zoom-in"></i>
                </span>
              </div>
              <div v-else style="height: 83px"></div>

              <!-- <preview-img :row-data="{ row, seq }" field="historyVersionDetailPicture" /> -->
            </template>
          </vxe-table-column>

          <vxe-table-column
            field="productName"
            title="商品信息"
            min-width="300"
            show-header-overflow
            show-overflow
          >
            <template v-slot="{ row }">
              <div>{{ row.productName }}</div>
              <div>{{ row.spec }}</div>
              <div>{{ row.manufacturer }}</div>
            </template>
          </vxe-table-column>

          <vxe-table-column
            field="brand"
            title="商品信息"
            min-width="200"
            show-header-overflow
            show-overflow
          >
            <template v-slot="{ row }">
              <div>{{ row.smallPackageCode }}</div>
              <div>{{ row.approvalNo }}</div>
              <div>{{ row.brand }}</div>
            </template>
          </vxe-table-column>

          <vxe-table-column
            field="productCode"
            title="商品编码"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>

          <vxe-table-column
            field="productId"
            title="商品id"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>

          <vxe-table-column
            field="originalProductCode"
            title="原商品编码"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>

          <vxe-table-column
            field="reasonStatus"
            title="拍摄原因"
            min-width="150"
            show-header-overflow
            show-overflow
          >
            <!-- 拍摄原因(1:新品上架, 2:设计驳回, 3:原图半驳, 4:运营驳回, 5:精修图半驳 , 6:更换新老包装, 7:新品再上架) -->
            <template v-slot="{ row }">{{
              [
                "",
                "新品上架",
                "设计驳回",
                "原图半驳",
                "运营驳回",
                "精修图半驳",
                "更换新老包装",
                "新品再上架",
              ][row.reasonStatus]
            }}</template>
          </vxe-table-column>

          <vxe-table-column
            field="historyVersionNum"
            title="历史图片版本数量"
            min-width="100"
            show-header-overflow
            show-overflow
            sortable
          ></vxe-table-column>

          <vxe-table-column
            field="pictureDetailVersion"
            title="绑定图片版本"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>

          <vxe-table-column
            field="originalPictureNum"
            title="原图上传数量"
            min-width="100"
            show-header-overflow
            show-overflow
            sortable
          >
            <template v-slot="{ row, seq }">
              <preview-img
                type="slot"
                :row-data="{ row, seq }"
                field="currentVersionOriginalPicture"
              >
                <el-button type="text" :disabled="!row.originalPictureNum">{{
                  row.originalPictureNum
                }}</el-button>
              </preview-img>
            </template>
          </vxe-table-column>

          <!-- 1.5.1 精修图删除数量 -->
          <vxe-table-column
            field="detailRejectPictureNum"
            title="运营删除数量"
            min-width="100"
            show-header-overflow
            show-overflow
          >
            <template>
              <!-- 子组件数据 修改父组件数据  -->
            </template>
          </vxe-table-column>

          <!-- 1.5.1 驳回原因 -->
          <vxe-table-column
            field="detailRejectReason"
            title="运营驳回原因"
            min-width="100"
            show-header-overflow
            show-overflow
          ></vxe-table-column>

          <vxe-table-column
            field="auditResult"
            title="审核意见"
            min-width="100"
            show-header-overflow
            show-overflow
          >
            <template v-slot="{ row }">
              <el-input
                :value="
                  (row.auditResult =
                    row.auditStatus && row.auditStatus != 1
                      ? row.detailRejectReason
                      : '')
                "
                maxlength="500"
              ></el-input>
            </template>
          </vxe-table-column>
        </vxe-table>
      </div>
    </el-form>
    <drag-img
      ref="dragImg"
      @refresh="queryList"
    ></drag-img>
    <!-- 精修图预览审核 -->
    <preview-audit
      ref="previewAudit"
      :show.sync="previewAuditDlgShow"
      :sku-num="tableData.length"
      :sku-index.sync="currentIndex"
      :sku-data="currRowAuditImgData"
      :all="tableData"
      @currentSkuData="currentSkuData"
      @refresh="queryList"
    />
  </div>
</template>

<script>
import {
  getProductDetail,
  auditProductDetail,
  submitDetailProduct,
} from "../../api/productPicture";

import previewImg from "@/components/uploadImg/preview";
import dragImg from "@/components/uploadImg/dragImg";
import previewAudit from "@/components/uploadImg/previewAudit";

import { parseTimestamp } from "@/utils";

export default {
  name: "",
  components: { previewImg, previewAudit, dragImg }, //previewAudit
  filters: {},
  props: {},
  created() {
    // 初始 数据
    this.queryList();
  },
  data() {
    return {
      form: {
        createTime: "",
        billNo: "",
        applicant: "",
        applicantOrganization: "",
      },
      // 列表查询条件
      query: {
        isZero: "",
        name: "",
      },
      tableData: [],
      subBtnState: false, // 提交按钮状态
      issue: [], // 存放不满足提交条件的行数据 索引集合

      previewAuditDlgShow: false, // 预览审核对话框状态
      currRowAuditImgData: {}, // 预览行数据
      // currentIndex设置为  "" 而不是0 是为了触发 openPreviewAudit 函数中 this.currentIndex = seq - 1;  初次复制为0时不触发watch的问题
      currentIndex: "", // 当前行索引
    };
  },
  computed: {
    applyCode() {
      return this.$route.query.applyCode;
    },
    businessType() {
      return this.$route.query.businessType;
    },
  },
  watch: {
    currentIndex(newV, oldV) {
      this.currRowAuditImgData = this.handleCurrentRowData(
        this.tableData[newV]
      );
    },
  },
  mounted() {},
  methods: {
    /**
     * @description:另选SKU时逻辑处理
     * @param {type} data 选择的SKU数据
     * @return {type}
     */
    currentSkuData(data) {
      this.queryList();
      // console.log(data,this.tableData[this.currentIndex])
      // this.tableData[this.currentIndex].brand=data.brand;
      // this.tableData[this.currentIndex].manufacturer=data.manufacturer;
      // this.tableData[this.currentIndex].smallPackageCode=data.smallPackageCode;
      // this.tableData[this.currentIndex].productName=data.skuName;
      // this.tableData[this.currentIndex].spec=data.spec;
      // this.tableData[this.currentIndex].approvalNo=data.smallPackageCode;
      let selectData = this.tableData[this.currentIndex];
      for (let item of selectData.historyVersionDetailPicture) {
        // 另选商品：sku维度，发现当前图片拍摄的是另一个sku时，点击按钮可筛选全部启用sku并选中，选中后将列表和预览图片浮层中的商品信息转成新选择的sku，同时将更换前的商品ID保存到列表原商品ID字段中，不管之前是啥审核状态，都变成待审核，提交前如果同一行多次另选商品，原商品ID只用记录最开始的ID
        item.auditStatus = 0;
      }
      (selectData.auditStatus = ""),
        (this.currRowAuditImgData = this.handleCurrentRowData(selectData));
      // productCode, // 商品编码
      // smallPackageCode, // 小包装条码
      // productName, // 商品名称
      // pictureDetailVersion, // 精修图版本
      // spec, // 规格/型号
      // approvalNo, // 批准文号
      // manufacturer, // 生产厂家
      // brand, // 品牌商标
      // this.dialogVisibleForSku=false;
      this.$refs.previewAudit.dialogVisibleForSku = false;
    },
    // 处理当前行数据 得到想要展示的图片数据
    handleCurrentRowData(row) {
      // console.log(row, "row");
      let {
        applyCode,
        productCode, // 商品编码
        smallPackageCode, // 小包装条码
        productName, // 商品名称
        pictureDetailVersion, // 精修图版本
        spec, // 规格/型号
        approvalNo, // 批准文号
        manufacturer, // 生产厂家
        brand, // 品牌商标
      } = row;

      return {
        applyCode,
        productCode, // 商品编码
        smallPackageCode, // 小包装条码
        productName, // 商品名称
        pictureDetailVersion, // 精修图版本
        spec, // 规格/型号
        approvalNo, // 批准文号
        manufacturer, // 生产厂家
        brand, // 品牌商标
        imgProductList: row.historyVersionDetailPicture,
        businessType: row.businessType,
      };
    },

    // 打开预览审核对话框
    openPreviewAudit(row, seq) {
      // console.log(row, seq);
      this.currentIndex = seq - 1; // 行索引
      if (this.businessType == 2) {
        this.previewAuditDlgShow = true;
      } else {
        this.$refs.dragImg.openDlg(
          JSON.parse(JSON.stringify(this.tableData)),
          this.currentIndex
        );
      }
    },

    // 获取待审核商品 - 数据、赋值
    getList(params) {
      // console.log(params, "待审核商品明细--查询参数");
      getProductDetail(params).then((result) => {
        this.tableData = result;
        this.currRowAuditImgData = this.handleCurrentRowData(
          this.tableData[this.currentIndex === "" ? 0 : this.currentIndex]
        );
        let {
          createUser,
          applyCode,
          createUserMechanism,
          createTime,
        } = this.tableData[0];

        this.form = {
          createTime: parseTimestamp(createTime),
          billNo: applyCode,
          applicant: createUser,
          applicantOrganization: createUserMechanism,
        };
      });
    },
    // 条件 查询
    queryList() {
      let params = {
        applyCode: this.applyCode, // 单据编号
        productStatus: 8, //商品状态码(0:图片拍摄中1:原图已上传 2:图片精修中 3:精修图已上传 4:精修图审核中 5:审核完毕)
        haveVersion: this.query.isZero, // // true ? 1 : 0 图片版本数量是否为零的商品
        multipleInfo: this.query.name, // 混合查询字段
      };
      this.getList(params);
    },
    // 批量操作-审核状态
    changeAuditStatus(state) {
      let selectArr = this.$refs.refVxeTable.getSelectRecords();
      // 先判断 是否有选中值
      if (selectArr.length) {
        this.$confirm(`确认审核？`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
        })
          .then(() => {
            // 提交审核结果
            this.submitAudit(selectArr, state);
          })
          .catch(() => {});
      } else {
        // 提示选中值不能为空
        this.$message.error("请选择列表数据");
      }
    },
    // 提交审核状态-待查看是否使用此函数
    submitAudit(selectArr, state) {
      let params = {
        applyCode: this.applyCode,
        taskDetailList: [],
      };
      selectArr.forEach((item) => {
        params.taskDetailList.push({
          productCode: item.productCode, // 商品编码
          auditStatus: state, // 审核状态
        });
      });
      auditProductDetail(params).then((res) => {
        if (res.retCode == 0) {
          this.$message.success("审核成功！");
          // 刷新当前页面list
          this.queryList();
        } else {
          this.$message.error(res.retMsg);
        }
      });
    },
    // 修改行样式
    rowClassName({ row, rowIndex }) {
      //{ row, rowIndex }
      // console.log(data)
      if (this.issue.includes(rowIndex)) {
        return "row-red";
      }
    },
    // 提交
    submit() {
      let arr1 = [];
      this.tableData.forEach((item, index) => {
        // 判断是否存在未审核商品 审核状态（0：待审核，1通过，2原图全驳，3原图半驳，4精修图全驳，5精修图半驳，11预审核通过，12预审核不通过）
        if (!item.auditStatus) {
          arr1.push(index);
        }
      });
      this.issue = arr1;
      if (this.issue.length) {
        this.$message.error("存在未审核商品，请全部审核后再提交");
        return;
      }
      let arr2 = [];
      // 审核不通过 - 校验审核意见
      this.tableData.forEach((item, index) => {
        // 审核状态（0：待审核，1通过，2原图全驳，3原图半驳，4精修图全驳，5精修图半驳，11预审核通过，12预审核不通过）
        if (
          item.auditStatus != 0 &&
          item.auditStatus != 1 &&
          !item.auditResult
        ) {
          arr2.push(index);
        }
      });
      this.issue = arr2;
      if (this.issue.length) {
        this.$message.error("审核不通过的商品，请填写审核意见");
        return;
      }
      if (arr1.length == 0 && arr2.length == 0) {
        // 校验通过 - 提交
        this.submitAll();
      }
    },
    // 提交
    submitAll() {
      let params = {
        // 操作类型（-1新建任务单,0上传原图,5领取精修图,1下载原图,2上传精修图,3领取,4提交审核,6领取预审核,7预审核）
        operation: 7,
        applyCode: this.applyCode,
        taskDetailList: [],
      };
      this.tableData.forEach((item) => {
        params.taskDetailList.push({
          productCode: item.productCode, // 商品编码
          auditResult: item.auditResult || "", // 审核意见
          auditStatus: item.auditStatus, //  审核状态（0：待审核，1通过，2原图全驳，3原图半驳，4精修图全驳，5精修图半驳，11预审核通过，12预审核不通过）
        });
      });
      this.subBtnState = true;
      submitDetailProduct(params)
        .then((res) => {
          if (res.retCode == 0) {
            this.$message.success({
              message: "提交成功！",
              type: "success",
              duration: "1000",
              onClose: ()=> {
                window.parent.CreateTab(
                  `../static/dist/index.html#/productPicture/taskToBeAudit?businessType=${this.businessType}`,
                  "待审核任务列表",
                  true
                );
                window.parent.CloseTab(
                  "../static/dist/index.html#/productPicture/taskToBeAuditDetail"
                );
              },
            });
          } else {
            this.$message.error(res.retMsg);
            this.subBtnState = false;
          }
        })
        .catch((err) => {
          this.$message.error(err);
          this.subBtnState = false;
        });
    }
  }
};
</script>

<style lang="scss" scoped>
.detail-container {
  padding: 20px;

  .el-form-item {
    /deep/ {
      .el-form-item__label {
        color: #292933;
        font-weight: normal;
      }
    }
  }

  .border-bottom-dashed {
    border-bottom: 1px dashed #e4e4eb;
  }

  .detail-title {
    margin: 20px;
    font-size: 16px;
    color: #292933;
  }

  .detail-query-condition {
    display: flex;
    line-height: 40px;
    margin-bottom: 15px;
    .child {
      margin-right: 10px;
    }
    .child:nth-child(2) {
      width: 400px;
    }
  }

  .mytable-style /deep/.vxe-body--row.row-red {
    background-color: #e56a6a;
    color: #fff;
  }

  // 图片小图
  .img-box {
    position: relative;
    display: inline-block;
    border-radius: 5px;
    width: 80px;
    height: 80px;
    .img-s {
      border-radius: 5px;
      width: 80px;
      height: 80px;
    }
    .img-actions {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      border-radius: 5px;
      cursor: pointer;
      text-align: center;
      opacity: 0;
      font-size: 24px;
      color: #fff;
      background-color: rgb(0, 0, 0);
      line-height: 80px;
      transition: opacity 0.3s;
    }
    .img-actions:hover {
      opacity: 0.5;
    }
  }
}
</style>
