import { DirectiveBinding } from '../../../../vue/types/options';
<template>
<div class="test">
  <el-divider content-position="center">测试需要支持的功能</el-divider>
  <el-checkbox-group v-model="checkList" style="margin-bottom:20px;">
    <el-checkbox label="行双击功能"></el-checkbox>
    <el-checkbox label="多选功能"></el-checkbox>
    <el-checkbox label="排序功能"></el-checkbox>
    <el-checkbox label="单元格内部事件"></el-checkbox>
    <el-checkbox label="内容过长时tips展示"></el-checkbox>
    <el-checkbox label="固定列"></el-checkbox>
    <el-checkbox label="导出EXCEL"></el-checkbox>
  </el-checkbox-group>
  <el-divider content-position="center"></el-divider>
  <div style="margin-bottom:10px">
    <el-button @click="exportEXCEL">导出EXCEL</el-button>
    <el-button>修改</el-button>
    <el-button>干点啥</el-button>
  </div>
  
  <el-table
    border
    ref="singleTable"
    :data="tableData"
    highlight-current-row
    @current-change="handleCurrentChange"
    @row-dblclick="dblclickEvent"
    style="width: 100%">
    <el-table-column
      type="selection"
      width="55">
    </el-table-column>
    <el-table-column
      sortable
      property="date"
      label="日期"
      width="120">
    </el-table-column>
    <el-table-column
      property="name"
      label="姓名"
      width="120">
    </el-table-column>
    <el-table-column
      property="address"
      show-overflow-tooltip
      label="地址" width="100">
    </el-table-column>
    <el-table-column
      property="address"
      label="地址" width="250">
    </el-table-column>
    <el-table-column
      property="address"
      label="地址" width="250">
    </el-table-column>
    <el-table-column
      property="address"
      label="地址" width="250">
    </el-table-column>
    <el-table-column
      property="address"
      label="地址" width="250">
    </el-table-column>
    <el-table-column
      property="address"
      label="地址" width="250">
    </el-table-column>
    <el-table-column
      property="address"
      label="地址" width="250">
    </el-table-column>
    <el-table-column
      property="address"
      label="地址" width="250">
    </el-table-column>
    <el-table-column
      fixed="right"
      label="操作"
      width="100">
      <template slot-scope="scope">
        <el-button @click="handleClick(scope.row)" type="text" size="small">查看</el-button>
        <el-button type="text" size="small">编辑</el-button>
      </template>
    </el-table-column>
  </el-table>
</div>
</template>

<script>
  export default {
    data() {
      return {
        checkList: ['行双击功能','多选功能',"排序功能","单元格内部事件","内容过长时tips展示","固定列","导出EXCEL"],
        tableData: [{
          date: '2016-05-02',
          name: '王小虎',
          address: '上海市普陀区金沙江路 1518 弄'
        }, {
          date: '2016-05-04',
          name: '王小虎',
          address: '上海市普陀区金沙江路 1517 弄'
        }, {
          date: '2016-05-01',
          name: '王小虎',
          address: '上海市普陀区金沙江路 1519 弄'
        }, {
          date: '2016-05-03',
          name: '王小虎',
          address: '上海市普陀区金沙江路 1516 弄'
        }],
        currentRow: null
      }
    },

    methods: {
      handleCurrentChange(val) {
        this.currentRow = val;
      },
      dblclickEvent(e){
        this.$message("我被双击了")
        console.log("我被双击了",e)

      },
      handleClick(e){
        this.$message("我是单元格内部事件")
         console.log("我是单元格内部事件",e)
      },
      exportEXCEL(){
        import('@/vendor/Export2Excel').then(excel => {
          const tHeader = ['日期', '姓名', '地址']
          const filterVal = ['date', 'name', 'address']
          const list = this.tableData
          const data = this.formatJson(filterVal, list)
          console.log(data,list)
          excel.export_json_to_excel({
            header: tHeader, //表头 必填
            data, //具体数据 必填
            filename: 'excel-list', //非必填
            autoWidth: true, //非必填
            bookType: 'xlsx' //非必填
          })
        })
      },
      formatJson(filterVal, jsonData) {
        return jsonData.map(v => filterVal.map(j => {
          return v[j]
        }))
      }
    }
  }
</script>
<style scoped lang="scss">
.test{
  padding: 30px;
}
</style>