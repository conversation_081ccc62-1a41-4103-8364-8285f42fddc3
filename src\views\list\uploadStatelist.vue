<template>
  <div class="v-container">
    <el-form :model="form" ref="form" label-width="110px">
      <el-row class="search-form">
        <el-col :xs="24" :sm="12" :md="8" :xl="6">
          <el-form-item label="申请时间" prop="applyDate">
            <el-date-picker
              v-model="form.applyDate"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :xs="12" :sm="12" :md="6" :xl="6">
          <el-form-item label="申请人" prop="applyer">
            <el-input v-model="form.applyer"></el-input>
          </el-form-item>
        </el-col>

        <el-col :xs="12" :sm="12" :md="6" :xl="6">
          <el-form-item label="解析状态" prop="modifyType">
            <el-select v-model="form.modifyType" placeholder="请选择">
              <el-option value label="全部"></el-option>
              <el-option value="1" label="SPU列表"></el-option>
              <el-option value="2" label="SKU列表"></el-option>
              <el-option value="3" label="商品列表"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :xs="24" :sm="12" :md="4" :xl="6">
          <div style="text-align:right">
            <el-button size="medium" type="primary" @click="queryList">查询</el-button>
            <el-button size="medium" @click="resetList">重置</el-button>
          </div>
        </el-col>
      </el-row>
    </el-form>

    <div class="table-wrap">
      <vxe-table
        border
        highlight-hover-row
        resizable
        height="100%"
        auto-resize
        size="small"
        align="center"
        :tooltip-config="{enterable: false}"
        :loading="tableLoading"
        :data="tableData"
        @sort-change="sortQueryList"
        ref="refVxeTable"
      >
        <vxe-table-column
          type="index"
          title="序号"
          width="60"
          show-header-overflow
          show-overflow
          fixed="left"
        ></vxe-table-column>

        <vxe-table-column
          field="applyDate"
          title="申请时间"
          min-width="150"
          show-header-overflow
          show-overflow
          sortable="custom"
        >
          <template v-slot="{ row }">{{ row.applyDate | dateTime }}</template>
        </vxe-table-column>

        <vxe-table-column
          field="applyer"
          title="申请人"
          min-width="100"
          show-header-overflow
          show-overflow
          sortable="custom"
        ></vxe-table-column>

        <vxe-table-column
          field="modifyType"
          title="导出列表"
          min-width="100"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">{{ ["","SPU列表","SKU列表","商品列表"][row.modifyType] }}</template>
        </vxe-table-column>

        <vxe-table-column
          field="applyCondition"
          title="查询条件"
          min-width="300"
          show-header-overflow
          show-overflow
        ></vxe-table-column>

        <vxe-table-column
          field="applyTotalNum"
          title="导出数量"
          min-width="100"
          show-header-overflow
          show-overflow
        ></vxe-table-column>

        <vxe-table-column
          field="downloadStatus"
          title="导出状态"
          min-width="100"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">{{ ["","导出中","导出成功","导出失败"][row.downloadStatus] }}</template>
        </vxe-table-column>

        <vxe-table-column title="操作" min-width="100" fixed="right" show-overflow>
          <template v-slot="{ row }">
            <el-link type="primary" :underline="false" @click="download(row)">下载</el-link>
          </template>
        </vxe-table-column>
      </vxe-table>

      <!-- 分页 -->
      <el-row class="custom-pagination-wrap">
        <el-col>
          <el-pagination
            background
            :current-page.sync="pageNum"
            :page-size.sync="pageSize"
            :page-sizes="[20, 50, 100, 200]"
            :total="total"
            layout="prev, pager, next, jumper,total, sizes"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { getExportListData } from "../../api/product.js";
import { getExportListDataSelf } from "../../api/product.js";
import { fileDownLoad } from "../../api/productPicture.js";
import { parseTime, parseDate } from "@/utils/index.js";

export default {
  name: "",
  components: {},
  filters: {
    dateTime(value) {
      return parseTime(value);
    },
  },
  props: {},
  data() {
    return {
      form: {
        applyDate: [], // 申请时间
        applyer: "", // 申请人
        modifyType: "", // 列表导出 下载类型(“”全部, 1:SPU, 2:SKU, 3:商品)
      },

      pageNum: 1,
      pageSize: 20,
      total: 0, //总条数

      sortFiled: "", // 排序查询
      sortRule: "", //(升序-ASC, 降序-DESC)

      tableLoading: false,
      tableData: [],
    };
  },
  computed: {},
  watch: {},
  created() {
    // 查询列表
    this.queryList();
  },
  mounted() {},
  methods: {
    getQueryParams() {
      let {
        applyDate,
        modifyType, // 拍摄任务单号
        applyer, // 发起人
      } = this.form;
      return {
        applyStartDate: applyDate[0] || "",
        applyEndDate: applyDate[1] || "",
        modifyType,
        applyer,
        downloadStatus: 2, //下载状态(1:导出中, 2:导出成功, 3:导出失败)
        page: this.pageNum,
        limit: this.pageSize,
        sortList: this.sortFiled
          ? [
              {
                order: "1",
                columnName: this.sortFiled,
                lift: this.sortRule, //(升序-ASC, 降序-DESC)
              },
            ]
          : [],
      };
    },

    // 排序查询
    sortQueryList({ column, property, order }) {
      this.pageNum = 1;
      this.sortFiled = property;
      this.sortRule = order;
      this.queryList();
    },

    // 查询
    async queryList() {
      this.tableLoading = true;
      let res=null;
      if(this.$route.query.type==1){
        res = await getExportListData(this.getQueryParams());
      }else{
        res = await getExportListDataSelf(this.getQueryParams());
      }
      this.tableLoading = false;
      this.tableData = res.data.list;
      this.total = res.data.total;
    },

    //  重置
    resetList() {
      this.$refs["form"].resetFields();
      this.queryList();
    },

    download(rowData) {
      let xlsxName = `导出商品列表数据_${parseDate(new Date())}.xlsx`;
      this.$message("开始下载，请耐心等待")
      fileDownLoad(rowData.downloadFileUrl, xlsxName)
        .then((resp) => {
          if (resp.data && resp.data.retCode) {
            //失败
            this.$message({
              showClose: true,
              type: "error",
              message: resp.data.retMsg,
            });
          } else {
            let blob = new Blob([resp]);
            let blobUrl = window.URL.createObjectURL(blob);
            let a = document.createElement("a");
            a.style.display = "none";
            a.download = xlsxName;
            a.href = blobUrl;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
          }
        })
        .catch((err) => {
          this.$message({
            showClose: true,
            type: "error",
            message: "下载失败，请重试",
          });
        });
    },

    /**
     * pageSize 切换每页条数
     */
    handleSizeChange(pageSize) {
      this.pageNum = 1;
      this.pageSize = pageSize;
      this.queryList();
    },

    /**
     * pageNum 切换页码 上一页 下一页 前往第几页
     */
    handleCurrentChange(currentPage) {
      this.pageNum = currentPage;
      this.queryList();
    },
  },
};
</script>

<style lang="scss" scoped>
.v-container {
  .search-form {
    width: 100%;
    padding: 15px;
    // border-bottom: 1px dashed #e4e4eb;
    .el-date-editor,
    .el-select {
      width: 100%;
    }
  }
  .tool-btn {
    width: 100%;
    padding: 15px;
  }
  .table-wrap {
    padding: 0 15px;
    min-height: 300px;
    width: 100%;
    height: calc(100vh - 177px);
  }
}
</style>
