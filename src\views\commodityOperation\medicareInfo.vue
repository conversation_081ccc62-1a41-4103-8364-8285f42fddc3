<template>
  <div class="wrapper">
    <el-form class="search-wrapper" label-width="100px" ref="form" inline>
      <el-form-item label="商品ID">
        <el-input placeholder="精准查询" v-model="form.productId"></el-input>
      </el-form-item>
      <el-form-item label="商品编码">
        <el-input
          placeholder="支持商品编码和原商品编码模糊查询"
          v-model="form.mixedQueryProductCode"
        ></el-input>
      </el-form-item>
      <el-form-item label="通用名">
        <el-input
          placeholder="支持通用名、商品名及对应的助记码模糊查询"
          v-model="form.mixedQueryGeneralName"
        ></el-input>
      </el-form-item>
      <el-form-item label="生产厂家">
        <el-input
          placeholder="支持生产厂家名称和助记码模糊查询"
          v-model="form.manufacturerName"
        ></el-input>
      </el-form-item>
      <el-form-item label="规格/型号">
        <el-input placeholder="模糊查询" v-model="form.spec"></el-input>
      </el-form-item>
      <el-form-item label="国家药品代码">
        <el-input
          placeholder="模糊查询"
          v-model="form.cnProductCode"
        ></el-input>
      </el-form-item>
      <el-form-item label="省份药品代码">
        <el-input
          placeholder="模糊查询"
          v-model="form.provinceProductCode"
        ></el-input>
      </el-form-item>
      <el-form-item label="省份">
        <el-select multiple v-model="form.provinceCoded" style="width: 185px">
          <el-option
            v-for="item in provinces"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          >
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <div style="text-align: right">
      <el-button
        type="primary"
        :loading="nationTableLoading || provinceTableLoading"
        @click="search"
        >查询</el-button
      >
      <el-button @click="resetForm">重置</el-button>
      <el-button type="primary" :loading="exportLoading" @click="exportExcel"
        >导出Excel</el-button
      >
      <el-button type="danger" @click="handleDelete">删除</el-button>
    </div>
    <el-divider></el-divider>
    <el-tabs v-model="medicalType" type="card">
      <el-tab-pane label="国家医保" name="nation">
        <vxe-table
          border
          highlight-hover-row
          resizable
          show-overflow
          size="small"
          align="center"
          :loading="nationTableLoading"
          :data="nationTableData"
          :column-config="{ minWidth: '100' }"
          ref="nationTable"
          @checkbox-all="selectAllEvent"
          @checkbox-change="selectChangeEvent"
        >
          <vxe-table-column type="checkbox" width="60"></vxe-table-column>
          <vxe-table-column
            type="seq"
            title="序号"
            width="60"
            min-width="auto"
          ></vxe-table-column>
          <vxe-table-column field="productId" title="商品ID"></vxe-table-column>
          <vxe-table-column field="skuCode" title="商品编码"></vxe-table-column>
          <vxe-table-column
            field="originalProductCode"
            title="原商品编码"
          ></vxe-table-column>
          <vxe-table-column
            field="generalName"
            title="通用名"
          ></vxe-table-column>
          <vxe-table-column field="skuName" title="商品名"></vxe-table-column>
          <vxe-table-column field="spec" title="规格/型号"></vxe-table-column>
          <vxe-table-column
            field="manufacturerName"
            title="生产厂家"
          ></vxe-table-column>
          <vxe-table-column
            field="marketAuthor"
            title="上市持有许可人"
            min-width="120"
          ></vxe-table-column>
          <vxe-table-column
            field="smallPackageCode"
            title="小包装条码"
          ></vxe-table-column>
          <vxe-table-column
            field="approvalNo"
            title="批准文号"
          ></vxe-table-column>
          <vxe-table-column
            field="packageUnitName"
            title="包装单位"
          ></vxe-table-column>
          <vxe-table-column
            field="dosageFormName"
            title="剂型"
          ></vxe-table-column>
          <vxe-table-column
            field="firstToSixthClassifyName"
            title="1-6级分类"
            min-width="180"
          ></vxe-table-column>
          <vxe-table-column
            field="standardCodes"
            title="药品本位码"
          ></vxe-table-column>
          <vxe-table-column
            field="cnProductCode"
            title="国家药品代码"
          ></vxe-table-column>
          <vxe-table-column
            field="zcategoryCode"
            title="药品总分类代码"
            min-width="120"
          ></vxe-table-column>
          <vxe-table-column
            field="overallCategory"
            title="药品总分类"
            min-width="120"
          ></vxe-table-column>
          <vxe-table-column
            field="overallCategoryCode"
            title="药品分类代码"
          ></vxe-table-column>
          <vxe-table-column
            field="productCategory"
            title="药品分类"
          ></vxe-table-column>
          <vxe-table-column
            field="medicareType"
            title="医保剂型"
          ></vxe-table-column>
          <vxe-table-column
            field="payType"
            title="支付甲乙丙类"
          ></vxe-table-column>
          <vxe-table-column
            field="payScale"
            title="个人支付占比"
          ></vxe-table-column>
          <vxe-table-column
            field="validTime"
            title="协议有效期"
          ></vxe-table-column>
          <vxe-table-column
            field="payStandard"
            title="医保支付标准"
          ></vxe-table-column>
          <vxe-table-column
            field="payTypeUnit"
            title="医保支付标准最小制剂单位"
          ></vxe-table-column>
          <vxe-table-column
            field="payPrice"
            title="医保支付价"
          ></vxe-table-column>
        </vxe-table>
        <vxe-pager
          border
          size="medium"
          :loading="nationTableLoading"
          :current-page="nationPage.page"
          :page-size="nationPage.size"
          :page-sizes="[20, 50, 100]"
          :total="nationPage.total"
          :layouts="[
            'PrevPage',
            'JumpNumber',
            'NextPage',
            'FullJump',
            'Sizes',
            'Total',
          ]"
          @page-change="onPageChange"
        >
        </vxe-pager>
      </el-tab-pane>
      <el-tab-pane label="省份医保" name="province">
        <vxe-table
          border
          highlight-hover-row
          show-overflow
          resizable
          size="small"
          align="center"
          :loading="provinceTableLoading"
          :data="provinceTableData"
          :column-config="{ minWidth: '100' }"
          ref="provinceTable"
          @checkbox-all="selectAllEvent"
          @checkbox-change="selectChangeEvent"
        >
          <vxe-table-column type="checkbox" width="60"></vxe-table-column>
          <vxe-table-column
            type="seq"
            title="序号"
            width="60"
          ></vxe-table-column>
          <vxe-table-column field="productId" title="商品ID"></vxe-table-column>
          <vxe-table-column field="skuCode" title="商品编码"></vxe-table-column>
          <vxe-table-column
            field="originalProductCode"
            title="原商品编码"
          ></vxe-table-column>
          <vxe-table-column
            field="generalName"
            title="通用名"
          ></vxe-table-column>
          <vxe-table-column field="skuName" title="商品名"></vxe-table-column>
          <vxe-table-column field="spec" title="规格/型号"></vxe-table-column>
          <vxe-table-column
            field="manufacturerName"
            title="生产厂家"
          ></vxe-table-column>
          <vxe-table-column
            field="marketAuthor"
            title="上市持有许可人"
            min-width="120"
          ></vxe-table-column>
          <vxe-table-column
            field="smallPackageCode"
            title="小包装条码"
          ></vxe-table-column>
          <vxe-table-column
            field="approvalNo"
            title="批准文号"
          ></vxe-table-column>
          <vxe-table-column
            field="packageUnitName"
            title="包装单位"
          ></vxe-table-column>
          <vxe-table-column
            field="dosageFormName"
            title="剂型"
          ></vxe-table-column>
          <vxe-table-column
            field="firstToSixthClassifyName"
            title="1-6级分类"
            min-width="180"
          ></vxe-table-column>
          <vxe-table-column
            field="standardCodes"
            title="药品本位码"
          ></vxe-table-column>
          <vxe-table-column
            field="cnProductCode"
            title="国家药品代码"
          ></vxe-table-column>
          <vxe-table-column field="province" title="省份"></vxe-table-column>
          <vxe-table-column
            field="provinceProductCode"
            title="省份药品代码"
          ></vxe-table-column>
          <vxe-table-column
            field="zcategoryCode"
            title="药品总分类代码"
            min-width="120"
          ></vxe-table-column>
          <vxe-table-column
            field="overallCategory"
            title="药品总分类"
            min-width="120"
          ></vxe-table-column>
          <vxe-table-column
            field="overallCategoryCode"
            title="药品分类代码"
          ></vxe-table-column>
          <vxe-table-column
            field="productCategory"
            title="药品分类"
          ></vxe-table-column>
          <vxe-table-column
            field="medicareType"
            title="医保剂型"
          ></vxe-table-column>
          <vxe-table-column
            field="payType"
            title="支付甲乙丙类"
          ></vxe-table-column>
          <vxe-table-column
            field="payScale"
            title="个人支付占比"
          ></vxe-table-column>
          <vxe-table-column
            field="validTime"
            title="协议有效期"
          ></vxe-table-column>
          <vxe-table-column
            field="payStandard"
            title="医保支付标准"
          ></vxe-table-column>
          <vxe-table-column
            field="payTypeUnit"
            title="医保支付标准最小制剂单位"
          ></vxe-table-column>
          <vxe-table-column
            field="payPrice"
            title="医保支付价"
          ></vxe-table-column>
        </vxe-table>
        <vxe-pager
          border
          size="medium"
          :loading="provinceTableLoading"
          :current-page="provincePage.page"
          :page-size="provincePage.size"
          :page-sizes="[20, 50, 100]"
          :total="provincePage.total"
          :layouts="[
            'PrevPage',
            'JumpNumber',
            'NextPage',
            'FullJump',
            'Sizes',
            'Total',
          ]"
          @page-change="onPageChange"
        >
        </vxe-pager>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import api from "@/api/commodityOperation";
import { filterEmptyField } from "./helper";

const initFormData = () => ({
  productId: "",
  mixedQueryProductCode: "",
  generalName: "",
  spec: "",
  manufacturerName: "",
  cnProductCode: "",
  provinceProductCode: "",
  provinceCoded: [],
});

export default {
  name: "MedicareInfo",
  data() {
    return {
      form: initFormData(),
      medicalType: "nation",
      provinces: [],
      nationTableLoading: false,
      nationTableData: [],
      nationPage: {
        page: 1,
        size: 20,
        total: 0,
      },
      provinceTableLoading: false,
      provinceTableData: [],
      provincePage: {
        page: 1,
        size: 20,
        total: 0,
      },
      exportLoading: false,
      checkedList: [],
    };
  },
  watch: {
    medicalType() {
      this.$refs.provinceTable.clearCheckboxRow();
      this.$refs.nationTable.clearCheckboxRow();
    },
  },
  methods: {
    getNationTable() {
      this.nationTableLoading = true;
      api
        .getMedicareList(
          1,
          filterEmptyField(this.form),
          this.nationPage.page,
          this.nationPage.size
        )
        .then((res) => {
          if (res.success) {
            this.nationTableData = res.data.list;
            this.nationPage.total = res.data.total;
          }
        })
        .finally(() => {
          this.nationTableLoading = false;
        });
    },
    getProvinceTable() {
      this.provinceTableLoading = true;
      api
        .getMedicareList(
          2,
          filterEmptyField(this.form),
          this.provincePage.page,
          this.provincePage.size
        )
        .then((res) => {
          if (res.success) {
            this.provinceTableData = res.data.list;
            this.provincePage.total = res.data.total;
          }
        })
        .finally(() => {
          this.provinceTableLoading = false;
        });
    },
    search() {
      if (this.medicalType == "nation") {
        this.getNationTable();
      } else {
        this.getProvinceTable();
      }
    },
    onPageChange({ currentPage, pageSize }) {
      if (this.medicalType == "nation") {
        this.nationPage.page = currentPage;
        this.nationPage.size = pageSize;
      } else {
        this.provincePage.page = currentPage;
        this.provincePage.size = pageSize;
      }
      this.search();
    },
    resetForm() {
      this.form = initFormData();
    },
    exportExcel() {
      this.exportLoading = true;
      api
        .exportMedicareList(
          filterEmptyField(this.form),
          this.medicalType == "nation" ? 1 : 2
        )
        .then((res) => {
          this.$alert(res.retMsg);
        })
        .finally(() => {
          this.exportLoading = false;
        });
    },
    selectAllEvent({ checked, records }) {
      this.checkedList = records;
    },
    selectChangeEvent({ checked, records }) {
      this.checkedList = records;
    },
    handleDelete() {
      if (this.checkedList.length == 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("此操作将永久删除医保数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let arr = [];
          this.checkedList.forEach((item) => {
            arr.push(item.id);
          });
          api
            .productMedicare({
              medicareIds: arr,
            })
            .then((res) => {
              if (!res.retCode) {
                let tableData =
                  this.medicalType == "nation"
                    ? this.nationTableLoading
                    : this.provinceTableData;
                if (tableData.length == arr.length) {
                  if (
                    this.provincePage.page * this.provincePage.size >=
                    this.provincePage.total
                  ) {
                    this.provincePage.page--;
                  }
                }
                this.search();
              } else {
                this.$message({
                  showClose: true,
                  type: "error",
                  message: res.retMsg,
                });
              }
            });
        })
        .catch(() => {});
    },
  },
  created() {
    api.getProvinceList().then((res) => {
      this.provinces = res;
    });
    this.getNationTable();
    this.getProvinceTable();
  },
};
</script>

<style lang="scss" scoped>
.wrapper {
  padding: 15px;
}
</style>
