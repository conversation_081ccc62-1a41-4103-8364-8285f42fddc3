<template>
  <div class="container">
    <el-dialog title="修改记录" :visible.sync="dialogVisible" width="500px">
      <div class="table-wrap">
        <vxe-table
          border
          highlight-hover-row
          resizable
          auto-resize
          size="small"
          align="center"
          :tooltip-config="{ enterable: false }"
          :loading="tableLoading"
          :data="tableData"
          :seq-config="{ startIndex: (pageNum - 1) * pageSize }"
          ref="refVxeTable"
        >
          <vxe-table-column
            type="seq"
            title="序号"
            width="60"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column
            field="createTime"
            title="申请时间"
            min-width="120"
            show-header-overflow
            show-overflow
            ><template v-slot="{ row }">
              {{ parseTime(row.createTime) }}
            </template></vxe-table-column
          >
          <vxe-table-column
            field="updateUser"
            title="申请人"
            width="120"
            show-header-overflow
            show-overflow
          >
          </vxe-table-column>
          <vxe-table-column
            field="applyContent"
            title="申请内容"
            width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
        </vxe-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { dictionaryRecordList } from "@/api/dict.js";
import { parseTimestamp } from "@/utils/index.js";
export default {
  name: "",
  data() {
    return {
      dialogVisible: false,
      tableData: [],
      tableLoading: false,
    };
  },
  methods: {
    open(id) {
      this.dialogVisible = true;
      this.tableLoading = true;
      dictionaryRecordList({
        limit: 100,
        page: 1,
        recordId: id,
        type: 1,
      }).then((res) => {
        if (!res.retCode) {
          this.tableLoading = false;
          this.tableData = res.data.list;
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      });
    },
    /**
     * @description: 格式化时间参数
     * @param {date} time 时间戳
     * @return: string 格式化后的时间戳
     */
    parseTime(time) {
      return parseTimestamp(time);
    },
  },
};
</script>