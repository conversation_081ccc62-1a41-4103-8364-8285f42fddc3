import request from "@/utils/request";
import qs from "qs"; // 请求数据 转成form data 形式

/**
 * author:caoshuwen
 * date: 2021-09-30
 * description:获取友商库图片来源列表
 * **/
export function getFriendList(data) {
  return request({
    url: "/api/productPictureReplace/getAllWebsite",
    method: "get"
  });
}

/**
 * author:caoshuwen
 * date: 2021-10-08
 * description:友商库搜图列表查询
 * **/
export function getFriendLibraryPictureByCondition(data) {
  return request({
    url: "/api/productPictureReplace/getFriendLibraryPictureByCondition",
    method: "post",
    data
  });
}

/**
 * author:caoshuwen
 * date: 2021-11-12
 * description:根据标准库id查询商品部分信息
 * **/
export function getProductInfoById(data) {
  return request({
      url: `/api/friendLibraryImages/getProductInfo/${data}`,
      method: 'get'
  })
}

/**
 * author:caoshuwen
 * date: 2021-11-15
 * description:获取友商图下载记录
 * **/
export function getDownloadImagesRecord(data) {
  return request({
    url: "/api/friendLibraryImages/getDownloadImagesRecord",
    method: "post",
    data
  });
}

/**
 * author:caoshuwen
 * date: 2021-11-15
 * description:下载全部标记的友商图片
 * **/
export function downloadImages(data) {
  return request({
    url: "/api/friendLibraryImages/downloadImages",
    method: "post",
    data
  });
}

/**
 * author:caoshuwen
 * date: 2021-11-15
 * description:统计数据导出列表下载次数接口
 * **/
export function downloadStatistics(data) {
  return request({
    url: "/api/download/downloadStatistics",
    method: "post",
    data
  });
}

/**
 * author:caoshuwen
 * date: 2022-05-09
 * description:查询条码详情
 * **/
export function barcodeApi(data) {
  return request({
    url: `/api/barcode/${data}`,
    method: "get"
  });
}