<template>
  <div class="component-container">
    <el-drawer
      :visible.sync="spuDrawer"
      destroy-on-close
      :size="'100%'"
      title="商品查询"
      :direction="'ttb'"
    >
      <div class="search-form-wrap">
        <el-form ref="spuDrawerFormRef" :model="spuDrawerForm">
          <el-row type="flex" style="justify-content: space-between">
            <!-- 商品分类 -->
            <el-col :lg="6" :md="8" :xs="24" :sm="12" :xl="6">
              <el-form-item label="商品分类">
                <el-select
                  v-model="spuDrawerForm.spuCategory"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="item in $store.getters.selectOptions
                      .spuCategoryOptions"
                    :key="'key_spuCategoryOptions_' + item.id"
                    :label="item.dictName"
                    :value="item.id"
                    :disabled="!item.isValid"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <!-- 品牌厂商 -->
            <el-col :lg="6" :md="8" :xs="24" :sm="12" :xl="6">
              <el-form-item label="品牌厂商">
                <el-input v-model="spuDrawerForm.brand" clearable></el-input>
              </el-form-item>
            </el-col>

            <!-- 通用名 -->
            <el-col :lg="6" :md="8" :xs="24" :sm="12" :xl="6">
              <el-form-item label="通用名">
                <el-input
                  v-model="spuDrawerForm.generalName"
                  maxlength="30"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>

            <!-- 是否受托生产 -->
            <el-col :lg="6" :md="8" :xs="24" :sm="12" :xl="6">
              <el-form-item label="是否受托生产">
                <el-select
                  v-model="spuDrawerForm.delegationProduct"
                  placeholder="请选择"
                  clearable
                >
                  <el-option label="全部" value></el-option>
                  <el-option label="是" :value="1"></el-option>
                  <el-option label="否" :value="0"></el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <!-- 六级分类 -->
            <el-col :lg="6" :md="8" :xs="24" :sm="12" :xl="6">
              <el-form-item label="六级分类">
                <el-cascader
                  :options="categoryList"
                  :props="{
                    checkStrictly: true,
                    value: 'id',
                    label: 'name',
                    lazy: true,
                    lazyLoad: lazyLoad,
                  }"
                  clearable
                  v-model="category"
                  @change="categoryChange"
                ></el-cascader>
              </el-form-item>
            </el-col>

            <!--规格型号-->
            <el-col :lg="6" :md="8" :xs="24" :sm="12" :xl="6">
              <el-form-item label="规格型号">
                <el-input
                  v-model="spuDrawerForm.spec"
                  maxlength="50"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>

            <!-- 生产厂家 -->
            <el-col :lg="6" :md="8" :xs="24" :sm="12" :xl="6">
              <el-form-item label="生产厂家">
                <el-input
                  v-model="spuDrawerForm.manufacturerName"
                  placeholder=""
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>

            <!--批准文号-->
            <el-col :lg="6" :md="8" :xs="24" :sm="12" :xl="6">
              <el-form-item label="批准文号">
                <el-input
                  v-model="spuDrawerForm.approvalNo"
                  maxlength="50"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>

            <!-- 按钮 -->
            <el-col
              :lg="24"
              :md="24"
              :xs="24"
              :sm="24"
              :xl="24"
              style="height: 40px"
            >
              <el-button type="primary" @click="getSpuTableData"
                >查询</el-button
              >
              <el-button @click="spuDrawerFormReset">重置</el-button>
            </el-col>
          </el-row>
        </el-form>
        <el-row style="margin-bottom: 20px">
          <el-col style="justify-content: flex-start">
            <el-button type="primary" @click="saveSpuCode()">保存</el-button>
            <el-button @click="cancelSpuCode()">取消</el-button>
          </el-col>
        </el-row>
        <!-- table -->
        <div class="table-wrap">
          <vxe-table
            class="my-vxe-table"
            ref="spuDrawerTable"
            border
            highlight-hover-row
            highlight-current-row
            height="100%"
            size="small"
            align="center"
            resizable
            @sort-change="sortSpuTable"
            :tooltip-config="{ enterable: false }"
            :loading="spuDrawerTableLoading"
            :data="spuDrawerTableData"
            @checkbox-all="selectAllEvent"
            @checkbox-change="selectChangeEvent"
          >
            <vxe-table-column
              type="checkbox"
              width="60"
              show-header-overflow
              show-overflow
              fixed="left"
            ></vxe-table-column>
            <vxe-table-column
              field="smallPackageCode"
              title="小包装条码"
              min-width="120"
              show-header-overflow
              show-overflow
              fixed="left"
            ></vxe-table-column>
            <vxe-table-column
              field="approvalNo"
              title="批准文号"
              min-width="120"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <vxe-table-column
              field="generalName"
              title="通用名"
              min-width="120"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <vxe-table-column
              field="spec"
              title="规格型号"
              min-width="120"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <vxe-table-column
              field="manufacturerName"
              title="生产厂家"
              min-width="120"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <vxe-table-column
              field="brand"
              title="品牌商标"
              min-width="120"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <vxe-table-column
              field="delegationProductName"
              title="是否委托生产"
              min-width="120"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <vxe-table-column
              field="delegationProductVender"
              title="委托生产厂家"
              min-width="120"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <vxe-table-column
              field="skuPrimaryTypeValue"
              title="主副商品"
              min-width="120"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <vxe-table-column
              field="packageUnitValue"
              title="包装单位"
              min-width="120"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <vxe-table-column
              field="sixCategoryUnionName"
              title="六级分类"
              min-width="120"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <vxe-table-column
              field="prescriptionCategoryValue"
              title="处方分类"
              min-width="120"
              show-header-overflow
              show-overflow
            ></vxe-table-column>
            <vxe-table-column
              field="preOperateStatusValue"
              title="预首营状态"
              min-width="120"
              show-header-overflow
              show-overflow
            >
            </vxe-table-column>
          </vxe-table>
        </div>
        <!-- 分页 -->
        <el-row style="margin-top: 10px">
          <el-col>
            <el-pagination
              background
              :current-page.sync="spuDrawerForm.page"
              :total="spuDrawerForm.total"
              layout="prev,pager,next,jumper,sizes,total"
              :page-sizes="[20, 50, 100, 200]"
              @size-change="pageSizeChange"
              @current-change="spuDrawerTablePageChange"
            ></el-pagination>
          </el-col>
        </el-row>
      </div>
    </el-drawer>
  </div>
</template>
<script>
import { getProductList } from "@/api/follow.js";
import { categoryList, findDictList } from "@/api/dict.js";

export default {
  name: "spuDrawerTable",
  data() {
    return {
      spuCategoryList: [],
      categoryList: [],
      category: [],
      spuDrawerForm: {
        spuCategory: "",
        brand: "",
        generalName: "",
        delegationProduct: "",
        spec: "",
        manufacturerName: "",
        approvalNo: "",
        limit: 20,
        page: 1
      },
      manufacturerOptions: [],
      spuDrawer: false,
      spuDrawerTableData: [],
      spuDrawerTableLoading: false,
      checkedList: [],
    };
  },
  created() {},
  methods: {
    show() {
      this.spuDrawer = true;
      this.getFixCategoryList({
        isValid: 1,
        level: 1,
        parentId: "",
      });
      this.getCategoryList();
      // this.searchManufacturerForDrawerForm();
      this.searchForm();
    },
    async searchForm() {
      try {
        this.spuDrawerTableLoading = true;
        let param = Object.assign({}, this.spuDrawerForm);
        let res = await getProductList(param);
        this.spuDrawerTableLoading = false;
        if (!res.retCode) {
          this.spuDrawerTableData = res.data.list;
          this.spuDrawerForm.total = res.data.total;
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      } catch (error) {
        console.error(error);
      }
    },
    getSpuTableData() {
      this.searchForm();
    },

    /**
     * @description: table服务器排序
     * @param {object} sort table数据对象 {column,property,order}
     * @return:undefined
     */
    sortSpuTable(sort) {
      if (!sort.order) {
        return;
      }
      let sortList = [
        {
          order: 0,
          columnName: sort.property,
          lift: sort.order.toLocaleUpperCase(),
        },
      ];
      this.spuDrawerForm.sortList = sortList;
      this.getSpuTableData();
    },

    /**
     * @description: 分页数量变更
     * @param {number} size 选中的分页数量
     * @return:
     */
    pageSizeChange(size) {
      this.spuDrawerForm.limit = size;
      this.getSpuTableData();
    },
    // 分页切换
    spuDrawerTablePageChange() {
      this.getSpuTableData();
    },

    // SPU复用查询表单重置
    spuDrawerFormReset() {
      // this.$refs[formName].resetFields();
      this.spuDrawerForm = {
        limit: 20,
        page: 1,
        spuCategory: "",
        brand: "",
        generalName: "",
        delegationProduct: "",
        spec: "",
        manufacturerName: "",
        approvalNo: "",
      };
      this.category = [];
      this.getSpuTableData();
      // this.$refs.spuDrawerFormRef.resetFields();
    },
    // 获取六级分类
    getFixCategoryList(data) {
      categoryList(data).then((res) => {
        if (!res.retCode) {
          this.categoryList = res.data;
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      });
    },
    // 获取商品大类
    getCategoryList() {
      findDictList({
        type: 7,
      }).then((res) => {
        this.spuCategoryList = res.data.list;
      });
    },

    // 动态加载六级分类
    lazyLoad(node, resolve) {
      categoryList({
        isValid: 1,
        level: node.level + 1,
        parentId: node.value,
      }).then((res) => {
        if (!res.retCode) {
          if (node.level == 5) {
            res.data.forEach((item) => {
              item.leaf = 6;
            });
          }
          resolve(res.data);
        } else {
          this.$message({
            showClose: true,
            type: "error",
            message: res.retMsg,
          });
        }
      });
    },
    // 六级分类改变
    categoryChange(e) {
      this.spuDrawerForm.firstCategory = e[0] ? e[0] : "";
      this.spuDrawerForm.secondCategory = e[1] ? e[1] : "";
      this.spuDrawerForm.thirdCategory = e[2] ? e[2] : "";
      this.spuDrawerForm.fourthCategory = e[3] ? e[3] : "";
      this.spuDrawerForm.fiveCategory = e[4] ? e[4] : "";
      this.spuDrawerForm.sixCategory = e[5] ? e[5] : "";
    },
    // 保存SPU复用
    saveSpuCode() {
      this.$emit("saveProduct", this.checkedList);
      this.spuDrawer = false;
    },
    // 取消
    cancelSpuCode() {
      this.spuDrawer = false;
      this._checkSpuCode = null;
    },
    /**
     * @description: table 行选中
     * @param {object} data 选中行数据对象
     * @param {object} event 事件对象
     * @return:
     */
    // 表格全选
    selectAllEvent({ checked, records }) {
      this.checkedList = records;
    },

    // 表格单选
    selectChangeEvent({ checked, records }) {
      this.checkedList = records;
    },
  },
};
</script>

<style lang="scss" scoped>
/**
   * 查询表单
   */
.search-form-wrap {
  width: 100%;
  padding: 15px;
  max-height: calc(100vh - 77px);
  border-bottom: 1px dashed #e4e4eb;
  overflow-y: scroll;
  .el-row {
    flex-wrap: wrap;

    .el-col {
      display: flex;
      justify-content: flex-end;

      .el-form-item {
        flex: 1;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        width: 100%;
        margin-right: 0;
        margin-bottom: 15px;

        /deep/ {
          .el-form-item__label {
            width: 96px;
            line-height: normal;
            padding: 0 12px;
            color: #292933;
            font-weight: normal;
          }
          .el-form-item__content {
            flex: 1;
          }
        }
        .el-cascader {
          width: 100%;
        }
        .el-input {
          width: 100%;
        }

        .el-select {
          width: 100%;
        }
      }
    }
  }
}
/**
   * table
   */
.table-wrap {
  width: 100%;
  min-height: 120px;
  height: calc(100vh - 430px);
  // padding: 0 15px;
}
</style>