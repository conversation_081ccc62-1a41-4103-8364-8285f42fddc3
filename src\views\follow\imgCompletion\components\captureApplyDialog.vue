<template>
  <div class="container">
    <el-dialog
      title="批量商品抓取图片申请"
      :visible.sync="dialogVisible"
      :before-close="close"
      width="800px"
    >
      <el-form
        :model="formData"
        :rules="rules"
        ref="ruleForm"
        class="form-wrap"
      >
        <el-form-item label="文件名称" prop="fileName" class="upload-wrap">
          <el-input
            v-model="formData.fileName"
            disabled=""
            placeholder="请通过右侧按钮选择文件"
          >
            <el-upload
              slot="append"
              class="upload-wrapper"
              ref="refUpload"
              :action="no"
              :multiple="false"
              :accept="fileAccept"
              :show-file-list="false"
              :auto-upload="false"
              :on-change="uploadOnChange"
            >
              选择文件
            </el-upload>
          </el-input>
        </el-form-item>
      </el-form>
      <div class="tip-wrap">
        <div>*注意：</div>
        <div>1、请按照模板上传文件；</div>
        <div>
          2、文件导入后，还未提交图片爬取申请，请在批量抓取图片申请记录菜单确认发送；
        </div>
        <div>3、申请人（爬虫提交的申请人）写在文件名上；</div>
        <div>4、一次最多上传3万个申请；</div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleDownLoad" :disabled="btnDis"
          >下载模板</el-button
        >
        <el-button type="primary" @click="handleSumit" :disabled="btnDis"
          >开始上传</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { picComplementCrawl } from "@/api/follow.js";
import { createDownloadElement } from "@/utils/index.js";

export default {
  data() {
    return {
      dialogVisible: false,
      formData: {
        fileName: "",
      },
      rules: {
        fileName: [
          {
            required: true,
            message: "请上传文件",
            trigger: "change",
          },
        ],
      },
      fileAccept: ".xlsx",
      btnDis: false,
    };
  },
  methods: {
    open() {
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.ruleForm.clearValidate();
        this.$refs.refUpload.clearFiles();
      });
    },
    close() {
      this.formData = {
        fileName: "",
      };
      this.dialogVisible = false;
    },
    uploadOnChange(file) {
      if (
        this.fileAccept.indexOf(
          file.name.substr(file.name.lastIndexOf(".") + 1)
        ) == -1
      ) {
        this.$message.warning(`请上传${this.fileAccept}格式的文件`);
        return;
      }
      this.formData.fileName = file.name;
      this.formData.file = file.raw;
    },
    handleSumit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let formData = new FormData();
          formData.append("file", this.formData.file);
          this.btnDis = true;
          picComplementCrawl(formData).then((res) => {
            this.btnDis = false;
            if (!res.retCode) {
              this.$message.success("上传成功");
              this.close();
            } else {
              this.$message.error(res.retMsg);
            }
          });
        }
      });
    },
    handleDownLoad() {
      createDownloadElement(
        "../../../static/assets/excel/图片补全爬图模板.xlsx",
        "批量商品抓取图片模板.xlsx"
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.form-wrap /deep/ {
  .radio-wrap /deep/ {
    .el-form-item {
      margin-bottom: 22px;
    }
    .el-form-item__label,
    .el-form-item__content {
      line-height: 20px;
    }
  }
  .upload-wrap.el-form-item {
    display: flex;
    .el-form-item__content {
      flex: 1;
    }
  }
}
.dialog-footer {
  display: flex;
  justify-content: center;
}
.tip-wrap {
  line-height: 24px;
}
</style>
