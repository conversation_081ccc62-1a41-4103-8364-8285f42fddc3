<template>
  <div class="component-container">
    <approval-process :approvalData="approvalData"></approval-process>
    <div style="display:flex;justify-content:center">
      <el-button v-if="!banBtn" @click="spuCodeMultiplexing" round style="margin:20px;"
        >点击此区域，搜索spu或sku，如没有符合要求的，需要先新建，审核通过后再建对应的副商品</el-button
      >
    </div>
    <div class="empty"></div>
    <!-- spu -->
    <div class="basic-info-title">SPU</div>
    <div style="padding:0 40px 20px">
      <vxe-table
        border
        highlight-hover-row
        resizable
        auto-resize
        size="small"
        align="center"
        :tooltip-config="{ enterable: false }"
        :data="spuTableData"
      >
        <vxe-table-column field="spuCode" title="SPU编码" min-width="80" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="largeCategoryName" title="商品大类" min-width="100" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="approvalNo" title="批准文号" min-width="110" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="businessScopeListFormat" title="经营范围" min-width="110" show-header-overflow show-overflow>
          <template v-slot="{ row }">
            <span>{{ urlParam.pageType ? row.businessScopeMultiStr : row.businessScopeListFormat }}</span>
          </template>
        </vxe-table-column>
        <vxe-table-column field="generalName" title="通用名" min-width="110" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="dosageFormName" title="剂型" min-width="70" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="shadingAttrName" title="存储属性" min-width="80" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column title="六级分类" min-width="190" show-header-overflow show-overflow>
          <template v-slot="{ row }">
            <span
              >{{ row.firstCategoryName }}>{{ row.secondCategoryName }}>{{ row.thirdCategoryName }}>{{ row.fourthCategoryName }}>{{
                row.fiveCategoryName
              }}>{{ row.sixCategoryName }}</span
            >
          </template>
        </vxe-table-column>
        <vxe-table-column field="manufacturerName" title="生产厂家" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="instructionSpec" title="批件规格" min-width="120" show-header-overflow show-overflow></vxe-table-column>
      </vxe-table>
    </div>
    <div class="empty"></div>
    <!-- sku -->
    <div class="basic-info-title">SKU</div>
    <div style="padding:0 40px 20px">
      <vxe-table
        border
        highlight-hover-row
        resizable
        auto-resize
        size="small"
        align="center"
        :tooltip-config="{ enterable: false }"
        :data="skuTableData"
      >
        <vxe-table-column type="seq" title="序号" min-width="50" show-header-overflow show-overflow> </vxe-table-column>
        <vxe-table-column field="skuCode" title="sku编码" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="skuName" title="商品名" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="brand" title="品牌商标" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="spec" title="规格型号" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="packageUnitName" title="包装单位" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column
          field="prescriptionCategoryName"
          title="处方分类"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column field="storageCondName" title="存储条件" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="validity" title="有效期" min-width="120" show-header-overflow show-overflow>
          <template v-slot="{ row }">
            <span>{{ row.validity }}{{ row.validityUnit | filterUnit }}</span>
          </template>
        </vxe-table-column>
        <vxe-table-column
          field="entrustedManufacturerName"
          title="委托生产厂家"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column field="standardCodes" title="本位码" min-width="120" show-header-overflow show-overflow></vxe-table-column>
        <vxe-table-column field="smallPackageCode" title="小包装条码" min-width="120" show-header-overflow show-overflow></vxe-table-column>
      </vxe-table>
    </div>
    <div class="empty"></div>
    <div class="basic-info-title">
      {{ urlParam.pageType ? "管理属性" : "添加管理属性"
      }}<span v-if="!urlParam.pageType" class="tip">注意：1、每添加一行，就添加了一个副商品；2、删除的副商品无法提交到工作流；</span>
    </div>
    <div style="width:100%;display:flex;justify-content:right">
      <el-button v-if="skuTableData.length && !banBtn" type="primary" style="margin-right:20px" @click="addSau">添加副商品</el-button>
    </div>
    <el-tabs type="border-card" v-model="tabIndex">
      <el-tab-pane v-for="(item, index) in skuTableData" :key="index" :label="'sku' + (index + 1)" :name="index">
        <vxe-table
          :height="250"
          style="padding-bottom:72px"
          border
          resizable
          auto-resize
          size="small"
          align="center"
          :row-class-name="rowClassName"
          :tooltip-config="{ enterable: false }"
          :data="sauTableData[index]"
          :editConfig="{ trigger: 'click', mode: 'cell', activeCellMethod: true }"
        >
          <vxe-table-column type="seq" title="序号" min-width="50" show-header-overflow show-overflow> </vxe-table-column>
          <vxe-table-column field="sauCode" title="商品编码" min-width="120" show-header-overflow show-overflow></vxe-table-column>
          <vxe-table-column field="skuCode" title="sku" min-width="120" show-header-overflow show-overflow></vxe-table-column>
          <vxe-table-column title="是否线下业务" min-width="120" show-header-overflow show-overflow>
            <template v-slot="{ row }">
              <el-radio :disabled="(!row.isNew && urlParam.pageType !== 'edit') || banEdit" v-model="row.offlineBusinessType" :label="1"
                >是</el-radio
              >
              <el-radio :disabled="(!row.isNew && urlParam.pageType !== 'edit') || banEdit" v-model="row.offlineBusinessType" :label="0"
                >否</el-radio
              >
            </template>
          </vxe-table-column>
          <vxe-table-column
            field="attrValue"
            title="销售渠道"
            min-width="120"
            show-header-overflow
            show-overflow
            :editRender="{ name: 'input', events: { input: changeComment } }"
          >
            <template #edit="{ row }">
              <el-input :disabled="(!row.isNew && urlParam.pageType !== 'edit') || banEdit" v-model="row.attrValue" maxlength="10"></el-input>
            </template>
          </vxe-table-column>
          <vxe-table-column
            v-if="urlParam.pageType"
            field="mergeProduct"
            title="合并商品"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>
          <vxe-table-column title="操作" min-width="120" show-header-overflow show-overflow>
            <template v-slot="{ row, $rowIndex }">
              <span v-if="row.isNew && !urlParam.pageType">
                <el-link :underline="false" type="primary" @click.stop="deleteItem($rowIndex)">删除</el-link></span
              >
            </template>
          </vxe-table-column>
        </vxe-table>
      </el-tab-pane>
    </el-tabs>
    <modify-record v-if="urlParam.pageType === 'detail'" :recordData="recordData" style="padding-bottom:10px"></modify-record>
    <el-row class="bottom-btns" v-if="!banEdit">
      <el-col :span="24" class="bottom-btn-wrap">
        <el-button :disabled="banSubmmit" type="primary" @click="submit()">提交商品信息</el-button>
      </el-col>
    </el-row>
    <!-- SPU 查询 -->
    <spu-drawer-table ref="spuDrawerTable" @saveSpu="saveSpu"></spu-drawer-table>
  </div>
</template>

<script>
import spuDrawerTable from "./spu/components/spuDrawerTable"
import modifyRecord from "./modifyRecord"
// 商品基础共用信息混入对象
import { productMixinBase } from "@/mixin/productMixinBase.js"
import { getSkuList } from "@/api/list"
import { getTotalDictionaryTree } from "@/api/dict.js"
import {
  getApplyInfo, //审批流信息
  addSau, //新增副商品
  editSau, //编辑副商品
  getProductData, //副商品详情
  modifyedSau //副商品详情2
} from "@/api/product"

export default {
  name: "",
  mixins: [productMixinBase],
  filters: {
    filterUnit(e) {
      switch (e) {
        case 1:
          return "日"
        case 2:
          return "月"
        case 3:
          return "年"
      }
    }
  },
  components: {
    spuDrawerTable,
    modifyRecord
  },
  watch: {},
  data() {
    return {
      banBtn: false,
      banEdit: false,
      tabIndex: 0,
      spuTableData: [],
      skuTableData: [],
      sauTableData: [],
      recordData: [],
      banSubmmit: false, //防抖节流标识
    }
  },
  computed: {
    // 路由参数
    urlParam() {
      return this.$route.query
    }
  },
  async created() {
    // 获取审批流信息
    this.getApplyInfo()
    if (this.urlParam.pageType) {
      this.banBtn = true
      this.banEdit = this.urlParam.pageType === "edit" ? false : true
      // 获取详情
      await this.getSauDetail()
    }
  },
  methods: {
    // 修改销售渠道
    changeComment({ rowIndex, column }) {
      this.sauTableData[this.tabIndex][rowIndex].attrValue = column.model.value
    },
    // 添加副商品btn
    addSau() {
      let temp = this.sauTableData[this.tabIndex][0]
      this.sauTableData[this.tabIndex].push({ spuCode: temp.spuCode, skuCode: temp.skuCode, offlineBusinessType: 0, attrValue: null, isNew: 1 })
    },
    // 删除副商品btn
    deleteItem(e) {
      this.sauTableData[this.tabIndex].splice(e, 1)
    },
    // 打开搜索spu sku弹框
    spuCodeMultiplexing() {
      this.$refs.spuDrawerTable.show()
    },
    // 保存搜索到的spu并通过spu编码搜索对应sku
    async saveSpu(spuInfo) {
      this.spuTableData = [spuInfo]
      this.$refs.spuDrawerTable.cancelSpuCode()
      try {
        const res = await getSkuList(spuInfo.spuCode)
        this.skuTableData = res.data
        this.sauTableData = []
        this.skuTableData.map(item => {
          this.sauTableData.push(item.sauList)
        })
      } catch (error) {}
      this.spuLoading = true
    },
    // 提交新增副商品/修改副商品
    async submit() {
      let temp = _.cloneDeep(this.sauTableData)
      // 二维数组转一维数组
      let param = [].concat.apply([], temp)
      console.log(param)
      if (this.urlParam.pageType === "edit") {
        if (param.length > 1) {
          this.$message.error("不合规数据！")
          return
        } else if (param[0].mergeProduct) {
          this.$message.error("合并商品不可以修改管理属性！")
          return
        }
      }
      if (this.urlParam.pageType !== "edit") {
        // 新增管理属性 忽略默认的
        let i = param.length
        while (i--) {
          if (param[i] && !param[i].isNew) {
            param.splice(i, 1)
          } else if (param[i] && !param[i].attrValue) {
            param[i].attrValue = null
          }
        }
        param = param.filter((e) => e != null);
      }
      try {
        console.log(param)
        if (!param.length) {
          this.$message.error("至少添加一条副商品")
          return
        }
        this.banSubmmit = true
        const res = this.urlParam.pageType === "edit" ? await editSau(param[0]) : await addSau({ sauList: param })
        this.banSubmmit = false
        if (res.retCode !== 0) {
          this.$message.error(res.retMsg)
        } else {
          try {
            parent.CreateTab("../static/dist/index.html#/product/productList", "商品列表", true)
            parent.CloseTab("../static/dist/index.html#/product/addDeputyProduct")
          } catch (error) {
            this.$router.push("/product/productList")
          }
        }
      } catch (error) {
        console.log(error)
      }
    },
    async getApplyInfo() {
      let param = {
        selectType: 1, // 查询类型, (0:草稿, 1:新增, 2:属性修改,3:审核或驳回修改,4:查询详情)
        applyCode: "", //单据编号
        productCode: "", //商品编码
        productType: 1, //商品类型(1-SPU;2-SKU;3-SAU)
        spuCode: "" //SPU编码
      }
      let res = await getApplyInfo(param)
      if (res.success) {
        this.approvalData = res.data
        if (this.urlParam.pageType) {
          this.approvalData.approvalProcess.showFlow = false
        }
      } else {
        this.$message.error(res.retMsg)
      }
    },
    // 详情
    async getSauDetail() {
      try {
        const res = this.urlParam.pageType === "edit" ? await modifyedSau(this.urlParam) : await getProductData(this.urlParam)
        console.log(res)
        if (res.retCode === 0) {
          this.spuTableData = [res.data.spu]
          this.skuTableData = res.data.sku
          if (this.urlParam.pageType === "edit") {
            this.sauTableData = [res.data.sau]
          } else {
            // 过滤
            this.sauTableData = [[]]
            res.data.sau.map(item => {
              if (item.skuCode === this.urlParam.skuCode) {
                this.sauTableData[0].push(item)
              }
            })
          }
          this.recordData = res.data.record
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 行样式 精确查询变蓝
    rowClassName({ row }) {
      if (row.sauCode === this.urlParam.productCode && this.urlParam.pageType === "detail") {
        return "row-blue"
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.component-container {
  padding-top: 0px;
  .loading {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
  }
  /deep/ .el-tabs__header {
    border-bottom: none;
    background-color: #f2f2f2;
  }
  /deep/.el-tabs__content {
    padding: 0 !important;
  }
  .el-tabs--border-card {
    min-height: 150px;
    border: none;
    box-shadow: none;
    /deep/ .el-tabs__content {
      padding-bottom: 80px;
      padding-left: 0;
      padding-right: 0;
    }
  }
  .bottom-btns {
    background: #f0f2f5;
    width: 100%;
    position: fixed;
    bottom: 0;
    padding: 15px;
    z-index: 10;
  }
}
.bottom-btn-wrap {
  display: flex;
  justify-content: center;
}
// csw
.empty {
  background: rgb(240, 242, 245);
  height: 20px;
}
.basic-info-title {
  font-weight: 600;
  margin-bottom: 22px;
  border-bottom: 1px solid #e4e4eb;
  line-height: 50px;
  padding-left: 20px;
}
.tip {
  margin-left: 20px;
  font-size: 12px;
  color: rgb(197, 197, 197);
}
/deep/ .row-blue {
  color: #fff;
  background-color: #3b95a8;
}
</style>
