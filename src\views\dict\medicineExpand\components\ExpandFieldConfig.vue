
<!--
 * @Descripttion: 
 * @Version: 1.0
 * @Author: zyh
 * @Date: 2025-02-07 14:55:47
 * @LastEditors: zyh
 * @LastEditTime: 2025-02-18 14:04:36
-->

<template>
    <div class="dialogContent">
        <el-dialog title="拓展值配置"
                   :visible.sync="dialogVisible"
                   width="1050px">
            <div class="table-wrap">
                <vxe-table border
                           highlight-hover-row
                           resizable
                           auto-resize
                           size="small"
                           align="center"
                           :tooltip-config="{ enterable: false }"
                           :loading="tableLoading"
                           :data="tableData"
                           :seq-config="{ startIndex: (pageNum - 1) * pageSize }"
                           ref="refVxeTable">
                    <vxe-table-column type="seq"
                                      title="序号"
                                      width="60"
                                      show-header-overflow
                                      show-overflow></vxe-table-column>
                    <vxe-table-column field="categoryName"
                                      title="拓展类型"
                                      width="160"
                                      show-header-overflow
                                      show-overflow>
                    </vxe-table-column>
                    <vxe-table-column field="commonName"
                                      title="通用名"
                                      width="160"
                                      show-header-overflow
                                      show-overflow>
                    </vxe-table-column>
                    <vxe-table-column field="dictName"
                                      title="拓展字段"
                                      width="180"
                                      show-header-overflow
                                      show-overflow>
                        <template v-slot="{row}">
                            <!-- 状态 0停用 1启用 -->
                            <div v-if="isEditing && (tableData.length == row.index + 1 )">
                                <el-select v-model="row.dictId"
                                           placeholder="请选择"
                                           filterable
                                           clearable>
                                    <el-option v-for="item in dictIdSourceList"
                                               :key="item.id"
                                               :label="item.value"
                                               :value="item.id"></el-option>
                                </el-select>
                            </div>
                            <span v-else>{{row.dictName}}</span>
                        </template>
                    </vxe-table-column>
                    <vxe-table-column field="dictValue"
                                      title="拓展值"
                                      show-header-overflow
                                      show-overflow>
                        <template v-slot="{ row }">
                            <el-input v-model.trim="row.dictValue"
                                      @blur="handleBlur(row)"
                                      placeholder="拓展值格式为：值1,值2 （英文逗号隔开）"></el-input>
                        </template>
                    </vxe-table-column>
                    <vxe-table-column field="updateUser"
                                      title="是否启用"
                                      width="120"
                                      show-header-overflow
                                      show-overflow>
                        <template v-slot="{ row }">
                            <!-- 状态 0停用 1启用 -->
                            <div v-if="isEditing && (tableData.length == row.index + 1 )">
                                <el-switch disabled
                                           v-model="row.status == 1" />
                            </div>
                            <el-switch v-else
                                       v-model="row.status == 1"
                                       @change="switchChange(row)" />
                        </template>
                    </vxe-table-column>
                </vxe-table>
            </div>
            <span slot="footer"
                  class="dialog-footer">
                <el-button type="primary"
                           size="medium"
                           :disabled="isEditing"
                           @click="addNewItem">新增</el-button>
                <el-button @click="save">保存</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import { apiExtQueryDetailList, apiExtDetailUpdateStatus, apiSelectDictList, apiExtDetailSaveItem, apiExtDetailItemUpdate } from "@/api/dict.js";
export default {
    name: "",
    data () {
        return {
            isEditing: false,
            dialogVisible: false,
            tableData: [],
            tableLoading: false,
            dictIdSourceList: [],
            currentRow: null,
        };
    },
    methods: {
        async getSelectDictList () {
            const res = await apiSelectDictList({ type: 1 });
            if (res.retCode == 0) {
                this.dictIdSourceList = res.data.list;
            } else {
                this.$message({
                    showClose: true,
                    type: "error",
                    message: res.retMsg,
                });
            }
        },
        open (row) {
            this.tableData = [];
            this.currentRow = row;
            this.isEditing = false;
            this.dialogVisible = true;
            this.queryDetailList();
            this.getSelectDictList();
        },
        async queryDetailList () {
            this.isEditing = false;
            this.tableLoading = true;
            let res = await apiExtQueryDetailList({
                // commonName和categoryCode为列表进入编辑
                commonName: this.currentRow.generalName || this.currentRow.commonName,
                categoryCode: this.currentRow.spuCategory || this.currentRow.categoryCode,
            });
            this.tableLoading = false;
            if (res.retCode == 0) {
                this.tableLoading = false;
                this.tableData = (res.data.list || []).map(item => {
                    return {
                        ...item,
                        dictValueRecord: item.dictValue
                    };
                });
                this.total = res.data.total;
            } else {
                this.$message({
                    showClose: true,
                    type: "error",
                    message: res.retMsg,
                });
            }
        },
        addNewItem () {
            this.isEditing = true;
            const commonName = this.currentRow.generalName || this.currentRow.commonName;
            const categoryCode = this.currentRow.spuCategory || this.currentRow.categoryCode;
            const categoryName = this.currentRow.spuCategoryName || this.currentRow.categoryName;
            this.tableData.push(
                { commonName, categoryCode, categoryName, dictName: '', dictValue: '', 'status': 1, index: this.tableData.length }
            );
            setTimeout(() => {
                const dialogBody = document.querySelector('.dialogContent .el-dialog__body');
                if (dialogBody) {
                    dialogBody.scroll({
                        top: dialogBody.scrollHeight,
                        behavior: 'smooth'
                    });
                }
            }, 100);
        },
        switchChange (row) {
            if (row.status == 1) {
                this.$confirm("停用后, 该拓展值不再被商业商品维护使用", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                }).then(() => {
                    this.updateStatus(row);
                })
            } else {
                this.updateStatus(row);
            }
        },
        async updateStatus (row) {
            const status = row.status == 1 ? 0 : 1;
            const formdata = new FormData();
            formdata.append("status", status);
            formdata.append("id", row.id);
            const res = await apiExtDetailUpdateStatus(formdata);
            if (res.retCode == 0) {
                this.queryDetailList();
            } else {
                this.$message({
                    showClose: true,
                    type: "error",
                    message: res.retMsg,
                });
            }
        },
        async handleBlur (row) {
            if (!row.id) return;//新增行不做编辑处理
            if (row.dictValueRecord == row.dictValue) return; //没有修改不请求
            const res = await apiExtDetailItemUpdate({ dictValue: row.dictValue, id: row.id });
            if (res.retCode == 0) {
                this.queryDetailList();
                this.$message.success('编辑完成～');
            } else {
                this.$message({
                    showClose: true,
                    type: "error",
                    message: res.retMsg,
                });
            }
        },

        async save () {
            if (!this.isEditing) return this.$message.info('请先新增编辑行');
            const lastItem = this.tableData[this.tableData.length - 1];
            const { categoryCode, commonName, dictId, dictValue, status } = lastItem;
            if (!dictId || !dictValue) {
                return this.$message.info('拓展值和拓展字段都不能为空');
            }
            const findItem = this.dictIdSourceList.find(item => item.id === dictId);
            const res = await apiExtDetailSaveItem({ categoryCode, commonName, dictName: findItem.value, dictId, dictValue, status });
            if (res.retCode == 0) {
                this.queryDetailList();
            } else {
                this.$message({
                    showClose: true,
                    type: "error",
                    message: res.retMsg,
                });
            }
        },
    },
};
</script>
<style lang="scss" scoped>
.search-form-box {
    margin-bottom: 12px;
    .el-form-item {
        margin-right: 20px;
        margin-bottom: 15px;
        .el-input {
            width: 150px;
        }
        .el-select {
            width: 140px;
        }
    }
}
</style>

<style lang="scss">
.vxe-table--tooltip-wrapper {
    z-index: 3000 !important;
}
</style>