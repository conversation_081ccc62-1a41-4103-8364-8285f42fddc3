{"name": "vue-admin-template", "version": "4.2.1", "description": "A vue admin template with Element UI & axios & iconfont & permission control & lint", "author": "Pan <<EMAIL>>", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build", "build:test": "vue-cli-service build --mode test", "build:staging": "vue-cli-service build", "build:prod": "vue-cli-service build", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml"}, "dependencies": {"axios": "0.18.1", "cos-js-sdk-v5": "^1.3.8", "dayjs": "^1.9.4", "echarts": "^5.0.2", "element-ui": "^2.12.0", "file-saver": "^2.0.2", "image-conversion": "^1.1.9", "jquery": "^3.5.1", "js-cookie": "2.2.0", "jszip": "^3.6.0", "lodash": "^4.17.15", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "script-loader": "^0.7.2", "simple-flakeid": "^0.0.5", "vue": "2.6.10", "vue-clipboard2": "^0.3.3", "vue-router": "3.0.6", "vuedraggable": "^2.24.2", "vuex": "3.1.0", "vxe-table": "^2.7.11", "xe-utils": "^2.3.0", "xlsx": "^0.16.9", "ztree": "^3.5.24"}, "devDependencies": {"@babel/core": "7.0.0", "@babel/register": "7.0.0", "@vue/cli-plugin-babel": "3.6.0", "@vue/cli-plugin-eslint": "^3.9.1", "@vue/cli-plugin-unit-jest": "^4.0.4", "@vue/cli-service": "^4.1.1", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "^9.5.1", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "^25.0.0", "chalk": "2.4.2", "connect": "3.6.6", "eslint": "5.15.3", "eslint-plugin-vue": "5.2.2", "html-webpack-plugin": "3.2.0", "less": "^3.10.3", "less-loader": "^5.0.0", "mockjs": "1.0.1-beta3", "node-sass": "^4.13.1", "runjs": "^4.3.2", "sass-loader": "^7.1.0", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "^0.7.2", "serve-static": "^1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "vue-template-compiler": "2.6.10", "vxe-table": "^2.6.22", "xe-utils": "^2.2.16"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}