<template>
  <div>
    <!-- 修图弹框 -->
    <el-dialog :visible.sync="dlgVisible" fullscreen class="text-lf" @close="closeDlg">
      <div class="model" v-show="model" @click="model = false">
          <div class="model-show">
              <img :src="modelSrc" alt="">
          </div>
          <div class="empty" v-if="showCropWidth<800">

          </div>
      </div>
      <div slot="title" class="dlg-title">
        <span>{{ imgTitle }}</span>
        <span class="cc" v-if="!!imgList[currImgIndex] && imgList[currImgIndex].hasOwnProperty('pictureWidth')">宽高：{{imgList[currImgIndex].pictureWidth + 'px * ' + imgList[currImgIndex].pictureHeight + 'px'}}</span>
      </div>
      <div class="top">
        <div class="productInfo" v-if="!!productInfo">
          <div class="btnBox">
            <span>操作：</span>
            <!-- <el-button size="mini" @click="preview">后退</el-button>
            <el-button size="mini" @click="next">前进</el-button> -->
            <el-button
              type="danger"
              size="mini"
              v-if="
                !!imgList[currImgIndex] &&
                imgList[currImgIndex].deleteStatus == 0
              "
              @click="handleDelete(true)"
              >删除</el-button
            >
            <el-button
              type="info"
              size="mini"
              v-else
              @click="handleDelete(false)"
              >取消删除</el-button
            >
            <div style="margin-top:10px">
             <el-button size="mini" type='primary' @click="digPhoto">一键抠图</el-button>
            </div>
            <div style="margin-top:10px">
             <el-button type='primary' size="mini" @click="rotate-=90;addRecord()">-90°</el-button>
             <el-input type='number' v-model='rotate' style="width:60px;height:30px;margin:0 5px"></el-input>
             <el-button type='primary' size="mini" @click="rotate+=90;addRecord()">+90°</el-button>
             <el-button type='primary' size="mini">旋转</el-button>
            </div>
            <div style="margin-top:10px">
             <span>提高亮度</span>
             <el-slider v-model="slider" @change="addRecord" style="width:80%"></el-slider>
            </div>
            <!-- <div style="margin-top:10px;display:flex;flex-direction:column;justify-content:center;align-items:center">
             <el-button @click="move('top',0)" type='primary' size="mini">↑</el-button>
             <div>
              <el-button @click="move('left',0)" type='primary' size="mini">←</el-button>
              <span>确定位置</span>
              <el-button @click="move('left',1)" type='primary' size="mini">→</el-button>
             </div>
             <el-button @click="move('top',1)" type='primary' size="mini">↓</el-button>
            </div> -->
            <!-- <div style="margin-top:10px">
              <el-button
                size="medium"
                icon="el-icon-zoom-out"
                title="缩小"
                @click="scale-=0.05;addRecord()"
              ></el-button>
              <span>确定大小</span>
              <el-button
                size="medium"
                icon="el-icon-zoom-in"
                title="放大"
                @click="scale+=0.05;addRecord()"
              ></el-button>
            </div> -->
            <div style="margin-top:10px">
              截图框大小:
              <el-input type='number' v-model='option.autoCropWidth' style="width:80px;height:30px;margin:0 5px"></el-input>*
              <el-input type='number' v-model='option.autoCropHeight' style="width:80px;height:30px;margin:0 5px"></el-input>
            </div>
            <div style="margin-top:10px">
              <el-button size="mini" type='primary' @click="revert">图片还原</el-button>
            </div>
            <div style="margin-top:10px">
              <el-button size="mini" :disabled="saveStatus !== 2 && saveStatus !== 3" type='primary' @click="previewPhoto(0)">最近保存-预览</el-button>
              <el-button size="mini" type='primary' @click="previewPhoto(1)">当前操作-预览</el-button>
            </div>
            <div style="margin-top:10px">
              <el-button size="mini" type='primary' @click="mark">标记为线下修图</el-button>
              <el-button size="mini" type='primary' @click="save(0)">保存</el-button>
              <!-- <el-button size="mini" type='primary' @click="save(1)">保存（不裁剪）</el-button> -->
            </div>
            <div style="margin-top:10px">
              原图尺寸：{{ imgWidth }} * {{ imgHeight }}
            </div>
            <div style="margin-top:10px">
              状态：{{ saveStatus ? ['', '线下修图', '保存'][saveStatus] : '未保存' }}
            </div>
          </div>
          <div class="title">商品资料：</div>
          <div class="list">
            <div class="label">通用名：</div>
            <div class="content">{{ productInfo.generalName }}</div>
          </div>
          <div class="list">
            <div class="label">商品大类：</div>
            <div class="content">{{ productInfo.spuCategoryName }}</div>
          </div>
          <div class="list">
            <div class="label">生产厂家：</div>
            <div class="content">{{ productInfo.manufacturerName }}</div>
          </div>
          <div class="list">
            <div class="label">规格型号：</div>
            <div class="content">{{ productInfo.spec }}</div>
          </div>
          <div class="list">
            <div class="label">批准文号：</div>
            <div class="content">{{ productInfo.approvalNo }}</div>
          </div>
          <div class="list">
            <div class="label">小包装条码：</div>
            <div class="content">{{ productInfo.smallPackageCode }}</div>
          </div>
        </div>
        <div class="preview-box">
          <span
          style="position:absolute;left:10px;top:400px"
            class="circle circle-lf"
            v-if="currImgIndex != 0"
            @click="handlePicturePreview(currImgIndex - 1)"
          >
            <i class="el-icon-arrow-left"></i>
          </span>
          <div class="img-l" :style="`width:${imgWidth}px;height:${imgWidth}px;transform:scale(${800/imgWidth});position:absolute;top:${(800-imgWidth)/2+10}px;left:${(800-imgWidth)/2}px;`">
            <!-- <div style="border:1px solid black;width:500px;height:500px;position:absolute;top:0;left:0px;z-index:500;pointer-events: none;"></div>
            <div style="border:1px solid blue;width:375px;height:375px;position:absolute;top:62px;left:63px;z-index:500;pointer-events: none;"></div>
            <div style="border:1px solid red;width:414px;height:333px;position:absolute;top:82px;left:43px;z-index:500;pointer-events: none;"></div>
            <div style="border:1px solid yellow;width:333px;height:414px;position:absolute;top:42px;left:84px;z-index:500;pointer-events: none;"></div>
            <div style="border:1px solid black;width:414px;height:414px;position:absolute;top:42px;left:43px;z-index:500;border-radius:50%;pointer-events: none;"></div> -->
            <!-- <img
              ref="img"
              :src="
                imgList[currImgIndex] ? imgList[currImgIndex].pictureUrl+`?x-oss-process=image/resize,p_${parseInt(scale*100)}/rotate,${rotate}/bright,${slider}` : ''
              "
              style="position: relative"
              v-drag
              @mouseup="mouseup"
            /> -->
            <vue-cropper ref="cropper"  :img="imgList[currImgIndex] ? imgList[currImgIndex].pictureUrl : ''"
              output-size="1" output-type="png"
              :info="true" :full="option.full"
              :can-move="option.canMove" :can-move-box="option.canMoveBox" :fixed-box="option.fixedBox"
              :original="option.original"
              :auto-crop="option.autoCrop" :auto-crop-width="option.autoCropWidth"
              :auto-crop-height="option.autoCropHeight" :center-box="option.centerBox"
              @real-time="realTime" :high="option.high" @img-load="imgLoad" @changeCrop="changeCrop"></vue-cropper>
          </div>

          <span
          style="position:absolute;left:760px;top:400px"
            class="circle circle-rt"
            v-if="currImgIndex != imgList.length - 1"
            @click="handlePicturePreview(currImgIndex + 1)"
          >
            <i class="el-icon-arrow-right"></i>
          </span>
          <span
          style="position:absolute;left:340px;top:820px;width:200px"
          >截图尺寸：{{showCropWidth}}*{{showCropHeight}}</span>
        </div>
      </div>
      <div class="text-center">
        {{ currImgIndex + 1 }} / {{ imgList.length }}
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { digPhotoApi } from "@/api/productPicture.js";
import vueCropper  from '@/components/vue-cropper/src/vue-cropper'
export default {
  components: {
    vueCropper
  },
  props: {
    productInfo: {
      type: Object,
      required: false,
      default: () => {},
    },
  },
  watch: {
    rotate(e) {
      if(e === '') {
        this.rotate = 0
      } else if(e<0) {
        this.rotate += 360
      } else if(e>360) {
        this.rotate -= 360
      }
    },
    scale(e) {
      if(e<0) {
        this.scale = 0
      }
    },
    currImgIndex(e) {
      this.option.autoCropWidth = 800
      this.option.autoCropHeight = 800
      if(e !== null) {
        this.imgList[e].dealOperation.originSrc = this.imgList[e].pictureUrl
        let src = this.imgList[e].pictureUrl.includes('?') ? this.imgList[e].pictureUrl : this.imgList[e].pictureUrl +`?x-oss-process=image/resize,p_${parseInt(this.scale*100)}/rotate,${this.rotate}`;
        let img = new Image()
        img.src = src
        img.onload = () => {
          this.imgWidth = img.width
          this.imgHeight = img.height
          console.log(img.width)
        }
      }
      if(e !== null && this.imgList[e].dealOperation.hasOwnProperty('rotate')) {
        this.saveStatus = this.imgList[e].dealOperation.saveStatus
        this.noCutting = 0
        this.rotate = this.imgList[e].dealOperation.rotate
        this.slider = this.imgList[e].dealOperation.slider
        this.scale = this.imgList[e].dealOperation.scale
        this.position_x = this.imgList[e].dealOperation.position_x
        this.position_y = this.imgList[e].dealOperation.position_y
        this.top = this.imgList[e].dealOperation.position_y + 'px'
        this.left = this.imgList[e].dealOperation.position_x + 'px'
        // this.$refs.img.style.top = this.imgList[e].dealOperation.position_y + 'px'
        // this.$refs.img.style.left = this.imgList[e].dealOperation.position_x + 'px'
      }
    }
  },
  computed: {},
  created() {},
  mounted() {},
  data() {
    return {
      model: false,
      modelSrc: '',
      imgWidth:0,
      imgHeight:0,
      showCropWidth:800,
      showCropHeight:800,

      option: {
          size: 1,//输出图片压缩比默认为1
          outputSize:1,
          outputType: 'png',//输出图片格式
          info:true,// 裁剪框的大小信息
          canScale:true,//图片是否允许滚轮缩放
          autoCrop:true,//是否默认生成截图框
          // 只有自动截图开启 宽度高度才生效
          autoCropWidth: 800,//截图框宽
          autoCropHeight: 800,//截图框高
          fixed:true,//是否开启截图框宽高固定比例
          //fixedNumber:[1,1],//        截图框的宽高比例
          full: false,//输出截图是否缩放
          canMove: true,//是否可以拖动图片
          fixedBox: false,//是否固定截图大小
          original: false,//上传图片按原始比例
          canMoveBox: true,//是否可以拖动截图框
          centerBox: false,//截图框能否超过图片
          high: false,//是否根据dpr输出高清图片
          infoTrue:false,
          enlarge:1,
          fixedNumber: [1, 1]

      },
      saveStatus: null, // 1:标记为线下修图 2:保存 3:保存（不裁剪）
      noCutting: 0,
      record: [], //操作记录
      frontRecord:[], //前进的操作记录
      slider: 0, //亮度滑块
      rotate: 0, //旋转角度
      imgTitle: "",
      scale: 1,
      dlgVisible: false,
      imgList: [],
      currImgIndex: null,
      smallPackageCode: "",
      position_x: 0,
      position_y: 0,
      left:0,
      top:0
    };
  },
  methods: {
    // 监听截图框宽高变化
    changeCrop(e) {
      // key: 0宽 1高
      if(e.key) {
        this.showCropHeight = e.value
      } else {
        this.showCropWidth = e.value
      }
    },
    // 图片还原
    revert(){
      this.imgList[this.currImgIndex].pictureUrl = this.imgList[this.currImgIndex].dealOperation.originSrc
    },
    // 预览图片 0历史 1当前
    previewPhoto(e) {
      console.log('previewPhoto'+e)
      this.$refs.cropper.getCropData((data) => {
        var img = data
        this.model = true
        this.modelSrc = e ? img : this.imgList[this.currImgIndex].dealOperation.dealSrc
      })
    },
    // 实时预览函数
    realTime(data) {
        // this.previews = data
        // console.log(data)
    },
    imgLoad(msg) {
      console.info("加载失败："+msg)
    //     if(msg!=''&&msg!='success'){
    //           console.info("重新加载")
    //       let _this = this;
    //     let image = new Image();
    //     image.crossOrigin = "*";
    //     // 处理缓存
    //     image.src = "http://upload.ybm100.com/G4/M00/0C/FF/CiIDh2CrXAaAIabQAAOHLIlp9cY15.jpeg";
    //     // 支持跨域图片
    //     console.info(image)
    //     image.onload = function () {
    // console.info( _this.option)
    //     let base64 =   _this.transBase64FromImage(image);
    //     console.info(base64)
    //     _this.option.img = base64
    //     }
    //     }
    },
    // 拖拽图片结束获取坐标并缓存操作
    // mouseup() {
    //   this.position_y = parseInt(this.$refs.img.style.top)
    //   this.position_x = parseInt(this.$refs.img.style.left)
    //   this.top = this.$refs.img.style.top
    //   this.left = this.$refs.img.style.left
    //   this.addRecord()
    // },
    // 方向键移动
    // move(direction,e) {
    //   if(direction === 'top') {
    //     if(!this.$refs.img.style.top) {
    //       this.$refs.img.style.top = '0px'
    //     } else if(e) {
    //       console.log(this.$refs.img.style.top)
    //       this.$refs.img.style.top = (parseInt(this.$refs.img.style.top)+10)+'px'
    //       this.top = (parseInt(this.$refs.img.style.top)+10)+'px'
    //       console.log(this.$refs.img.style.top)
    //     } else {
    //       this.$refs.img.style.top = (parseInt(this.$refs.img.style.top)-10)+'px'
    //       this.top = (parseInt(this.$refs.img.style.top)-10)+'px'
    //       console.log(this.$refs.img.style.top)
    //     }
    //     this.position_y = parseInt(this.$refs.img.style.top)
    //   } else {
    //     if(!this.$refs.img.style.left) {
    //       this.$refs.img.style.left = '0px'
    //     } else if(e) {
    //       this.$refs.img.style.left = (parseInt(this.$refs.img.style.left)+10)+'px'
    //       this.left = (parseInt(this.$refs.img.style.left)+10)+'px'
    //     } else {
    //       this.$refs.img.style.left = (parseInt(this.$refs.img.style.left)-10)+'px'
    //       this.left = (parseInt(this.$refs.img.style.left)-10)+'px'
    //     }
    //     this.position_x = parseInt(this.$refs.img.style.left)
    //   }
    //   this.addRecord()
    // },
    // 添加记录
    addRecord() {
      this.frontRecord = []
      if(this.record.lengt>10) {
        this.record.shift()
      }
      this.record.push({
        rotate: this.rotate,
        slider: this.slider,
        scale: this.scale,
        position_x: this.position_x,
        position_y: this.position_y,
        left: this.left,
        top: this.top
      })
    },
    // 后退
    preview() {
      console.log('back')
      if(this.record.length === 0) {
        return
      } else if(this.record.length === 1) {
        this.rotate = 0
        this.slider = 0
        this.scale = 1
        this.position_x = 0
        this.position_y = 0
        // this.$refs.img.style.top = 0
        // this.$refs.img.style.left = 0
      } else {
        this.frontRecord.push(this.record[this.record.length-1])
        this.rotate = this.record[this.record.length-2].rotate
        this.slider = this.record[this.record.length-2].slider,
        this.scale = this.record[this.record.length-2].scale,
        this.position_x = this.record[this.record.length-2].position_x,
        this.position_y = this.record[this.record.length-2].position_y
        // this.$refs.img.style.top = this.record[this.record.length-2].top
        // this.$refs.img.style.left = this.record[this.record.length-2].left
        this.record.pop()
      }
    },
    // 前进
    next() {
      if(this.frontRecord.length === 0) {
        return
      }
      this.record.push(this.frontRecord[this.frontRecord.length-1])
      this.rotate = this.record[this.record.length-1].rotate
      this.slider = this.record[this.record.length-1].slider,
      this.scale = this.record[this.record.length-1].scale,
      this.position_x = this.record[this.record.length-1].position_x,
      this.position_y = this.record[this.record.length-1].position_y
      // this.$refs.img.style.top = this.record[this.record.length-1].top
      // this.$refs.img.style.left = this.record[this.record.length-1].left
      this.frontRecord.pop()
    },
    save(e, saveStatus) {
      this.noCutting = e
      console.log(this.imgList)
      let src = this.imgList[e].pictureUrl.includes('?') ? this.imgList[e].pictureUrl : this.imgList[e].pictureUrl +`?x-oss-process=image/resize,p_${parseInt(this.scale*100)}/rotate,${this.rotate}`;
      let img = new Image()
      img.src = src
      img.onload = () => {
        console.log(img.width)
      let direction = undefined // diretion: 0左上 1右上 2右下 3左下
      this.record = []
      this.frontRecord = []
      if(saveStatus) {
        this.saveStatus = saveStatus
      } else {
        this.imgList[this.currImgIndex].offlineRetouchStatus = 0
        this.imgList[this.currImgIndex].onlineRetouchStatus = 1
        this.saveStatus = e ? 3 : 2
      }
      this.$refs.cropper.getCropData((data) => {
        this.imgList[this.currImgIndex].dealOperation = {
          dealSrc: data,
          saveStatus: this.saveStatus,
          rotate: this.rotate,
          slider: this.slider,
          scale: this.scale,
          position_x: this.position_x,
          position_y: this.position_y,
          imgWidth: img.width,
          imgHeight: img.height,
          noCutting: e ? 1 : 0,
          direction
        }
        this.$emit('updateImgList',this.imgList)
      })
      }
    },
    // 一键抠图
    digPhoto() {
      let tempsrc = ''
      this.$refs.cropper.getCropData((data) => {
        tempsrc = data
        let param = {
        pictureId: this.imgList[this.currImgIndex].pictureId,
        baseImg:tempsrc
        // pictureUrl: this.imgList[this.currImgIndex].pictureUrl+`?x-oss-process=image/resize,p_${parseInt(this.scale*100)}/rotate,${this.rotate}/bright,${this.slider}`
        }
      digPhotoApi(param).then(res => {
        console.log(res)
        if(res.retCode === 0) {
          this.imgList[this.currImgIndex].pictureUrl = res.data
        } else {
          this.$message.error(res.retMsg || '接口调用失败')
        }
        this.scale = 1
        this.rotate = 0
        this.slider = 0
      })
      })
    },
    // 标记为线下修图
    mark() {
      this.imgList[this.currImgIndex].offlineRetouchStatus = 1
      this.saveStatus = 1
      this.imgList[this.currImgIndex].dealOperation = {
        saveStatus: this.saveStatus
      }
      this.save(this.noCutting, this.saveStatus)
    },
    // 清空操作
    clearOperation() {
      this.saveStatus = null
      this.position_x = 0
      this.position_y = 0
      this.left = 0
      this.top = 0
      this.record = []
      this.frontRecord =[]
      this.slider = 0
      this.rotate = 0
      this.scale = 1
      // this.$refs.img.style.top = 0
      // this.$refs.img.style.left = 0
    },
    // 打开弹框
    openDlg(list, index, str) {
      this.imgList = list;
      this.currImgIndex = index;
      this.smallPackageCode = str;
      this.scale = 1;
      this.rotate = 0;
      this.dlgVisible = true;
      this.changeTitle(this.currImgIndex);
      this.$nextTick(() => {
        // let el = this.$refs.img.style;
        // el.setProperty("transform", `scale(${this.scale.toFixed(2)})`);
      });
    },
    closeDlg() {
      this.clearOperation()
      this.currImgIndex = null
      this.record = []
      this.frontRecord = []
      this.dlgVisible = false;
    },
    // 预览图片
    handlePicturePreview(index) {
      // this.save(this.noCutting, this.saveStatus)
      this.noCutting = 0
      if (index >= 0 && index < this.imgList.length) {
        setTimeout(() => {
          this.clearOperation()
          this.currImgIndex = index;
        }, 100);
      }
      this.changeTitle(index);
    },
    // 修改标题
    changeTitle(e) {
      if (this.smallPackageCode) {
        this.imgTitle = `${this.smallPackageCode}-${
          this.imgList[e].pictureName
        }`;
      } else {
        this.imgTitle = `${this.imgList[e].pictureName}`;
      }
    },
    handleDelete(isFlag) {
      if (isFlag) {
        if(this.record.length !== 0) {
          this.save(1)
        }
        this.imgList[this.currImgIndex].deleteStatus = 1;
      } else {
        this.imgList[this.currImgIndex].deleteStatus = 0;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.el-upload-list {
  display: none;
}
.el-dialog__wrapper /deep/ .el-dialog .el-dialog__body {
  padding: 0 0 20px 0;
}
.el-dialog__wrapper /deep/ .el-dialog {
  // max-height: 100%;
}
.text-center {
  text-align: center;
  margin-top: 10px;
}
.text-lf {
  text-align: left;
}
.text-rt {
  text-align: right;
}
.img-slot:hover {
  cursor: pointer;
}
.fot-btn {
  margin: 0 20px;
}
.img-box {
  position: relative;
  display: inline-block;
  border-radius: 5px;
  width: 80px;
  height: 80px;
}
.img-s {
  border-radius: 5px;
  width: 80px;
  height: 80px;
}
.img-status {
  position: absolute;
  width: 100px;
  height: 100px;
  top: 10px;
  right: 10px;
  z-index: 200;
  img {
    width: 100%;
  }
}
.img-l {
  width: 800px;
  overflow: hidden;
  position: relative;
  // width: 100%;
  height: 800px;
  // overflow: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  img {
    user-select: none;
  }
}

.img-actions {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  border-radius: 5px;
  cursor: pointer;
  text-align: center;
  opacity: 0;
  font-size: 24px;
  color: #fff;
  background-color: rgb(0, 0, 0, 0.5);
  line-height: 80px;
  transition: opacity 0.3s;
}
.img-actions:hover {
  opacity: 0.5;
}

.circle {
  position: absolute;
  z-index: 2000;
  top: calc(50% - 15px);
  width: 30px;
  height: 30px;
  border-radius: 50%;
  opacity: 0.5;
  background-color: #ccc;
  text-align: center;
  font-size: 22px;
  line-height: 30px;
  transition: opacity 0.3s;
}
.circle-lf {
  left: 20px;
}
.circle-rt {
  right: 20px;
}
.circle:hover {
  opacity: 1;
}
.top {
  min-width: 1500px;
  position: relative;
  display: flex;
  .btnBox {
    width: 400px;
    position: absolute;
    right: 20px;
    top: 30px;
    z-index: 999;
  }
  .productInfo {
    width: 200px;
    padding: 20px;
    line-height: 32px;
    .title {
      font-size: 18px;
      font-weight: bold;
      color: #333;
    }
    .list {
      .label {
        font-weight: bold;
        color: #333;
      }
      .content {
        text-indent: 10px;
      }
    }
  }
  .preview-box {
    margin-left:70px;
    position: relative;
    // flex: 0.6;
    padding-top: 10px;
  }
}
.dlg-title{
  display: flex;
  justify-content: space-between;
  .cc{
    padding-right: 50px;
  }
}
// /deep/ .el-dialog__wrapper .el-dialog {
//   height: 700px!important;
// }
.model {
    position: fixed;
    z-index: 2100;
    width: 100vw;
    height: 100vh;
    overflow: auto;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.8);
}
.model-show {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100vw;
    height: 100vh;
}
.model img {
    display: block;
    margin: auto;
    max-width: 80%;
    user-select: none;
    background-position: 0px 0px, 10px 10px;
    background-size: 20px 20px;
/**-moz-animation: ;  background-image: linear-gradient(45deg, #eee 25%, transparent 25%, transparent 75%, #eee 75%, #eee 100%), linear-gradient(45deg, #eee 25%, white 25%, white 75%, #eee 75%, #eee 100%);*/
    background: #ffffff;
    z-index: 21;
}
.empty {
  width: 800px;
  height: 800px;
  background: #ffffff;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  z-index: 20;
}
</style>
