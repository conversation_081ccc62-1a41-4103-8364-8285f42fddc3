<template>
<!-- 
props
    scope={
        applyCode:'',//string 必填 单据编号
        disabled:false,//bool 必填 上传按钮是否可用
        type:'',//string 必填  附件类型（1:上传原图包，4:上传精修图包）
        buttonType:'',//string 必填 按钮类型(link:文字链接，button:按钮) 
    }

slot
    default  按钮和弹窗title的显示文本

method
    success({scope,url})
        scope 原样返回
        url 上传api返回的url
 -->
  <div class='zip-file-upload-container'>
    <template v-if="scope.buttonType==='link'">
        <el-link :underline="false" :type="scope.disabled?'info':'primary'" :disabled="scope.disabled" @click="btnShowFileUpload">
            <slot name="default">上传</slot>
        </el-link>
    </template>
    <template v-else-if="scope.buttonType==='button'">
        <el-button size="medium" :type="scope.disabled?'info':'primary'" :disabled="scope.disabled" @click="btnShowFileUpload">
            <slot name="default">上传</slot>
        </el-button>
    </template>
    

    <!-- dialog -->
    <el-dialog 
        :visible.sync="dialogVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :modal-append-to-body="true"
        :append-to-body="true"
        custom-class="zip-file-upload-dialog"
    >
        <span slot="title" class="dialog-title">
          <slot name="default">上传</slot>
        </span>
        <el-row>
            <el-col>
                <div class="file-name-shower">{{selectedFileName}}</div>
            </el-col>
        </el-row>
        <span slot="footer" class="dialog-footer">
            <el-upload
                class="upload-wrapper"
                ref="refUpload"
                :action="action"
                :data="{applyCode:scope.applyCode,annexType:scope.type}"
                :multiple="false"
                accept=".zip,.ZIP"
                :show-file-list="false"
                :auto-upload="false"
                :on-change="uploadOnChange"
                :on-success="uploadOnSuccess"
                :on-error="uploadOnError"
                :disabled="uploadDisabled"
            >
                <el-button size="medium" slot="trigger" :disabled="uploadDisabled">
                    <span>选择文件</span>
                    <el-popover
                        placement="top-start"
                        title="提示"
                        width="300"
                        trigger="hover"
                    >
                        <span class="popover-content">
                            1、图片放在文件夹中，将文件夹压缩成ZIP格式后上传。<br>
                            2、文件夹内图片支持jpg、png、jpeg、bmp格式，不支持pdf或其他非图片的文件格式。<br>
                            3、图片名称格式：商品编码+“-”+数字。<br>
                            4、上传后图片为新增一个版本，版本号为12位上传日期（eg：202001010403）。
                        </span>
                        <i class="el-icon-question el-icon--right icon-question" slot="reference"></i>
                    </el-popover>
                </el-button>
                <el-button size="medium" type="primary" :disabled="uploadBtnDisabled" :loading="uploadBtnLoading" @click="uploadSubmit">{{ uploadBtnLoading?"上传中":"开始上传" }}</el-button>
            </el-upload>
        </span>
    </el-dialog>

  </div>
</template>

<script>
export default {
  name:"",
  components: {},
  filters:{},
  props:{
      scope:{
          type:Object
      }
  },
  data() {
    return {
        dialogVisible: false,
        selectedFileName:'',//选择的文件名

        //upload action url
        action:process.env.VUE_APP_BASE_API+'/api/picture/add/uploadPicturePackage',

        uploadDisabled:false,//上传组件禁用
        uploadBtnDisabled:true,//上传按钮禁用
        uploadBtnLoading:false,//上传按钮loading
    }
  },
  computed:{},
  watch:{},
  created(){},
  mounted(){},
  methods:{
    /**
     * 上传按钮，触发dialog弹窗
     */
    btnShowFileUpload(){
        this.dialogVisible=true;
    },

    /**
     * 选择上传文件
     */
    uploadOnChange(file, fileList){
        //只在添加文件时触发
        if(file.status==="ready"){
            this.selectedFileName = file.name;
            this.uploadBtnDisabled = false;
            if(fileList.length > 1){
                fileList.splice(0, fileList.length-1);
            }
        }
    },

    /**
     * 上传到服务器
     */
    uploadSubmit() {
        if(this.$refs.refUpload.uploadFiles.length){
            //before-upload返回false时，会清空uploadFiles
            //if(this.$refs.refUpload.uploadFiles[0].raw.type !== 'application/zip'){
            let fileName=this.$refs.refUpload.uploadFiles[0].name;
            if(!['zip','ZIP'].includes(fileName.substr(fileName.lastIndexOf('.')+1))){
                this.$message({
                    showClose: true,
                    type: 'error',
                    message: '请上传ZIP格式的文件'
                });
                return false;
            }
            this.uploadDisabled=true;
            this.uploadBtnDisabled=true;
            this.uploadBtnLoading=true;
            this.$refs.refUpload.submit();
        }
    },
    
    /**
     * 上传成功回调
     */
    uploadOnSuccess(response, file, fileList){
        if(response.retCode===0){
            /**
            this.$message({
                message: '上传成功',
                type: 'success'
            });
            */
            this.$emit('success',{scope:this.scope,url:response.data});
            this.dialogVisible=false;
        }else{
            this.$message({
                message: response.retMsg,
                type: 'error'
            });
        }
        this.selectedFileName="";
        this.uploadDisabled=false;
        this.uploadBtnDisabled=true;
        this.uploadBtnLoading=false;
    },

    /**
     * 上传失败回调
     */
    uploadOnError(err, file, fileList){
        this.$message({
            message: '上传失败,请重试',
            type: 'error'
        });
        this.selectedFileName="";
        this.uploadDisabled=false;
        this.uploadBtnDisabled=true;
        this.uploadBtnLoading=false;
    }
  }
}
</script>

<style lang="scss" scoped>
.zip-file-upload-container{
    display: inline-block;

    .el-link{
        user-select: none;
    }
}

.el-dialog__wrapper{
    /deep/ {
        .el-dialog.zip-file-upload-dialog  {
            width: 20%;
            min-width: 400px;

            .el-dialog__header{
                text-align: left;
            }

            .el-dialog__body {
                //min-height: 200px;
                max-height: 500px;
                overflow-y: auto;
            }

            .file-name-shower{
                width: 100%;
                height: 40px;
                line-height: 40px;
                border: 1px solid #DCDFE6;
                border-radius: 4px;
                padding: 0 15px;
                text-align: left;
            }

            .upload-wrapper{

                .el-button{
                    margin-left: 10px;
                }

                /deep/{
                    .el-upload{
                        .el-button{
                            &>span{
                                font-size: 0!important;
                            }

                            & span{
                                font-size: 14px;
                            }
                            .icon-question{
                                margin-left: 2px;
                                font-size: 16px;
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>