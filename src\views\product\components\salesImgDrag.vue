<template>
  <div class="preview-original">
    <!-- 预览原图对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="show"
      width="800px"
      height="80vh"
      class="dlg1"
      @close="close"
    >
      <div class="preview-box">
        <span
          v-if="skuIndex != 0"
          class="circle circle-lf"
          @click="handlePicturePreview(skuIndex - 1)"
        >
          <i class="el-icon-arrow-left"></i>
        </span>
        <draggable
          class="drag-wrap"
          v-model="draggableData"
          style="background: #ffffff"
        >
          <el-row :gutter="10"
            ><el-col
              :span="row.title == '器械许可证图片列表' ? 24 : 8"
              v-for="(row, parIndex) in draggableData"
              :key="parIndex"
            >
              <el-card>
                <div class="title">{{ row.title }}</div>
                <draggable
                  class="img-wrap"
                  :list="row.imgList"
                  :group="{ name: 'row' }"
                >
                  <el-col
                    :span="row.title == '器械许可证图片列表' ? 8 : 24"
                    v-for="(item, index) in row.imgList"
                    :key="index"
                  >
                    <el-card>
                      <img
                        @click="previewImg(index, parIndex)"
                        :class="{
                          abnormal: item.styleState == 1,
                          normal: item.styleState == 2,
                        }"
                        :src="item.pictureUrl"
                        alt=""
                      />
                      <span
                        >{{ item.pictureName }}</span
                      >
                    </el-card>
                  </el-col>
                </draggable>
              </el-card>
            </el-col>
          </el-row>
        </draggable>
        <span
          v-if="skuIndex != allData.length - 1"
          class="circle circle-rt"
          @click="handlePicturePreview(skuIndex + 1)"
        >
          <i class="el-icon-arrow-right"></i>
        </span>
      </div>
      <div class="btn-wrap">
        <el-button-group style="">
          <el-button type="primary" @click="save">保存</el-button>
        </el-button-group>
      </div>
    </el-dialog>
    <preview-img
      ref="previewImg"
      :imgList="preImgList"
      :currImgIndex="preIndex"
      :smallPackageCode="!!allData[skuIndex] && allData[skuIndex].smallPackageCode"
    ></preview-img>
  </div>
</template>

<script>
import {
  sortPicture, // 1.5.1 审核精修图
} from "@/api/productPicture.js";
import draggable from "vuedraggable";
import previewImg from "@/components/uploadImg/previewImg";
import skuList from "@/components/common/skuList";
export default {
  name: "",
  components: {
    draggable,
    previewImg,
    skuList,
  },
  filters: {},
  props: ["imgTitle"],
  watch: {},
  data() {
    return {
      draggableData: [],
      title: "",
      preImgList: [],
      preIndex: 0,
      allData: [],
      skuIndex: 0,
      show: false,
      reason: "",
    };
  },
  computed: {},
  watch: {},
  created() {
  
  },
  mounted() {},
  methods: {
    previewImg(index, parIndex) {
      let flag = true;
      this.preIndex = 0;
      this.preImgList = [];
      this.draggableData.forEach((list, dParIndex) => {
        list.imgList.forEach((item, dIndex) => {
          if (dParIndex == parIndex && dIndex == index) {
            flag = false;
          }
          if (flag) {
            this.preIndex++;
          }
          this.preImgList.push(item);
        });
      });
      console.log(3333);
      this.$refs.previewImg.openDlg(this.preImgList, this.preIndex);
    },
    // 关闭的回调
    closeDlg() {
      this.show = false;
    },
    // 打开的回调
    openDlg(allData, skuIndex) {
      this.allData = allData;
      this.skuIndex = skuIndex;
      this.getDragDate();
      this.getDiaTitle();
      this.show = true;
    },
    // 切换sku
    handlePicturePreview(index) {
      if (index >= 0 && index < this.allData.length) {
        this.skuIndex = index;
      }
      this.getDragDate();
      this.getDiaTitle();
    },
    getDiaTitle() {
      const data = this.allData[this.skuIndex],
        arr = this.imgTitle.split("-");
      this.title = `${data.businessCode}_${this.skuIndex + 1}/${
        this.allData.length
      }_${data.pictureVersion}_${arr[3]}`;
    },
    getDragDate() {
      if (this.allData[this.skuIndex].pictureVersion == 999999999) {
        this.draggableData = [{ imgList: [], title: "器械许可证图片列表" }];
      } else {
        this.draggableData = [
          { imgList: [], title: "主图" },
          { imgList: [], title: "外包装" },
          { imgList: [], title: "说明书" },
        ];
      }

      this.allData[this.skuIndex].detailPictureList.forEach((item) => {
        if (this.allData[this.skuIndex].pictureVersion == 999999999) {
          this.draggableData[0].imgList.push(item);
        } else {
          if (item.pictureOrdinal == 1) {
            this.draggableData[0].imgList.push(item);
          } else if (item.pictureOrdinal >= 2 && item.pictureOrdinal <= 5) {
            this.draggableData[1].imgList.push(item);
          } else {
            this.draggableData[2].imgList.push(item);
          }
        }
      });
    },

    // 审核通过
    save() {
      let arr = [];
      let flag = true;
      this.draggableData.forEach((list, parentIndex) => {
        if (this.allData[this.skuIndex].pictureVersion == 999999999) {
          if (parentIndex == 0 && list.imgList.length > 10) {
            this.$message.error("器械许可证图片数量最多只能10张");
            flag = false;
            return;
          }
        } else {
          if (parentIndex == 0 && list.imgList.length != 1) {
            this.$message.error("主图数量必须为1张");
            flag = false;
            return;
          }
          if (parentIndex == 1 && list.imgList.length > 4) {
            this.$message.error("外包装最多只能4张");
            flag = false;
            return;
          }
          if (parentIndex == 2 && list.imgList.length > 5) {
            this.$message.error("说明书最多只能5张");
            flag = false;
            return;
          }
        }
        if (flag) {
          list.imgList.forEach((item, index) => {
            let pictureOrdinal = 0;
            if (this.allData[this.skuIndex].pictureVersion == 999999999) {
              if (parentIndex == 0) {
                pictureOrdinal = index + 11;
              }
            } else {
              if (parentIndex == 0) {
                pictureOrdinal = 1;
              } else if (parentIndex == 1) {
                pictureOrdinal = index + 2;
              } else {
                pictureOrdinal = index + 6;
              }
            }
            let obj = {
              pictureId: item.id,
              pictureOrdinal: pictureOrdinal,
              pictureName: item.pictureName,
            };
            arr.push(obj);
          });
        }
      });
      if (flag) {
        this.sortPicture(arr);
      }
    },
    sortPicture(pictures) {
      sortPicture({
        productCode: this.allData[this.skuIndex].businessCode,
        pictures,
      }).then((res) => {
        if (res.retCode == 0) {
          this.$message.success("操作成功！");
          this.$emit("refresh");
          this.closeDlg();
        } else {
          this.$message.error("操作失败！");
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.drag-wrap /deep/ {
  padding: 0 10px;
  width: 100%;
  .el-card__body {
    padding: 10px;
  }
  .title {
    text-align: center;
    font-size: 20px;
    font-weight: bold;
    padding-bottom: 10px;
  }
}
.img-wrap /deep/ {
  .el-card__body {
    display: flex;
    align-items: center;
    justify-content: center;
    // padding-left: 50px;
    // flex-direction: de;
    position: relative;
    .watermark {
      position: absolute;
      top: -5px;
      right: 0;
      width: 50px;
      height: 50px;
    }
    .delete-btn {
      position: absolute;
      top: 0;
      right: 0;
      font-size: 12px;
      color: #fff;
      padding: 5px;
      background: #f56c6c;
      border-top-right-radius: 5px;
      &.cancel {
        background: #909399;
      }
    }
    span {
      flex: 1;
      text-align: center;
      position: relative;
      z-index: 3;
      padding-left: 5px;
    }
  }
  img {
    height: 80px;
    width: 80px;
    border: 5px solid #fff;
    &.abnormal {
      border-color: #f56c6c;
    }
    &.normal {
      border-color: #67c23a;
    }
  }
}
.preview-box {
  display: flex;
  align-items: center;
}
.circle {
  z-index: 2000;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  opacity: 0.5;
  background-color: #ccc;
  text-align: center;
  font-size: 22px;
  line-height: 30px;
  transition: opacity 0.3s;
}
.circle-lf {
  left: 20px;
}
.circle-rt {
  right: 20px;
}
.circle:hover {
  opacity: 1;
}
.btn-wrap {
  display: flex;
  justify-content: flex-end;
  padding: 20px 10px 10px 0;
}
</style>