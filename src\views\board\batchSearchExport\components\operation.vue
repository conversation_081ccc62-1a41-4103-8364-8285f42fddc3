<template>
  <div class="task-to-be-film-container" v-loading="loading">
    <div class="loading" v-loading="uploadLoading" v-show="uploadLoading"></div>
    <!-- 查询表单 -->
    <div class="search-form-wrap">
      <el-form :model="model" ref="refSearchForm">
          <el-row style="margin-bottom:20px">
              <!-- 按钮 -->
          <el-col>
            <span class="upload">导入文件</span><div class="file-name-shower"><p style="width:260px">{{selectedFileName}}</p><i @click="removeFile" v-show='uploadDisabled' class="el-icon-circle-close"></i></div>
            <el-upload
                accept='.xlsx'
                :data="param"
                ref="refUpload"
                class="upload-demo"
                :action="action"
                :on-change="uploadOnChange"
                :show-file-list="false"
                :auto-upload="false"
                :multiple="false"
                :limit="1"
                :disabled="uploadDisabled"
                :on-success="uploadOnSuccess"
                :on-error="uploadOnError">
            <el-button style="margin:0 10px" type="primary" :disabled="uploadDisabled">点击上传</el-button>
            </el-upload>
            <el-button size="medium" @click="downloadModel">下载模板</el-button>
          </el-col>
          </el-row>
        <el-row type="flex">
          <el-col :span="24" :offset="0">
            <el-form-item label="商品属性">
              <el-checkbox-group v-model="productAttrList"  :disabled="imgAttrList.length">
                <el-checkbox label="spuCategory:商品大类">商品大类</el-checkbox>
                <el-checkbox label="spuCode:spu编码">SPU编码</el-checkbox>
                <el-checkbox label="approvalNo:批准文号">批准文号</el-checkbox>
                <el-checkbox label="businessScopeMulti:所属经营范围">所属经营范围</el-checkbox>
                <el-checkbox label="generalName:通用名">通用名</el-checkbox>
                <el-checkbox label="dosageForm:剂型">剂型</el-checkbox>
                <el-checkbox label="shadingAttr:存储属性">存储属性</el-checkbox>
                <el-checkbox label="categoryName:六级分类">六级分类</el-checkbox>
                <el-checkbox label="categoryCode:分类编码">分类编码</el-checkbox>
                <el-checkbox label="manufacturer:生产厂家">生产厂家</el-checkbox>
                <el-checkbox label="manufacturerType:厂家分类">厂家分类</el-checkbox>
                <el-checkbox label="inRate:进项税率">进项税率</el-checkbox>
                <el-checkbox label="outRate:销项税率">销项税率</el-checkbox>
                <el-checkbox label="instructionSpec:批件规格">批件规格</el-checkbox>
                <el-checkbox label="mediaUr:批件图片">批件图片</el-checkbox>
                <el-checkbox label="chronicDiseasesVariety:慢病品种">慢病品种</el-checkbox>
                <el-checkbox label="skuCode:sku编码">SKU编码</el-checkbox>
                <el-checkbox label="skuName:商品名">商品名</el-checkbox>
                <el-checkbox label="spec:规格型号">规格型号</el-checkbox>
                <el-checkbox label="packageUnit:包装单位">包装单位</el-checkbox>
                <el-checkbox label="brand:品牌商标">品牌商标</el-checkbox>
                <el-checkbox label="prescriptionCategory:处方分类">处方分类</el-checkbox>
                <el-checkbox label="validity:有效期">有效期</el-checkbox>
                <el-checkbox label="delegationProduct:是否委托生产">是否委托生产</el-checkbox>
                <el-checkbox label="storageCond:存储条件">存储条件</el-checkbox>
                <el-checkbox label="brandCategory:品牌分类">品牌分类</el-checkbox>
                <el-checkbox label="qualityStandard:质量标准">质量标准</el-checkbox>
                <el-checkbox label="storage:贮藏">贮藏</el-checkbox>
                <el-checkbox label="conEvaluateVariety:一致性评价品种">一致性评价品种</el-checkbox>
                <el-checkbox label="hospitalVariety:医院品种">医院品种</el-checkbox>
                <el-checkbox label="smallPackageCode:小包装条码">小包装条码</el-checkbox>
                <el-checkbox label="sauAttrValue:管理属性">管理属性</el-checkbox>
                <el-checkbox label="preOperateStatus:是否自营商品">是否自营商品</el-checkbox>
                <el-checkbox label="noSmallPackageCode:是否无小包装条码">是否无小包装条码</el-checkbox>
                <el-checkbox label="offlineBusinessType:是否线下业务">是否线下业务</el-checkbox>
                <el-checkbox label="skuPrimaryType:主副商品">选择商品类型（主商品or副商品）</el-checkbox>
            </el-checkbox-group>
            </el-form-item>
          </el-col>
          <el-col :span="24" :offset="0">
            <el-form-item label="图片属性">
              <el-checkbox-group v-model="imgAttrList" :disabled="productAttrList.length">
                <el-checkbox label="pictureVersionCount:线上精修版本数量">线上精修版本数量</el-checkbox>
                <el-checkbox label="pictureVersion:版本号">版本号</el-checkbox>
                <el-checkbox label="jgsl:版本号绑定的机构数量">版本号绑定的机构数量</el-checkbox>
                <el-checkbox label="tpsl:版本号包含的图片数量">版本号包含的图片数量</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
          <el-col :span="10" :offset="9">
            <el-form-item>
              <p>注意：</p>
              <p>1、一次最多上传10000条商品信息；</p>
              <p>2、原商品编码、商品编码、标准库ID，在一个表格中选择一列即可；</p>
              <p>3、一个任务只能导出商品属性或图片属性，两种属性无法在同一个任务中导出；</p>
              <el-button type="primary" @click="batchExport" :disabled="!banBtn && (!param.queryType || !fileList.length)">确定进行批量导出</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script>
import { createDownloadElement } from "@/utils/index.js";
export default {
  name: "",
  components: { },
  filters: {},
  props: {},
  data() {
    return {
      banBtn: false,
      param: {
          resultField:[],
          queryType:0
      }, //上传附带的参数
      productAttrList:[], //商品属性
      imgAttrList:[], //图片属性
      uploadDisabled:false,
      selectedFileName:'',
      action:process.env.VUE_APP_BASE_API+'/api/reportCompletion/import/batchQueryInfo',
      loading: false,
      formData: {},
      fileList:[]
    };
  },
  computed: {},
  watch: {
      fileList(val) {
          if(!val.length) {
              this.uploadDisabled = false
          }
      },
      productAttrList(e) {
          if(e.length) {
              this.param.queryType = 1
              this.param.resultField = e
          }
      },
      imgAttrList(e) {
          if(e.length) {
              this.param.queryType = 2
              this.param.resultField = e
          }
      },
  },
  created() {},
  mounted() {},
  methods: {
    // 批量导出
    batchExport() {
        this.$refs.refUpload.submit()
        this.banBtn = true
    },
    // 导出成功回调
    uploadOnSuccess(e) {
        if(e.retCode === 0) {
            this.removeFile()
            this.param = {}
            this.productAttrList = []
            this.imgAttrList = []
            this.$emit('changeTab','first')
        } else {
            this.$message.error(e.retMsg)
        }
        this.banBtn = false
    },
    // 导出失败回调
    uploadOnError() {
        this.banBtn = false
        this.$message.error(e.retMsg)
    },
    // 下载模板
    downloadModel() {
        createDownloadElement("../../../static/assets/excel/批量查询模板.xlsx", "批量查询模板.xlsx")
    },
    // 移除选择的文件
    removeFile() {
        this.fileList = []
        this.selectedFileName = ''
        this.$refs.refUpload.clearFiles();
    },
    /**
     * 选择上传文件
     */
    uploadOnChange(file, fileList){
        //只在添加文件时触发
        if(file.status==="ready"){
            this.uploadDisabled = true
            this.selectedFileName = file.name;
            this.uploadBtnDisabled = false;
            if(fileList.length > 1){
                fileList.splice(0, fileList.length-1);
            }
            this.fileList = fileList
        }
    },
  },
};
</script>
<style lang="scss" scoped>
.task-to-be-film-container {
  .loading {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
  }
  /**
   * 查询表单
   */
  .search-form-wrap {
    width: 100%;
    padding: 15px;
    border-bottom: 1px dashed #e4e4eb;

    .el-row {
      flex-wrap: wrap;

      .el-col {
        display: flex;
        // justify-content: flex-end;

        .el-form-item {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          width: 100%;
          margin-right: 0;
          margin-bottom: 15px;

          /deep/ {
            .el-form-item__label {
              width: 96px;
              line-height: normal;
              padding: 0 12px;
              color: #292933;
              font-weight: normal;
            }

            .el-form-item__content {
              flex: 1;

              .el-range-editor.el-input__inner {
                width: 100%;

                .el-range-separator {
                  width: 14px;
                  padding: 0;
                }
              }
            }
          }

          .el-input {
            width: 100%;
          }

          .el-select {
            width: 100%;
          }
        }
      }
    }
  }
}
.file-name-shower{
    width: 300px;
    height: 40px;
    line-height: 40px;
    border: 1px solid #DCDFE6;
    border-radius: 4px;
    padding: 0 15px;
    text-align: left;
    display: flex;
    justify-content: space-between;
    align-items: center;
    p {
      position: relative;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
}
.upload{
    width: 96px;
    line-height: 40px;
    padding: 0 12px;
    color: #292933;
    font-size:14px;
    font-weight: normal;
}
p{
  margin: 0;
}
</style>