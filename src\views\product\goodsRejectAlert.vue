<template>
  <el-dialog title="纠错驳回" :visible.sync="rejectReasonDlog" width="30%">
    <el-radio-group v-model="radio">
      <el-radio :label="6" class="row">
        <div class="row">
          <div style="margin-left: 5px">提报的信息与其他标品信息相同</div>
          <el-input v-model="storageID" placeholder="请输入标准库ID" style="margin-left: 10px"></el-input>
        </div>
      </el-radio>
      <el-radio :label="7" style="margin-top: 10px"> 信息不全或不正确 </el-radio>
    </el-radio-group>
    <el-input
      style="margin-top: 30px"
      v-model="reason"
      type="textarea"
      :rows="2"
      maxlength="100"
      placeholder="请输入具体驳回原因"
      clearable
      show-word-limit
    ></el-input>
    <span slot="footer">
      <el-button size="medium" @click="rejectReasonDlog = false">取 消</el-button>
      <el-button size="medium" type="primary" @click="sureFunction()">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      radio: 6,
      storageID: "",
      rejectReasonDlog: false,
      reason: "",
    }
  },
  methods: {
    open() {
      this.rejectReasonDlog = true
    },
    sureFunction() {
      if (this.radio === 6 && !this.storageID) {
        this.$message.warning("填写标准库ID")
        return
      }

      if (this.radio === 7 && !this.reason) {
        this.$message.warning("请填写驳回原因")
        return
      }
      let reason = this.radio === 6?'提报的信息与其他标品信息相同':'信息不全或不正确'
      if(this.radio === 7 && this.reason){
        reason = reason + ',' + this.reason;
      }
      
      let data = {
        rejectOpinionNumber:this.radio,
        productId:this.radio === 7?'':this.storageID,
        rejectOpinion:reason
      }

      this.$emit("sure", data)
    },
  },
}
</script>

<style scoped>
.row {
  display: flex;
  flex-direction: row;
  align-items: center;
}
</style>