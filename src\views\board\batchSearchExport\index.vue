<template>
  <div class="container">
    <el-tabs v-model="tabName" type="card">
      <el-tab-pane label="操作记录" name="first">
        <record ref='record'></record>
      </el-tab-pane>
      <el-tab-pane label="操作页面" name="second">
        <operation @changeTab="changeTab"></operation>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import record from "./components/record";
import operation from "./components/operation";

export default {
  name: "",
  components: {
    record,
    operation,
  },
  data() {
    return {
      tabName: "first",
    };
  },
  created() {
    if(this.$route.query.tab) {
      this.tabName = this.$route.query.tab
    }
  },
  computed: {},
  methods: {
    changeTab(name) {
      this.tabName = name;
      if(name == 'first'){
        this.$alert("导出文件为加密文件，密码已发送至您的OA邮箱。请勿将密码和表格中的内容流传给他人，如内容泄露，后果自负！", "确认弹窗", {
          confirmButtonText: "确定",
        })
          .then(() => {})
          .catch(() => {});
        this.$refs.record.searchForm();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding: 15px 15px 0;
}
</style>
