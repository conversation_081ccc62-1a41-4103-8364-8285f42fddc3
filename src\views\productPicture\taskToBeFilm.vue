<template>
  <div class="task-to-be-film-container">
    <!-- 查询表单 -->
    <div class="search-form-wrap">
      <el-form :model="model" ref="refSearchForm">
        <el-row type="flex">
          <!-- 申请时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="申请时间" prop="createTime">
              <el-date-picker
                v-model="model.createTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>

          <!-- 所属机构 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="所属机构" prop="mechanism">
              <el-select v-model="model.mechanism" placeholder="请选择">
                <el-option
                  v-for="item in model.mechanismOptions"
                  :key="'key_mechanismOptions_'+item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 申请人 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="申请人" prop="applyer">
              <el-input v-model="model.applyer" placeholder="模糊查询"></el-input>
            </el-form-item>
          </el-col>

          <!-- 单据编号 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="单据编号" prop="applyCode">
              <el-input v-model="model.applyCode" placeholder="模糊查询"></el-input>
            </el-form-item>
          </el-col>

          <!-- 按钮 -->
          <el-col :span="6" :offset="18">
            <el-button type="primary" size="medium" @click="btnSearchClick">查询</el-button>
            <el-button size="medium" @click="btnResetClick">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- table表单 -->
    <!-- table -->
    <div class="table-wrap">
      <vxe-table
        border
        highlight-hover-row
        resizable
        height="100%"
        auto-resize
        size="small"
        align="center"
        :tooltip-config="{enterable: false}"
        :loading="tableLoading"
        :data="tableData"
        @cell-dblclick="cellDBLClickEvent"
        ref="refVxeTable"
      >
        <vxe-table-column
          type="seq"
          title="序号"
          width="60"
          show-header-overflow
          show-overflow
          fixed="left"
        ></vxe-table-column>
        <vxe-table-column
          field="createTime"
          title="申请时间"
          min-width="120"
          show-header-overflow
          show-overflow
        ><template v-slot="{ row }">
            <span>{{ row.createTime | parseTimestamp }}</span>
          </template></vxe-table-column>
        <vxe-table-column
          field="applyCode"
          title="单据单号"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="productAmount"
          title="任务单商品数量"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="applyUser"
          title="申请人"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          field="mechanismName"
          title="申请人所属机构"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
      </vxe-table>
    </div>

    <!-- 分页 -->
    <el-row class="custom-pagination-wrap">
      <el-col>
        <el-pagination
          background
          :current-page.sync="pageNum"
          :page-sizes="[20, 50, 100, 200]"
          :page-size.sync="pageSize"
          :total="total"
          layout="prev, pager, next, jumper,total, sizes"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {
  getMechanismList,
  getproductPictureShootList,
  fileDownLoad,
} from "@/api/productPicture.js";

export default {
  name: "",
  components: {},
  filters: {},
  props: {},
  data() {
    return {
      pageNum: 1,
      pageSize: 20,
      total: 0, //总条数

      model: {
        //申请时间
        createTime: [],

        //所属机构
        mechanism: "",
        mechanismOptions: [
          {
            value: "",
            label: "全部",
          },
        ],

        //申请人
        applyer: "",

        //单据编号
        applyCode: "",
      },
      tableLoading: false,
      tableData: [],

      //"上传原图"按钮去详情页单击事件的时间戳,防止重复点击
      timeStamp: 0,
    };
  },
  computed: {},
  watch: {},
  created() {
    /**
     * 查询机构
     */
    getMechanismList({
      codeType: "dept",
    })
      .then((resp) => {
        if (resp.retCode === 0) {
          resp.data.forEach((item) => {
            if (item.mechanismId) {
              this.model.mechanismOptions.push({
                value: item.mechanismId,
                label: item.mechanismName,
              });
            }
          });
        }
      })
      .catch((err) => {
        console.log(err);
      });

    this.searchForm();
  },
  mounted() {},
  methods: {
    /**
     * 查询table数据
     */
    searchForm() {
      this.tableLoading = true;
      getproductPictureShootList({
        applyListType: "0",
        createStartTime: this.model.createTime
          ? this.model.createTime.length
            ? this.model.createTime[0]
            : ""
          : "",
        createEndTime: this.model.createTime
          ? this.model.createTime.length
            ? this.model.createTime[1]
            : ""
          : "",
        mechanism: this.model.mechanism,
        applyUser: this.model.applyer,
        applyCode: this.model.applyCode,
        page: this.pageNum,
        limit: this.pageSize,
      })
        .then((resp) => {
          if (resp.retCode === 0) {
            this.total = resp.data.total;
            this.tableData = resp.data.list;
          } else {
            this.$message({
              showClose: true,
              type: "error",
              message: resp.retMsg,
            });
          }
        })
        .catch((err) => {
          this.$message({
            showClose: true,
            type: "error",
            message: "请求发生错误，请重试",
          });
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },

    /**
     * 查询按钮点击
     */
    btnSearchClick() {
      this.pageNum = 1;
      this.searchForm();
    },

    /**
     * 重置
     */
    btnResetClick() {
      this.$refs["refSearchForm"].resetFields();
      this.model.createTime = null;
      this.pageNum = 1;
      this.searchForm();
    },

    /**
     * pageSize 改变事件
     */
    handleSizeChange(pageSize) {
      this.pageNum = 1;
      this.pageSize = pageSize;
      this.searchForm();
    },

    /**
     * pageNum 改变事件
     */
    handleCurrentChange(currentPage) {
      this.pageNum = currentPage;
      this.searchForm();
    },

    /**
     * table 行双击
     */
    cellDBLClickEvent({ row, rowIndex, column, columnIndex }, event) {
      //操作列双击不跳转，避免事件冒泡
      if (columnIndex !== 8 && !row.linkDisabled) {
        this.gotoDetailPage(row.applyCode, undefined);
      }
    },

    /**
     * 去详情页
     */
    gotoDetailPage(applyCode, e) {
      if (e) {
        //"上传原图"按钮单击
        if (!this.timeStamp) {
          this.timeStamp = e.timeStamp;
          this.go(applyCode);
        } else {
          if (e.timeStamp - this.timeStamp < 666) {
            //双击
            return false;
          } else {
            this.timeStamp = e.timeStamp;
            this.go(applyCode);
          }
        }
      } else {
        //双击行
        this.go(applyCode);
      }
    },

    /**
     * 调详情页iframe
     */
    go(applyCode) {
      this.timeStamp = 0;
      window.parent.CreateTab(
        "../static/dist/index.html#/productPicture/taskToBeFilmDetail?applyCode=" +
          applyCode,
        "待拍摄任务明细页",
        true
      );
    },
    /**
     * 下载失败原因excel
     */
    downloadExcel(rowData) {
      console.log(rowData);
      //下载原因
      fileDownLoad(rowData.analysisFailUrl, rowData.annexName)
        .then((resp) => {
          if (resp.data && resp.data.retCode) {
            //失败
            this.$message({
              showClose: true,
              type: "error",
              message: resp.data.retMsg,
            });
          } else {
            let blob = new Blob([resp]);
            let blobUrl = window.URL.createObjectURL(blob);
            let a = document.createElement("a");
            a.style.display = "none";
            a.download = rowData.annexName;
            a.href = blobUrl;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
          }
        })
        .catch((err) => {
          this.$message({
            showClose: true,
            type: "error",
            message: "下载失败，请重试",
          });
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.task-to-be-film-container {
  /**
   * 查询表单
   */
  .search-form-wrap {
    width: 100%;
    padding: 15px;
    border-bottom: 1px dashed #e4e4eb;

    .el-row {
      flex-wrap: wrap;

      .el-col {
        display: flex;
        justify-content: flex-end;

        .el-form-item {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          width: 100%;
          margin-right: 0;
          margin-bottom: 15px;

          /deep/ {
            .el-form-item__label {
              width: 96px;
              line-height: normal;
              padding: 0 12px;
              color: #292933;
              font-weight: normal;
            }

            .el-form-item__content {
              flex: 1;

              .el-range-editor.el-input__inner {
                width: 100%;

                .el-range-separator {
                  width: 14px;
                  padding: 0;
                }
              }
            }
          }

          .el-input {
            width: 100%;
          }

          .el-select {
            width: 100%;
          }
        }
      }
    }
  }

  /**
     * table
     */
  .table-wrap {
    width: 100%;
    min-height: 440px;
    height: calc(100vh - 240px);
    padding: 0 15px;
    margin-top: 15px;
  }
}
</style>
