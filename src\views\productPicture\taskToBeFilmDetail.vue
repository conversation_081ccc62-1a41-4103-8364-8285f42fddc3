<template>
  <div class="detail-container">
    <div class="text-rt">
      <el-button type="primary" size="medium" @click="submit()" :disabled="subBtnState">提交</el-button>
    </div>
    <el-form :model="form" ref="form" label-width="130px">
      <el-row class="border-bottom-dashed">
        <el-col :span="24">
          <h4 class="detail-title">申请信息</h4>
        </el-col>

        <!-- 申请时间 -->
        <el-col :span="6">
          <el-form-item label="申请时间" prop="createTime">
            <el-input v-model="form.createTime" :disabled="true"></el-input>
          </el-form-item>
        </el-col>

        <!-- 单据编号 -->
        <el-col :span="6">
          <el-form-item label="单据编号" prop="billNo">
            <el-input v-model="form.billNo" :disabled="true"></el-input>
          </el-form-item>
        </el-col>

        <!-- 申请人 -->
        <el-col :span="6">
          <el-form-item label="申请人" prop="applicant">
            <el-input v-model="form.applicant" :disabled="true"></el-input>
          </el-form-item>
        </el-col>

        <!-- 申请人所属机构 -->
        <el-col :span="6">
          <el-form-item label="申请人所属机构" prop="applicantOrganization">
            <el-input v-model="form.applicantOrganization" :disabled="true"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <h4 class="detail-title">商品明细</h4>
        </el-col>

        <el-col :span="24" class="detail-query-condition">
          <!-- 查询条件 -->
          <span class="child">
            <el-checkbox v-model="query.isZero">只看图片版本数量不为0的商品</el-checkbox>
          </span>

          <!-- 查询条件 -->
          <span class="child">
            <el-input v-model="query.name" placeholder="商品名称/小包装条码/批准文号" style="width:100%"></el-input>
          </span>

          <!-- 查询条件 -->
          <span class="child">
            <el-button type="primary" size="medium" @click="queryList" style="margin-right:15px">查询</el-button>

            <zip-file-upload
              :scope="{
                applyCode:$route.query.applyCode,
                disabled:false,
                type:'1',
                buttonType:'button'
              }"
              @success="uploadOriginalImg"
            >上传原图</zip-file-upload>
            <span class="package-name">{{ originalPackageName }}</span>
          </span>
        </el-col>
      </el-row>

      <div class="table-wrap">
        <vxe-table
          border
          highlight-hover-row
          resizable
          min-height="500px"
          align="center"
          :tooltip-config="{enterable: false}"
          :data="tableData"
          ref="refVxeTable"
        >
          <vxe-table-column type="index" title="序号" width="60" show-header-overflow show-overflow></vxe-table-column>

          <vxe-table-column
            field="historyVersionNum"
            title="图片版本数量"
            min-width="130"
            show-header-overflow
            show-overflow
            sortable
          ></vxe-table-column>

          <vxe-table-column
            type="seq"
            field="historyVersionDetailPicture"
            title="已有图片预览"
            min-width="100"
            show-header-overflow
            show-overflow
          >
            <template v-slot="{ row, seq }">
              <preview-img
                :operation="0"
                :row-data="{ row, seq }"
                :params="params"
                field="historyVersionDetailPicture"
                @update-list="getList"
              />
            </template>
          </vxe-table-column>

          <vxe-table-column
            field="productName"
            title="商品信息"
            min-width="300"
            show-header-overflow
            show-overflow
          >
            <template v-slot="{row}">
              <div>
                <div>{{ row.productName}}</div>
                <div>{{ row.spec}}</div>
                <div>{{ row.manufacturer}}</div>
              </div>
            </template>
          </vxe-table-column>

          <vxe-table-column
            field="brand"
            title="商品信息"
            min-width="200"
            show-header-overflow
            show-overflow
          >
            <template v-slot="{row}">
              <div>
                <div>{{ row.smallPackageCode}}</div>
                <div>{{ row.approvalNo}}</div>
                <div>{{ row.brand}}</div>
              </div>
            </template>
          </vxe-table-column>

          <vxe-table-column
            field="productCode"
            title="商品编码"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>

          <vxe-table-column
            field="productId"
            title="商品id"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>

          <vxe-table-column
            field="originalProductCode"
            title="原商品编码"
            min-width="120"
            show-header-overflow
            show-overflow
          ></vxe-table-column>

          <vxe-table-column
            field="reasonStatus"
            title="拍摄原因"
            min-width="150"
            show-header-overflow
            show-overflow
          >
            <!-- 拍摄原因(1:新品上架, 2:原图全驳, 3:原图半驳, 4:精修图全驳, 5:精修图半驳) -->
            <template
              v-slot="{ row }"
            >{{ ['','新品上架','原图全驳', '原图半驳','精修图全驳', '精修图半驳'][row.reasonStatus] }}</template>
          </vxe-table-column>

          <vxe-table-column
            field="pictureDetailVersion"
            title="绑定图片版本"
            min-width="100"
            show-header-overflow
            show-overflow
          ></vxe-table-column>

          <vxe-table-column
            field="originalPictureNum"
            title="原图上传数量"
            min-width="130"
            show-header-overflow
            show-overflow
            sortable
          >
            <template v-slot="{ row, seq }">
              <preview-img
                type="slot"
                :row-data="{ row, seq }"
                :operation="1"
                :params="params"
                field="currentVersionOriginalPicture"
                @update-list="getList"
              >
                <el-button
                  type="text"
                  :disabled="!row.originalPictureNum"
                >{{ row.originalPictureNum }}</el-button>
              </preview-img>
            </template>
          </vxe-table-column>
        </vxe-table>
      </div>
    </el-form>
  </div>
</template>

<script>
import {
  productShootTaskListFind,
  getProductDetail,
  submitDetailProduct,
} from "../../api/productPicture";

import previewImg from "@/components/uploadImg/preview";
import ZipFileUpload from "@/components/productPicture/zipFileUpload";
import { parseTimestamp } from "@/utils";

export default {
  name: "",
  components: { previewImg, ZipFileUpload },
  filters: {},
  props: {},
  created() {
    // 初始 数据
    this.queryList();
    // 查询该单据是否有上传原图包
    this.queryPackage();
  },
  data() {
    return {
      params: {},
      form: {
        createTime: "",
        billNo: "",
        applicant: "",
        applicantOrganization: "",
      },
      query: {
        isZero: "",
        name: "",
      },
      tableData: [],
      subBtnState: false, // 提交按钮状态
      originalPackageName: "未上传", // 上传原图包名
    };
  },
  computed: {
    applyCode() {
      return this.$route.query.applyCode;
    },
  },
  watch: {},
  mounted() {},
  methods: {
    // 获取待拍摄商品 - 数据、赋值
    getList(params) {
      console.log(params, "待拍摄商品明细--查询参数");
      getProductDetail(params).then((result) => {
        console.log(result, "待拍摄商品明细--返回数据");
        this.tableData = result;
        // console.log(this.tableData[0], "第一条数据----");
        let {
          createUser,
          applyCode,
          createUserMechanism,
          createTime,
        } = this.tableData[0];

        this.form = {
          createTime: parseTimestamp(createTime),
          billNo: applyCode,
          applicant: createUser,
          applicantOrganization: createUserMechanism,
        };
      });
    },
    // 条件 查询
    queryList() {
      let params = {
        applyCode: this.applyCode, // 单据编号
        productStatus: 0, //商品状态码(0:图片拍摄中1:原图已上传 2:图片精修中 3:精修图已上传 4:精修图审核中 5:审核完毕)
        haveVersion: this.query.isZero, // // true ? 1 : 0 图片版本数量是否为零的商品
        multipleInfo: this.query.name, // 混合查询字段
      };
      this.params = params;
      this.getList(params);
    },
    // 查询是否有上传原图包
    queryPackage() {
      productShootTaskListFind({
        applyCode: this.applyCode,
        applyListType: 4,
      }).then((res) => {
        let list = res.data.list;
        console.log(list, "--原图包信息--");
        if (list.length) {
          let {
            annexName, // 附件名称
            annexStatus, // 附件状态
            annexType, // 附件类型
            annexUrl, // 附件地址
          } = list[0];
          this.originalPackageName = annexName ? annexName : "未上传";
        }
      });
    },
    // 上传原图
    uploadOriginalImg({ scope, url }) {
      console.log({ scope, url }, "---上传原图成功后的回调---");
      if (url) {
        let { annexName } = url;
        this.originalPackageName = annexName;
        this.$message.success("上传成功！");
      }
    },
    // 提交
    submit() {
      let obj = {
        operation: 0, //（-1新建任务单,0上传原图,1下载原图,2上传精修图,3领取,4提交审核）
        applyCode: this.applyCode,
      };
      // 先校验是否有上传原图（zip包）
      if (this.originalPackageName !== "未上传") {
        // 有 不校验列表中 图片版本字段 原图上传数量字段
        this.postSubmitData(obj);
      } else {
        // 至少 维护其中一个 版本 和 上传数量
        let version = this.tableData.filter((item) => {
          return !item.pictureDetailVersion;
        });
        let num = this.tableData.filter((item) => {
          return !item.originalPictureNum;
        });
        // console.log(version);
        // console.log(num);
        if (version.length && num.length) {
          this.$message.error("有商品未绑定版本或上传原图");
        } else {
          this.postSubmitData(obj);
        }
      }
    },
    postSubmitData(obj) {
      this.subBtnState = true;
      submitDetailProduct(obj)
        .then((res) => {
          console.log(res, "提交成功----");
          if (res.retCode == 0) {
            this.$message.success({
              message: "提交成功！",
              type: "success",
              duration: "1000",
              onClose: function () {
                window.parent.CreateTab(
                  `../static/dist/index.html#/productPicture/taskToBeFilm`,
                  "待拍摄任务列表",
                  true
                );
                window.parent.CloseTab(
                  "../static/dist/index.html#/productPicture/taskToBeFilmDetail"
                );
              },
            });
          } else {
            this.$message.error(res.retMsg);
            this.subBtnState = false;
          }
        })
        .catch((err) => {
          this.$message.error(err);
          this.subBtnState = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.detail-container {
  padding: 20px;

  .el-form-item {
    /deep/ {
      .el-form-item__label {
        color: #292933;
        font-weight: normal;
      }
    }
  }

  .border-bottom-dashed {
    border-bottom: 1px dashed #e4e4eb;
  }

  .detail-title {
    margin: 20px;
    font-size: 16px;
    color: #292933;
  }
  .detail-query-condition {
    display: flex;
    line-height: 40px;
    margin-bottom: 15px;
    .child {
      margin-right: 10px;
    }
    .child:nth-child(2) {
      width: 400px;
    }
  }
  .package-name {
    margin-left: 20px;
    font-size: 14px;
    color: #c0c4cc;
  }
}
</style>
