<template>
  <div class="task-to-be-film-container" v-loading="loading">
    <div class="loading" v-loading="uploadLoading" v-show="uploadLoading"></div>
    <!-- 查询表单 -->
    <div class="search-form-wrap">
      <el-form :model="model" ref="refSearchForm">
        <el-row type="flex">
          <!-- 来源 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="来源">
              <el-select
                v-model="formData.source"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" value=""></el-option>
                <el-option label="百草" :value="1"></el-option>
                <el-option label="系统上传" :value="2"></el-option>
                <el-option label="荷叶健康" :value="3"></el-option>
                <el-option label="智慧脸商城" :value="4"></el-option>
                <el-option label="POP" :value="5"></el-option>
                <el-option label="EC" :value="6"></el-option>
                <el-option label="客服" :value="7"></el-option>
                <el-option label="供多多" :value="8"></el-option>
                <el-option label="智鹿" :value="9"></el-option>
                <el-option label="神农" :value="10"></el-option>
                <el-option label="运营创建-改精修图" :value="11"></el-option>
                <el-option label="系统创建-改精修图" :value="12"></el-option>
                <el-option label="爬虫" :value="20"></el-option>
                <el-option label="新品上报" :value="21"></el-option>
                <el-option label="友商库-自动创建" :value="22"></el-option>
                <el-option label="友商库-运营创建" :value="23"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 原图申请人 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="原图申请人" prop="applyUser">
              <el-input
                v-model="formData.applyUser"
                placeholder="请输入申请人"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 初审审核人 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6">
            <el-form-item label="初审审核人" prop="reviewUser">
              <el-input
                v-model="formData.reviewUser"
                placeholder="请输入审核人"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 申请时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="tabName === 'allTask' || tabName === 'editDetail'">
            <el-form-item label="申请时间" prop="createTime">
              <el-date-picker
                v-model="formData.createTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                clearable
              ></el-date-picker>
            </el-form-item>
          </el-col>

          <!-- 审核状态 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="tabName === 'allTask'">
            <el-form-item label="审核状态">
              <el-select
                v-model="formData.auditStatus"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" value=""></el-option>
                <el-option label="通过" :value="1"></el-option>
                <el-option label="驳回" :value="2"></el-option>
                <el-option label="未审核" :value="0"></el-option>
                <el-option label="线下修图" :value="3"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 商品编码 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="tabName !== 'editDetail'">
            <el-form-item label="商品编码" prop="mixtureProduct">
              <el-input
                v-model="formData.mixtureProduct"
                placeholder="输入商品ID，原商品编码，商品编码搜索"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 商品名称 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="tabName === 'allTask'">
            <el-form-item label="商品名称" prop="productName">
              <el-input
                v-model="formData.productName"
                placeholder="模糊查询"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 是否自营 -->
           <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="tabName === 'allTask'">
            <el-form-item label="是否自营">
              <el-select
                v-model="formData.businessType"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" value=""></el-option>
                <el-option label="自营" :value="1"></el-option>
                <el-option label="非自营" :value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 批准文号 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="tabName === 'allTask'">
            <el-form-item label="批准文号" prop="approvalNo">
              <el-input
                v-model="formData.approvalNo"
                placeholder="模糊查询"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 是否高优 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="isUnfold && tabName === 'allTask'">
            <el-form-item label="是否高优">
              <el-select
                v-model="formData.whetherHighQualityGoods"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" :value='null'></el-option>
                <el-option label="高优" :value="1"></el-option>
                <el-option label="非高优" :value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 图片数量 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="isUnfold && tabName === 'allTask'">
            <el-form-item label="图片数量" prop="designReceivePictureQuantity">
              <el-input
                type="number"
                v-model="formData.designReceivePictureQuantity"
                placeholder="请输入图片数量"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 任务ID -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="tabName === 'shootTask'">
            <el-form-item label="任务ID">
              <el-input
                v-model="formData2.applyCode"
                placeholder="请输入任务ID"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 操作状态 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="tabName === 'shootTask'">
            <el-form-item label="操作状态">
               <el-select
                v-model="formData2.retouchOperateStatus"
                placeholder="请选择操作状态"
                clearable
              >
                <el-option label="全部" value=""></el-option>
                <el-option label="未下载" :value="0"></el-option>
                <el-option label="已下载" :value="1"></el-option>
                <el-option label="已上传" :value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 线上精修时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="tabName === 'editDetail'">
            <el-form-item label="线上精修时间" prop="onlineRetouchTime">
              <el-date-picker
                v-model="formData3.onlineRetouchTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                clearable
              ></el-date-picker>
            </el-form-item>
          </el-col>

          <!-- 线上精修人 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="tabName === 'editDetail'">
            <el-form-item label="线上精修人" prop="uploadTime">
              <el-input
                v-model="formData3.onlineRetouchUser"
                placeholder="请输入线上精修人"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 商品ID -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="tabName === 'editDetail'">
            <el-form-item label="商品ID" prop="uploadTime">
              <el-input
                v-model="formData3.productId"
                placeholder="请输入商品ID"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 图片序号 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="tabName === 'shootTask' || tabName === 'editDetail'">
            <el-form-item label="图片序号">
              <el-input
                v-model="formData2.pictureOrdinal"
                placeholder="请输入图片序号"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 是否高优 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="tabName === 'shootTask'">
            <el-form-item label="是否高优">
              <el-select
                v-model="formData.whetherHighQualityGoods"
                placeholder="请选择"
                clearable
              >
                <el-option label="全部" :value='null'></el-option>
                <el-option label="高优" :value="1"></el-option>
                <el-option label="非高优" :value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 精修图上传人 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="tabName === 'shootTask'">
            <el-form-item label="精修图上传人">
              <el-input
                v-model="formData2.pictureUploader"
                placeholder="请输入精修图上传人"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>

          <!-- 精修图上传时间 -->
          <el-col :lg="8" :xs="24" :sm="12" :xl="6" v-if="isUnfold && tabName === 'shootTask'">
            <el-form-item label="精修图上传时间" prop="uploadTime">
              <el-date-picker
                v-model="formData2.uploadTime"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                clearable
              ></el-date-picker>
            </el-form-item>
          </el-col>

          <!-- 按钮 -->
          <el-col :span="6" :offset="18">
            <el-button
              v-show="tabName !== 'editDetail'"
              type="primary"
              size="medium"
              @click="
                () => {
                  isUnfold = !isUnfold;
                }
              "
              >{{ isUnfold ? "收起" : "展开" }}</el-button
            >
            <el-button type="primary" size="medium" @click="btnSearchClick"
              >查询</el-button
            >
            <el-button size="medium" @click="btnResetClick">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- table上方按钮组 -->
    <div class="btn-group-wrap">
      <div class="tab-wrap">
        <el-tabs v-model="tabName" type="card">
          <el-tab-pane label="线上处理" name="allTask"> </el-tab-pane>
          <el-tab-pane label="下载精修图" name="shootTask"> </el-tab-pane>
          <el-tab-pane label="线上修图明细" name="editDetail"> </el-tab-pane>
          <!-- <el-tab-pane label="待精修任务" name="truingTask"> </el-tab-pane> -->
        </el-tabs>
      </div>
 
      <div class="btn-wrap">
        <el-upload
          slot="append"
          class="upload-wrapper"
          ref="refUpload"
          :action="no"
          :data="disposeForm"
          :multiple="false"
          accept=".zip,.ZIP"
          :show-file-list="false"
          :auto-upload="false"
          :on-change="importPhoto"
        ><el-button
          type="primary"
          size="medium"
          :disabled="btnDis"
          >批量导入精修图
           <el-popover
              placement="top-start"
              title="提示"
              width="300"
              trigger="hover"
            >
              <span class="popover-content">
                1、图片放在文件夹中，将文件夹压缩成ZIP格式后上传。<br />
                2、文件夹内图片支持jpg、png、jpeg、bmp格式，不支持pdf或其他非图片的文件格式。<br />
                3、图片名称格式：商品编码+“-”+数字。
              </span>
              <i
                class="el-icon-question el-icon--right icon-question"
                slot="reference"
              ></i>
            </el-popover>
          </el-button>
        </el-upload>
        <el-button
          v-if="tabName == 'shootTask'"
          type="primary"
          size="medium"
          :disabled="btnDis"
          @click="downloadPhoto"
          >批量下载图片</el-button
        >
        <vxe-column-filter tableRefName="refVxeTable"></vxe-column-filter>
      </div>
      <div style="width:500px;margin-left: 15px;"><el-progress  class="upload_progress" v-if="showUploadProgress" :percentage="uploadProgress"></el-progress></div>
        
    </div>
    <!-- table -->
    <div class="table-wrap">
      <vxe-table
        border
        highlight-hover-row
        resizable
        height="100%"
        auto-resize
        size="small"
        align="center"
        :tooltip-config="{ enterable: false }"
        :loading="tableLoading"
        :seq-config="{ startIndex: (pageNum - 1) * pageSize }"
        ref="refVxeTable"
        :data="tableData"
        @checkbox-all="selectAllEvent"
        @checkbox-change="selectChangeEvent"
      >
        <vxe-table-column
          type="checkbox"
          width="60"
          show-header-overflow
          show-overflow
          fixed="left"
        ></vxe-table-column>
        <vxe-table-column
          type="seq"
          title="序号"
          width="60"
          show-header-overflow
          show-overflow
          fixed="left"
        ></vxe-table-column>
        <vxe-table-column
          v-if="tabName === 'shootTask' || tabName === 'editDetail'"
          :key='1'
          field="applyCode"
          title="任务ID"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          :key='2'
          field="source"
          title="来源"
          min-width="120"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">
            <span>{{ row.source | filterSource }}</span>
          </template>
        </vxe-table-column>
        <vxe-table-column
          :key='3'
          field="applyUser"
          title="原图申请人"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          :key='5'
          v-if="tabName === 'allTask'"
          field="createTime"
          title="申请时间"
          min-width="150"
          show-header-overflow
          show-overflow
          ><template v-slot="{ row }">
            <span>{{ row.createTime  | parseTimestamp }}</span>
          </template>
          </vxe-table-column>
          <vxe-table-column
          :key='18'
          v-if="tabName === 'shootTask' || tabName === 'editDetail'"
          field="applyDate"
          title="原图申请时间"
          min-width="150"
          show-header-overflow
          show-overflow
          ><template v-slot="{ row }">
            <span>{{ row.applyDate  | parseTimestamp }}</span>
          </template>
          </vxe-table-column>
        <vxe-table-column
          :key='13'
          field="reviewUser"
          title="初审审核人"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          :key='19'
          v-if="tabName === 'editDetail'"
          field="onlineRetouchUser"
          title="线上精修处理人"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          :key='20'
          v-if="tabName === 'editDetail'"
          title="线上精修时间"
          min-width="120"
          show-header-overflow
          show-overflow
        >
        <template v-slot="{ row }">
            <span>{{ row.onlineRetouchTime  | parseTimestamp }}</span>
          </template>
        </vxe-table-column>
        <vxe-table-column
          :key='4'
          v-if="tabName === 'allTask'"
          field="designReceivePictureQuantity"
          title="图片数量"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          :key='6'
          field="whetherHighQualityGoodsName"
          title="是否高优商品"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          :key='7'
          v-if="tabName === 'allTask'"
          field="auditStatus"
          title="当前审核状态"
          min-width="120"
          show-header-overflow
          show-overflow
        >
          <template v-slot="{ row }">
            <span>{{ row.auditStatus | filterAuditStatus }}</span>
          </template>
        </vxe-table-column>
         <vxe-table-column
          :key='8'
          field="productId"
          title="商品ID"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          :key='9'
          v-if="tabName === 'shootTask' || tabName === 'editDetail'"
          field="pictureOrdinal"
          title="图片序号"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          :key='21'
          v-if="tabName === 'shootTask'"
          field="pictureDownloader"
          title="原图下载人"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          :key='14'
          v-if="tabName === 'shootTask'"
          field="pictureDownloadTime"
          title="原图下载时间"
          min-width="120"
          show-header-overflow
          show-overflow
        ><template v-slot="{ row }">
            <span>{{ row.pictureDownloadTime | parseTimestamp }}</span>
          </template>
        </vxe-table-column>
        <vxe-table-column
          :key='15'
          v-if="tabName === 'shootTask'"
          field="pictureUploader"
          title="精修上传人"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          :key='16'
          v-if="tabName === 'shootTask'"
          field="pictureUploadTime"
          title="精修图上传时间"
          min-width="120"
          show-header-overflow
          show-overflow
        ><template v-slot="{ row }">
            <span>{{ row.pictureUploadTime | parseTimestamp }}</span>
          </template>
        </vxe-table-column>
         <vxe-table-column
          :key='17'
          v-if="tabName === 'shootTask'"
          field="retouchOperateStatus"
          title="操作状态"
          min-width="120"
          show-header-overflow
          show-overflow
        ><template v-slot="{ row }">
            <span>{{ row.retouchOperateStatus | filterStatus }}</span>
          </template></vxe-table-column>
        <vxe-table-column
          :key='11'
          v-if="tabName === 'allTask'"
          field="productName"
          title="商品名称"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          :key='12'
          v-if="tabName === 'allTask'"
          field="approvalNo"
          title="批准文号"
          min-width="120"
          show-header-overflow
          show-overflow
        ></vxe-table-column>
        <vxe-table-column
          title="操作"
          width="120"
          show-header-overflow
          show-overflow
          fixed="right"
        >
          <template v-slot="{ row }">
            <span>
              <el-link
                :underline="false"
                :disabled="row.auditStatus != 0"
                type="primary"
                @click.stop="disposeImg(row)"
                >{{ row.auditStatus != 0 ? "已处理" : "处理" }}</el-link
              >
            </span>
          </template>
        </vxe-table-column>
      </vxe-table>
    </div>

    <!-- 分页 -->
    <el-row class="custom-pagination-wrap">
      <el-col>
        <el-pagination
          background
          :current-page.sync="pageNum"
          :page-sizes="[20, 50, 100, 200]"
          :page-size.sync="pageSize"
          :total="total"
          layout="prev, pager, next, jumper,total, sizes"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </el-col>
    </el-row>
  </div>
</template>

<script>
var COS = require("cos-js-sdk-v5")

import {
  getOnlineEditList,
  pageRetouchTaskReceived,
  pageRetouchTaskOfflinePicture,
  uploadRetouchTaskOfflineRetouchPicture,
  downloadRetouchTaskOfflinePicture
} from "@/api/productPicture.js";
import { getTempKey } from "@/api/follow.js"

const JSZip = require("jszip");

export default {
  name: "",
  components: {},
  filters: {
    filterApplyReason: function (value) {
      switch (value * 1) {
        case 5:
          return "图片补全";
        default:
          return "无";
      }
    },
    filterContainPictureStatus: function (value) {
      switch (value * 1) {
        case 0:
          return "否";
        case 1:
          return "是";
        default:
          return "否";
      }
    },
    filterAuditStatus: function (value) {
      switch (value * 1) {
        case 0:
          return "未审核";
        case 1:
          return "通过";
        case 2:
          return "驳回";
        case 3:
        return "线下修图";
        default:
          return "-";
      }
    },
  },
  props: {
    businessType:{
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      disposeForm: {
        usePictureStatus: "",
        pictureQualityStatus: 1,
        fileName: "",
        type: [],
        productRecognizeMethod: 1,
      },
      productPictureBase64List: [],
      page: 1,
      loading: false,
      pageNum: 1,
      pageSize: 20,
      total: 0, //总条数
      formData: {
        source: "",
        createTime: "",
        productId: "", //商品ID
        mixtureProduct: "", //商品编码
        productName: "", //通用名
        approvalNo: "",
        auditStatus: 0, //审核状态
        applyUser: "",
        reviewUser: "", // 初审审核人
        designReceivePictureQuantity: null, //图片数量
      },
      formData2: {
        applyCode: '', //任务ID
        retouchOperateStatus: '', //操作状态
        pictureOrdinal: '', //图片序号
        pictureUploader:"", // 精修图上传人
        uploadTime: "", //精修图上传时间
      },
      formData3: {
        onlineRetouchTime:"",//线上精修时间
        onlineRetouchUser:"", //线上精修人
        productId:"", //商品ID
      },
      tableLoading: false,
      tableData: [],
      checkedList: [],
      isUnfold: false,
      btnDis: false,
      tabName: "allTask",
      columns: [],
      cos: null,
      tempKey: {},
      showUploadProgress: false,
      uploadProgress: 0,
    };
  },
  computed: {},
  watch: {
    tabName(val) {
      this.page = 1;
      this.searchForm();
      this.checkedList = [];
      this.refreshColumn();
    },
  },
  created() {
    this.searchForm();
    this.$nextTick(() => {
      this.columns = this.$refs.refVxeTable.getColumns();
      this.refreshColumn();
      this.getTempKeyApi()
    });
  },
  mounted() {},
  methods: {
    async getTempKeyApi() {
      try {
        const { data } = await getTempKey()
        this.tempKey = data
        window.tempKey = data

        this.cos = new COS({
          // getAuthorization 必选参数
          getAuthorization: function (options, callback) {
            callback({
              TmpSecretId: window.tempKey.tmpSecretId,
              TmpSecretKey: window.tempKey.tmpSecretKey,
              SecurityToken: window.tempKey.sessionToken,
              // 建议返回服务器时间作为签名的开始时间，避免用户浏览器本地时间偏差过大导致签名错误
              StartTime: window.tempKey.startTime, // 时间戳，单位秒，如：1580000000
              ExpiredTime: window.tempKey.expiredTime, // 时间戳，单位秒，如：1580000000
            })
          },
        })
      } catch (error) {
        console.log(error)
      }
    },

    /**
     * 查询table数据
     */
    async searchForm() {
      try {
        this.tableLoading = true;
        if (this.tabName === "editDetail") {
          let temp = Object.assign({}, this.formData)
          let param = {
            page: this.pageNum,
            limit: this.pageSize,
            source: temp.source ? temp.source : null,
            applyUser: temp.applyUser ? temp.applyUser : null,
            reviewUser: temp.reviewUser ? temp.reviewUser : null,
            createTimeStart: temp.createTime ? temp.createTime[0] : null,
            createTimeEnd: temp.createTime ? temp.createTime[1] : null,
            onlineRetouchTimeStart: this.formData3.onlineRetouchTime ? this.formData3.onlineRetouchTime[0] : null,
            onlineRetouchTimeEnd: this.formData3.onlineRetouchTime ? this.formData3.onlineRetouchTime[1] : null,
            onlineRetouchUser: this.formData3.onlineRetouchUser || null,
            productId: this.formData3.productId || null,
            pictureOrdinal: this.formData2.pictureOrdinal || null
          }
          const res = await getOnlineEditList(param)
          this.tableLoading = false
          this.tableData = res.data.list
          this.total = res.data.total
        } else {
          let param = Object.assign({}, this.formData);
          param.page = this.pageNum;
          param.limit = this.pageSize;
          param.createTimeStart  = param.createTime ? param.createTime[0] : null;
          param.createTimeEnd = param.createTime ? param.createTime[1] : null;
          param.source = param.source ? param.source : null
          param.approvalNo = param.approvalNo ? param.approvalNo : null
          param.applyUser = param.applyUser ? param.applyUser : null
          param.reviewUser = param.reviewUser ? param.reviewUser : null
          param.designReceivePictureQuantity = param.designReceivePictureQuantity || param.designReceivePictureQuantity === 0 ? +param.designReceivePictureQuantity : null
          param.businessType = param.businessType ? param.businessType : null
          param.whetherHighQualityGoods = param.whetherHighQualityGoods
          delete param.createTime;
          if(this.tabName === 'allTask') {
            let res = await pageRetouchTaskReceived(param);
            this.tableLoading = false;
            if (res.success) {
              this.tableData = res.data.list;
              this.total = res.data.total;
            } else {
              this.$message({
                showClose: true,
                type: "error",
                message: res.retMsg,
              });
            }
          } else if (this.tabName === 'shootTask') {
            let param2 = Object.assign({}, this.formData2)
            param2.productId = this.formData.productId ? this.formData.productId : null
            param2.mixtureProduct = this.formData.mixtureProduct ? this.formData.mixtureProduct : null
            param2.source = this.formData.source ? this.formData.source : null
            param2.applyUser = this.formData.applyUser ? this.formData.applyUser : null
            param2.reviewUser = param.reviewUser ? param.reviewUser : null
            param2.businessType = param.businessType ? param.businessType : null
            param2.whetherHighQualityGoods = param.whetherHighQualityGoods
            param2.pictureUploader = param2.pictureUploader || null
            param2.pictureUploadStartTime = param2.uploadTime ? param2.uploadTime[0] : null
            param2.pictureUploadEndTime = param2.uploadTime ? param2.uploadTime[1] : null
            param2.page = this.pageNum
            param2.limit = this.pageSize
            let res = await pageRetouchTaskOfflinePicture(param2)
            this.tableLoading = false;
            if (res.success) {
              this.tableData = res.data.list;
              this.total = res.data.total;
            } else {
              this.$message({
                showClose: true,
                type: "error",
                message: res.retMsg,
              });
            }
          }
        }
      } catch (error) {
        console.error(error);
      }
    },

    // 表格全选
    selectAllEvent({ checked, records }) {
      this.checkedList = records;
    },

    // 表格单选
    selectChangeEvent({ checked, records }) {
      this.checkedList = records;
    },

    /**
     * 查询按钮点击
     */
    btnSearchClick() {
      this.pageNum = 1;
      this.searchForm();
    },

    /**
     * 重置
     */
    btnResetClick() {
      this.formData = {
        approvalNo: "", //申请原因
        source: "",
        createTime: "",
        productId: "", //商品ID
        mixtureProduct: "", //商品编码
        productName: "", //通用名
        auditStatus: "", //审核状态
        applyUser: "",
        reviewUser: "",
        designReceivePictureQuantity: "", //图片数量
      };
      this.formData2 = {
        applyCode: '', //任务ID
        retouchOperateStatus: '', //操作状态
        pictureOrdinal: '', //图片序号
        pictureUploader: "",
        uploadTime:"",
      }
      this.formData3 = {
        onlineRetouchTime:"",//线上精修时间
        onlineRetouchUser:"", //线上精修人
        productId:"", //商品ID
      },
      this.pageNum = 1;
      this.searchForm();
    },

    /**
     * pageSize 改变事件
     */
    handleSizeChange(pageSize) {
      this.pageNum = 1;
      this.pageSize = pageSize;
      this.searchForm();
    },

    /**
     * pageNum 改变事件
     */
    handleCurrentChange(currentPage) {
      this.pageNum = currentPage;
      this.searchForm();
    },
    // zip上传 批量导入精修图
    importPhoto(file) {
      let that = this;
      this.disposeForm.fileName = file.name;
      this.productPictureBase64List = [];
      let file_count = 0, // 将上传数
        file_reded_count = 0 // 已读取数
      let fileblobList = []
      const jszip = new JSZip();
      jszip.loadAsync(file.raw).then((zip) => {
        this.showUploadProgress = true
        // 读取zip数量
        for (let key in zip.files) {
          if (!zip.files[key].dir) {
            file_count++ // 文件数
          }
        }

        // 读取zip
        for (let key in zip.files) {
          // 判断是否是目录
          if (!zip.files[key].dir) {
            let filename = zip.files[key].name;
            if (/\.(png|jpg|jpeg|bmp)$/.test(filename)) {
              // 判断是否是图片格式
              // 判断图片大小
              // if(zip.files[key]._data.compressedSize/1024/1000 > 2) {
              //   this.$message.error("上传图片大小不能超过 2MB!");
              //   break
              // }
              // let base = zip.file(filename).async("base64"); // 将图片转化为base64格式
              let blobimg = zip.file(filename).async("blob")
              let nameCob = filename.split("/").reverse()

              blobimg.then((res) => {
                file_reded_count++
                fileblobList.push({
                  Bucket: this.tempKey.bucket,
                  Region: this.tempKey.region,
                  Key: `BMP/product/${this.guid()}__${nameCob[0]}`,
                  Body: res,
                })
                      // 文件全部已读则调用补全接口
                if (file_count === file_reded_count) {
                  that.cos.uploadFiles(
                    {
                      files: fileblobList,
                      SliceSize: 1024 * 1024 * 5 /* 触发分块上传的阈值，超过5MB使用分块上传，非必须 */,
                      onProgress: function (info) {
                        var percent = Math.floor(info.percent * 10000) / 100
                        // var speed = Math.floor((info.speed / 1024 / 1024) * 100) / 100
                        // console.log("进度：" + percent + "%; 速度：" + speed + "Mb/s;")
                        that.uploadProgress = percent
                      },
                      onFileFinish: function (err, data, options) {
                        console.log(options.Key + " 上传" + (err ? "失败" : "完成"))
                      },
                    },
                    function (err, data) {
                      console.log("uploadFiles:", err || data)
                      setTimeout(() => {
                        that.showUploadProgress = false
                      }, 1000);
                      if (err) {
                        that.$alert(`上传文件失败，错误 ${err}`, "错误", {
                          confirmButtonText: "确定",
                        })
                          .then(() => {})
                          .catch(() => {})
                      } else if (data && data.files) {
                        data.files.forEach((file) => {
                          let filePath = file.data.Location.split("__")

                          that.productPictureBase64List.push({
                            pictureName: filePath[1],
                            pictureUrl: window.tempKey.host +'/'+ file.options.Key, // 上传腾讯云后的图片url
                          })
                        })

                        // 请求接口
                        uploadRetouchTaskOfflineRetouchPicture(that.productPictureBase64List).then(res => {
                          if(res.retCode === 0) {
                            that.$message.success(res.data);
                          }
                        })
                      }
                    }
                  )
                }
              })
            } else {
              that.showUploadProgress = false
              this.$message.error("上传图片只能是jpg，png，jpeg，bmp格式！");
              // break
            }
          }
        }
      });
    },
    // 批量下载图片
    downloadPhoto() {
      console.log(this.checkedList)
      let temp = []
      this.checkedList.map(item => {
        temp.push(item.id)
      })
      if(temp.length === 0) {
        this.$message.error('勾选商品才能批量下载')
        return
      }
      downloadRetouchTaskOfflinePicture(temp.join()).then(res => {
        if(res) {
          let blob = new Blob([res]);
          let blobUrl = window.URL.createObjectURL(blob);
          let a = document.createElement("a");
          a.style.display = "none";
          a.download ="待精修图片.zip";
          a.href = blobUrl;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
        }
      })
    },
    //审核图片详情
    disposeImg(row) {
      try {
        parent.CreateTab(
          `../static/dist/index.html#/productPicture/dealphoto?id=${row.id}&page=${this.$route.query.page || this.businessType}`,
          "待精修任务列表处理"
        );
      } catch {
        this.$router.push({
          path: "/productPicture/dealphoto",
          query: {
            id: row.id,
            page: this.$route.query.page || this.businessType,
          },
        });
      }
    },
    refreshColumn() {
      if (this.tabName == "allTask") {
        this.columns.forEach((item) => {
          if (item.type == "checkbox") {
            item.visible = false;
          }
          if (item.title == "操作") {
            item.visible = true;
          }
        });
      } else if(this.tabName == "editDetail"){
        this.columns.forEach((item) => {
          if (item.type == "checkbox") {
            item.visible = false;
          }
          if (item.title == "操作") {
            item.visible = false;
          }
        })
      } else {
        this.columns.forEach((item) => {
          if (item.title == "操作") {
            item.visible = false;
          }
          if (item.type == "checkbox") {
            item.visible = true;
          }
        });
      }
      this.$refs.refVxeTable.refreshColumn();
    },
    guid() {
      return "xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx".replace(/[xy]/g, function (c) {
        let r = (Math.random() * 16) | 0,
          v = c == "x" ? r : (r & 0x3) | 0x8
        return v.toString(16)
      })
    },
  },
};
</script>
<style lang="scss" scoped>
.dialog-wrap /deep/ {
  .el-dialog__body {
    height: 500px;
    overflow-y: scroll;
  }
  .el-form-item {
    display: flex;
    height: 40px;
    padding: 0 50px;
    .el-form-item__content {
      width: 100%;
      position: relative;
      .usePictureStatus-tip {
        color: #f56c6c;
        position: absolute;
        top: 25px;
      }
    }
    .btn-wrap {
      width: 100%;
      display: flex;
      justify-content: center;
    }
  }
  .el-form-item__label {
    width: 200px;
  }
}
.task-to-be-film-container {
  .loading {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
  }
  /**
   * 查询表单
   */
  .search-form-wrap {
    width: 100%;
    padding: 15px;
    border-bottom: 1px dashed #e4e4eb;

    .el-row {
      flex-wrap: wrap;

      .el-col {
        display: flex;
        justify-content: flex-end;

        .el-form-item {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          width: 100%;
          margin-right: 0;
          margin-bottom: 15px;

          /deep/ {
            .el-form-item__label {
              width: 96px;
              line-height: normal;
              padding: 0 12px;
              color: #292933;
              font-weight: normal;
            }

            .el-form-item__content {
              flex: 1;

              .el-range-editor.el-input__inner {
                width: 100%;

                .el-range-separator {
                  width: 14px;
                  padding: 0;
                }
              }
            }
          }

          .el-input {
            width: 100%;
          }

          .el-select {
            width: 100%;
          }
        }
      }
    }
  }
  .tab-wrap {
    padding: 0 15px;
  }
  /**
     * table
     */
  .table-wrap {
    width: 100%;
    min-height: 440px;
    height: calc(100vh - 295px);
    padding: 0 15px;
  }
}
.img-default {
  height: 400px;
  width: 70%;
  margin: 0 auto;
  background: #ccc;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  .icon {
    font-size: 64px;
    color: #fff;
    margin-bottom: 10px;
  }
}
.img-box {
  display: flex;
  justify-content: center;
  height: 400px;
  width: 70%;
  margin: 0 auto;
  .img-xt-list {
    width: 130px;
    padding-left: 50px;
    display: flex;
    flex-direction: column;
    .btn {
      height: 20px;
      text-align: center;
      line-height: 20px;
      background: #ccc;
      color: #fff;
      width: 100%;
      cursor: pointer;
      &.el-icon-arrow-up {
        margin-bottom: 5px;
      }
      &.el-icon-arrow-down {
        margin-top: 5px;
      }
    }
    .img-list {
      flex: 1;
      display: flex;
      flex-direction: column;
      .list {
        height: 25%;
        width: 80px;
        padding: 5px 0;
        .img-wrap {
          height: 100%;
          width: 100%;
          border: 6px solid #ccc;
          cursor: pointer;
          &.active {
            border: 6px solid rgba(245, 108, 108, 0.5);
          }
          img {
            display: block;
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }
  .img-dt {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: hidden;
    .title-wrap {
      height: 50px;
      font-size: 22px;
      font-weight: bold;
      padding-bottom: 10px;
      display: flex;
      align-items: center;
      .tip {
        color: #ccc;
        font-size: 16px;
        text-align: right;
        font-weight: normal;
        padding: 0px 10px 0px 5px;
      }
      .delete {
        color: #f56c6c;
        cursor: pointer;
        font-size: 12px;
        .el-icon-delete {
          font-size: 20px;
        }
      }
    }
    .img-wrap {
      flex: 1;
      height: 350px;
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      position: relative;
      img {
        border: 6px solid #ccc;
        height: auto;
        width: auto;
        max-height: 100%;
        max-width: 100%;
      }
    }
  }
}

/**
     * 按钮组
     */
.btn-group-wrap {
  display: flex;
  align-items: center;
  padding: 20px 0;
  .btn-wrap /deep/ {
    display: flex;
    padding-left: 30px;
    .el-button {
      margin: 0 5px;
    }
  }
  .tab-wrap /deep/ {
    .el-tabs__header {
      margin-bottom: 0px;
    }
  }
}

.el-link.el-link--primary.is-disabled {
  color: #bababa;
}
.upload-img-wrap {
  display: flex;
  img {
    height: 50px;
    width: 60px;
    padding-right: 10px;
  }
}
</style>