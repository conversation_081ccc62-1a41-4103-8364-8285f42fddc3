{"_args": [["vue-cropper@0.5.6", "D:\\caoshuwen\\Desktop\\cropper(1)"]], "_from": "vue-cropper@0.5.6", "_id": "vue-cropper@0.5.6", "_inBundle": false, "_integrity": "sha1-o4yY1ALaFCG9XnU14WuXI/EDyv0=", "_location": "/vue-cropper", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "vue-cropper@0.5.6", "name": "vue-cropper", "escapedName": "vue-cropper", "rawSpec": "0.5.6", "saveSpec": null, "fetchSpec": "0.5.6"}, "_requiredBy": ["/"], "_resolved": "https://registry.npm.taobao.org/vue-cropper/download/vue-cropper-0.5.6.tgz", "_spec": "0.5.6", "_where": "D:\\caoshuwen\\Desktop\\cropper(1)", "author": {"name": "goodboy"}, "browserify": {"transform": ["vueify"]}, "bugs": {"url": "https://github.com/xyxiao001/vue-cropper/issues"}, "description": "A simple Vue picture clipping plugin", "devDependencies": {"@babel/core": "^7.1.2", "@babel/plugin-transform-runtime": "^7.1.0", "@babel/preset-env": "^7.1.0", "babel-loader": "^8.0.0-beta.0", "babel-plugin-transform-runtime": "^6.23.0", "babel-polyfill": "^6.26.0", "babel-runtime": "^6.26.0", "css-loader": "^1.0.0", "style-loader": "^0.23.1", "vue": "^2.5.17", "vue-loader": "^15.4.2", "vue-template-compiler": "^2.5.17", "webpack": "^4.20.2", "webpack-cli": "^3.1.2"}, "homepage": "https://github.com/xyxiao001/vue-cropper#readme", "keywords": ["vue", "cropper", "vue-cropper", "vue-component", "vue-cropper-component"], "license": "ISC", "main": "./dist/index.js", "name": "vue-cropper", "repository": {"type": "git", "url": "git+https://github.com/xyxiao001/vue-cropper.git"}, "scripts": {"build": "rm -rf ./dist && webpack --config webpack.config.js"}, "version": "0.5.6"}