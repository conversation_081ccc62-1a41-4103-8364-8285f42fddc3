<template>
  <div class="preview-original">
    <!-- 预览原图对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="show"
      width="800px"
      @open="openDlg"
      @close="closeDlg"
      class="dlg1"
    >
      <div class="pic-box">
        <img v-show="rejectStatus" src="../../assets/images/reject.png" class="reject-png" />
        <img v-show="deleteStatus" src="../../assets/images/delete.png" class="delete-png" />
        <h3 class="h3 txt-center">{{ subTitle }}</h3>

        <el-carousel
          trigger="click"
          height="400px"
          :autoplay="false"
          @change="change"
          :prev="prev"
          :next="next"
          :setActiveItem="prev"
          indicator-position="none"
        >
          <el-carousel-item v-for="item in imgList" :key="item">
            <!-- 当前图片 -->
            <div class="img-l">
              <img ref="img" :src="item.pictureUrl" style="max-height:100%;max-width:100%" />
            </div>
          </el-carousel-item>
        </el-carousel>

        <div class="height41">
          <p class="p red txt-center" v-show="rejectArr.length">
            <!-- 正在驳回原图：已驳回sku{{ rejectSkuNum }}个，已驳回pic{{ rejectpictureOrdinal }}张 -->
            正在驳回原图：已驳回sku{{ rejectSkuNum }}个，已删除pic{{ deletePictureOrdinal }}张
          </p>
        </div>
      </div>
      <span slot="footer" class="dlg-footer">
        <el-button-group class="btn-lf">
          <el-button size="medium" icon="el-icon-zoom-in" title="放大" @click="toBig"></el-button>
          <el-button size="medium" icon="el-icon-zoom-out" title="缩小" @click="toSmall"></el-button>
        </el-button-group>

        <!-- 1.5.4 -->

        <template v-if="currImgData.readonly != 1">
          <el-button size="medium" v-show="rejectStatus" @click="cancelReject">取消驳回sku</el-button>
          <el-button
            size="medium"
            v-show="!rejectStatus"
            type="primary"
            @click="rejectReasonDlg = true"
          >设计驳回</el-button>
           <!-- subTitle.includes('999999999') 判断是否为 器械图片预览原图 -->
          <el-button
            size="medium"
            type="primary"
            v-show="!deleteStatus && subTitle.includes('999999999')"
            :disabled="rejectStatus"
            @click="deleteOriginal"
          >删除原图</el-button>
          <el-button size="medium" v-show="deleteStatus && subTitle.includes('999999999')" @click="cancelDelete">取消删除原图</el-button>
        </template>

        <el-button v-show="rejectArr.length" size="medium" type="primary" @click="submit">提交</el-button>
      </span>
    </el-dialog>

    <!-- 选择驳回原因 -->
    <el-dialog
      title="请选择驳回原因"
      :visible.sync="rejectReasonDlg"
      width="400px"
      @close="$refs.ruleForm.resetFields()"
    >
      <el-form :model="ruleForm" ref="ruleForm" style="height:100px">
        <el-form-item
          label="原图驳回原因"
          prop="reason"
          :rules="{ required: true, message: '必选', trigger: 'change' }"
        >
          <el-select v-model="ruleForm.reason" placeholder="请选择">
            <el-option
              v-for="item in reasonDict"
              :key="item"
              :value="item.label"
              :label="item.label"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button size="medium" @click="rejectReasonDlg = false">取 消</el-button>
        <el-button size="medium" type="primary" @click="changeRejectStatus('ruleForm')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  postRejectOriginal, // 1.5.1 驳回原图
} from "@/api/productPicture.js";

export default {
  name: "",
  components: {},
  filters: {},
  props: {
    // 状态
    show: {
      type: Boolean,
      default: false,
    },
    imgData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      scale: 1,
      currImgIndex: 0, // 当前图片索引
      rejectReasonDlg: false, // 驳回对话框状态
      rejectArr: [], // 驳回数组
      ruleForm: {
        reason: "", // 驳回原因
      },
      reasonDict: [
        { value: "1", label: "拍摄不完整" },
        { value: "2", label: "拍摄曝光" },
        { value: "3", label: "文字拍摄不清晰" },
        { value: "4", label: "不需要" }, //
        { value: "5", label: "图片缺失" }, // 1.5.3 新增 图片缺失
      ],
    };
  },
  computed: {
    // 单据编号
    applyCode() {
      return this.imgData.applyCode;
    },

    // 原图提交人
    originalUser() {
      return this.imgData.originalUser;
    },

    // 原图商品数量
    originalProductNum() {
      return this.imgData.originalProductNum;
    },

    // 原图上传数量
    originalPictureNum() {
      return this.imgData.originalPictureNum;
    },

    // 所有图片
    imgList() {
      return this.imgData.imgList ? this.imgData.imgList : [];
    },

    // 当前图片数据
    currImgData() {
      return this.imgList.length ? this.imgList[this.currImgIndex] : {};
    },

    // 标题
    title() {
      return this.imgList.length
        ? [
            this.applyCode,
            this.originalUser,
            `${this.currImgData.skuOrder}/${this.originalProductNum}`,
            `${this.currImgIndex + 1}/${this.originalPictureNum}`,
          ].join("_")
        : "";
    },

    // 子标题
    subTitle() {
      return this.imgList.length
        ? `${this.currImgData.productCode || ""}_${
            this.currImgData.productName || ""
          }_${this.currImgData.pictureVersion || ""}-${
            this.currImgData.pictureOrdinal || ""
          }`
        : "";
    },

    // 驳回sku总数
    rejectSkuNum() {
      let arr1 = this.rejectArr
        .filter((item) => {
          return item.auditStatus == 2; // 驳回
        })
        .map((item) => {
          return item.productCode;
        });
      let arr2 = arr1.reduce(function (prev, cur, index, arr) {
        // console.log(prev, cur);
        return !prev.includes(cur) ? prev.concat(cur) : prev;
      }, []);
      return arr2.length;
    },

    // 驳回pic总数
    rejectpictureOrdinal() {
      return this.rejectArr.length;
    },

    // 删除pic总数
    deletePictureOrdinal() {
      let arr = this.rejectArr.filter((item) => {
        return item.auditStatus == 3; // 删除
      });
      return arr.length;
    },

    // 当前商品驳回状态
    rejectStatus() {
      let arr = this.rejectArr.filter((item) => {
        return item.pictureId == this.currImgData.pictureId;
      });

      return arr.length && arr[0].auditStatus == 2 ? true : false;
    },

    // 当前商品删除状态
    deleteStatus() {
      let arr = this.rejectArr.filter((item) => {
        return item.pictureId == this.currImgData.pictureId;
      });
      // 判断当前图片是否是删除状态
      return arr.length && arr[0].auditStatus == 3 ? true : false;
    },
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    // 关闭的回调
    closeDlg() {
      this.$emit("update:show", false);
      this.scale = 1;
      this.currImgIndex = 0; // 当前图片索引
      this.rejectArr = []; // 驳回数组
      this.ruleForm.reason = ""; // 驳回原因
    },

    // 打开的回调
    openDlg() {
      this.currImgIndex = 0;
    },

    // 上一张 下一张 图片
    change(index) {
      this.currImgIndex = index;
      this.scale != 1 && this.styleRecovery();
    },

    // 放大
    toBig() {
      let el = this.$refs.img[this.currImgIndex].style;
      if (this.scale >= 5) {
        this.$message.error("已经达到最大尺寸");
      } else {
        this.scale += 0.2;
        el.setProperty("transform", `scale(${this.scale.toFixed(2)})`);
      }
    },

    // 缩小
    toSmall() {
      let el = this.$refs.img[this.currImgIndex].style;
      if (this.scale <= 0.22) {
        this.$message.error("已经达到最小尺寸");
      } else {
        this.scale -= 0.2;
        el.setProperty("transform", `scale(${this.scale.toFixed(2)})`);
      }
    },

    // 图片样式复原
    styleRecovery() {
      this.scale = 1;
      let el = this.$refs.img[this.currImgIndex].style;
      el.setProperty("transform", `scale(1)`);
    },

    // 修改为驳回状态
    changeRejectStatus(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // ------驳回当前sku商品所有图片 并过滤 readonly == 1只读数据
          let arr1 = this.imgList
            .filter((item) => {
              return (
                item.productCode == this.currImgData.productCode &&
                item.readonly != 1
              );
            })
            .map((item) => {
              let {
                productCode,
                pictureOrdinal,
                pictureId,
                pictureUrl,
                pictureName,
              } = item;
              return {
                productCode,
                pictureOrdinal,
                pictureId,
                pictureUrl,
                pictureName,
                auditStatus: 2, // 审核状态(0:待审核, 1:通过, 2:驳回 3:删除)
                rejectReason: this.ruleForm.reason,
              };
            });

          // 从驳回数组中找出不是当前被驳回商品的所有图片
          let arr2 = this.rejectArr.filter((item) => {
            return item.productCode != this.currImgData.productCode;
          });

          this.rejectArr = [...arr1, ...arr2];

          this.rejectReasonDlg = false;
        } else {
          return false;
        }
      });
    },

    // 取消驳回
    cancelReject() {
      //  this.$confirm("是否取消驳回本图？", "提示", {
      this.$confirm("是否取消驳回本SKU？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then((_) => {
          // 取消驳回当前sku 所有图片
          this.rejectArr = this.rejectArr.filter((item) => {
            return item.productCode != this.currImgData.productCode;
          });
        })
        .catch((_) => {});
    },

    // deleteOriginal 单个删除原图
    deleteOriginal() {
      let {
        productCode,
        pictureOrdinal,
        pictureId,
        pictureUrl,
        pictureName,
      } = this.currImgData;
      // 处理驳回数据状态
      let curObj = {
        productCode,
        pictureOrdinal,
        pictureId,
        pictureUrl,
        pictureName,
        auditStatus: 3, // 审核状态(0:待审核, 1:通过, 2:驳回, 3:删除)
        rejectReason: "",
      };
      // 从驳回数组中找出当前被删除图片的位置
      let index = this.rejectArr.findIndex((item) => {
        return item.pictureId == this.currImgData.pictureId;
      });
      // 修改对应位置的数据
      index == -1
        ? this.rejectArr.push(curObj)
        : this.$set(this.rejectArr, index, curObj);
    },

    // cancelDelete
    cancelDelete() {
      // 取消删除当前图片
      let index = this.rejectArr.findIndex((item) => {
        return item.pictureId == this.currImgData.pictureId;
      });
      index !== -1 && this.rejectArr.splice(index, 1);
    },

    // 提交
    submit() {
      this.$confirm(
        //是否将${this.rejectSkuNum}个sku中的${this.rejectpictureOrdinal}张原图驳回
        `是否确认驳回${this.rejectSkuNum}个sku，删除${this.deletePictureOrdinal}张图片？`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(async () => {
          let reasonArr = this.rejectArr.map((item) => {
            return item.rejectReason;
          });
          let reasonStr = [...new Set(reasonArr)].join(";");

          // 整理数据 提交
          let params = {
            applyCode: this.applyCode, // 	单据编号
            originalRejectReason: reasonStr, //	原图驳回原因 去重拼接
            rejectOriginalPictureList: this.rejectArr,
          };

          console.log(params, "提交");

          let data = await postRejectOriginal(params);
          if (data.retCode == 0) {
            this.$message.success("提交成功！");
            // 提交成功 要刷新列表（操作父组件）
            this.$emit("refresh", true);
            this.closeDlg();
          } else {
            this.$message.error("操作失败！");
          }
        })
        .catch((_) => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.preview-original {
  .el-dialog__wrapper.dlg1 /deep/ .el-dialog .el-dialog__body {
    padding: 20px 0;
  }

  .el-dialog__wrapper /deep/ .el-dialog {
    top: 50% !important;
  }

  h3 {
    margin: 0 0 20px;
  }

  .height41 {
    height: 41px;
  }

  .p {
    margin: 0;
    padding-top: 20px;
  }

  .red {
    color: red;
  }

  .txt-center {
    text-align: center;
  }

  .pic-box {
    position: relative;
    .reject-png {
      position: absolute;
      width: 150px;
      top: 0;
      right: 0;
      z-index: 100;
    }
    .delete-png {
      position: absolute;
      width: 100px;
      top: 0;
      right: 20px;
      z-index: 100;
    }
  }

  .dlg-footer {
    display: inline-block;
    width: 100%;
    clear: both;
    .btn-lf {
      float: left;
    }
  }

  .el-carousel__container .is-active /deep/ .img-l {
    position: relative;
    width: 800px;
    height: 400px;
    overflow: auto;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>